 steps:
  # Build maven application
 - name: maven:3.8.3-openjdk-17
   entrypoint: mvn
   args: ['package','-Dmaven.test.skip=true']
 # Build the container image
 - name: 'gcr.io/cloud-builders/docker'
   args: ['build', '-t', 'southamerica-east1-docker.pkg.dev/inu-ct-2209/registrydocker/clinicall-backend-hml:$COMMIT_SHA', '-f' ,'./clinicall-web/Dockerfile', '.']
 # Push the container image to Container Registry'/
 - name: 'gcr.io/cloud-builders/docker'
   args: ['push', 'southamerica-east1-docker.pkg.dev/inu-ct-2209/registrydocker/clinicall-backend-hml:$COMMIT_SHA']
 # Deploy container image to Cloud Run
 - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
   entrypoint: gcloud
   args:
   - 'run'
   - 'deploy'
   - 'clinicall-backend-hml'
   - '--image'
   - 'southamerica-east1-docker.pkg.dev/inu-ct-2209/registrydocker/clinicall-backend-hml:$COMMIT_SHA'
   - '--region'
   - 'southamerica-east1'
 images:
 - 'southamerica-east1-docker.pkg.dev/inu-ct-2209/registrydocker/clinicall-backend-hml:$COMMIT_SHA'