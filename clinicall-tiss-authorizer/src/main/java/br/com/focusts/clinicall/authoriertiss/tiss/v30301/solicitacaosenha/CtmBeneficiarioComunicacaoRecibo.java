
package br.com.focusts.clinicall.authoriertiss.tiss.v30301.solicitacaosenha;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * &lt;p&gt;Java class for ctm_beneficiarioComunicacaoRecibo complex type.
 * 
 * &lt;p&gt;The following schema fragment specifies the expected content contained within this class.
 * 
 * &lt;pre&gt;
 * &amp;lt;complexType name="ctm_beneficiarioComunicacaoRecibo"&amp;gt;
 *   &amp;lt;complexContent&amp;gt;
 *     &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
 *       &amp;lt;sequence&amp;gt;
 *         &amp;lt;element name="statusComunicacao" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_simNao"/&amp;gt;
 *         &amp;lt;element name="beneficiarioComunicacao" type="{http://www.ans.gov.br/padroes/tiss/schemas}ctm_beneficiarioComunicacao"/&amp;gt;
 *         &amp;lt;element name="mensagemErro" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_motivoGlosa" minOccurs="0"/&amp;gt;
 *       &amp;lt;/sequence&amp;gt;
 *     &amp;lt;/restriction&amp;gt;
 *   &amp;lt;/complexContent&amp;gt;
 * &amp;lt;/complexType&amp;gt;
 * &lt;/pre&gt;
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ctm_beneficiarioComunicacaoRecibo", propOrder = {
    "statusComunicacao",
    "beneficiarioComunicacao",
    "mensagemErro"
})
public class CtmBeneficiarioComunicacaoRecibo {

    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected DmSimNao statusComunicacao;
    @XmlElement(required = true)
    protected CtmBeneficiarioComunicacao beneficiarioComunicacao;
    protected CtMotivoGlosa mensagemErro;

    /**
     * Gets the value of the statusComunicacao property.
     * 
     * @return
     *     possible object is
     *     {@link DmSimNao }
     *     
     */
    public DmSimNao getStatusComunicacao() {
        return statusComunicacao;
    }

    /**
     * Sets the value of the statusComunicacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link DmSimNao }
     *     
     */
    public void setStatusComunicacao(DmSimNao value) {
        this.statusComunicacao = value;
    }

    /**
     * Gets the value of the beneficiarioComunicacao property.
     * 
     * @return
     *     possible object is
     *     {@link CtmBeneficiarioComunicacao }
     *     
     */
    public CtmBeneficiarioComunicacao getBeneficiarioComunicacao() {
        return beneficiarioComunicacao;
    }

    /**
     * Sets the value of the beneficiarioComunicacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtmBeneficiarioComunicacao }
     *     
     */
    public void setBeneficiarioComunicacao(CtmBeneficiarioComunicacao value) {
        this.beneficiarioComunicacao = value;
    }

    /**
     * Gets the value of the mensagemErro property.
     * 
     * @return
     *     possible object is
     *     {@link CtMotivoGlosa }
     *     
     */
    public CtMotivoGlosa getMensagemErro() {
        return mensagemErro;
    }

    /**
     * Sets the value of the mensagemErro property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtMotivoGlosa }
     *     
     */
    public void setMensagemErro(CtMotivoGlosa value) {
        this.mensagemErro = value;
    }

}
