
package br.com.focusts.clinicall.authoriertiss.tiss.v40100.webservice;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * &lt;p&gt;Classe Java de anonymous complex type.
 * 
 * &lt;p&gt;O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * &lt;pre&gt;
 * &amp;lt;complexType&amp;gt;
 *   &amp;lt;complexContent&amp;gt;
 *     &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
 *       &amp;lt;sequence&amp;gt;
 *         &amp;lt;element name="tissFault" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_tissFault"/&amp;gt;
 *       &amp;lt;/sequence&amp;gt;
 *     &amp;lt;/restriction&amp;gt;
 *   &amp;lt;/complexContent&amp;gt;
 * &amp;lt;/complexType&amp;gt;
 * &lt;/pre&gt;
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "tissFault"
})
@XmlRootElement(name = "tissFaultWS")
public class TissFaultWS {

    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected StTissFault tissFault;

    /**
     * Obtém o valor da propriedade tissFault.
     * 
     * @return
     *     possible object is
     *     {@link StTissFault }
     *     
     */
    public StTissFault getTissFault() {
        return tissFault;
    }

    /**
     * Define o valor da propriedade tissFault.
     * 
     * @param value
     *     allowed object is
     *     {@link StTissFault }
     *     
     */
    public void setTissFault(StTissFault value) {
        this.tissFault = value;
    }

}
