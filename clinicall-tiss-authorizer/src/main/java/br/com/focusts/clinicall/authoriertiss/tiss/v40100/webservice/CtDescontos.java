
package br.com.focusts.clinicall.authoriertiss.tiss.v40100.webservice;

import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * &lt;p&gt;Classe Java de ct_descontos complex type.
 * 
 * &lt;p&gt;O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * &lt;pre&gt;
 * &amp;lt;complexType name="ct_descontos"&amp;gt;
 *   &amp;lt;complexContent&amp;gt;
 *     &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
 *       &amp;lt;sequence&amp;gt;
 *         &amp;lt;element name="indicador" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_debitoCreditoIndicador"/&amp;gt;
 *         &amp;lt;element name="tipoDebitoCredito" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_debitoCreditoTipo"/&amp;gt;
 *         &amp;lt;element name="descricaoDbCr" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_texto40"/&amp;gt;
 *         &amp;lt;element name="valorDbCr" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_decimal8-2"/&amp;gt;
 *       &amp;lt;/sequence&amp;gt;
 *     &amp;lt;/restriction&amp;gt;
 *   &amp;lt;/complexContent&amp;gt;
 * &amp;lt;/complexType&amp;gt;
 * &lt;/pre&gt;
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_descontos", propOrder = {
    "indicador",
    "tipoDebitoCredito",
    "descricaoDbCr",
    "valorDbCr"
})
public class CtDescontos {

    @XmlElement(required = true)
    protected String indicador;
    @XmlElement(required = true)
    protected String tipoDebitoCredito;
    @XmlElement(required = true)
    protected String descricaoDbCr;
    @XmlElement(required = true)
    protected BigDecimal valorDbCr;

    /**
     * Obtém o valor da propriedade indicador.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndicador() {
        return indicador;
    }

    /**
     * Define o valor da propriedade indicador.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndicador(String value) {
        this.indicador = value;
    }

    /**
     * Obtém o valor da propriedade tipoDebitoCredito.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoDebitoCredito() {
        return tipoDebitoCredito;
    }

    /**
     * Define o valor da propriedade tipoDebitoCredito.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoDebitoCredito(String value) {
        this.tipoDebitoCredito = value;
    }

    /**
     * Obtém o valor da propriedade descricaoDbCr.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescricaoDbCr() {
        return descricaoDbCr;
    }

    /**
     * Define o valor da propriedade descricaoDbCr.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescricaoDbCr(String value) {
        this.descricaoDbCr = value;
    }

    /**
     * Obtém o valor da propriedade valorDbCr.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getValorDbCr() {
        return valorDbCr;
    }

    /**
     * Define o valor da propriedade valorDbCr.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setValorDbCr(BigDecimal value) {
        this.valorDbCr = value;
    }

}
