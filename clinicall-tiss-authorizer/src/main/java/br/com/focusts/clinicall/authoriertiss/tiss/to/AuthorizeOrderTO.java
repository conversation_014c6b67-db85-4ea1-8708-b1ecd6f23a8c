package br.com.focusts.clinicall.authoriertiss.tiss.to;

import lombok.Getter;
import lombok.Setter;

import java.math.BigInteger;
import java.time.LocalDate;
@Getter
@Setter
public class AuthorizeOrderTO {

    private String number;
    private String operatorGuideNumber;
    private String enrollment;
    private String typeGuide;
    private LocalDate date;
    private String namePatient;
    private Boolean newBorn;
    private String nameCompany;
    private String cnpjCompany;
    private String cnesCompany;
    private String nameProfessional;
    private String councilTypeProfessional;
    private String councilNumberProfessional;
    private String ufProfessional;
    private String cbosProfessional;
    private String cid;
    private String serviceCharacter;
    private String nameProcedure;
    private String tableProcedure;
    private String codeProcedure;
    private BigInteger quantity;
    private String password;

}
