
package br.com.focusts.clinicall.authoriertiss.tiss.v30301.solicitacaosenha;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * &lt;p&gt;Java class for dm_statusComunicacaoBeneficiario.
 * 
 * &lt;p&gt;The following schema fragment specifies the expected content contained within this class.
 * &lt;pre&gt;
 * &amp;lt;simpleType name="dm_statusComunicacaoBeneficiario"&amp;gt;
 *   &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&amp;gt;
 *     &amp;lt;enumeration value="P"/&amp;gt;
 *     &amp;lt;enumeration value="B"/&amp;gt;
 *   &amp;lt;/restriction&amp;gt;
 * &amp;lt;/simpleType&amp;gt;
 * &lt;/pre&gt;
 * 
 */
@XmlType(name = "dm_statusComunicacaoBeneficiario")
@XmlEnum
public enum DmStatusComunicacaoBeneficiario {

    P,
    B;

    public String value() {
        return name();
    }

    public static DmStatusComunicacaoBeneficiario fromValue(String v) {
        return valueOf(v);
    }

}
