package br.com.focusts.clinicall.authoriertiss.tiss.v40100.webservice;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 3.4.0
 * 2023-06-26T10:37:14.522-03:00
 * Generated source version: 3.4.0
 *
 */
@WebService(targetNamespace = "http://www.ans.gov.br/tiss/ws/tipos/tissenviodocumentos/v40100", name = "tissEnvioDocumentos_PortType")
@XmlSeeAlso({ObjectFactory.class})
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface TissEnvioDocumentosPortType {

    @WebMethod(operationName = "tissEnvioDocumentos_Operation")
    @WebResult(name = "reciboDocumentosWS", targetNamespace = "http://www.ans.gov.br/padroes/tiss/schemas", partName = "reciboDocumentos")
    public ReciboDocumentosWS tissEnvioDocumentosOperation(

        @WebParam(partName = "envioDocumentos", name = "envioDocumentoWS", targetNamespace = "http://www.ans.gov.br/padroes/tiss/schemas")
        EnvioDocumentoWS envioDocumentos
    ) throws TissFault;
}
