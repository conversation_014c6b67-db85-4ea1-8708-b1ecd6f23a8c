
package br.com.focusts.clinicall.authoriertiss.tiss.v30301.solicitacaosenha;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * &lt;p&gt;Java class for ct_situacaoProtocolo complex type.
 * 
 * &lt;p&gt;The following schema fragment specifies the expected content contained within this class.
 * 
 * &lt;pre&gt;
 * &amp;lt;complexType name="ct_situacaoProtocolo"&amp;gt;
 *   &amp;lt;complexContent&amp;gt;
 *     &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
 *       &amp;lt;choice&amp;gt;
 *         &amp;lt;element name="mensagemErro" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_motivoGlosa"/&amp;gt;
 *         &amp;lt;element name="situacaoDoProtocolo" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_protocoloStatus"/&amp;gt;
 *         &amp;lt;element name="situacaoProtocoloAnexo" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_protocoloAnexoStatus"/&amp;gt;
 *       &amp;lt;/choice&amp;gt;
 *     &amp;lt;/restriction&amp;gt;
 *   &amp;lt;/complexContent&amp;gt;
 * &amp;lt;/complexType&amp;gt;
 * &lt;/pre&gt;
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_situacaoProtocolo", propOrder = {
    "mensagemErro",
    "situacaoDoProtocolo",
    "situacaoProtocoloAnexo"
})
public class CtSituacaoProtocolo {

    protected CtMotivoGlosa mensagemErro;
    protected CtProtocoloStatus situacaoDoProtocolo;
    protected CtProtocoloAnexoStatus situacaoProtocoloAnexo;

    /**
     * Gets the value of the mensagemErro property.
     * 
     * @return
     *     possible object is
     *     {@link CtMotivoGlosa }
     *     
     */
    public CtMotivoGlosa getMensagemErro() {
        return mensagemErro;
    }

    /**
     * Sets the value of the mensagemErro property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtMotivoGlosa }
     *     
     */
    public void setMensagemErro(CtMotivoGlosa value) {
        this.mensagemErro = value;
    }

    /**
     * Gets the value of the situacaoDoProtocolo property.
     * 
     * @return
     *     possible object is
     *     {@link CtProtocoloStatus }
     *     
     */
    public CtProtocoloStatus getSituacaoDoProtocolo() {
        return situacaoDoProtocolo;
    }

    /**
     * Sets the value of the situacaoDoProtocolo property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtProtocoloStatus }
     *     
     */
    public void setSituacaoDoProtocolo(CtProtocoloStatus value) {
        this.situacaoDoProtocolo = value;
    }

    /**
     * Gets the value of the situacaoProtocoloAnexo property.
     * 
     * @return
     *     possible object is
     *     {@link CtProtocoloAnexoStatus }
     *     
     */
    public CtProtocoloAnexoStatus getSituacaoProtocoloAnexo() {
        return situacaoProtocoloAnexo;
    }

    /**
     * Sets the value of the situacaoProtocoloAnexo property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtProtocoloAnexoStatus }
     *     
     */
    public void setSituacaoProtocoloAnexo(CtProtocoloAnexoStatus value) {
        this.situacaoProtocoloAnexo = value;
    }

}
