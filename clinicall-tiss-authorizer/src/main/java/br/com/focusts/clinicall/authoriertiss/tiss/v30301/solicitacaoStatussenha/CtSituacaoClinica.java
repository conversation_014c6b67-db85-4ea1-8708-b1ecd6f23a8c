
package br.com.focusts.clinicall.authoriertiss.tiss.v30301.solicitacaoStatussenha;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * &lt;p&gt;Java class for ct_situacaoClinica complex type.
 * 
 * &lt;p&gt;The following schema fragment specifies the expected content contained within this class.
 * 
 * &lt;pre&gt;
 * &amp;lt;complexType name="ct_situacaoClinica"&amp;gt;
 *   &amp;lt;complexContent&amp;gt;
 *     &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
 *       &amp;lt;sequence&amp;gt;
 *         &amp;lt;element name="dentes" maxOccurs="unbounded"&amp;gt;
 *           &amp;lt;complexType&amp;gt;
 *             &amp;lt;complexContent&amp;gt;
 *               &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
 *                 &amp;lt;sequence&amp;gt;
 *                   &amp;lt;element name="elementoDentario" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_dente"/&amp;gt;
 *                   &amp;lt;element name="condicaoClinica" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_condicaoClinica"/&amp;gt;
 *                 &amp;lt;/sequence&amp;gt;
 *               &amp;lt;/restriction&amp;gt;
 *             &amp;lt;/complexContent&amp;gt;
 *           &amp;lt;/complexType&amp;gt;
 *         &amp;lt;/element&amp;gt;
 *       &amp;lt;/sequence&amp;gt;
 *     &amp;lt;/restriction&amp;gt;
 *   &amp;lt;/complexContent&amp;gt;
 * &amp;lt;/complexType&amp;gt;
 * &lt;/pre&gt;
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_situacaoClinica", propOrder = {
    "dentes"
})
public class CtSituacaoClinica {

    @XmlElement(required = true)
    protected List<CtSituacaoClinica.Dentes> dentes;

    /**
     * Gets the value of the dentes property.
     * 
     * &lt;p&gt;
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a &lt;CODE&gt;set&lt;/CODE&gt; method for the dentes property.
     * 
     * &lt;p&gt;
     * For example, to add a new item, do as follows:
     * &lt;pre&gt;
     *    getDentes().add(newItem);
     * &lt;/pre&gt;
     * 
     * 
     * &lt;p&gt;
     * Objects of the following type(s) are allowed in the list
     * {@link CtSituacaoClinica.Dentes }
     * 
     * 
     */
    public List<CtSituacaoClinica.Dentes> getDentes() {
        if (dentes == null) {
            dentes = new ArrayList<CtSituacaoClinica.Dentes>();
        }
        return this.dentes;
    }


    /**
     * &lt;p&gt;Java class for anonymous complex type.
     * 
     * &lt;p&gt;The following schema fragment specifies the expected content contained within this class.
     * 
     * &lt;pre&gt;
     * &amp;lt;complexType&amp;gt;
     *   &amp;lt;complexContent&amp;gt;
     *     &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
     *       &amp;lt;sequence&amp;gt;
     *         &amp;lt;element name="elementoDentario" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_dente"/&amp;gt;
     *         &amp;lt;element name="condicaoClinica" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_condicaoClinica"/&amp;gt;
     *       &amp;lt;/sequence&amp;gt;
     *     &amp;lt;/restriction&amp;gt;
     *   &amp;lt;/complexContent&amp;gt;
     * &amp;lt;/complexType&amp;gt;
     * &lt;/pre&gt;
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "elementoDentario",
        "condicaoClinica"
    })
    public static class Dentes {

        @XmlElement(required = true)
        protected String elementoDentario;
        @XmlElement(required = true)
        @XmlSchemaType(name = "string")
        protected DmCondicaoClinica condicaoClinica;

        /**
         * Gets the value of the elementoDentario property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getElementoDentario() {
            return elementoDentario;
        }

        /**
         * Sets the value of the elementoDentario property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setElementoDentario(String value) {
            this.elementoDentario = value;
        }

        /**
         * Gets the value of the condicaoClinica property.
         * 
         * @return
         *     possible object is
         *     {@link DmCondicaoClinica }
         *     
         */
        public DmCondicaoClinica getCondicaoClinica() {
            return condicaoClinica;
        }

        /**
         * Sets the value of the condicaoClinica property.
         * 
         * @param value
         *     allowed object is
         *     {@link DmCondicaoClinica }
         *     
         */
        public void setCondicaoClinica(DmCondicaoClinica value) {
            this.condicaoClinica = value;
        }

    }

}
