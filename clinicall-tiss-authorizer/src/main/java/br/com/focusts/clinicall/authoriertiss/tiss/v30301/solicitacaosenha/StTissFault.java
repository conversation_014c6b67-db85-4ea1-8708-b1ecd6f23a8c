
package br.com.focusts.clinicall.authoriertiss.tiss.v30301.solicitacaosenha;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * &lt;p&gt;Java class for st_tissFault.
 * 
 * &lt;p&gt;The following schema fragment specifies the expected content contained within this class.
 * &lt;pre&gt;
 * &amp;lt;simpleType name="st_tissFault"&amp;gt;
 *   &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&amp;gt;
 *     &amp;lt;enumeration value="DestinatarioInvalido"/&amp;gt;
 *     &amp;lt;enumeration value="RemetenteInvalido"/&amp;gt;
 *     &amp;lt;enumeration value="LoginInvalido"/&amp;gt;
 *     &amp;lt;enumeration value="VersaoInvalida"/&amp;gt;
 *     &amp;lt;enumeration value="HashInvalido"/&amp;gt;
 *     &amp;lt;enumeration value="SchemaInvalido"/&amp;gt;
 *     &amp;lt;enumeration value="ErroInesperadoServidor"/&amp;gt;
 *   &amp;lt;/restriction&amp;gt;
 * &amp;lt;/simpleType&amp;gt;
 * &lt;/pre&gt;
 * 
 */
@XmlType(name = "st_tissFault")
@XmlEnum
public enum StTissFault {

    @XmlEnumValue("DestinatarioInvalido")
    DESTINATARIO_INVALIDO("DestinatarioInvalido"),
    @XmlEnumValue("RemetenteInvalido")
    REMETENTE_INVALIDO("RemetenteInvalido"),
    @XmlEnumValue("LoginInvalido")
    LOGIN_INVALIDO("LoginInvalido"),
    @XmlEnumValue("VersaoInvalida")
    VERSAO_INVALIDA("VersaoInvalida"),
    @XmlEnumValue("HashInvalido")
    HASH_INVALIDO("HashInvalido"),
    @XmlEnumValue("SchemaInvalido")
    SCHEMA_INVALIDO("SchemaInvalido"),
    @XmlEnumValue("ErroInesperadoServidor")
    ERRO_INESPERADO_SERVIDOR("ErroInesperadoServidor");
    private final String value;

    StTissFault(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static StTissFault fromValue(String v) {
        for (StTissFault c: StTissFault.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
