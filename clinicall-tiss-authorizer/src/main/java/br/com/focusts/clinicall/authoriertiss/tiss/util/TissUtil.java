package br.com.focusts.clinicall.authoriertiss.tiss.util;

import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringReader;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.security.*;
import java.security.cert.X509Certificate;
import java.text.Normalizer;
import java.util.Collections;
import java.util.Date;
import java.util.Enumeration;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.PropertyException;
import javax.xml.bind.Unmarshaller;
import javax.xml.crypto.dsig.CanonicalizationMethod;
import javax.xml.crypto.dsig.DigestMethod;
import javax.xml.crypto.dsig.Reference;
import javax.xml.crypto.dsig.SignedInfo;
import javax.xml.crypto.dsig.Transform;
import javax.xml.crypto.dsig.XMLSignature;
import javax.xml.crypto.dsig.XMLSignatureFactory;
import javax.xml.crypto.dsig.dom.DOMSignContext;
import javax.xml.crypto.dsig.keyinfo.KeyInfo;
import javax.xml.crypto.dsig.keyinfo.KeyInfoFactory;
import javax.xml.crypto.dsig.keyinfo.X509Data;
import javax.xml.crypto.dsig.spec.C14NMethodParameterSpec;
import javax.xml.crypto.dsig.spec.TransformParameterSpec;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import br.com.focusts.clinicall.fw.exception.ApplicationException;
import br.com.focusts.clinicall.util.AESUtil;
import br.com.focusts.clinicall.util.FileUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.w3c.dom.Attr;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

public class TissUtil {


    public static String getAtributosConcatenados(Node pNodo) {
        String w_resp = "";

        if (pNodo instanceof Node) {
            if (pNodo.hasChildNodes()) {
                NodeList w_nl = pNodo.getChildNodes();
                for (int i = 0; i < w_nl.getLength(); i++) {
                    Node w_n = w_nl.item(i);
                    w_resp += getAtributosConcatenados(w_n);
                }
            } else {
                w_resp = pNodo.getTextContent().trim();
            }
        }
        return w_resp;
    }

    public static String getStringConcatenados(Document xmlDocument,  String tagName) {

        Node nodeCabecalhoCorpo = pegarCabecalho(xmlDocument, tagName);
        Node nodeCorpo = pegarCorpo(xmlDocument, tagName);

        String objsConcatenados = getAtributosConcatenados(nodeCabecalhoCorpo) + getAtributosConcatenados(nodeCorpo);

        return objsConcatenados;
    }

    public static StringWriter sign(String xml, String base64Certificate, String password) throws Exception {

        File xmlFile = createXmlFile(xml);

        // Decodifica a representação base64 para um array de bytes
        password = AESUtil.decrypt(password);
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        Document document = dbf.newDocumentBuilder().parse(xmlFile);

        // Load your keystore and get the private key and certificate
        KeyStore keystore = validateKeyStoreCertificate(base64Certificate, password);
        String alias = "";
        Enumeration
                <String> aliases = keystore.aliases();
        while (aliases.hasMoreElements()) {
            alias = aliases.nextElement();
        }


        PrivateKey privateKey = (PrivateKey) keystore.getKey(alias, password.toCharArray());
        X509Certificate cert = validateKeyStoreExpiration(keystore, alias);

        // Create a DOMSignContext
        DOMSignContext dsc = new DOMSignContext(privateKey, document.getDocumentElement());

        // Create an XMLSignatureFactory
        XMLSignatureFactory fac = XMLSignatureFactory.getInstance("DOM");

        // Create a Reference to the document's content
        Reference ref = fac.newReference("", fac.newDigestMethod(DigestMethod.SHA256, null),
                Collections.singletonList(fac.newTransform(Transform.ENVELOPED, (TransformParameterSpec) null)), null, null);

        // Create the SignedInfo
        SignedInfo si = fac.newSignedInfo(
                fac.newCanonicalizationMethod(CanonicalizationMethod.INCLUSIVE, (C14NMethodParameterSpec) null),
                fac.newSignatureMethod("http://www.w3.org/2001/04/xmldsig-more#rsa-sha256", null), Collections.singletonList(ref));

        // Create a KeyInfo containing the X.509 certificate
        KeyInfoFactory kif = fac.getKeyInfoFactory();
        X509Data x509Data = kif.newX509Data(Collections.singletonList(cert));
        KeyInfo ki = kif.newKeyInfo(Collections.singletonList(x509Data));

        // Create the XML Signature
        XMLSignature signature = fac.newXMLSignature(si, ki);

        // Sign the document
        signature.sign(dsc);

        Element signatureElement = (Element) document.getElementsByTagNameNS("http://www.w3.org/2000/09/xmldsig#", "Signature").item(0);
        Attr attributeToRemove = signatureElement.getAttributeNode("xmlns");
        signatureElement.removeAttributeNode(attributeToRemove);
        signatureElement.removeAttribute(attributeToRemove.getName());

        // Update all child elements of Signature to use the ns2 namespace prefix
        updateNamespace(signatureElement, "ns2");


        // Print or save the signed document
//        printXmlDocument(document);

        return convertDocumentToXml(document);
    }

    public static X509Certificate validateKeyStoreExpiration(KeyStore keystore, String alias) {
        X509Certificate cert = null;
        try {
            cert = (X509Certificate) keystore.getCertificate(alias);
        } catch (KeyStoreException e) {
            throw new ApplicationException("Problema para processar o certificado.");
        }

        Date currentDate = new Date();
        var teste = cert.getNotAfter();
        System.out.println("validade");
        System.out.println(teste);
        if (currentDate.before(cert.getNotBefore())) {
            throw new ApplicationException("Certificado ainda não é válido.");
        }

        if (currentDate.after(cert.getNotAfter())) {
            throw new ApplicationException("Certificado expirou.");
        }
        return cert;
    }

    public static void updateNamespace(Element element, String newPrefix) {
        for (Node child = element.getFirstChild(); child != null; child = child.getNextSibling()) {
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                Element childElement = (Element) child;
                childElement.setPrefix(newPrefix);
                updateNamespace(childElement, newPrefix);
            }
        }
    }

    public static KeyStore validateKeyStoreCertificate(String certificadoBase64, String password) throws KeyStoreException {
        byte[] certificateBytes = java.util.Base64.getDecoder().decode(certificadoBase64);
        KeyStore keystore = KeyStore.getInstance("PKCS12");
        try {
            keystore.load(new ByteArrayInputStream(certificateBytes), password.toCharArray());
        }catch (Exception e){
            if (e.getMessage().equals("keystore password was incorrect")){
                throw new ApplicationException("Senha do Certificado incorreta");
            }else {
                throw new ApplicationException("Problema para processar o certificado.");
            }
        }
     return keystore;
    }


    public static StringWriter convertDocumentToXml(Document document) throws Exception {
        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();

        // Convert DOMSource to a string
        StringWriter writer = new StringWriter();
        transformer.transform(new DOMSource(document), new StreamResult(writer));

        return writer;
    }


    public static void printXmlDocument(Document document) {
        try {
            // Create a Transformer
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();

            // Configure transformer settings (optional)
            transformer.setOutputProperty("indent", "yes");
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");

            // Create a DOMSource from the Document
            DOMSource source = new DOMSource(document);

            // Create a StreamResult to print to the console
            StreamResult consoleResult = new StreamResult(System.out);

            // Transform the DOMSource to the StreamResult
            transformer.transform(source, consoleResult);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static File createXmlFile(String xmlContenth) throws IOException {
        String filePathXml = FileUtil.generateRandonFilename("xml");

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePathXml))) {
            writer.write(xmlContenth);
        }

        return new File(filePathXml);
    }


    protected static Node pegarCabecalho(Document xmlDocument, String tagName) {
        Node node = null;
        NodeList nodeCabecalho = xmlDocument.getElementsByTagName(tagName+":cabecalho");
        if (nodeCabecalho.getLength() > 0) {
            node = nodeCabecalho.item(0);
        }

        return node;
    }

    protected static Node pegarCorpo(Document xmlDocument, String tagName) {
        Node node = null;
        NodeList nodeCorpoPrestador = xmlDocument.getElementsByTagName(tagName+":operadoraParaPrestador");
        NodeList nodeCorpoOperadora = xmlDocument.getElementsByTagName(tagName+":prestadorParaOperadora");
        if (nodeCorpoPrestador.getLength() > 0) {
            node = nodeCorpoPrestador.item(0);
        }
        if (nodeCorpoOperadora.getLength() > 0) {
            node = nodeCorpoOperadora.item(0);
        }

        return node;
    }

    public static String gerarHas(String objConcatenados,  String codification) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        String wdgs = null;
        MessageDigest wmd = MessageDigest.getInstance("MD5");
        wmd.reset();
        if (codification.equals("ISO-8859-1")) {
            wmd.update(objConcatenados.getBytes(StandardCharsets.ISO_8859_1));
        } else {
            wmd.update(objConcatenados.getBytes());
        }
        byte[] wdg = wmd.digest();
        StringBuffer hexString = new StringBuffer();
        for (int i = 0; i < wdg.length; i++) {
            String w_dup = Integer.toHexString(0xFF & wdg[i]);
            if (w_dup.length() < 2)
                w_dup = "0" + w_dup;
            hexString.append(w_dup);
        }
        return wdgs = hexString.toString();
    }

    public static  String processXmlHash(StringWriter writer, String condingXML, String tag)
            throws SAXException, IOException, ParserConfigurationException, NoSuchAlgorithmException {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document xmlDocument = builder.parse(new InputSource(new StringReader(writer.toString())));
        var conding = "UTF-8";
        if (condingXML.equals("ISO-8859-1")) {
            conding = "ISO-8859-1";
        }
        return gerarHas(getStringConcatenados(xmlDocument, tag), conding);
    }

    public static String convertToMD5(String originalString) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            byte[] digest = messageDigest.digest(originalString.getBytes());
            BigInteger bigInt = new BigInteger(1, digest);
            String md5String = bigInt.toString(16);
            while (md5String.length() < 32) {
                md5String = "0" + md5String;
            }
            return md5String;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static StringWriter generateXml(Object requestWS) throws JAXBException, PropertyException {
        StringWriter writer = new StringWriter();
        JAXBContext jaxbContext = JAXBContext.newInstance(requestWS.getClass());
        Marshaller jaxbMarshaller = jaxbContext.createMarshaller();
        jaxbMarshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
        jaxbMarshaller.marshal(requestWS, writer);
        return writer;
    }


    public static StringWriter processXml(Object xml, String condingXML) throws JAXBException, PropertyException {
        StringWriter writer = new StringWriter();
        JAXBContext jaxbContext = JAXBContext.newInstance(xml.getClass());
        Marshaller jaxbMarshaller = jaxbContext.createMarshaller();

        if (condingXML.equals("ISO-8859-1")) {
            jaxbMarshaller.setProperty(Marshaller.JAXB_ENCODING, "ISO-8859-1");
        }

        jaxbMarshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
        jaxbMarshaller.marshal(xml, writer);
        String content = writer.toString();
        content = Normalizer.normalize(content, Normalizer.Form.NFD).replaceAll("[^\\p{ASCII}]", "");
        writer = new StringWriter();
        writer.write(content);
        return writer;
    }

    public static byte[] convertXmlInByteArrayZipFile(String xml, String name, String condingForXML) throws Exception {

        Path tempFileXml = Files.createTempFile(name, ".xml");

        if (condingForXML.equals("ISO-8859-1")) {
            FileUtils.write(tempFileXml.toFile(), xml, "ISO-8859-1");
        } else {
            FileUtils.write(tempFileXml.toFile(), xml, "UTF-8");
        }

        Path tempFileZip = Files.createTempFile(name, ".zip");
        FileUtil.zipFile(tempFileXml.toString(), tempFileZip.toString());
        return FileUtil.convertFileToByteArray(tempFileZip.toFile());
    }

    public static String extrairVersao(String xmlString) {
        String padraoTag = "<ans:Padrao>";
        int startIndex = xmlString.indexOf(padraoTag);
        if (startIndex != -1) {
            int endIndex = xmlString.indexOf("</ans:Padrao>", startIndex);
            if (endIndex != -1) {
                return xmlString.substring(startIndex + padraoTag.length(), endIndex);
            }
        }
        return null;
    }

    public static <T> T xmlToObject(String xml, Class<T> targetClass) throws JAXBException {
        InputStream inputStream = new ByteArrayInputStream(xml.getBytes());

        JAXBContext jaxbContext = JAXBContext.newInstance(targetClass);
        Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
        return (T) unmarshaller.unmarshal(inputStream);
    }



    public static String processhash(Object requestWS, String condingXML, String tag) throws IOException, ParserConfigurationException, NoSuchAlgorithmException, SAXException, JAXBException {
        var xml = TissUtil.processXml(requestWS, condingXML);
        return TissUtil.processXmlHash(xml,condingXML, tag);
    }

    public static BigInteger plusSequencialItem(BigInteger sequencialItem) {
        return sequencialItem = sequencialItem.add(new BigInteger("1"));
    }


}
