package br.com.focusts.clinicall.authoriertiss.tiss.v40100.webservice;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 3.4.0
 * 2023-06-26T10:28:34.988-03:00
 * Generated source version: 3.4.0
 *
 */
@WebService(targetNamespace = "http://www.ans.gov.br/tiss/ws/tipos/tisssolicitacaostatusautorizacao/v40100", name = "tissSolicitacaoStatusAutorizacao_PortType")
@XmlSeeAlso({ObjectFactory.class})
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface TissSolicitacaoStatusAutorizacaoPortType {

    @WebMethod(operationName = "tissSolicitacaoStatusAutorizacao_Operation")
    @WebResult(name = "situacaoAutorizacaoWS", targetNamespace = "http://www.ans.gov.br/padroes/tiss/schemas", partName = "situacaoAutorizacao")
    public SituacaoAutorizacaoWS tissSolicitacaoStatusAutorizacaoOperation(

        @WebParam(partName = "solicitacaoStatusAutorizacao", name = "solicitacaoStatusAutorizacaoWS", targetNamespace = "http://www.ans.gov.br/padroes/tiss/schemas")
        SolicitacaoStatusAutorizacaoWS solicitacaoStatusAutorizacao
    ) throws TissFault;
}
