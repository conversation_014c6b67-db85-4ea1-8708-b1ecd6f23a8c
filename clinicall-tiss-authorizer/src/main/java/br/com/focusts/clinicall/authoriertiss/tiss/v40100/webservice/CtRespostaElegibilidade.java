
package br.com.focusts.clinicall.authoriertiss.tiss.v40100.webservice;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * &lt;p&gt;Classe Java de ct_respostaElegibilidade complex type.
 * 
 * &lt;p&gt;O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * &lt;pre&gt;
 * &amp;lt;complexType name="ct_respostaElegibilidade"&amp;gt;
 *   &amp;lt;complexContent&amp;gt;
 *     &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
 *       &amp;lt;choice&amp;gt;
 *         &amp;lt;element name="codigoGlosa" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_motivoGlosa"/&amp;gt;
 *         &amp;lt;element name="reciboElegibilidade" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_elegibilidadeRecibo"/&amp;gt;
 *       &amp;lt;/choice&amp;gt;
 *     &amp;lt;/restriction&amp;gt;
 *   &amp;lt;/complexContent&amp;gt;
 * &amp;lt;/complexType&amp;gt;
 * &lt;/pre&gt;
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_respostaElegibilidade", propOrder = {
    "codigoGlosa",
    "reciboElegibilidade"
})
public class CtRespostaElegibilidade {

    protected CtMotivoGlosa codigoGlosa;
    protected CtElegibilidadeRecibo reciboElegibilidade;

    /**
     * Obtém o valor da propriedade codigoGlosa.
     * 
     * @return
     *     possible object is
     *     {@link CtMotivoGlosa }
     *     
     */
    public CtMotivoGlosa getCodigoGlosa() {
        return codigoGlosa;
    }

    /**
     * Define o valor da propriedade codigoGlosa.
     * 
     * @param value
     *     allowed object is
     *     {@link CtMotivoGlosa }
     *     
     */
    public void setCodigoGlosa(CtMotivoGlosa value) {
        this.codigoGlosa = value;
    }

    /**
     * Obtém o valor da propriedade reciboElegibilidade.
     * 
     * @return
     *     possible object is
     *     {@link CtElegibilidadeRecibo }
     *     
     */
    public CtElegibilidadeRecibo getReciboElegibilidade() {
        return reciboElegibilidade;
    }

    /**
     * Define o valor da propriedade reciboElegibilidade.
     * 
     * @param value
     *     allowed object is
     *     {@link CtElegibilidadeRecibo }
     *     
     */
    public void setReciboElegibilidade(CtElegibilidadeRecibo value) {
        this.reciboElegibilidade = value;
    }

}
