
package br.com.focusts.clinicall.authoriertiss.tiss.v30301.solicitacaosenha;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * &lt;p&gt;Java class for ct_respostaElegibilidade complex type.
 * 
 * &lt;p&gt;The following schema fragment specifies the expected content contained within this class.
 * 
 * &lt;pre&gt;
 * &amp;lt;complexType name="ct_respostaElegibilidade"&amp;gt;
 *   &amp;lt;complexContent&amp;gt;
 *     &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
 *       &amp;lt;choice&amp;gt;
 *         &amp;lt;element name="mensagemErro" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_motivoGlosa"/&amp;gt;
 *         &amp;lt;element name="reciboElegibilidade" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_elegibilidadeRecibo"/&amp;gt;
 *       &amp;lt;/choice&amp;gt;
 *     &amp;lt;/restriction&amp;gt;
 *   &amp;lt;/complexContent&amp;gt;
 * &amp;lt;/complexType&amp;gt;
 * &lt;/pre&gt;
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_respostaElegibilidade", propOrder = {
    "mensagemErro",
    "reciboElegibilidade"
})
public class CtRespostaElegibilidade {

    protected CtMotivoGlosa mensagemErro;
    protected CtElegibilidadeRecibo reciboElegibilidade;

    /**
     * Gets the value of the mensagemErro property.
     * 
     * @return
     *     possible object is
     *     {@link CtMotivoGlosa }
     *     
     */
    public CtMotivoGlosa getMensagemErro() {
        return mensagemErro;
    }

    /**
     * Sets the value of the mensagemErro property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtMotivoGlosa }
     *     
     */
    public void setMensagemErro(CtMotivoGlosa value) {
        this.mensagemErro = value;
    }

    /**
     * Gets the value of the reciboElegibilidade property.
     * 
     * @return
     *     possible object is
     *     {@link CtElegibilidadeRecibo }
     *     
     */
    public CtElegibilidadeRecibo getReciboElegibilidade() {
        return reciboElegibilidade;
    }

    /**
     * Sets the value of the reciboElegibilidade property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtElegibilidadeRecibo }
     *     
     */
    public void setReciboElegibilidade(CtElegibilidadeRecibo value) {
        this.reciboElegibilidade = value;
    }

}
