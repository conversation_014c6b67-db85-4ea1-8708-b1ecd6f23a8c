
package br.com.focusts.clinicall.authoriertiss.tiss.v40100.webservice;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * &lt;p&gt;Classe Java de dm_condicaoClinica.
 * 
 * &lt;p&gt;O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * &lt;pre&gt;
 * &amp;lt;simpleType name="dm_condicaoClinica"&amp;gt;
 *   &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&amp;gt;
 *     &amp;lt;maxLength value="1"/&amp;gt;
 *     &amp;lt;enumeration value="A"/&amp;gt;
 *     &amp;lt;enumeration value="E"/&amp;gt;
 *     &amp;lt;enumeration value="H"/&amp;gt;
 *     &amp;lt;enumeration value="C"/&amp;gt;
 *     &amp;lt;enumeration value="R"/&amp;gt;
 *   &amp;lt;/restriction&amp;gt;
 * &amp;lt;/simpleType&amp;gt;
 * &lt;/pre&gt;
 * 
 */
@XmlType(name = "dm_condicaoClinica")
@XmlEnum
public enum DmCondicaoClinica {

    A,
    E,
    H,
    C,
    R;

    public String value() {
        return name();
    }

    public static DmCondicaoClinica fromValue(String v) {
        return valueOf(v);
    }

}
