
package br.com.focusts.clinicall.authoriertiss.tiss.v40100.webservice;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * &lt;p&gt;Classe Java de ct_identEquipeSADT complex type.
 * 
 * &lt;p&gt;O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * &lt;pre&gt;
 * &amp;lt;complexType name="ct_identEquipeSADT"&amp;gt;
 *   &amp;lt;complexContent&amp;gt;
 *     &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
 *       &amp;lt;sequence&amp;gt;
 *         &amp;lt;element name="grauPart" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_grauPart" minOccurs="0"/&amp;gt;
 *         &amp;lt;element name="codProfissional"&amp;gt;
 *           &amp;lt;complexType&amp;gt;
 *             &amp;lt;complexContent&amp;gt;
 *               &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
 *                 &amp;lt;choice&amp;gt;
 *                   &amp;lt;element name="codigoPrestadorNaOperadora" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_texto14"/&amp;gt;
 *                   &amp;lt;element name="cpfContratado" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_CPF"/&amp;gt;
 *                 &amp;lt;/choice&amp;gt;
 *               &amp;lt;/restriction&amp;gt;
 *             &amp;lt;/complexContent&amp;gt;
 *           &amp;lt;/complexType&amp;gt;
 *         &amp;lt;/element&amp;gt;
 *         &amp;lt;element name="nomeProf" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_texto70"/&amp;gt;
 *         &amp;lt;element name="conselho" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_conselhoProfissional"/&amp;gt;
 *         &amp;lt;element name="numeroConselhoProfissional" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_texto15"/&amp;gt;
 *         &amp;lt;element name="UF" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_UF"/&amp;gt;
 *         &amp;lt;element name="CBOS" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_CBOS"/&amp;gt;
 *       &amp;lt;/sequence&amp;gt;
 *     &amp;lt;/restriction&amp;gt;
 *   &amp;lt;/complexContent&amp;gt;
 * &amp;lt;/complexType&amp;gt;
 * &lt;/pre&gt;
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_identEquipeSADT", propOrder = {
    "grauPart",
    "codProfissional",
    "nomeProf",
    "conselho",
    "numeroConselhoProfissional",
    "uf",
    "cbos"
})
public class CtIdentEquipeSADT {

    protected String grauPart;
    @XmlElement(required = true)
    protected CtIdentEquipeSADT.CodProfissional codProfissional;
    @XmlElement(required = true)
    protected String nomeProf;
    @XmlElement(required = true)
    protected String conselho;
    @XmlElement(required = true)
    protected String numeroConselhoProfissional;
    @XmlElement(name = "UF", required = true)
    protected String uf;
    @XmlElement(name = "CBOS", required = true)
    protected String cbos;

    /**
     * Obtém o valor da propriedade grauPart.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGrauPart() {
        return grauPart;
    }

    /**
     * Define o valor da propriedade grauPart.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGrauPart(String value) {
        this.grauPart = value;
    }

    /**
     * Obtém o valor da propriedade codProfissional.
     * 
     * @return
     *     possible object is
     *     {@link CtIdentEquipeSADT.CodProfissional }
     *     
     */
    public CtIdentEquipeSADT.CodProfissional getCodProfissional() {
        return codProfissional;
    }

    /**
     * Define o valor da propriedade codProfissional.
     * 
     * @param value
     *     allowed object is
     *     {@link CtIdentEquipeSADT.CodProfissional }
     *     
     */
    public void setCodProfissional(CtIdentEquipeSADT.CodProfissional value) {
        this.codProfissional = value;
    }

    /**
     * Obtém o valor da propriedade nomeProf.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeProf() {
        return nomeProf;
    }

    /**
     * Define o valor da propriedade nomeProf.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeProf(String value) {
        this.nomeProf = value;
    }

    /**
     * Obtém o valor da propriedade conselho.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getConselho() {
        return conselho;
    }

    /**
     * Define o valor da propriedade conselho.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setConselho(String value) {
        this.conselho = value;
    }

    /**
     * Obtém o valor da propriedade numeroConselhoProfissional.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroConselhoProfissional() {
        return numeroConselhoProfissional;
    }

    /**
     * Define o valor da propriedade numeroConselhoProfissional.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroConselhoProfissional(String value) {
        this.numeroConselhoProfissional = value;
    }

    /**
     * Obtém o valor da propriedade uf.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUF() {
        return uf;
    }

    /**
     * Define o valor da propriedade uf.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUF(String value) {
        this.uf = value;
    }

    /**
     * Obtém o valor da propriedade cbos.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCBOS() {
        return cbos;
    }

    /**
     * Define o valor da propriedade cbos.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCBOS(String value) {
        this.cbos = value;
    }


    /**
     * &lt;p&gt;Classe Java de anonymous complex type.
     * 
     * &lt;p&gt;O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
     * 
     * &lt;pre&gt;
     * &amp;lt;complexType&amp;gt;
     *   &amp;lt;complexContent&amp;gt;
     *     &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
     *       &amp;lt;choice&amp;gt;
     *         &amp;lt;element name="codigoPrestadorNaOperadora" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_texto14"/&amp;gt;
     *         &amp;lt;element name="cpfContratado" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_CPF"/&amp;gt;
     *       &amp;lt;/choice&amp;gt;
     *     &amp;lt;/restriction&amp;gt;
     *   &amp;lt;/complexContent&amp;gt;
     * &amp;lt;/complexType&amp;gt;
     * &lt;/pre&gt;
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "codigoPrestadorNaOperadora",
        "cpfContratado"
    })
    public static class CodProfissional {

        protected String codigoPrestadorNaOperadora;
        protected String cpfContratado;

        /**
         * Obtém o valor da propriedade codigoPrestadorNaOperadora.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCodigoPrestadorNaOperadora() {
            return codigoPrestadorNaOperadora;
        }

        /**
         * Define o valor da propriedade codigoPrestadorNaOperadora.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCodigoPrestadorNaOperadora(String value) {
            this.codigoPrestadorNaOperadora = value;
        }

        /**
         * Obtém o valor da propriedade cpfContratado.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCpfContratado() {
            return cpfContratado;
        }

        /**
         * Define o valor da propriedade cpfContratado.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCpfContratado(String value) {
            this.cpfContratado = value;
        }

    }

}
