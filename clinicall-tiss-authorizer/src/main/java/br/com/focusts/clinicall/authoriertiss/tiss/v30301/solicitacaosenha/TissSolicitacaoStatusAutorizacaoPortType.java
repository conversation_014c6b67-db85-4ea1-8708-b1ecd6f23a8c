package br.com.focusts.clinicall.authoriertiss.tiss.v30301.solicitacaosenha;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 3.4.0
 * 2023-07-11T21:07:14.653-03:00
 * Generated source version: 3.4.0
 *
 */
@WebService(targetNamespace = "http://www.ans.gov.br/tiss/ws/tipos/tisssolicitacaostatusautorizacao/v30301", name = "tissSolicitacaoStatusAutorizacao_PortType")
@XmlSeeAlso({ObjectFactory.class})
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface TissSolicitacaoStatusAutorizacaoPortType {

    @WebMethod(operationName = "tissSolicitacaoStatusAutorizacao_Operation")
    @WebResult(name = "situacaoAutorizacaoWS", targetNamespace = "http://www.ans.gov.br/padroes/tiss/schemas", partName = "situacaoAutorizacao")
    public SituacaoAutorizacaoWS tissSolicitacaoStatusAutorizacaoOperation(

        @WebParam(partName = "solicitacaoStatusAutorizacao", name = "solicitacaoStatusAutorizacaoWS", targetNamespace = "http://www.ans.gov.br/padroes/tiss/schemas")
        SolicitacaoStatusAutorizacaoWS solicitacaoStatusAutorizacao
    ) throws TissFault;
}
