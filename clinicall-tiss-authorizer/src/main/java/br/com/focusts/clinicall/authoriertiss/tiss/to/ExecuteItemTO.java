package br.com.focusts.clinicall.authoriertiss.tiss.to;

import java.time.LocalDate;

public class ExecuteItemTO {

    private String token;
    private LocalDate dataExecute;

    private String longitude;

    private String latitude;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public LocalDate getDataExecute() {
        return dataExecute;
    }

    public void setDataExecute(LocalDate dataExecute) {
        this.dataExecute = dataExecute;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }
}
