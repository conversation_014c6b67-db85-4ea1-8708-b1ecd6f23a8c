
package br.com.focusts.clinicall.authoriertiss.tiss.v40100.webservice;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * &lt;p&gt;Classe Java de ct_contratadoDadosNome complex type.
 * 
 * &lt;p&gt;O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * &lt;pre&gt;
 * &amp;lt;complexType name="ct_contratadoDadosNome"&amp;gt;
 *   &amp;lt;complexContent&amp;gt;
 *     &amp;lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&amp;gt;
 *       &amp;lt;sequence&amp;gt;
 *         &amp;lt;choice&amp;gt;
 *           &amp;lt;element name="codigoPrestadorNaOperadora" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_texto14"/&amp;gt;
 *           &amp;lt;element name="cpfContratado" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_CPF"/&amp;gt;
 *           &amp;lt;element name="cnpjContratado" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_CNPJ"/&amp;gt;
 *         &amp;lt;/choice&amp;gt;
 *         &amp;lt;element name="nomeContratado" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_texto70"/&amp;gt;
 *       &amp;lt;/sequence&amp;gt;
 *     &amp;lt;/restriction&amp;gt;
 *   &amp;lt;/complexContent&amp;gt;
 * &amp;lt;/complexType&amp;gt;
 * &lt;/pre&gt;
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_contratadoDadosNome", propOrder = {
    "codigoPrestadorNaOperadora",
    "cpfContratado",
    "cnpjContratado",
    "nomeContratado"
})
public class CtContratadoDadosNome {

    protected String codigoPrestadorNaOperadora;
    protected String cpfContratado;
    protected String cnpjContratado;
    @XmlElement(required = true)
    protected String nomeContratado;

    /**
     * Obtém o valor da propriedade codigoPrestadorNaOperadora.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoPrestadorNaOperadora() {
        return codigoPrestadorNaOperadora;
    }

    /**
     * Define o valor da propriedade codigoPrestadorNaOperadora.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoPrestadorNaOperadora(String value) {
        this.codigoPrestadorNaOperadora = value;
    }

    /**
     * Obtém o valor da propriedade cpfContratado.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCpfContratado() {
        return cpfContratado;
    }

    /**
     * Define o valor da propriedade cpfContratado.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCpfContratado(String value) {
        this.cpfContratado = value;
    }

    /**
     * Obtém o valor da propriedade cnpjContratado.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCnpjContratado() {
        return cnpjContratado;
    }

    /**
     * Define o valor da propriedade cnpjContratado.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCnpjContratado(String value) {
        this.cnpjContratado = value;
    }

    /**
     * Obtém o valor da propriedade nomeContratado.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeContratado() {
        return nomeContratado;
    }

    /**
     * Define o valor da propriedade nomeContratado.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeContratado(String value) {
        this.nomeContratado = value;
    }

}
