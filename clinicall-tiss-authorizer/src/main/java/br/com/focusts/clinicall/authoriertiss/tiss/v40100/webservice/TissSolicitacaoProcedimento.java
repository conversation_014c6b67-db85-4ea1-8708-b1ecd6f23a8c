package br.com.focusts.clinicall.authoriertiss.tiss.v40100.webservice;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.4.0
 * 2023-06-26T10:28:34.519-03:00
 * Generated source version: 3.4.0
 *
 */
@WebServiceClient(name = "tissSolicitacaoProcedimento",
                  wsdlLocation = "file:/home/<USER>/Documentos/GitHub/Focus_INuvem/clinicall-backend/clinicall-tiss-authorizer/src/main/resources/v40100.solicitacaoprocedimentos/tissSolicitacaoProcedimento_1.wsdl",
                  targetNamespace = "http://www.ans.gov.br/tiss/ws/tipos/tisssolicitacaoprocedimento/v40100")
public class TissSolicitacaoProcedimento extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://www.ans.gov.br/tiss/ws/tipos/tisssolicitacaoprocedimento/v40100", "tissSolicitacaoProcedimento");
    public final static QName TissSolicitacaoProcedimentoPort = new QName("http://www.ans.gov.br/tiss/ws/tipos/tisssolicitacaoprocedimento/v40100", "tissSolicitacaoProcedimento_Port");
    static {
        URL url = null;
        try {
            url = new URL("file:/home/<USER>/Documentos/GitHub/Focus_INuvem/clinicall-backend/clinicall-tiss-authorizer/src/main/resources/v40100.solicitacaoprocedimentos/tissSolicitacaoProcedimento_1.wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(TissSolicitacaoProcedimento.class.getName())
                .log(java.util.logging.Level.INFO,
                     "Can not initialize the default wsdl from {0}", "file:/home/<USER>/Documentos/GitHub/Focus_INuvem/clinicall-backend/clinicall-tiss-authorizer/src/main/resources/v40100.solicitacaoprocedimentos/tissSolicitacaoProcedimento_1.wsdl");
        }
        WSDL_LOCATION = url;
    }

    public TissSolicitacaoProcedimento(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public TissSolicitacaoProcedimento(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public TissSolicitacaoProcedimento() {
        super(WSDL_LOCATION, SERVICE);
    }

    public TissSolicitacaoProcedimento(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public TissSolicitacaoProcedimento(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public TissSolicitacaoProcedimento(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }




    /**
     *
     * @return
     *     returns TissSolicitacaoProcedimentoPortType
     */
    @WebEndpoint(name = "tissSolicitacaoProcedimento_Port")
    public TissSolicitacaoProcedimentoPortType getTissSolicitacaoProcedimentoPort() {
        return super.getPort(TissSolicitacaoProcedimentoPort, TissSolicitacaoProcedimentoPortType.class);
    }

    /**
     *
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns TissSolicitacaoProcedimentoPortType
     */
    @WebEndpoint(name = "tissSolicitacaoProcedimento_Port")
    public TissSolicitacaoProcedimentoPortType getTissSolicitacaoProcedimentoPort(WebServiceFeature... features) {
        return super.getPort(TissSolicitacaoProcedimentoPort, TissSolicitacaoProcedimentoPortType.class, features);
    }

}
