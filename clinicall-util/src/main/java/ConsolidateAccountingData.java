import java.io.BufferedReader;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class ConsolidateAccountingData {
    public static void main(String[] args) {
        // Caminhos das pastas com os arquivos
        String basePathTotais = "C:/data/totais/"; // Ajuste para a pasta dos arquivos de totais
        String basePathAgrupados = "C:/data/agrupados/"; // Ajuste para a pasta dos arquivos agrupados
        String[] fileNames = {
                "Servidores_001-200.txt",
                "Servidores_201-400.txt",
                "Servidores_401-600.txt",
                "Servidores_601-800.txt",
                "Servidores_801-1000.txt",
                "Servidores_1001-1200.txt",
                "Servidores_1201-1400.txt",
                "Servidores_1401-1500.txt"
        };

        // Estruturas para totais
        Map<String, Long> empresaTotais = new HashMap<>(); // CODIGO_EMPRESA|NOME_EMPRESA -> QTDE (totais)
        Map<String, Long> arquivoTotais = new HashMap<>(); // Nome do arquivo -> Total de lançamentos (totais)

        // Estruturas para agrupados
        Map<String, Long> combinacoesUnicasPorArquivo = new HashMap<>(); // Nome do arquivo -> Número de combinações únicas
        Map<String, Long> totalLançamentosAgrupados = new HashMap<>(); // Nome do arquivo -> Total de lançamentos (agrupados)

        // Processar arquivos de totais
        for (String fileName : fileNames) {
            String filePath = basePathTotais + fileName;
            long totalLançamentosArquivo = 0;

            try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
                String linha;
                while ((linha = br.readLine()) != null) {
                    String[] partes = linha.split(";");
                    if (partes.length >= 5) {
                        String codigoEmpresa = partes[2];
                        String nomeEmpresa = partes[3];
                        long qtde;
                        try {
                            qtde = Long.parseLong(partes[4].trim());
                        } catch (NumberFormatException e) {
                            System.err.println("Erro ao parsear QTDE na linha (totais): " + linha);
                            continue;
                        }

                        String chaveEmpresa = codigoEmpresa + "|" + nomeEmpresa;
                        empresaTotais.merge(chaveEmpresa, qtde, Long::sum);
                        totalLançamentosArquivo += qtde;
                    }
                }
                arquivoTotais.put(fileName, totalLançamentosArquivo);
            } catch (IOException e) {
                System.err.println("Erro ao processar arquivo de totais " + filePath + ": " + e.getMessage());
            }
        }

        // Processar arquivos agrupados
        for (String fileName : fileNames) {
            String filePath = basePathAgrupados + fileName;
            long totalLançamentosArquivo = 0;
            Set<String> combinacoesUnicas = new HashSet<>();

            try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
                String linha;
                while ((linha = br.readLine()) != null) {
                    String[] partes = linha.split(";");
                    if (partes.length >= 9) { // Espera-se pelo menos 9 colunas para a segunda query
                        String codigoEmpresa = partes[2];
                        String nomeEmpresa = partes[3];
                        String contaContabil = partes[4];
                        String dataLan = partes[5];
                        String tipoDebCre = partes[6];
                        long qtde;
                        try {
                            qtde = Long.parseLong(partes[7].trim());
                        } catch (NumberFormatException e) {
                            System.err.println("Erro ao parsear QTDE na linha (agrupados): " + linha);
                            continue;
                        }
                        String origem = partes[8];

                        // Consolidação de totais
                        String chaveEmpresa = codigoEmpresa + "|" + nomeEmpresa;
                        empresaTotais.merge(chaveEmpresa, qtde, Long::sum); // Atualiza totais com dados agrupados
                        totalLançamentosArquivo += qtde;

                        // Contagem de combinações únicas
                        String chaveCombinacao = codigoEmpresa + "|" + dataLan + "|" + contaContabil + "|" + tipoDebCre + "|" + origem;
                        combinacoesUnicas.add(chaveCombinacao);
                    }
                }
                totalLançamentosAgrupados.put(fileName, totalLançamentosArquivo);
                combinacoesUnicasPorArquivo.put(fileName, (long) combinacoesUnicas.size());
            } catch (IOException e) {
                System.err.println("Erro ao processar arquivo agrupado " + filePath + ": " + e.getMessage());
            }
        }

        // Gerar resumo
        try (FileWriter writer = new FileWriter(basePathTotais + "resumo_consolidado.txt")) {
            // Resumo por arquivo (totais)
            writer.write("Resumo por Arquivo (Totais)\n");
            writer.write("Arquivo;Total_Lançamentos\n");
            for (Map.Entry<String, Long> entry : arquivoTotais.entrySet()) {
                writer.write(entry.getKey() + ";" + entry.getValue() + "\n");
            }

            // Resumo por arquivo (agrupados)
            writer.write("\nResumo por Arquivo (Agrupados)\n");
            writer.write("Arquivo;Total_Lançamentos;Combinações_Únicas\n");
            for (String fileName : fileNames) {
                writer.write(fileName + ";" + totalLançamentosAgrupados.getOrDefault(fileName, 0L) + ";" + combinacoesUnicasPorArquivo.getOrDefault(fileName, 0L) + "\n");
            }

            // Resumo por empresa
            writer.write("\nResumo por Empresa\n");
            writer.write("CODIGO_EMPRESA;NOME_EMPRESA;QTDE_TOTAL\n");
            for (Map.Entry<String, Long> entry : empresaTotais.entrySet()) {
                String[] partes = entry.getKey().split("\\|");
                writer.write(partes[0] + ";" + partes[1] + ";" + entry.getValue() + "\n");
            }

            // Totais gerais
            long totalGeralTotais = arquivoTotais.values().stream().mapToLong(Long::longValue).sum();
            long totalGeralAgrupados = totalLançamentosAgrupados.values().stream().mapToLong(Long::longValue).sum();
            long totalCombinacoesUnicas = combinacoesUnicasPorArquivo.values().stream().mapToLong(Long::longValue).sum();
            writer.write("\nTotal Geral de Lançamentos (Totais): " + totalGeralTotais + "\n");
            writer.write("Total Geral de Lançamentos (Agrupados): " + totalGeralAgrupados + "\n");
            writer.write("Total de Combinações Únicas: " + totalCombinacoesUnicas + "\n");
        } catch (IOException e) {
            System.err.println("Erro ao escrever resumo: " + e.getMessage());
        }

        // Exibir resumo no console
        System.out.println("Resumo por Arquivo (Totais):");
        arquivoTotais.forEach((arquivo, total) -> System.out.println(arquivo + ": " + total));
        System.out.println("\nResumo por Arquivo (Agrupados):");
        for (String fileName : fileNames) {
            System.out.println(fileName + ": Total=" + totalLançamentosAgrupados.getOrDefault(fileName, 0L) + ", Combinações Únicas=" + combinacoesUnicasPorArquivo.getOrDefault(fileName, 0L));
        }
        System.out.println("\nTotal Geral de Lançamentos (Totais): " + arquivoTotais.values().stream().mapToLong(Long::longValue).sum());
        System.out.println("Total Geral de Lançamentos (Agrupados): " + totalLançamentosAgrupados.values().stream().mapToLong(Long::longValue).sum());
        System.out.println("Total de Combinações Únicas: " + combinacoesUnicasPorArquivo.values().stream().mapToLong(Long::longValue).sum());
    }
}