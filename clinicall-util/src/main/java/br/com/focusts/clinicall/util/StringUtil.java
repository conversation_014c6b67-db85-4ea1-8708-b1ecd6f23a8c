package br.com.focusts.clinicall.util;

import java.text.DecimalFormat;
import java.text.Normalizer;
import java.util.Base64;
import java.util.regex.Pattern;

public class StringUtil {

    public static String encodeBase64(String text){
        String textoHtml= text;
        String encodedString = Base64.getEncoder().encodeToString(textoHtml.getBytes());

        return  encodedString;
    }

    public static boolean isNullOrBlankOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    public static String decodeBase64(String text){

        byte[] bytesDecodificados = Base64.getDecoder().decode(text); // Decodifica a string em base64
        String stringDecodificada = new String(bytesDecodificados); // Converte os bytes decodificados em uma string

        return  stringDecodificada;
    }

    public static boolean isBase64(String text) {
        try {
            Base64.getDecoder().decode(text);
            return true; // A string é uma representação válida em Base64
        } catch (IllegalArgumentException e) {
            return false; // A string não é uma representação válida em Base64
        }
    }

    public static String zeroLeft(String number){
        DecimalFormat df = new DecimalFormat("00");
        String result = number;
        if (Integer.parseInt(number) > 0 && Integer.parseInt(number) <10){
            result = df.format(Integer.parseInt(number));;
        }
        return result;
    }

    public static String changeSize(String valor, int sizeMax) {
        if (valor.length() >= sizeMax) {
            return valor.substring(0, sizeMax);
        } else {
            return String.format("%-" + sizeMax + "s", valor);
        }
    }

    public static String removerAcentos(String texto) {
        return texto = Normalizer.normalize(texto, Normalizer.Form.NFD).replaceAll("[^\\p{ASCII}]", "");
    }

    public static String safeGet(Object value) {
        return value != null ? value.toString() : "";
    }

}
