package br.com.focusts.clinicall.util;

public class ValidateUtil {

    public static boolean isCnpjValid(String cnpj) {
        if (cnpj.length() != 14) {
            return false;
        }

        int[] digits = new int[14];

        // Converte os dígitos do CNPJ para um array de inteiros
        for (int i = 0; i < 14; i++) {
            digits[i] = Character.getNumericValue(cnpj.charAt(i));
        }

        // Calcula o primeiro dígito verificador
        int firstVerifierDigit = calculateVerifierDigitCnpj(digits, 11);
        // Calcula o segundo dígito verificador
        int secondVerifierDigit = calculateVerifierDigitCnpj(digits, 12);

        // Verifica se os dígitos verificadores calculados coincidem com os do CNPJ
        return digits[12] == firstVerifierDigit && digits[13] == secondVerifierDigit;
    }

    public static boolean isCpfValid(String cpf) {


        if (cpf.length() != 11) {
            return false;
        }

        // Verifica se todos os dígitos são iguais
        if (cpf.matches("(\\d)\\1{10}")) {
            return false;
        }

        int[] digits = new int[11];

        // Converte os dígitos do CPF para um array de inteiros
        for (int i = 0; i < 11; i++) {
            digits[i] = Character.getNumericValue(cpf.charAt(i));
        }

        // Calcula o primeiro dígito verificador
        int firstVerifierDigit = calculateVerifierDigitCpf(digits, 9);
        // Calcula o segundo dígito verificador
        int secondVerifierDigit = calculateVerifierDigitCpf(digits, 10);

        // Verifica se os dígitos verificadores calculados coincidem com os do CPF
        return digits[9] == firstVerifierDigit && digits[10] == secondVerifierDigit;
    }

    private static int calculateVerifierDigitCnpj(int[] digits, int position) {
        int weight = 2;
        int sum = 0;

        for (int i = position; i >= 0; i--) {
            sum += digits[i] * weight;
            weight = (weight == 9) ? 2 : weight + 1;
        }

        int remainder = sum % 11;

        return (remainder < 2) ? 0 : (11 - remainder);
    }

    private static int calculateVerifierDigitCpf(int[] digits, int position) {
        int weight = position + 1;
        int sum = 0;

        for (int i = 0; i < position; i++) {
            sum += digits[i] * weight;
            weight--;
        }

        int remainder = sum % 11;
        int result = 11 - remainder;

        return (result == 10 || result == 11) ? 0 : result;
    }
    public static boolean isCNSValid(String s) {
        if (s.matches("[1-2]\\d{10}00[0-1]\\d") || s.matches("[7-9]\\d{14}")) {
            return somaPonderada(s) % 11 == 0;
        }
        return false;
    }

    private static int somaPonderada(String s) {
        char[] cs = s.toCharArray();
        int soma = 0;
        for (int i = 0; i < cs.length; i++) {
            soma += Character.digit(cs[i], 10) * (15 - i);
        }
        return soma;
    }
}
