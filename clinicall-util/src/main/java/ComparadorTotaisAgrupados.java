import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

public class ComparadorTotaisAgrupados {

    static class Registro {
        String empresaId;
        String clienteId;
        String nome;
        long qtde;

        Registro(String empresaId, String clienteId, String nome, long qtde) {
            this.empresaId = empresaId;
            this.clienteId = clienteId;
            this.nome = nome;
            this.qtde = qtde;
        }
    }

    public static void main(String[] args) throws IOException {
        String pathTotais = "C:/data/totais";
        String pathAgrupados = "C:/data/agrupados";

        Map<String, Registro> totais = carregarRegistros(pathTotais);
        Map<String, Registro> agrupados = carregarRegistros(pathAgrupados);

        List<String> saida = new ArrayList<>();
        saida.add("empresa_id;cliente_id;nome_cliente;qtde_total;qtde_agrupada;percentual;eficiencia");

        for (String chave : totais.keySet()) {
            Registro regTotal = totais.get(chave);
            Registro regAgrup = agrupados.getOrDefault(chave,
                    new Registro(regTotal.empresaId, regTotal.clienteId, regTotal.nome, 0));

            double percentual = regTotal.qtde > 0 ? (100.0 * regAgrup.qtde / regTotal.qtde) : 0.0;
            double eficiencia = regAgrup.qtde > 0 ? ((double) regTotal.qtde / regAgrup.qtde) : 0.0;

            saida.add(String.format("%s;%s;%s;%d;%d;%.2f;%.2f",
                    regTotal.empresaId,
                    regTotal.clientId,
                    regTotal.nome,
                    regTotal.qtde,
                    regAgrup.qtde,
                    percentual,
                    eficiencia));
        }

        Path saidaPath = Paths.get("C:/data/resultado_comparacao.csv");
        Files.write(saidaPath, saida);

        System.out.println("✅ Comparação concluída. Resultado salvo em: " + saidaPath);
    }

    private static Map<String, Registro> carregarRegistros(String path) throws IOException {
        Map<String, Registro> mapa = new HashMap<>();

        Files.walk(Paths.get(path))
                .filter(Files::isRegularFile)
                .forEach(arquivo -> {
                    try {
                        List<String> linhas = Files.readAllLines(arquivo);
                        for (String linha : linhas) {
                            linha = linha.replace("\uFEFF", ""); // Remove BOM invisível

                            String[] partes = linha.split(";", -1);
                            if (partes.length >= 5) {
                                String empresaId = partes[1].trim();
                                String clienteId = partes[2].trim();
                                String nome = partes[3].trim();
                                String qtdeStr = partes[4].trim();

                                try {
                                    long qtde = Long.parseLong(qtdeStr);
                                    String chave = empresaId + ";" + clienteId;

                                    mapa.merge(chave, new Registro(empresaId, clienteId, nome, qtde),
                                            (antigo, novoReg) -> {
                                                antigo.qtde += novoReg.qtde;
                                                return antigo;
                                            });
                                } catch (NumberFormatException ex) {
                                    System.err.println("⚠ Erro ao converter qtde: [" + qtdeStr + "] na linha: " + linha);
                                }
                            } else {
                                System.err.println("⚠ Linha ignorada (campos insuficientes): " + linha);
                            }
                        }
                    } catch (IOException e) {
                        System.err.println("❌ Erro ao ler arquivo: " + arquivo);
                    }
                });

        return mapa;
    }
}