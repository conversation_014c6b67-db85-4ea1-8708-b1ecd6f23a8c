
package br.com.focusts.clinicall.web.modules.tables.accreditation.controller;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.controller.AbstractController;
import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.fw.to.SimpleEntityTO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.facade.TableTypeFacade;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.TableTypePO;
import br.com.focusts.clinicall.service.modules.tables.general.enums.TypeTableTypeEnum;

@RestController
@RequestMapping("/tableType")
public class TableTypeController extends AbstractController<TableTypePO, java.lang.Long> {

	@Autowired
	private TableTypeFacade tableTypeFacade;

	@Override
	public CrudFacade<TableTypePO, java.lang.Long> getCrudFacade() {
		return tableTypeFacade;
	}

	@GetMapping("/typeTableType")
	public SimpleEntityTO getSimpleEntitTypeTableTypeEnum() {
		return super.getSimpleEntityFromEnun(TypeTableTypeEnum.values());
	}

	@PostMapping(value = "/search")
	@PreAuthorize("hasAuthority('search-tables-accreditation-table_type-list')")
	public EntityResponse<Page<TableTypePO>> search(@RequestBody PageSearchTO pageSearchTO) {

		Page<TableTypePO> page = tableTypeFacade.findByCodeContainingOrNameContaining(pageSearchTO);

		return new EntityResponse<Page<TableTypePO>>(page, "*", "content.*");
	}

	@GetMapping("/wAll/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-tables-accreditation-table_type-edit')")
	public EntityResponse<TableTypePO> findByIdWithAll(@PathVariable Long id) {
		return new EntityResponse<TableTypePO>(getCrudFacade().findById(id), "*", "parent.*");
	}

	@GetMapping("/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-tables-accreditation-table_type-edit')")
	public EntityResponse<TableTypePO> findById(@PathVariable Long id) {
		return new EntityResponse<TableTypePO>(getCrudFacade().findById(id), "*");
	}

	@PostMapping("/")
	@PreAuthorize("hasAuthority('insert-tables-accreditation-table_type-edit')")
	public EntityResponse<Response> insert(@RequestBody @Valid TableTypePO tableTypePO) {

		tableTypePO = getCrudFacade().save(tableTypePO);

		SuccessResponse<TableTypePO> successResponse = new SuccessResponse<TableTypePO>(Response.INFO,	GlobalKeyMessagesConstants.INSERT_SUCCESS, tableTypePO);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@PutMapping("/")
	@PreAuthorize("hasAuthority('update-tables-accreditation-table_type-edit')")
	public EntityResponse<Response> update(@RequestBody @Valid TableTypePO tableTypePO) {

		tableTypePO = getCrudFacade().update(tableTypePO);

		SuccessResponse<TableTypePO> successResponse = new SuccessResponse<TableTypePO>(Response.INFO,	GlobalKeyMessagesConstants.UPDATE_SUCCESS, tableTypePO);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@DeleteMapping("/{id}")
	@PreAuthorize("hasAuthority('delete-tables-accreditation-table_type-edit')")
	public Response delete(@PathVariable Long id) {

		getCrudFacade().delete(id);

		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
	}

	@GetMapping("/")
	@PreAuthorize("hasAuthority('search-tables-accreditation-table_type-list')")
	public EntityResponse<List<TableTypePO>> findAll() {
		return new EntityResponse<List<TableTypePO>>(getCrudFacade().findAll(), "*");
	}

}
