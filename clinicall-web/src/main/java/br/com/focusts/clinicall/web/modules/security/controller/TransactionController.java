package br.com.focusts.clinicall.web.modules.security.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import br.com.focusts.clinicall.fw.controller.AbstractController;
import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.security.facade.TransactionFacade;
import br.com.focusts.clinicall.service.modules.register.security.po.TransactionPO;
import br.com.focusts.clinicall.service.modules.system.po.TransactionFilterPO;

@RestController
@RequestMapping("/transaction")
public class TransactionController extends AbstractController<TransactionPO, Long> {

	@Autowired
	private TransactionFacade transactionFacade;

	@Override
	public CrudFacade<TransactionPO, Long> getCrudFacade() {
		return transactionFacade;
	}
	
	@PostMapping(value = "/search")
	public EntityResponse<Page<TransactionPO>> search(@RequestBody PageSearchTO pageSearchTO) {

		Page<TransactionPO> page = this.transactionFacade.findByNameContainingOrCodeContainingOrViewNameContaining(pageSearchTO);
		
		return new EntityResponse<Page<TransactionPO>>(page, "*","content.id","content.code","content.path","content.name","content.description","content.module","content.module.name");
	}
	
	@GetMapping("/wView/{id}")
	@ResponseBody
	public EntityResponse<TransactionPO> findByIdWithModule(@PathVariable Long id) {
		return new EntityResponse<TransactionPO>(getCrudFacade().findById(id), "*","view.id","view.name");
	}


	@GetMapping("/{id}/filter")
	@ResponseBody
	public EntityResponse<List<TransactionFilterPO>> findByTransaction_id(@PathVariable Long id) {
		return new EntityResponse<List<TransactionFilterPO>>(transactionFacade.findByTransaction_id(id), "*","transaction.*","filter.*");
	}
	

}
