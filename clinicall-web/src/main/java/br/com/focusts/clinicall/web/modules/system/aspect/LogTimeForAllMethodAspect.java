package br.com.focusts.clinicall.web.modules.system.aspect;

import br.com.focusts.clinicall.util.TenantContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StopWatch;

//@Aspect
//@Component
public class LogTimeForAllMethodAspect
{
    private static final Logger LOGGER = LogManager.getLogger(LogTimeForAllMethodAspect.class);
    //AOP expression for which methods shall be intercepted
    @Around("execution(* br.com.focusts.clinicall..*(..)))")
    public Object profileAllMethods(ProceedingJoinPoint proceedingJoinPoint) throws Throwable
    {
        MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
        //Get intercepted method details
        String className = methodSignature.getDeclaringType().getSimpleName();
        String methodName = methodSignature.getName();
        final StopWatch stopWatch = new StopWatch();
        //Measure method execution time
        stopWatch.start();
        Object result = proceedingJoinPoint.proceed();
        stopWatch.stop();
        //Log method execution time
        LOGGER.info("RequestID: " + TenantContext.getRequestId() +
                        " Tenant: " + TenantContext.getCurrentTenant() +
                        " User: "  + SecurityContextHolder.getContext().getAuthentication() +
                         " Execution time of " + className + "." + methodName + " :: " + stopWatch.getTotalTimeMillis() + " ms");
        return result;
    }
}