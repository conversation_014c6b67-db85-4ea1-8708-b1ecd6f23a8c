package br.com.focusts.clinicall.web.modules.security.filter;

import java.io.IOException;
import java.util.Arrays;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import br.com.focusts.clinicall.fw.exception.ApplicationException;
import br.com.focusts.clinicall.util.WebUtil;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class CustomCorsFilter implements Filter {

	@Value("${tenats-enable}")
	private String endPointFrontend;

	@Value("${tenats-enable}")
	private String tenants;
	
	protected final Log logger = LogFactory.getLog(getClass());

	public CustomCorsFilter(String tenantList) {
		this.tenants = tenantList;
	}
	
	public CustomCorsFilter() {
	}

	@Override
	public void destroy() {

	}

	@Override
	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)
			throws IOException, ServletException {


		HttpServletResponse response = (HttpServletResponse) res;
		HttpServletRequest request = (HttpServletRequest) req;
		String originheader = request.getHeader("origin");
		String ip = WebUtil.getIp(request);
		
		String allowedDomain = tenants.concat(",documentserver, teleconsulta, portal");
		
		
		if (! StringUtils.isEmpty(allowedDomain) && ! StringUtils.isEmpty(originheader)){
			var endPointList = allowedDomain.split(",");
			var origin = originheader.split("\\.")[0].split("//")[1];
			var result = Arrays.stream(endPointList).filter(ed -> ed.endsWith(origin)).findFirst();
			result.ifPresent(s -> response.setHeader("Access-Control-Allow-Origin", originheader ));
			if(result.isEmpty() ){
				logger.info("originheader= " + originheader + " ");
			}

		}else {
			logger.info("originheader= " + originheader + " ");
		}
		response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Allow-Headers", "X-Auth-Token, X-Auth-Token-Critical-Operation, X-TenantID, Set-Cookie, Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With, Reload-Permission");
        response.setHeader("Access-Control-Expose-Headers", "X-Auth-Token, X-Auth-Token-Critical-Operation, X-TenantID, Reload-Permission");
		response.setHeader("Access-Control-Max-Age", "3600");
		response.setHeader("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS,HEADER");

		
		if (!"OPTIONS".equals(request.getMethod())) {
			chain.doFilter(req, res);
		} 

	}

	@Override
	public void init(FilterConfig arg0) throws ServletException {
	}

}
