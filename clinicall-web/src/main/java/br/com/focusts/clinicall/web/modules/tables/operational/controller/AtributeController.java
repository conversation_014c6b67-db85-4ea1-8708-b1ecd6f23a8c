
package br.com.focusts.clinicall.web.modules.tables.operational.controller;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.controller.AbstractController;
import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.operational.facade.AtributeFacade;
import br.com.focusts.clinicall.service.modules.tables.operational.po.AtributePO;

@RestController
@RequestMapping("/atribute")
public class AtributeController extends AbstractController<AtributePO, java.lang.Long> {

	@Autowired
	private AtributeFacade atributeFacade;

	@Override
	public CrudFacade<AtributePO, java.lang.Long> getCrudFacade() {
		return atributeFacade;
	}

	@PostMapping(value = "/search/{context}")
	@PreAuthorize("hasAuthority('search-tables-operational-' + #context + '-list')")
	public EntityResponse<Page<AtributePO>> search(@PathVariable String context,
			@RequestBody PageSearchTO pageSearchTO) {

		Page<AtributePO> page = atributeFacade.findByContextAndNameContaining(context, pageSearchTO);

		return new EntityResponse<Page<AtributePO>>(page, "*", "content.*");
	}

	@PostMapping(value = "/search/simple/{context}")
	public EntityResponse<Page<AtributePO>> searchSimple(@PathVariable String context,
			@RequestBody PageSearchTO pageSearchTO) {

		Page<AtributePO> page = atributeFacade.findByContextAndNameContaining(context, pageSearchTO);

		return new EntityResponse<Page<AtributePO>>(page, "*", "content.*");
	}
	@GetMapping("/simple/{context}/{id}")
	@ResponseBody
	public EntityResponse<AtributePO> findByIdSimple(@PathVariable String context,@PathVariable java.lang.Long id) {
		return new EntityResponse<AtributePO>(getCrudFacade().findById(id), "*");
	}

	@GetMapping("/wAll/{id}/{context}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-tables-operational-' + #context + '-edit')")
	public EntityResponse<AtributePO> findByIdWithAll(@PathVariable String context,@PathVariable java.lang.Long id) {
		return new EntityResponse<AtributePO>(getCrudFacade().findById(id), "*");
	}

	@GetMapping("/{id}/{context}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-tables-operational-' + #context + '-edit')")
	public EntityResponse<AtributePO> findById(@PathVariable String context,@PathVariable java.lang.Long id) {
		return new EntityResponse<AtributePO>(getCrudFacade().findById(id), "*");
	}

	@PostMapping("/{context}")
	@PreAuthorize("hasAuthority('insert-tables-operational-' + #context + '-edit')")
	public EntityResponse<Response> insert(@PathVariable String context, @RequestBody @Valid AtributePO atributePO) {

		atributePO = getCrudFacade().save(atributePO);

		SuccessResponse<AtributePO> successResponse = new SuccessResponse<AtributePO>(Response.INFO,
				GlobalKeyMessagesConstants.INSERT_SUCCESS, atributePO);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@PutMapping("/{context}")
	@PreAuthorize("hasAuthority('update-tables-operational-' + #context + '-edit')")
	public EntityResponse<Response> update(@PathVariable String context, @RequestBody @Valid AtributePO atributePO) {

		atributePO = getCrudFacade().update(atributePO);

		SuccessResponse<AtributePO> successResponse = new SuccessResponse<AtributePO>(Response.INFO,
				GlobalKeyMessagesConstants.UPDATE_SUCCESS, atributePO);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@DeleteMapping("/{id}/{context}")
	@PreAuthorize("hasAuthority('delete-tables-operational-' + #context + '-edit')")
	public Response delete(@PathVariable String context, @PathVariable Long id) {

		getCrudFacade().delete(id);

		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
	}

	@GetMapping("/{context}")
	@PreAuthorize("hasAuthority('search-tables-operational-' + #context + '-list')")
	public EntityResponse<List<AtributePO>> findAll(@PathVariable String context) {
		return new EntityResponse<List<AtributePO>>(getCrudFacade().findAll(), "*");
	}

}
