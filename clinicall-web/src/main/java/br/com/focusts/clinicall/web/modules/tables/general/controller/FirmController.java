
package br.com.focusts.clinicall.web.modules.tables.general.controller;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.controller.AbstractController;
import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.general.facade.FirmFacade;
import br.com.focusts.clinicall.service.modules.tables.general.po.FirmPO;
import br.com.focusts.clinicall.service.modules.tables.general.to.FirmTO;
import org.springframework.web.bind.annotation.RequestParam;


@RestController
@RequestMapping("/firm")
public class FirmController extends AbstractController<FirmPO, java.lang.Long> {

	@Autowired
	private FirmFacade firmFacade;

	@Override
	public CrudFacade<FirmPO, java.lang.Long> getCrudFacade() {
		return firmFacade;
	}

	@PostMapping(value = "/search")
	@PreAuthorize("hasAuthority('search-tables-general-firm-list')")
	public EntityResponse<Page<FirmPO>> search(@RequestBody PageSearchTO pageSearchTO) {

		Page<FirmPO> page = firmFacade
				.findByNameContainingOrFullNameContainingOrRegistryContainingOrCnesContainingOrRegistryType_nameContaining(
						pageSearchTO);

		return new EntityResponse<Page<FirmPO>>(page, "*", "content.*");
	}

	@PostMapping(value = "/simple-list/search")
	public EntityResponse<Page<FirmPO>> searchSimple(@RequestBody PageSearchTO pageSearchTO) {

		Page<FirmPO> page = firmFacade
				.findByNameContainingOrFullNameContainingOrRegistryContainingOrCnesContainingOrRegistryType_nameContaining(
						pageSearchTO);

		return new EntityResponse<Page<FirmPO>>(page, "*", "content.*");
	}

	@PostMapping(value = "/v2/simple-list/search")
	public EntityResponse<Page<FirmTO>> searchSimplev2(@RequestBody PageSearchTO pageSearchTO) {

		Page<FirmTO> page = firmFacade.findByName(pageSearchTO);

		return new EntityResponse<Page<FirmTO>>(page, "*","content.*");
	}



	@GetMapping("/wAll/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-tables-general-firm-edit')")
	public EntityResponse<FirmPO> findByIdWithAll(@PathVariable java.lang.Long id) {
		return new EntityResponse<FirmPO>(getCrudFacade().findById(id), "*");
	}

	@GetMapping("/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-tables-general-firm-edit')")
	public EntityResponse<FirmPO> findById(@PathVariable java.lang.Long id) {
		return new EntityResponse<FirmPO>(getCrudFacade().findById(id), "*", "address.*", "registryType.*", "address.*",
				"address.addressType.*", "address.city.*", "contactList", "contactList.*", "contactList.contactItem",
				"contactList.contactItem.*");
	}

	@GetMapping("/registry/{registry}")
	public EntityResponse<FirmPO> findFirmByRegistry(@PathVariable String registry) {
		FirmPO firm = firmFacade.findFirmByRegistry(registry);
		return new EntityResponse<FirmPO>(firm, "*", "address.*", "registryType.*", "address.*",
				"address.addressType.*", "address.city.*", "contactList", "contactList.*", "contactList.contactItem",
				"contactList.contactItem.*");
	}


	@PostMapping("/")
	@PreAuthorize("hasAuthority('insert-tables-general-firm-edit')")
	public EntityResponse<Response> insert(@RequestBody @Valid FirmPO firmPO) {

		firmPO = getCrudFacade().save(firmPO);

		SuccessResponse<FirmPO> successResponse = new SuccessResponse<FirmPO>(Response.INFO,
				GlobalKeyMessagesConstants.INSERT_SUCCESS, firmPO);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@PutMapping("/")
	@PreAuthorize("hasAuthority('update-tables-general-firm-edit')")
	public EntityResponse<Response> update(@RequestBody @Valid FirmPO firmPO) {

		firmPO = getCrudFacade().update(firmPO);

		SuccessResponse<FirmPO> successResponse = new SuccessResponse<FirmPO>(Response.INFO,
				GlobalKeyMessagesConstants.UPDATE_SUCCESS, firmPO);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@DeleteMapping("/{id}")
	@PreAuthorize("hasAuthority('delete-tables-general-firm-edit')")
	public Response delete(@PathVariable Long id) {

		getCrudFacade().delete(id);

		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
	}

	@GetMapping("/")
	public EntityResponse<List<FirmPO>> findAll() {
		return new EntityResponse<List<FirmPO>>(getCrudFacade().findAll(), "*", "address.*", "registryType.*",
				"address.*", "address.addressType.*", "address.city.*");
	}

}
