package br.com.focusts.clinicall.web.modules.partners.controller.nuclearis.controller;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.service.modules.partners.repository.NuclearesTabelaQueryDslRepository;
import br.com.focusts.clinicall.service.modules.partners.service.PartnersNuclearisService;
import br.com.focusts.clinicall.service.modules.partners.to.nuclearisTO.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import java.util.List;

@RestController
@RequestMapping("/nuclearis/ttabelascontroller")
public class TTabelaController {

    @Autowired
    private NuclearesTabelaQueryDslRepository repository;

    @Autowired
    private PartnersNuclearisService partnersNuclearisService;

    @GetMapping(value={"/nacionalidades", "/nacionalidades/{name}"})
    public EntityResponse<Response> listCountries (@PathVariable(required = false) String name){
        List<NacionalidadeTO> countries = repository.findCountries(name, name);
        SuccessResponse<List<NacionalidadeTO>> successResponse =
                    new SuccessResponse<List<NacionalidadeTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, countries);
        return new EntityResponse<Response>(successResponse);
    }

    @GetMapping("/cidades/{code}")
    public EntityResponse<Response> listCities (@PathVariable String code){
        List<CidadeTO> cities = repository.findCities(code);
        SuccessResponse<List<CidadeTO>> successResponse =
                new SuccessResponse<List<CidadeTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, cities);
        return new EntityResponse<Response>(successResponse);
    }

    @GetMapping("/operadora")
    public EntityResponse<Response> litProviders(){
        List<OperadoraTO> provides = List.of(
                new OperadoraTO("CLARO"),
                new OperadoraTO("TIM"),
                new OperadoraTO("VIVO"));
        SuccessResponse<List<OperadoraTO>> successResponse =
                new SuccessResponse<List<OperadoraTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, provides);
        return new EntityResponse<Response>(successResponse);
    }

    @GetMapping("/racas")
    public EntityResponse<Response> listBreeds (){
        List<RacaTO> breeds = repository.findBreeds();
        SuccessResponse<List<RacaTO>> successResponse =
                new SuccessResponse<List<RacaTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, breeds);
        return new EntityResponse<Response>(successResponse);
    }

    @GetMapping(value={"/especialidades", "/especialidades/{name}"})
    public EntityResponse<Response> listSpecialties (@PathVariable(required = false) String name){
        List<EspecialidadeTO> specialties = repository.findSpecialties(name);
        SuccessResponse<List<EspecialidadeTO>> successResponse =
                new SuccessResponse<List<EspecialidadeTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, specialties);
        return new EntityResponse<Response>(successResponse);
    }

    @GetMapping(value={"/usuarios", "/usuarios/{name}"})
    public EntityResponse<Response> listUsers(@PathVariable(required = false) String name){
        List<UsuarioTO> users = repository.findUsers(name);
        SuccessResponse<List<UsuarioTO>> successResponse =
                new SuccessResponse<List<UsuarioTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, users);
        return new EntityResponse<Response>(successResponse);
    }

    @GetMapping(value={"/statusguia", "/statusguia/{name}"})
    public EntityResponse<Response> listStatusGuide(@PathVariable(required = false) String name){
        List<StatusGuiaTO> statusGuide = repository.findStatus(name);
        SuccessResponse<List<StatusGuiaTO>> successResponse =
                new SuccessResponse<List<StatusGuiaTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, statusGuide);
        return new EntityResponse<Response>(successResponse);
    }

    @PutMapping("/statusguia")
    public EntityResponse<Response> updateStatus(@RequestBody StatusGuiaTO statusGuide) {
        partnersNuclearisService.updateStatusGuia(statusGuide);
        return listStatusGuide(statusGuide.getDescricaoStatus());
    }

    @PostMapping("/statusguia")
    public EntityResponse<Response> insertStatus(@RequestBody StatusGuiaTO statusGuide) {
        partnersNuclearisService.updateStatusGuia(statusGuide);
        return listStatusGuide(statusGuide.getDescricaoStatus());
    }

    @GetMapping("/instituicoes")
    public EntityResponse<Response> listCompanies() {
        List<InstituicaoTO> companies = repository.findCompanies();
        SuccessResponse<List<InstituicaoTO>> successResponse =
                new SuccessResponse<List<InstituicaoTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, companies);
        return new EntityResponse<Response>(successResponse);
    }

    @GetMapping("/tipopagamento")
    public EntityResponse<Response> listPaymentTypes() {
        List<TipoPagamentoTO> paymentTypes = repository.findPaymentTypes();
        SuccessResponse<List<TipoPagamentoTO>> successResponse =
                new SuccessResponse<List<TipoPagamentoTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, paymentTypes);
        return new EntityResponse<Response>(successResponse);
    }

}