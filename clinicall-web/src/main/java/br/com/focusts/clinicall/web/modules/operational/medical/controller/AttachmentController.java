package br.com.focusts.clinicall.web.modules.operational.medical.controller;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.controller.AbstractController;
import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.medical.facade.AttachmentFacade;
import br.com.focusts.clinicall.service.modules.operational.medical.facade.RequestFacade;
import br.com.focusts.clinicall.service.modules.operational.medical.facade.RequestItemFacade;
import br.com.focusts.clinicall.service.modules.operational.medical.facade.RequestModelFacade;
import br.com.focusts.clinicall.service.modules.operational.medical.po.AttachmentPO;
import br.com.focusts.clinicall.service.modules.operational.medical.po.RequestItemPO;
import br.com.focusts.clinicall.service.modules.operational.medical.po.RequestModelPO;
import br.com.focusts.clinicall.service.modules.operational.medical.po.RequestPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.to.CheckedTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/attachment")
public class AttachmentController extends AbstractController<AttachmentPO, Long> {

	@Autowired
	private AttachmentFacade attachmentFacade;

	@Override
	public CrudFacade<AttachmentPO, Long> getCrudFacade() {
		return attachmentFacade;
	}

	@PostMapping("/")
	public EntityResponse<Response> insert(@RequestBody @Valid AttachmentPO attachmentPO) {
		attachmentPO = getCrudFacade().save(attachmentPO);

		SuccessResponse<AttachmentPO> successResponse = new SuccessResponse<AttachmentPO>(Response.INFO,
				GlobalKeyMessagesConstants.INSERT_SUCCESS, attachmentPO);

		return new EntityResponse<Response>(successResponse, "*","data.*" ,"data.eartefact.*", "data.performer", "data.performer.id",
				"data.performer.professional", "data.performer.professional.person", "data.performer.professional.person.name");
	}

	@GetMapping("/")
	public EntityResponse<List<AttachmentPO>> findAll() {
		return new EntityResponse<List<AttachmentPO>>(getCrudFacade().findAll(), "*", "eartefact.*");
	}

	@DeleteMapping("/{id}")
	public Response delete(@PathVariable Long id) {

		getCrudFacade().delete(id);

		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
	}

	@PostMapping(value = "/search/{id}")
	public EntityResponse<Page<AttachmentPO>> searchByPatientId(@RequestBody PageSearchTO pageSearchTO,
															 @PathVariable java.lang.Long id) {

		Page<AttachmentPO> page = attachmentFacade.findByDatedContainingByPatientId(pageSearchTO, id);

		return new EntityResponse<Page<AttachmentPO>>(page, "*","content.*" ,"content.eartefact.*", "content.performer", "content.performer.id",
				"content.performer.professional", "content.performer.professional.person", "content.performer.professional.person.name");
	}

	@PostMapping(value = "/search/{patientId}/{type}")
	public EntityResponse<Page<AttachmentPO>> searchByPatientId(@RequestBody PageSearchTO pageSearchTO,
																@PathVariable java.lang.Long patientId, @PathVariable java.lang.Integer type) {

		Page<AttachmentPO> page = attachmentFacade.findByPatientIdAndType(patientId, type, pageSearchTO);

		return new EntityResponse<Page<AttachmentPO>>(page, "*","content.*" ,"content.eartefact.*", "content.performer", "content.performer.id",
				"content.performer.professional", "content.performer.professional.person", "content.performer.professional.person.name");
	}

	@PostMapping(value = "/search/{patientId}/{type}/{documentId}")
	public EntityResponse<Page<AttachmentPO>> searchByPatientIdAndDocumentId(@RequestBody PageSearchTO pageSearchTO,
																@PathVariable java.lang.Long patientId, @PathVariable java.lang.Integer type, @PathVariable java.lang.Long documentId) {

		Page<AttachmentPO> page = attachmentFacade.findByPatientIdAndTypeAndDocumentId(patientId, type,documentId, pageSearchTO);

		return new EntityResponse<Page<AttachmentPO>>(page, "*","content.*" ,"content.eartefact.*", "content.performer", "content.performer.id",
				"content.performer.professional", "content.performer.professional.person", "content.performer.professional.person.name");
	}

	@PostMapping(value = "/description/{attachementId}")
	public EntityResponse<String> insertDescriptionCapture(@RequestBody String description, @PathVariable java.lang.Long attachementId) {

		String descriptionResponse = attachmentFacade.insertDescriptionCapture(description, attachementId);

		return new EntityResponse<String>(descriptionResponse, "*");
	}

	@PostMapping(value = "/checked/{attachementId}")
	public EntityResponse<Boolean> insertDescriptionCapture(@RequestBody CheckedTO checked, @PathVariable java.lang.Long attachementId) {

		Boolean descriptionResponse = attachmentFacade.checkedCapture(checked.getChecked(), attachementId);

		return new EntityResponse<Boolean>(descriptionResponse, "*");
	}

}
