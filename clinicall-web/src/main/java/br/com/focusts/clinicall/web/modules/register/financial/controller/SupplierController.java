
package br.com.focusts.clinicall.web.modules.register.financial.controller;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.controller.AbstractController;
import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.financial.facade.SupplierFacade;
import br.com.focusts.clinicall.service.modules.register.financial.po.SupplierPO;

@RestController
@RequestMapping("/supplier")
public class SupplierController extends AbstractController<SupplierPO,java.lang.Long> {

	@Autowired
	private SupplierFacade supplierFacade;

	@Override
	public CrudFacade<SupplierPO,java.lang.Long> getCrudFacade() {
		return supplierFacade;
	}

	@PostMapping(value = "/search")
	@PreAuthorize("hasAuthority('search-register-financial-supplier-list')")
	public EntityResponse<Page<SupplierPO>> search(@RequestBody PageSearchTO pageSearchTO) {

          Page<SupplierPO> page = supplierFacade.findByNameStartingWith(pageSearchTO);

          return new EntityResponse<Page<SupplierPO>>(page, "*","content.*");
	}

	@GetMapping("/wAll/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-register-financial-supplier-edit')")
	public EntityResponse<SupplierPO> findByIdWithAll(@PathVariable java.lang.Long id) {
            return new EntityResponse<SupplierPO>(getCrudFacade().findById(id), "*");
	}

	@GetMapping("/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-register-financial-supplier-edit')")
	public EntityResponse<SupplierPO> findById(@PathVariable java.lang.Long id) {
            return new EntityResponse<SupplierPO>(getCrudFacade().findById(id), "*", "registryType.*", "person.id", "bankDetail", "bankDetail.*", "bankDetail.bank.*");
	}

	@PostMapping("/")
	@PreAuthorize("hasAuthority('insert-register-financial-supplier-edit')")
	public EntityResponse<Response> insert(@RequestBody @Valid SupplierPO supplierPO) {
		
	    supplierPO = getCrudFacade().save(supplierPO);
		
	    SuccessResponse<SupplierPO> successResponse = new SuccessResponse<SupplierPO>(Response.INFO, GlobalKeyMessagesConstants.INSERT_SUCCESS, supplierPO);

	    return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@PutMapping("/")
	@PreAuthorize("hasAuthority('update-register-financial-supplier-edit')")
	public EntityResponse<Response> update(@RequestBody @Valid SupplierPO supplierPO) {
		
	    supplierPO = getCrudFacade().update(supplierPO);
		
	    SuccessResponse<SupplierPO> successResponse = new SuccessResponse<SupplierPO>(Response.INFO, GlobalKeyMessagesConstants.UPDATE_SUCCESS, supplierPO);

	    return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

        @DeleteMapping("/{id}")
	@PreAuthorize("hasAuthority('delete-register-financial-supplier-edit')")
	public Response delete(@PathVariable Long id) {

		getCrudFacade().delete(id);
		
		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
	}

	@GetMapping("/")
	@PreAuthorize("hasAuthority('search-register-financial-supplier-list')")
	public EntityResponse<List<SupplierPO>> findAll() {
		return new EntityResponse<List<SupplierPO>>(getCrudFacade().findAll(), "*");
	}


}
