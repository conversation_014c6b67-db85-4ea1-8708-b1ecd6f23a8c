
package br.com.focusts.clinicall.web.modules.register.operational.controller;

import java.util.List;

import javax.validation.Valid;

import br.com.focusts.clinicall.service.modules.operational.scheduling.po.SchedulePO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.to.ScheduleFilterTO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.to.ScheduleTO;
import org.apache.maven.lifecycle.Schedule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.controller.AbstractController;
import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.fw.to.SimpleEntityTO;
import br.com.focusts.clinicall.service.modules.register.operational.enums.OperationTypeEnum;
import br.com.focusts.clinicall.service.modules.register.operational.facade.OperationFacade;
import br.com.focusts.clinicall.service.modules.register.operational.po.OperationPO;
import br.com.focusts.clinicall.service.modules.register.organizational.facade.CompanyFacade;
import br.com.focusts.clinicall.service.modules.register.organizational.po.CompanyPO;
import br.com.focusts.clinicall.service.modules.register.security.facade.UserFacade;
import br.com.focusts.clinicall.service.modules.register.security.po.UserPO;

@RestController
@RequestMapping("/operation")
public class OperationController extends AbstractController<OperationPO,java.lang.Long> {

	@Autowired
	private OperationFacade operationFacade;
	
	@Autowired
	private UserFacade userFacade;
	
	@Autowired
	private CompanyFacade companyFacade;

	@Override
	public CrudFacade<OperationPO,java.lang.Long> getCrudFacade() {
		return operationFacade;
	}

	@PostMapping(value = "/search")
	@PreAuthorize("hasAuthority('search-register-operational-operation-list')")
	public EntityResponse<Page<OperationPO>> search(@RequestBody PageSearchTO pageSearchTO) {

          Page<OperationPO> page = operationFacade.findByCompany_nameContaining(pageSearchTO);

          return new EntityResponse<Page<OperationPO>>(page, "*","content.*", "content.user","content.user.login",
        		  "content.performer.professional",  "content.performer.id", "content.performer.professional.person", "content.performer.professional.id", "content.performer.professional.person.id", "content.performer.professional.person.name",
        		  "content.company", "content.company.id", "content.company.name");
	}

	@GetMapping("/wAll/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-register-operational-operation-edit')")
	public EntityResponse<OperationPO> findByIdWithAll(@PathVariable java.lang.Long id) {
            return new EntityResponse<OperationPO>(getCrudFacade().findById(id), "*",   "user", "user.id", "user.login", "user.person", "user.person.name", 
            		"performer", "performer.id", "performer.professional", "performer.professional.person", "performer.professional.person.name", 
            		"company", "company.id", "company.name", "company.companyParent", "company.companyParent.id", "company.companyParent.name");
	}

	@GetMapping("/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-register-operational-operation-edit')")
	public EntityResponse<OperationPO> findById(@PathVariable java.lang.Long id) {
            return new EntityResponse<OperationPO>(getCrudFacade().findById(id), "*");
	}

	@PostMapping("/")
	@PreAuthorize("hasAuthority('insert-register-operational-operation-edit')")
	public EntityResponse<Response> insert(@RequestBody @Valid OperationPO operationPO) {
		
	    operationPO = getCrudFacade().save(operationPO);
		
	    SuccessResponse<OperationPO> successResponse = new SuccessResponse<OperationPO>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, operationPO);

	    return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@PutMapping("/")
	@PreAuthorize("hasAuthority('update-register-operational-operation-edit')")
	public EntityResponse<Response> update(@RequestBody @Valid OperationPO operationPO) {
		
	    operationPO = getCrudFacade().update(operationPO);
		
	    SuccessResponse<OperationPO> successResponse = new SuccessResponse<OperationPO>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, operationPO);

	    return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

    @DeleteMapping("/{id}")
	@PreAuthorize("hasAuthority('delete-register-operational-operation-edit')")
	public Response delete(@PathVariable Long id) {

		getCrudFacade().delete(id);
		
		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
	}

	@GetMapping("/")
	@PreAuthorize("hasAuthority('search-register-operational-operation-list')")
	public EntityResponse<List<OperationPO>> findAll() {
		return new EntityResponse<List<OperationPO>>(getCrudFacade().findAll(), "*");
	}
	
	@PostMapping(value = "/user/search")
	@PreAuthorize("hasAuthority('search-register-security-user-list')")
	public EntityResponse<Page<UserPO>> searchPersonByUser(@RequestBody PageSearchTO pageSearchTO) {

		Page<UserPO> page = userFacade.findByEmailContainingOrLoginContaining(pageSearchTO);
		
		return new EntityResponse<Page<UserPO>>(page, "*","content.id","content.person", "content.person.name", "content.person.id");
	}

	@GetMapping("/operationType/simpleList")
	public SimpleEntityTO getSimpleEntityViewType() {
		return super.getSimpleEntityFromEnun(OperationTypeEnum.values());
	}
	
	@GetMapping(value = "/company/mainCompany")
	public EntityResponse<List<CompanyPO>> findByCompanyParentIsNull() {
          return new EntityResponse<List<CompanyPO>>(companyFacade.findByCompanyParentIsNull(), "id","name");
	}
	
	@GetMapping(value = "/company/unitCompany/{companyParentId}")
	public EntityResponse<List<CompanyPO>> findByCompanyParentId(@PathVariable Long companyParentId) {
          return new EntityResponse<List<CompanyPO>>(companyFacade.findByCompanyParentId(companyParentId), "id","name");
	}

	@PostMapping("/findBySchedule")
	public EntityResponse<List<SchedulePO>> findBySchedule(@RequestBody ScheduleFilterTO scheduleFilterTO) {
		return new EntityResponse<List<SchedulePO>>(operationFacade.findBySchedule(scheduleFilterTO), "id","hour");
	}

	@DeleteMapping("/deleteScheduleFree")
	@PreAuthorize("hasAuthority('delete-operational-schedule-appointment')")
	public Response deleteScheduleFree(@RequestBody List<SchedulePO> scheduleList) {
		operationFacade.deleteScheduleFree(scheduleList);

		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
	}

}
