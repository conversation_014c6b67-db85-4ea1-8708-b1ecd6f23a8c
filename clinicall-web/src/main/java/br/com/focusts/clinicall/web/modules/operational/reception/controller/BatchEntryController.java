
package br.com.focusts.clinicall.web.modules.operational.reception.controller;

import java.util.List;

import javax.validation.Valid;

import br.com.focusts.clinicall.authoriertiss.tiss.to.ResponseMessageTO;
import br.com.focusts.clinicall.service.modules.operational.billing.to.FilterTO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.BatchEntryFilterPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.BatchEntryItemPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.BatchEntrySpecPO;
import br.com.focusts.clinicall.service.modules.operational.reception.to.BatchEntryItemTO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.to.PatientTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.controller.AbstractController;
import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.reception.facade.BatchEntryFacade;
import br.com.focusts.clinicall.service.modules.operational.reception.po.BatchEntryPO;

@RestController
@RequestMapping("/batchEntry")
public class BatchEntryController extends AbstractController<BatchEntryPO,java.lang.Long> {

	@Autowired
	private BatchEntryFacade batchEntryFacade;

	@Override
	public CrudFacade<BatchEntryPO,java.lang.Long> getCrudFacade() {
		return batchEntryFacade;
	}
	/*
	 * CONTROLLER BATCH-ENTRY
	 *  **/
	@PostMapping(value = "/search")
	@PreAuthorize("hasAuthority('search-operational-reception-batchentry-list')")
	public EntityResponse<Page<BatchEntryPO>> search(@RequestBody PageSearchTO pageSearchTO) {

		Page<BatchEntryPO> page = batchEntryFacade.find(pageSearchTO);

		return new EntityResponse<Page<BatchEntryPO>>(page, "*","content.*", "!content.batchEntrySpec");
	}
	@PostMapping(value = "/search/simple")
	public EntityResponse<Page<BatchEntryPO>> searchSimpleList(@RequestBody PageSearchTO pageSearchTO) {

		Page<BatchEntryPO> page = batchEntryFacade.findByClosed(pageSearchTO);

		return new EntityResponse<Page<BatchEntryPO>>(page, "*","content", "content.number", "content.id");
	}

	@GetMapping("/wAll/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-operational-reception-batchentry-edit')")
	public EntityResponse<BatchEntryPO> findByIdWithAll(@PathVariable java.lang.Long id) {
		return new EntityResponse<BatchEntryPO>(getCrudFacade().findById(id), "*");
	}

	@GetMapping("/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-operational-reception-batchentry-edit')")
	public EntityResponse<BatchEntryPO> findById(@PathVariable java.lang.Long id) {
		return new EntityResponse<BatchEntryPO>(getCrudFacade().findById(id),
				"*",
				"batchEntrySpec",
				"batchEntrySpec.id"
				);
	}

	@PostMapping("/")
	@PreAuthorize("hasAuthority('insert-operational-reception-batchentry-edit')")
	public EntityResponse<Response> insert(@RequestBody @Valid BatchEntryPO batchEntryPO) {

		batchEntryPO = getCrudFacade().save(batchEntryPO);

		SuccessResponse<BatchEntryPO> successResponse = new SuccessResponse<BatchEntryPO>(Response.INFO, GlobalKeyMessagesConstants.INSERT_SUCCESS, batchEntryPO);

		return new EntityResponse<Response>(successResponse,
				"*",
				"data.*"
		);
	}


	@PutMapping("/")
	@PreAuthorize("hasAuthority('update-operational-reception-batchentry-edit')")
	public EntityResponse<Response> update(@RequestBody @Valid BatchEntryPO batchEntryPO) {

		batchEntryPO = getCrudFacade().update(batchEntryPO);

		SuccessResponse<BatchEntryPO> successResponse = new SuccessResponse<BatchEntryPO>(Response.INFO, GlobalKeyMessagesConstants.UPDATE_SUCCESS, batchEntryPO);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@DeleteMapping("/{id}")
	@PreAuthorize("hasAuthority('delete-operational-reception-batchentry-edit')")
	public Response delete(@PathVariable Long id) {

		batchEntryFacade.delete(id);

		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
	}

	@GetMapping("/")
	@PreAuthorize("hasAuthority('search-operational-reception-batchentry-list')")
	public EntityResponse<List<BatchEntryPO>> findAll() {
		return new EntityResponse<List<BatchEntryPO>>(getCrudFacade().findAll(), "*");
	}

	@PostMapping("/search/patient")
	public EntityResponse<Page<PatientTO>> filterPatient(@RequestBody FilterTO filterTo) {
		var patientPage = batchEntryFacade.filterPatient(filterTo);
		return new EntityResponse<Page<PatientTO>>(patientPage, "*", "content.*");
	}


	/*
	 * CONTROLLER BATCH-ENTRY-ITEM
	 *  **/
	@PostMapping("/batch-entry-item/insert")
	public EntityResponse<Response> insertBatchEntryItemTO(@RequestBody BatchEntryItemTO batchEntryItem){
		batchEntryFacade.insertBatchEntryItem(batchEntryItem);

		SuccessResponse<BatchEntryPO> successResponse = new SuccessResponse<BatchEntryPO>(Response.INFO, GlobalKeyMessagesConstants.INSERT_SUCCESS);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}
	@DeleteMapping("/batch-entry-item/remove/{batchEntryItemId}")
	public EntityResponse<Response> deleteBatchEntryItemTO(@PathVariable Long batchEntryItemId){
		batchEntryFacade.deleteBatchEntryItem(batchEntryItemId);

		SuccessResponse<BatchEntryPO> successResponse = new SuccessResponse<BatchEntryPO>(Response.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}
	@PostMapping("/batch-entry-item/whith-spec/insert")
	public EntityResponse<Response> insertBatchEntryItemWhithSpec(@RequestBody BatchEntryItemPO batchEntryItem){
		batchEntryItem = batchEntryFacade.insertBatchEntryItemWhithSpec(batchEntryItem);

		SuccessResponse<BatchEntryItemPO> successResponse = new SuccessResponse<BatchEntryItemPO>(Response.INFO, GlobalKeyMessagesConstants.INSERT_SUCCESS,batchEntryItem);

		return new EntityResponse<Response>(successResponse,
				"*",
				"data.*",
				"data.batchEntry",
				"data.batchEntry.id",
				"data.batchEntrySpec",
				"data.batchEntrySpec.id",
				"data.patient",
				"data.patient.id"
		);
	}

	@PostMapping("/batchEntryItem/{batchEntryId}")
	public EntityResponse<Page<BatchEntryItemPO>> filterPatient(@PathVariable Long batchEntryId, @RequestBody PageSearchTO pageSearchTO) {
		return new EntityResponse<Page<BatchEntryItemPO>>(batchEntryFacade.findBatchEntryResultByBatchEntry_Id(batchEntryId,pageSearchTO),
				"*",
				"content.*",
				"content.batchEntrySpec",
				"content.batchEntrySpec.id",
				"content.batchEntrySpec.companyCostCenter",
				"content.batchEntrySpec.companyCostCenter.*",
				"content.batchEntrySpec.companyCostCenter.company",
				"content.batchEntrySpec.companyCostCenter.company.id",
				"content.batchEntrySpec.companyCostCenter.company.name",
				"content.batchEntrySpec.companyCostCenter.company.alias",
				"content.batchEntrySpec.companyCostCenter.costCenter",
				"content.batchEntrySpec.companyCostCenter.costCenter.*",
				"content.batchEntrySpec.exameType",
				"content.batchEntrySpec.exameType.id",
				"content.batchEntrySpec.exameType.code",
				"content.batchEntrySpec.exameType.name",
				"content.batchEntrySpec.insurance",
				"content.batchEntrySpec.insurance.name",
				"content.batchEntrySpec.insurance.id",
				"content.batchEntrySpec.procedure",
				"content.batchEntrySpec.procedure.id",
				"content.batchEntrySpec.procedure.name",
				"content.batchEntrySpec.amount",
				"content.batchEntrySpec.professional",
				"content.batchEntrySpec.professional.id",
				"content.batchEntrySpec.professional.person",
				"content.batchEntrySpec.professional.person.id",
				"content.batchEntrySpec.professional.person.name",
				"content.batchEntrySpec.performer",
				"content.batchEntrySpec.performer.id",
				"content.batchEntrySpec.performer.professional",
				"content.batchEntrySpec.performer.professional.id",
				"content.batchEntrySpec.performer.professional.person",
				"content.batchEntrySpec.performer.professional.person.id",
				"content.batchEntrySpec.performer.professional.person.name",
				"content.batchEntry",
				"content.batchEntry.id",
				"content.batchEntry.batchEntrySpec",
				"content.batchEntry.batchEntrySpec.id",
				"content.patient",
				"content.patient.id",
				"content.patient.person",
				"content.patient.person.name",
				"content.patient.person.birthday",
				"content.patient.enrollment",
				"content.patient.person.cpf",
				"content.patient.insurance",
				"content.patient.insurance.id",
				"content.patient.insurance.name"
		);
	}

	/*
	 * CONTROLLER BATCH-ENTRY-SPEC
	 *  **/
	@GetMapping("/batch-entry-spec/{id}")
	public EntityResponse<BatchEntrySpecPO> findByBatchEntrySpecId(@PathVariable Long id){

		return new EntityResponse<BatchEntrySpecPO>(batchEntryFacade.findByBatchEntrySpecId(id),
				"*",
				"companyCostCenter",
				"companyCostCenter.*",
				"companyCostCenter.company",
				"companyCostCenter.company.id",
				"companyCostCenter.company.name",
				"companyCostCenter.company.alias",
				"companyCostCenter.costCenter",
				"companyCostCenter.costCenter.*",
				"exameType",
				"exameType.id",
				"exameType.code",
				"exameType.name",
				"insurance",
				"insurance.name",
				"insurance.id",
				"procedure",
				"procedure.id",
				"procedure.name",
				"professional",
				"professional.id",
				"professional.person",
				"professional.person.id",
				"professional.person.name",
				"performer",
				"performer.id",
				"performer.professional",
				"performer.professional.id",
				"performer.professional.person",
				"performer.professional.person.id",
				"performer.professional.person.name"
		);
	}

	@PostMapping("/batch-entry-spec/insert")
	public EntityResponse<BatchEntrySpecPO> insertBatchEntrySpec(@RequestBody BatchEntrySpecPO batchEntrySpec){
		batchEntrySpec = batchEntryFacade.insertBatchEntrySpec(batchEntrySpec);

		return new EntityResponse<BatchEntrySpecPO>(batchEntrySpec, "*");
	}

	@DeleteMapping("/batch-entry-spec/remove/{id}")
	public EntityResponse<Response> removeBatchEntrySpec(@PathVariable Long id){
		batchEntryFacade.removeBatchEntrySpec(id);

		SuccessResponse<BatchEntryPO> successResponse = new SuccessResponse<BatchEntryPO>(Response.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}
	/*
	 * CONTROLLER BATCH-ENTRY-RESULT
	 *  **/
	@PostMapping("insert/batchEntryResult/batchEntry/{batchEntryId}")
	public EntityResponse<Response> insertBatchEntryResult(@PathVariable Long batchEntryId) {
		List<ResponseMessageTO> responseMensage = batchEntryFacade.insertBatchEntryResult(batchEntryId);
		SuccessResponse<List<ResponseMessageTO>> successResponse = new SuccessResponse<List<ResponseMessageTO>>(Response.INFO, GlobalKeyMessagesConstants.UPDATE_SUCCESS, responseMensage);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}


	/*
	 * CONTROLLER BATCH-ENTRY-FILTER
	 *  **/
	@GetMapping("{batchEntryId}/batch-entry-filter/list")
	public EntityResponse<Response> findListBtachEntry(@PathVariable Long batchEntryId){
		List<BatchEntryFilterPO> batchEntryFilterPOList = batchEntryFacade.findByBatchEntryList(batchEntryId);
		SuccessResponse<List<BatchEntryFilterPO>> successResponse = new SuccessResponse<List<BatchEntryFilterPO>>(Response.INFO, GlobalKeyMessagesConstants.UPDATE_SUCCESS, batchEntryFilterPOList);

		return new EntityResponse<Response>(successResponse, "*", "data.*", "data.filter", "data.filter.key");
	}
}
