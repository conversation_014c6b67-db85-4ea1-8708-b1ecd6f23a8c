package br.com.focusts.clinicall.web.http.util;

import javax.servlet.ServletRequest;
import javax.servlet.ServletRequestWrapper;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.io.Charsets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.util.ContentCachingRequestWrapper;

import br.com.focusts.clinicall.web.http.to.RequestDetailTO;

public class ClientInfoUtil {

	public static RequestDetailTO getRequesDetail(HttpServletRequest request) {

		String url = request.getRequestURL().toString();
		String queryString = request.getQueryString();
		String fullURL = ClientInfoUtil.getFullURL(request);
		String uri = request.getRequestURI();
		String scheme = request.getScheme();
		String serverName = request.getServerName();
		int portNumber = request.getServerPort();


		String contextPath = ClientInfoUtil.getContextPath(request);
		String servletPath = ClientInfoUtil.getServletPath(request);
		String pathInfo = ClientInfoUtil.getPathInfo(request);

		String referer = ClientInfoUtil.getReferer(request);
		String clientIpAddr = ClientInfoUtil.getClientIpAddr(request);
		String clientOS = ClientInfoUtil.getClientOS(request);
		String clientBrowser = ClientInfoUtil.getClientBrowser(request);
		String userAgent = ClientInfoUtil.getUserAgent(request);

		ContentCachingRequestWrapper underlyingCachingRequest = getUnderlyingCachingRequest(request);

		String body = StringUtils.EMPTY;

		if(underlyingCachingRequest!=null && underlyingCachingRequest.getContentAsByteArray()!=null) {
			body = new String(underlyingCachingRequest.getContentAsByteArray(), Charsets.UTF_8);
			body = StringUtils.normalizeSpace(body);
		}

		String method = request.getMethod();

		RequestDetailTO requestDetailTO = new RequestDetailTO();
		requestDetailTO.setBody(body);
		requestDetailTO.setClientBrowser(clientBrowser);
		requestDetailTO.setClientIpAddr(clientIpAddr);
		requestDetailTO.setClientOS(clientOS);
		requestDetailTO.setContextPath(contextPath);
		requestDetailTO.setFullURL(fullURL);
		requestDetailTO.setPathInfo(pathInfo);
		requestDetailTO.setPortNumber(String.valueOf(portNumber));
		requestDetailTO.setQueryString(queryString);
		requestDetailTO.setReferer(referer);
		requestDetailTO.setScheme(scheme);
		requestDetailTO.setServerName(serverName);
		requestDetailTO.setServletPath(servletPath);
		requestDetailTO.setUri(uri);
		requestDetailTO.setUrl(url);
		requestDetailTO.setUserAgent(userAgent);
		requestDetailTO.setMethod(method);

		return requestDetailTO;
	}

	private static String getContextPath(HttpServletRequest request) {
		String contextPath = "";
		try{
			contextPath =request.getContextPath();
		}catch (Exception e){
		}
		return contextPath;
	}
	private static String getServletPath(HttpServletRequest request) {
		String servletPath = "";
		try{
			servletPath =request.getServletPath();
		}catch (Exception e){
		}
		return servletPath;
	}

	private static String getPathInfo(HttpServletRequest request) {
		String pathInfo = "";
		try{
			pathInfo =request.getPathInfo();
		}catch (Exception e){
		}
		return pathInfo;
	}



	public static String getReferer(HttpServletRequest request) {
		String referer = "";
		try {
			referer = request.getHeader("referer");
		}catch(Exception e){
		}
		return referer;
	}


	public static String getFullURL(HttpServletRequest request) {
		final StringBuffer requestURL = request.getRequestURL();
		final String queryString = request.getQueryString();

		final String result = queryString == null ? requestURL.toString()
				: requestURL.append('?').append(queryString).toString();

		return result;
	}

	public static String getClientIpAddr(HttpServletRequest request) {
		String ip = request.getHeader("X-Forwarded-For");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("HTTP_CLIENT_IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("HTTP_X_FORWARDED_FOR");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
		}
		return ip;
	}

	public static String getClientOS(HttpServletRequest request) {
		final String browserDetails = request.getHeader("User-Agent");

		// =================OS=======================
		if (browserDetails != null) {

			final String lowerCaseBrowser = browserDetails.toLowerCase();
			if (lowerCaseBrowser.contains("windows")) {
				return "Windows";
			} else if (lowerCaseBrowser.contains("mac")) {
				return "Mac";
			} else if (lowerCaseBrowser.contains("x11")) {
				return "Unix";
			} else if (lowerCaseBrowser.contains("android")) {
				return "Android";
			} else if (lowerCaseBrowser.contains("iphone")) {
				return "IPhone";
			} else {
				return "UnKnown, More-Info: " + browserDetails;
			}
		} else {
			return "UnKnown";
		}
	}

	public static String getClientBrowser(HttpServletRequest request) {
		final String browserDetails = request.getHeader("User-Agent");
		String browser = "";

		if (browserDetails != null) {

			final String user = browserDetails.toLowerCase();

			// ===============Browser===========================
			if (user.contains("msie")) {
				String substring = browserDetails.substring(browserDetails.indexOf("MSIE")).split(";")[0];
				browser = substring.split(" ")[0].replace("MSIE", "IE") + "-" + substring.split(" ")[1];
			} else if (user.contains("safari") && user.contains("version")) {
				browser = (browserDetails.substring(browserDetails.indexOf("Safari")).split(" ")[0]).split("/")[0] + "-"
						+ (browserDetails.substring(browserDetails.indexOf("Version")).split(" ")[0]).split("/")[1];
			} else if (user.contains("opr") || user.contains("opera")) {
				if (user.contains("opera"))
					browser = (browserDetails.substring(browserDetails.indexOf("Opera")).split(" ")[0]).split("/")[0]
							+ "-"
							+ (browserDetails.substring(browserDetails.indexOf("Version")).split(" ")[0]).split("/")[1];
				else if (user.contains("opr"))
					browser = ((browserDetails.substring(browserDetails.indexOf("OPR")).split(" ")[0]).replace("/",
							"-")).replace("OPR", "Opera");
			} else if (user.contains("chrome")) {
				browser = (browserDetails.substring(browserDetails.indexOf("Chrome")).split(" ")[0]).replace("/", "-");
			} else if ((user.indexOf("mozilla/7.0") > -1) || (user.indexOf("netscape6") != -1)
					|| (user.indexOf("mozilla/4.7") != -1) || (user.indexOf("mozilla/4.78") != -1)
					|| (user.indexOf("mozilla/4.08") != -1) || (user.indexOf("mozilla/3") != -1)) {
				// browser=(userAgent.substring(userAgent.indexOf("MSIE")).split("
				// ")[0]).replace("/", "-");
				browser = "Netscape-?";

			} else if (user.contains("firefox")) {
				browser = (browserDetails.substring(browserDetails.indexOf("Firefox")).split(" ")[0]).replace("/", "-");
			} else if (user.contains("rv")) {
				browser = "IE";
			} else {
				browser = "UnKnown, More-Info: " + browserDetails;
			}
		} else {
			browser = "UnKnown";
		}

		return browser;
	}

	public static String getUserAgent(HttpServletRequest request) {
		return request.getHeader("User-Agent");
	}

	private static ContentCachingRequestWrapper getUnderlyingCachingRequest(ServletRequest request) {
		if (ContentCachingRequestWrapper.class.isAssignableFrom(request.getClass())) {
			return (ContentCachingRequestWrapper) request;
		}
		if (request instanceof ServletRequestWrapper) {
			return getUnderlyingCachingRequest(((ServletRequestWrapper) request).getRequest());
		}
		return null;
	}
}
