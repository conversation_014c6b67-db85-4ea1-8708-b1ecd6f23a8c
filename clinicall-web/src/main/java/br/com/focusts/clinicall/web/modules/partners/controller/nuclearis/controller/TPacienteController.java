package br.com.focusts.clinicall.web.modules.partners.controller.nuclearis.controller;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.service.modules.partners.service.PartnersNuclearisService;
import br.com.focusts.clinicall.service.modules.partners.to.nuclearisTO.NuclearisCoberturaTO;
import br.com.focusts.clinicall.service.modules.partners.to.nuclearisTO.NuclearisPatientTO;
import br.com.focusts.clinicall.service.modules.partners.to.nuclearisTO.NuclearisPlanoTO;
import br.com.focusts.clinicall.service.modules.partners.to.nuclearisTO.NuclearisSolicitanteTO;
import br.com.focusts.clinicall.service.modules.register.operational.po.PatientPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


@RestController
@RequestMapping("/nuclearis/tpacientecontroller")
public class TPacienteController {

    @Autowired
    private PartnersNuclearisService partnerNuclearisService;

    // ----------------------PACIENTE-------------------------------------//
    @GetMapping("/paciente/{patientId}")
    public EntityResponse<Response> getPatient(@PathVariable Long patientId) {
        NuclearisPatientTO patient = partnerNuclearisService.findPatientByIdFromNucleris(patientId);

        SuccessResponse<NuclearisPatientTO> successResponse = new SuccessResponse<NuclearisPatientTO>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, patient);
        return new EntityResponse<Response>(successResponse);
    }

    @GetMapping("/pacientes")
    public EntityResponse<Response> getPatientList(@RequestParam Map<String, String> params) {
        Page<NuclearisPatientTO> patient = partnerNuclearisService.findPatientListFromNucleris(params);

        SuccessResponse<Page<NuclearisPatientTO>> successResponse = new SuccessResponse<Page<NuclearisPatientTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, patient);
        return new EntityResponse<Response>(successResponse);
    }

    @GetMapping("/pacientes/{name}")
    public EntityResponse<Response> getPatientListByName(@PathVariable String name, @RequestParam Map<String, String> params) {
        Page<NuclearisPatientTO> patient = partnerNuclearisService.findPatientListFromNuclearisByName(params, name);

        SuccessResponse<Page<NuclearisPatientTO>> successResponse = new SuccessResponse<Page<NuclearisPatientTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, patient);
        return new EntityResponse<Response>(successResponse);
    }

    @PutMapping("/paciente")
    public EntityResponse<Response> updatePatient(@RequestBody NuclearisPatientTO patient) {
        SuccessResponse<NuclearisPatientTO> successResponse = null;

       PatientPO patientPO = partnerNuclearisService.updatePatientNuclearis(patient);

        return getPatient(patientPO.getId());
    }
}
