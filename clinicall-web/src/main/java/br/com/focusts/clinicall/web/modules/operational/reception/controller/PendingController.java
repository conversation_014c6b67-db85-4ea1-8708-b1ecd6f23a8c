
package br.com.focusts.clinicall.web.modules.operational.reception.controller;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.controller.AbstractController;
import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.reception.facade.PendingFacade;
import br.com.focusts.clinicall.service.modules.operational.reception.po.PendingPO;

@RestController
@RequestMapping("/pending")
public class PendingController extends AbstractController<PendingPO,java.lang.Long> {

	@Autowired
	private PendingFacade pendingFacade;

	@Override
	public CrudFacade<PendingPO,java.lang.Long> getCrudFacade() {
		return pendingFacade;
	}

	@PostMapping(value = "/search")
	@PreAuthorize("hasAuthority('search-tables-general-pending-list')")
	public EntityResponse<Page<PendingPO>> search(@RequestBody PageSearchTO pageSearchTO) {

          Page<PendingPO> page = pendingFacade.findByName(pageSearchTO);

          return new EntityResponse<Page<PendingPO>>(page, "*","content.*");
	}

	@GetMapping("/wAll/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-tables-general-pending-edit')")
	public EntityResponse<PendingPO> findByIdWithAll(@PathVariable java.lang.Long id) {
            return new EntityResponse<PendingPO>(getCrudFacade().findById(id), "*");
	}

	@GetMapping("/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-tables-general-pending-edit')")
	public EntityResponse<PendingPO> findById(@PathVariable java.lang.Long id) {
            return new EntityResponse<PendingPO>(getCrudFacade().findById(id), "*");
	}

	@PostMapping("/")
	@PreAuthorize("hasAuthority('insert-tables-general-pending-edit')")
	public EntityResponse<Response> insert(@RequestBody @Valid PendingPO pendingPO) {
		
	    pendingPO = getCrudFacade().save(pendingPO);
		
	    SuccessResponse<PendingPO> successResponse = new SuccessResponse<PendingPO>(Response.INFO, GlobalKeyMessagesConstants.INSERT_SUCCESS, pendingPO);

	    return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@PutMapping("/")
	@PreAuthorize("hasAuthority('update-tables-general-pending-edit')")
	public EntityResponse<Response> update(@RequestBody @Valid PendingPO pendingPO) {
		
	    pendingPO = getCrudFacade().update(pendingPO);
		
	    SuccessResponse<PendingPO> successResponse = new SuccessResponse<PendingPO>(Response.INFO, GlobalKeyMessagesConstants.UPDATE_SUCCESS, pendingPO);

	    return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

        @DeleteMapping("/{id}")
	@PreAuthorize("hasAuthority('delete-tables-general-pending-edit')")
	public Response delete(@PathVariable Long id) {

		getCrudFacade().delete(id);
		
		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
	}

	@GetMapping("/")
	@PreAuthorize("hasAuthority('search-tables-general-pending-list')")
	public EntityResponse<List<PendingPO>> findAll() {
		return new EntityResponse<List<PendingPO>>(getCrudFacade().findAll(), "*");
	}


}
