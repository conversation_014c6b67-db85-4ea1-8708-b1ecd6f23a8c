
package br.com.focusts.clinicall.web.modules.register.organizational.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.controller.AbstractController;
import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.organizational.facade.CostCenterFacade;
import br.com.focusts.clinicall.service.modules.register.organizational.po.CostCenterPO;

@RestController
@RequestMapping("/costCenter")
public class CostCenterController extends AbstractController<CostCenterPO,java.lang.Long> {

	@Autowired
	private CostCenterFacade costCenterFacade;

	@Override
	public CrudFacade<CostCenterPO,java.lang.Long> getCrudFacade() {
		return costCenterFacade;
	}

	@PostMapping(value = "/search")
	@PreAuthorize("hasAuthority('search-register-organizational-cost_center-list')")
	public EntityResponse<Page<CostCenterPO>> search(@RequestBody PageSearchTO pageSearchTO) {

          Page<CostCenterPO> page = costCenterFacade.findByNameContaining(pageSearchTO);

          return new EntityResponse<Page<CostCenterPO>>(page, "*","content.*");
	}

	@PostMapping(value = "/search/parameter")
	public EntityResponse<Page<CostCenterPO>> searchCostCenterToParameter(@RequestBody PageSearchTO pageSearchTO) {

		Page<CostCenterPO> page = costCenterFacade.findByNameContaining(pageSearchTO);

		return new EntityResponse<Page<CostCenterPO>>(page, "*","content.*");
	}

	@GetMapping("/wAll/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-register-organizational-cost_center-edit')")
	public EntityResponse<CostCenterPO> findByIdWithAll(@PathVariable Long id) {
		return new EntityResponse<CostCenterPO>(getCrudFacade().findById(id), "*", "parent.*", "parent.parent.*");
	}

	@GetMapping("/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-register-organizational-cost_center-edit')")
	public EntityResponse<CostCenterPO> findById(@PathVariable Long id) {
		return new EntityResponse<CostCenterPO>(getCrudFacade().findById(id), "*");
	}

	@GetMapping("/parameter/{id}")
	@ResponseBody
	public EntityResponse<CostCenterPO> findByIdToParameter(@PathVariable Long id) {
		return new EntityResponse<CostCenterPO>(getCrudFacade().findById(id), "*");
	}

	@PostMapping("/")
	@PreAuthorize("hasAuthority('insert-register-organizational-cost_center-edit')")
	public EntityResponse<Response> insert(@RequestBody CostCenterPO costCenterPO) {
		
		costCenterPO = getCrudFacade().save(costCenterPO);
		
		SuccessResponse<CostCenterPO> successResponse = new SuccessResponse<CostCenterPO>(Response.INFO, GlobalKeyMessagesConstants.INSERT_SUCCESS, costCenterPO);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@PutMapping("/")
	@PreAuthorize("hasAuthority('update-register-organizational-cost_center-edit')")
	public EntityResponse<Response> update(@RequestBody CostCenterPO costCenterPO) {

		costCenterPO = getCrudFacade().update(costCenterPO);

		SuccessResponse<CostCenterPO> successResponse = new SuccessResponse<CostCenterPO>(Response.INFO, GlobalKeyMessagesConstants.UPDATE_SUCCESS, costCenterPO);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}
	
	@DeleteMapping("/{id}")
	@PreAuthorize("hasAuthority('delete-register-organizational-cost_center-edit')")
	public Response delete(@PathVariable Long id) {

		getCrudFacade().delete(id);
		
		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
	}

	@GetMapping("/")
	@PreAuthorize("hasAuthority('search-register-organizational-cost_center-list')")
	public EntityResponse<List<CostCenterPO>> findAll() {
		return new EntityResponse<List<CostCenterPO>>(getCrudFacade().findAll(), "*");
	}

}
