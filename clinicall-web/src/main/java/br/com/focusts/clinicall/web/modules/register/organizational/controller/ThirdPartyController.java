
package br.com.focusts.clinicall.web.modules.register.organizational.controller;

import java.util.List;

import javax.validation.Valid;

import br.com.focusts.clinicall.service.modules.register.accreditation.po.InsurancePO;
import br.com.focusts.clinicall.service.modules.register.organizational.to.ThirdPartyParticipationTO;
import br.com.focusts.clinicall.service.modules.register.organizational.to.ThirdPartyTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.controller.AbstractController;
import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.organizational.facade.ThirdPartyFacade;
import br.com.focusts.clinicall.service.modules.register.organizational.po.ThirdPartyPO;

@RestController
@RequestMapping("/thirdParty")
public class ThirdPartyController extends AbstractController<ThirdPartyPO,java.lang.Long> {

	@Autowired
	private ThirdPartyFacade thirdPartyFacade;

	@Override
	public CrudFacade<ThirdPartyPO,java.lang.Long> getCrudFacade() {
		return thirdPartyFacade;
	}

	@PostMapping(value = "/search")
	@PreAuthorize("hasAuthority('search-register-organizational-thirdparty-list')")
	public EntityResponse<Page<ThirdPartyTO>> search(@RequestBody PageSearchTO pageSearchTO) {

          Page<ThirdPartyTO> page = thirdPartyFacade.searchThirdParty(pageSearchTO);

          return new EntityResponse<Page<ThirdPartyTO>>(page);
	}

	@PostMapping(value = "/searchSimple")
	public EntityResponse<Page<ThirdPartyTO>> searchSimple(@RequestBody PageSearchTO pageSearchTO) {

		Page<ThirdPartyTO> page = thirdPartyFacade.searchThirdParty(pageSearchTO);

		return new EntityResponse<Page<ThirdPartyTO>>(page);
	}

	@PostMapping(value = "/searchParticipation/{id}")
	public EntityResponse<Page<ThirdPartyParticipationTO>> searchThirdPartyParticipation(@PathVariable java.lang.Long id, @RequestBody PageSearchTO pageSearchTO) {

		Page<ThirdPartyParticipationTO> page = thirdPartyFacade.searchThirdPartyParticipation(pageSearchTO, id);

		return new EntityResponse<Page<ThirdPartyParticipationTO>>(page);
	}

	@GetMapping(value = "/searchParticipation/{thirdPartyId}/{procedureId}")
	public EntityResponse<Response> findThirdPartyParticipation(@PathVariable java.lang.Long thirdPartyId, @PathVariable java.lang.Long procedureId) {

		ThirdPartyParticipationTO partyParticipationTO = thirdPartyFacade.findThirdPartyParticipation(thirdPartyId, procedureId);

		SuccessResponse<ThirdPartyParticipationTO> successResponse = null;

		if (partyParticipationTO != null){
			successResponse = new SuccessResponse<ThirdPartyParticipationTO>(Response.INFO,
					GlobalKeyMessagesConstants.INSERT_SUCCESS, partyParticipationTO);
		}else {
			successResponse = new SuccessResponse<ThirdPartyParticipationTO>(Response.INFO,
					GlobalKeyMessagesConstants.REGISTER_NOT_FOUND);
		}

		return new EntityResponse<Response>(successResponse);
	}

	@GetMapping("/wAll/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-register-organizational-thirdparty-edit')")
	public EntityResponse<ThirdPartyPO> findByIdWithAll(@PathVariable java.lang.Long id) {
            return new EntityResponse<ThirdPartyPO>(getCrudFacade().findById(id), "*");
	}

	@GetMapping("/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-register-organizational-thirdparty-edit')")
	public EntityResponse<ThirdPartyPO> findById(@PathVariable java.lang.Long id) {
            return new EntityResponse<ThirdPartyPO>(getCrudFacade().findById(id), "*",
					"professional" , "professional.id", "professional.person" , "professional.person.name",
					"performer" , "performer.id", "performer.professional", "performer.professional.person" , "performer.professional.person.name",
					"person" , "person.id", "person.name",
					"firm" , "firm.id", "firm.name");
	}

	@PostMapping("/")
	@PreAuthorize("hasAuthority('insert-register-organizational-thirdparty-edit')")
	public EntityResponse<Response> insert(@RequestBody @Valid ThirdPartyPO thirdPartyPO) {
		
	    thirdPartyPO = getCrudFacade().save(thirdPartyPO);
		
	    SuccessResponse<ThirdPartyPO> successResponse = new SuccessResponse<ThirdPartyPO>(Response.INFO, GlobalKeyMessagesConstants.INSERT_SUCCESS, thirdPartyPO);

	    return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@PutMapping("/")
	@PreAuthorize("hasAuthority('update-register-organizational-thirdparty-edit')")
	public EntityResponse<Response> update(@RequestBody @Valid ThirdPartyPO thirdPartyPO) {
		
	    thirdPartyPO = getCrudFacade().update(thirdPartyPO);
		
	    SuccessResponse<ThirdPartyPO> successResponse = new SuccessResponse<ThirdPartyPO>(Response.INFO, GlobalKeyMessagesConstants.UPDATE_SUCCESS, thirdPartyPO);

	    return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@DeleteMapping("/{id}")
	@PreAuthorize("hasAuthority('delete-register-organizational-thirdparty-edit')")
	public Response delete(@PathVariable Long id) {

		getCrudFacade().delete(id);
		
		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
	}

	@GetMapping("/{fromId}/{toId}")
	public Response replaceListPaticipationThirdParty(@PathVariable Long fromId, @PathVariable Long toId) {

		thirdPartyFacade.replaceListPaticipationThirdParty(fromId, toId);

		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.INSERT_SUCCESS);
	}

	@GetMapping("/")
	@PreAuthorize("hasAuthority('search-register-organizational-thirdparty-list')")
	public EntityResponse<List<ThirdPartyPO>> findAll() {
		return new EntityResponse<List<ThirdPartyPO>>(getCrudFacade().findAll(), "*");
	}


}
