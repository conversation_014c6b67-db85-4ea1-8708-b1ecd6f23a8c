
package br.com.focusts.clinicall.web.modules.register.organizational.controller;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Set;

import javax.validation.Valid;

import net.bytebuddy.asm.Advice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.controller.AbstractController;
import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.fw.to.SimpleEntityTO;
import br.com.focusts.clinicall.service.modules.register.organizational.enums.EnvironmentStatusEnum;
import br.com.focusts.clinicall.service.modules.register.organizational.enums.EnvironmentTypeEnum;
import br.com.focusts.clinicall.service.modules.register.organizational.facade.CompanyFacade;
import br.com.focusts.clinicall.service.modules.register.organizational.facade.EnvironmentFacade;
import br.com.focusts.clinicall.service.modules.register.organizational.po.CostCenterPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.EnvironmentPO;

@RestController
@RequestMapping("/environment")
public class EnvironmentController extends AbstractController<EnvironmentPO, java.lang.Long> {

	@Autowired
	private EnvironmentFacade environmentFacade;

	@Autowired
	private CompanyFacade companyFacade;

	@Override
	public CrudFacade<EnvironmentPO, java.lang.Long> getCrudFacade() {
		return environmentFacade;
	}

	@PostMapping(value = "/search")
	@PreAuthorize("hasAuthority('search-register-organizational-environment-list')")
	public EntityResponse<Page<EnvironmentPO>> search(@RequestBody PageSearchTO pageSearchTO) {

		Page<EnvironmentPO> page = environmentFacade.findByNameContainingOrDescriptionContaining(pageSearchTO);

		return new EntityResponse<Page<EnvironmentPO>>(page, "*", "content.*", "content.parent.*");
	}

	@PostMapping(value = "/search/companyId/{companyId}")
	public EntityResponse<Page<EnvironmentPO>> searchSimple(@PathVariable Long companyId, @RequestBody PageSearchTO pageSearchTO) {

		Page<EnvironmentPO> page = environmentFacade.findByNameContainingOrDescriptionContainingAndCompanyCostCenter_Company_idOrAndCompanyCostCenterIsNull(companyId,pageSearchTO);

		return new EntityResponse<Page<EnvironmentPO>>(page, "*", "content.*", "content.parent.*");
	}

	@GetMapping("/wAll/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-register-organizational-environment-edit')")
	public EntityResponse<EnvironmentPO> findByIdWithAll(@PathVariable java.lang.Long id) {
		return new EntityResponse<EnvironmentPO>(getCrudFacade().findById(id), "*");
	}

	@GetMapping("/costCenter/{companyId}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-register-organizational-environment-edit')")
	public EntityResponse<List<CostCenterPO>> findCostCenterByCompanyId(@PathVariable java.lang.Long companyId) {
		return new EntityResponse<List<CostCenterPO>>(companyFacade.findById(companyId).getCostCenterList(), "*");
	}

	@GetMapping("/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-register-organizational-environment-edit')")
	public EntityResponse<EnvironmentPO> findById(@PathVariable java.lang.Long id) {
		return new EntityResponse<EnvironmentPO>(getCrudFacade().findById(id), "*", "companyCostCenter.*",
				"companyCostCenter.company.*", "companyCostCenter.costCenter.*","parent.*");
	}

	@PostMapping("/")
	@PreAuthorize("hasAuthority('insert-register-organizational-environment-edit')")
	public EntityResponse<Response> insert(@RequestBody @Valid EnvironmentPO environmentPO) {

		environmentPO = environmentFacade.save(environmentPO);

		SuccessResponse<EnvironmentPO> successResponse = new SuccessResponse<EnvironmentPO>(Response.INFO,
				GlobalKeyMessagesConstants.INSERT_SUCCESS, environmentPO);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@PutMapping("/")
	@PreAuthorize("hasAuthority('update-register-organizational-environment-edit')")
	public EntityResponse<Response> update(@RequestBody @Valid EnvironmentPO environmentPO) {

		environmentPO = getCrudFacade().update(environmentPO);

		SuccessResponse<EnvironmentPO> successResponse = new SuccessResponse<EnvironmentPO>(Response.INFO,
				GlobalKeyMessagesConstants.UPDATE_SUCCESS, environmentPO);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@DeleteMapping("/{id}")
	@PreAuthorize("hasAuthority('delete-register-organizational-environment-edit')")
	public Response delete(@PathVariable Long id) {

		getCrudFacade().delete(id);

		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
	}

	@GetMapping("/")
	@PreAuthorize("hasAuthority('search-register-organizational-environment-list')")
	public EntityResponse<List<EnvironmentPO>> findAll() {
		return new EntityResponse<List<EnvironmentPO>>(getCrudFacade().findAll(), "*");
	}

	@GetMapping(value = "/main")
	public EntityResponse<List<EnvironmentPO>> findByParentIsNull() {
		return new EntityResponse<List<EnvironmentPO>>(environmentFacade.findByParentIsNull(), "*");
	}

	@GetMapping("/type/simpleList")
	public SimpleEntityTO getSimpleEntityEnvironmentType() {
		return super.getSimpleEntityFromEnun(EnvironmentTypeEnum.values());
	}

	@GetMapping("/status/simpleList")
	public SimpleEntityTO getSimpleEntityEnvironmentStatus() {
		return super.getSimpleEntityFromEnun(EnvironmentStatusEnum.values());
	}

	@GetMapping("/free/{companyId}/{scheduleDate}/{scheduleHour}")
	public EntityResponse<List<EnvironmentPO>> findEnvironmentFree(@PathVariable Long companyId, @PathVariable String scheduleDate, @PathVariable String scheduleHour) {
		LocalDate  date = LocalDate.parse(scheduleDate);
		LocalTime hour = LocalTime.parse(scheduleHour);
		return new EntityResponse<List<EnvironmentPO>>(environmentFacade.findEnvironmentFree(companyId, date, hour), "id", "name");
	}

}
