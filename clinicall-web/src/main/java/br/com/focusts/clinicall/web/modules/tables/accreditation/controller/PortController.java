
package br.com.focusts.clinicall.web.modules.tables.accreditation.controller;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.controller.AbstractController;
import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.facade.PortFacade;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.PortPO;

@RestController
@RequestMapping("/port")
public class PortController extends AbstractController<PortPO,java.lang.Long> {

	@Autowired
	private PortFacade portFacade;

	@Override
	public CrudFacade<PortPO,java.lang.Long> getCrudFacade() {
		return portFacade;
	}

	@PostMapping(value = "/search")
	@PreAuthorize("hasAuthority('search-tables-accreditation-port-list')")
	public EntityResponse<Page<PortPO>> search(@RequestBody PageSearchTO pageSearchTO) {

          Page<PortPO> page = portFacade.findByNameContainingOrValueContainingOrCurrency_nameContaining(pageSearchTO);

          return new EntityResponse<Page<PortPO>>(page, "*","content.*", "content.currency.*");
	}

	@GetMapping("/wAll/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-tables-accreditation-port-edit')")
	public EntityResponse<PortPO> findByIdWithAll(@PathVariable java.lang.Long id) {
            return new EntityResponse<PortPO>(getCrudFacade().findById(id), "*", "currency.*");
	}

	@GetMapping("/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-tables-accreditation-port-edit')")
	public EntityResponse<PortPO> findById(@PathVariable java.lang.Long id) {
            return new EntityResponse<PortPO>(getCrudFacade().findById(id), "*");
	}

	@PostMapping("/")
	@PreAuthorize("hasAuthority('insert-tables-accreditation-port-edit')")
	public EntityResponse<Response> insert(@RequestBody @Valid PortPO portPO) {
		
	    portPO = getCrudFacade().save(portPO);
		
	    SuccessResponse<PortPO> successResponse = new SuccessResponse<PortPO>(Response.INFO, GlobalKeyMessagesConstants.INSERT_SUCCESS, portPO);

	    return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@PutMapping("/")
	@PreAuthorize("hasAuthority('update-tables-accreditation-port-edit')")
	public EntityResponse<Response> update(@RequestBody @Valid PortPO portPO) {
		
	    portPO = getCrudFacade().update(portPO);
		
	    SuccessResponse<PortPO> successResponse = new SuccessResponse<PortPO>(Response.INFO, GlobalKeyMessagesConstants.UPDATE_SUCCESS, portPO);

	    return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

    @DeleteMapping("/{id}")
	@PreAuthorize("hasAuthority('delete-tables-accreditation-port-edit')")
	public Response delete(@PathVariable Long id) {

		getCrudFacade().delete(id);
		
		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
	}

	@GetMapping("/")
	@PreAuthorize("hasAuthority('search-tables-accreditation-port-list')")
	public EntityResponse<List<PortPO>> findAll() {
		return new EntityResponse<List<PortPO>>(getCrudFacade().findAll(), "*");
	}


}
