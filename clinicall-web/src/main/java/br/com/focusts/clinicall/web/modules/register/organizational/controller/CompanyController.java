
package br.com.focusts.clinicall.web.modules.register.organizational.controller;

import static br.com.focusts.clinicall.util.FileUtil.compressImage;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Base64;
import java.util.List;

import br.com.focusts.clinicall.service.modules.operational.billing.po.EartefactPO;
import br.com.focusts.clinicall.service.modules.register.organizational.to.CompanyTO;
import br.com.focusts.clinicall.service.modules.register.organizational.to.UploadCertiticateTO;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import br.com.focusts.clinicall.fw.enums.DescriptionEnumUtils;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.controller.AbstractController;
import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.organizational.facade.CompanyFacade;
import br.com.focusts.clinicall.service.modules.register.organizational.facade.CostCenterFacade;
import br.com.focusts.clinicall.service.modules.register.organizational.po.CompanyCostCenterPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.CompanyPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.CostCenterPO;
import br.com.focusts.clinicall.service.modules.system.enums.ImageTypeEnum;
import br.com.focusts.clinicall.service.modules.system.po.ImagePO;
import br.com.focusts.clinicall.util.DateUtil;

@RestController
@RequestMapping("/company")
public class CompanyController extends AbstractController<CompanyPO, java.lang.Long> {

	@Autowired
	private CompanyFacade companyFacade;

	@Autowired
	private CostCenterFacade costCenterFacade;

	@Override
	public CrudFacade<CompanyPO, java.lang.Long> getCrudFacade() {
		return companyFacade;
	}

	@GetMapping(value = "/mainCompany")
	public EntityResponse<List<CompanyPO>> findByCompanyParentIsNull() {

		return new EntityResponse<List<CompanyPO>>(companyFacade.findByCompanyParentIsNull(), "id", "name", "alias");
	}

	@GetMapping(value = "/unityCompany")
	public EntityResponse<List<CompanyPO>> findByCompanyParentIsNotNull() {

		return new EntityResponse<List<CompanyPO>>(companyFacade.findByCompanyParentIsNotNull(), "id", "name", "alias",
				"cnpj", "tradeName");
	}

	/*
	 * Type "U" = PEGAR SOMENTE UNIDADES
	 * Type "C" = PEGAR SOMENTE EMPRESAS
	 * TYPE "NULL" = PEGA AMBOS
	 */
	@PostMapping(value = "/search")
	public EntityResponse<Page<CompanyPO>> search(@RequestParam(name = "type", required = false) String type,
			@RequestBody PageSearchTO pageSearchTO) {
		Page<CompanyPO> page = null;

		if (type == null) {
			page = companyFacade
					.findByNameContainingOrTradeNameContainingOrCnesContainingOrAddress_addressContainingOrCompany_nameContaining(
							pageSearchTO);
		} else if (type.equals("U")) {
			page = companyFacade.findByParentIsNotNullOrUnityTrueAndNameContaining(pageSearchTO);
		} else if (type.equals("C")) {
			page = companyFacade.findByCompanyParentIsNullAndNameContaining(pageSearchTO);
		}

		return new EntityResponse<Page<CompanyPO>>(page, "*", "content", "content.companyParent.*", "content.id",
				"content.name", "content.cnpj", "content.alias",
				"content.cnes", "content.name", "content.tradeName", "content.");
	}

	@PostMapping(value = "/search/simpleList")
	public EntityResponse<Page<CompanyPO>> searchSimpleList(@RequestParam(name = "type", required = false) String type,
			@RequestBody PageSearchTO pageSearchTO) {
		Page<CompanyPO> page = null;

		if (type == null) {
			page = companyFacade
					.findByNameContainingOrTradeNameContainingOrCnesContainingOrAddress_addressContainingOrCompany_nameContaining(
							pageSearchTO);
		} else if (type.equals("U")) {
			page = companyFacade.findByParentIsNotNullOrUnityTrueAndNameContaining(pageSearchTO);
		} else if (type.equals("C")) {
			page = companyFacade.findByCompanyParentIsNullAndNameContaining(pageSearchTO);
		}

		return new EntityResponse<Page<CompanyPO>>(page, "*", "content.id", "content.name", "content.alias");
	}

	@GetMapping("/wAll/{id}")
	@ResponseBody
	@PreAuthorize("hasAuthority('find-register-organizational-company-edit')")
	public EntityResponse<CompanyPO> findByIdWithAll(@PathVariable Long id) {
		return new EntityResponse<CompanyPO>(getCrudFacade().findById(id), "*",
				"companyList", "companyList.id", "companyList.name",
				"costCenterList.*",
				"address",
				"registryType",
				"registryType.*",
				"address.country.*",
				"address.addressType.*", "address.*", "address.city.*", "address.city.state.id",
				"address.city.state.initials", "image.*",
				"contactList", "contactList.id", "contactList.type", "contactList.contactItem",
				"contactList.contactItem.id", "contactList.contactItem.description",
				"contactList.contactItem.email", "contactList.contactItem.phone", "contactList.contactItem.type",
				"contactList.contactItem.others", "contactList.contactItem.contactListParent",
				"contactList.contactItem.parentId",
				"restrictableComponents.id", "restrictableComponents.restrictionType", "companyParent",
				"companyParent.*", "shiftList.*", "!shiftList.company");
	}

	@GetMapping("/{id}")
	@ResponseBody
	// @PreAuthorize("hasAuthority('find-register-organizational-company-edit')")
	public EntityResponse<CompanyPO> findById(@PathVariable Long id) {
		return new EntityResponse<CompanyPO>(getCrudFacade().findById(id), "*", "costCenterList.*",
				"companyCostCenterList.*", "contactList", "contactList.id", "contactList.type",
				"contactList.contactItem",
				"contactList.contactItem.id", "contactList.contactItem.description",
				"contactList.contactItem.email", "contactList.contactItem.phone", "contactList.contactItem.type",
				"contactList.contactItem.others", "contactList.contactItem.contactListParent",
				"contactList.contactItem.parentId", "companyCostCenterList.costCenter.*");
	}

	@PostMapping("/")
	@PreAuthorize("hasAuthority('insert-register-organizational-company-edit')")
	public EntityResponse<Response> insert(@RequestBody CompanyPO companyPO) {

		companyPO = getCrudFacade().save(companyPO);

		SuccessResponse<CompanyPO> successResponse = new SuccessResponse<CompanyPO>(Response.INFO,
				GlobalKeyMessagesConstants.INSERT_SUCCESS, companyPO);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@PutMapping("/")
	@PreAuthorize("hasAuthority('update-register-organizational-company-edit')")
	public EntityResponse<Response> update(@RequestBody CompanyPO companyPO) {

		companyPO = getCrudFacade().update(companyPO);

		SuccessResponse<CompanyPO> successResponse = new SuccessResponse<CompanyPO>(Response.INFO,
				GlobalKeyMessagesConstants.UPDATE_SUCCESS, companyPO);

		return new EntityResponse<Response>(successResponse, "*", "data.*",
				"data.companyList", "data.companyList.id", "data.companyList.name",
				"data.costCenterList.*",
				"data.address", "data.address.addressType.*", "data.address.*", "data.address.city.*",
				"data.address.city.state.id",
				"address.city.state.initials", "image.*",
				"data.contactList", "data.contactList.id", "data.contactList.type", "data.contactList.contactItem",
				"data.contactList.contactItem.id", "data.contactList.contactItem.description",
				"data.contactList.contactItem.email", "data.contactList.contactItem.phone",
				"data.contactList.contactItem.type",
				"data.contactList.contactItem.others", "data.contactList.contactItem.contactListParent",
				"data.contactList.contactItem.parentId",
				"data.restrictableComponents.id", "data.restrictableComponents.restrictionType", "data.companyParent",
				"data.companyParent.*", "data.shiftList.*", "!data.shiftList.company");
	}

	@DeleteMapping("/{id}")
	@PreAuthorize("hasAuthority('delete-register-organizational-company-edit')")
	public Response delete(@PathVariable Long id) {

		getCrudFacade().delete(id);

		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
	}

	@GetMapping("/")
	@PreAuthorize("hasAuthority('search-register-organizational-company-list')")
	public EntityResponse<List<CompanyPO>> findAll() {
		return new EntityResponse<List<CompanyPO>>(getCrudFacade().findAll(), "id", "name");
	}

	@GetMapping("/costCenter/{companyId}/{costCenterId}")
	public EntityResponse<CompanyCostCenterPO> CompanyCostCenter(
			@PathVariable Long companyId, @PathVariable Long costCenterId) {
		return new EntityResponse<CompanyCostCenterPO>(
				companyFacade.findByCompanyCostCenterId(companyId, costCenterId));
	}

	@GetMapping("/costCenter/")
	public EntityResponse<List<CostCenterPO>> findAllCostCenter() {
		return new EntityResponse<List<CostCenterPO>>(costCenterFacade.findAll(), "*");
	}

	@PostMapping("/uploadLogo/{id}")
	@PreAuthorize("hasAuthority('insert-register-organizational-company-edit')")
	public EntityResponse<Response> handleFileUpload(@RequestParam("file") MultipartFile fileAgreement,
			@PathVariable Long id) throws IOException {

		CompanyPO companyPO = companyFacade.findById(id);

		ImagePO image = new ImagePO();

		String format = fileAgreement.getContentType().split("/")[1];

		String encodeImage = compressImage(fileAgreement.getBytes(), format);

		image.setContent(encodeImage);

		image.setDescription("logo empresa");

		ImageTypeEnum imageTypeEnum = DescriptionEnumUtils.fromValue(ImageTypeEnum.class, format.toUpperCase());
		image.setFormat(imageTypeEnum.getValue());
		image.setDated(LocalDateTime.now());

		companyPO.setImage(image);

		companyPO = companyFacade.update(companyPO);

		SuccessResponse<CompanyPO> successResponse = new SuccessResponse<CompanyPO>(Response.INFO,
				GlobalKeyMessagesConstants.UPDATE_SUCCESS, companyPO);

		return new EntityResponse<Response>(successResponse, "*", "data.*", "data.image.*");
	}

	@PutMapping("/{id}/image")
	@PreAuthorize("hasAuthority('update-register-organizational-company-edit')")
	public Response deleteImage(@PathVariable Long id) {
		CompanyPO company = companyFacade.findById(id);
		company.setImage(null);
		companyFacade.update(company);
		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
	}

	@GetMapping("companyCostCenter/{companyId}")
	public EntityResponse<List<CompanyCostCenterPO>> findByCompanyCostCenter(@PathVariable Long companyId) {
		return new EntityResponse<List<CompanyCostCenterPO>>(companyFacade.findCompanyCostCenterByCompany(companyId),
				"*", "costCenter.*", "company.*");
	}

	@PostMapping("upload-certification")
	public EntityResponse<Response> uploadCetification(
			@RequestParam("file") MultipartFile certiicationUploadFile,
			@RequestParam("params") MultipartFile paramsFile) throws Exception {

		byte[] certificadoBytes = certiicationUploadFile.getBytes();

		String paramsJson = new String(paramsFile.getBytes(), StandardCharsets.UTF_8);
		ObjectMapper objectMapper = new ObjectMapper();
		UploadCertiticateTO uploadCertiticateTO = objectMapper.readValue(paramsJson, UploadCertiticateTO.class);

		String certificadoBase64 = Base64.getEncoder().encodeToString(certificadoBytes);

		String password = uploadCertiticateTO.getPassword();
		Long companyId = uploadCertiticateTO.getCompanyId();

		companyFacade.uploadCetification(certificadoBase64, password, companyId);

		SuccessResponse<CompanyPO> successResponse = new SuccessResponse<CompanyPO>(Response.INFO,
				GlobalKeyMessagesConstants.UPDATE_SUCCESS);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@GetMapping("upload-certification/{companyId}")
	public EntityResponse<Response> findCetification(@PathVariable Long companyId) throws IOException {

		SuccessResponse<EartefactPO> successResponse = null;
		EartefactPO eartefactPO = companyFacade.findCetification(companyId);
		if (eartefactPO.getId() == null) {
			successResponse = new SuccessResponse<EartefactPO>(Response.INFO,
					GlobalKeyMessagesConstants.REGISTER_NOT_FOUND);
		} else {
			successResponse = new SuccessResponse<EartefactPO>(Response.INFO, GlobalKeyMessagesConstants.UPDATE_SUCCESS,
					eartefactPO);
		}
		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@GetMapping("hour-final-shift/{companyId}")
	public EntityResponse<Response> findMaxHourShiftCompany(@PathVariable Long companyId) {

		LocalTime hourFinal = companyFacade.findMaxHourShiftCompany(companyId);
		if (hourFinal == null) {
			hourFinal = LocalTime.of(20, 00);
		}
		SuccessResponse<String> successResponse = new SuccessResponse<String>(Response.INFO,
				GlobalKeyMessagesConstants.OPERATION_SUCCESS, DateUtil.formatLocalTime(hourFinal, "HH:mm"));
		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@DeleteMapping("certification/{companyId}")
	public EntityResponse<Response> deleteCetification(@PathVariable Long companyId) throws IOException {

		SuccessResponse<EartefactPO> successResponse = null;
		companyFacade.deletCetification(companyId);
		successResponse = new SuccessResponse<>(Response.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}
}
