
package br.com.focusts.clinicall.web.modules.tables.accreditation.controller;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.controller.AbstractController;
import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.accreditation.facade.CurrencyFacade;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.CurrencyPO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.facade.StandardTableItemFacade;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.StandardTableItemPO;

@RestController
@RequestMapping("/standardTableItem")
public class StandardTableItemController extends AbstractController<StandardTableItemPO, java.lang.Long> {

	@Autowired
	private StandardTableItemFacade standardTableItemFacade;

	@Autowired
	private CurrencyFacade currencyFacade;

	@Override
	public CrudFacade<StandardTableItemPO, java.lang.Long> getCrudFacade() {
		return standardTableItemFacade;
	}

	@PostMapping("/")
	@PreAuthorize("hasAuthority('insert-tables-accreditation-standard_table-edit')")
	public EntityResponse<Response> insert(@RequestBody @Valid StandardTableItemPO standardTableItemPO) {

		standardTableItemPO = getCrudFacade().save(standardTableItemPO);

		SuccessResponse<StandardTableItemPO> successResponse = new SuccessResponse<StandardTableItemPO>(Response.INFO,
				GlobalKeyMessagesConstants.INSERT_SUCCESS, standardTableItemPO);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@PutMapping("/")
	@PreAuthorize("hasAuthority('update-tables-accreditation-standard_table-edit')")
	public EntityResponse<Response> update(@RequestBody @Valid StandardTableItemPO standardTableItemPO) {

		standardTableItemPO = getCrudFacade().update(standardTableItemPO);

		SuccessResponse<StandardTableItemPO> successResponse = new SuccessResponse<StandardTableItemPO>(Response.INFO,
				GlobalKeyMessagesConstants.UPDATE_SUCCESS, standardTableItemPO);

		return new EntityResponse<Response>(successResponse, "*", "data.*");
	}

	@PostMapping(value = "/search")
	public EntityResponse<Page<StandardTableItemPO>> search(@RequestBody PageSearchTO pageSearchTO) {

		Page<StandardTableItemPO> page = standardTableItemFacade.findByNameContaining(pageSearchTO);

		return new EntityResponse<Page<StandardTableItemPO>>(page, "*", "content.*");
	}

	@DeleteMapping("/{id}")
	@PreAuthorize("hasAuthority('delete-tables-accreditation-standard_table-edit')")
	public Response delete(@PathVariable Long id) {

		getCrudFacade().delete(id);

		return new SuccessResponse<>(SuccessResponse.INFO, GlobalKeyMessagesConstants.DELETE_SUCCESS);
	}

	@GetMapping("/wAll/{id}")
	@ResponseBody
	public EntityResponse<StandardTableItemPO> findByIdWithAll(@PathVariable java.lang.Long id) {
		return new EntityResponse<StandardTableItemPO>(getCrudFacade().findById(id), "*", "currency.*");
	}

	@PostMapping(value = "/search/standardTable/{standardTableId}")
	public EntityResponse<Page<StandardTableItemPO>> searchByStandardTableId(@PathVariable Long standardTableId,
			@RequestBody PageSearchTO pageSearchTO) {

		Page<StandardTableItemPO> page = standardTableItemFacade
				.findByStandardTableIdAndNameContainingAndCodeContaining(standardTableId, pageSearchTO);

		return new EntityResponse<Page<StandardTableItemPO>>(page, "*", "content.*", "content.currency.*",
				"!content.standardTableItemList");
	}

	@PostMapping(value = "/search/standardTable/{standardTableId}/range-code/{codeMin}/{codeMax}")
	public EntityResponse<Page<StandardTableItemPO>> searchByStandardTableIdRangeCode(
			@PathVariable Long standardTableId, @RequestBody PageSearchTO pageSearchTO, @PathVariable String codeMin,
			@PathVariable String codeMax) {

		Page<StandardTableItemPO> page = standardTableItemFacade
				.findByStandardTableIdARange(standardTableId, pageSearchTO, Long.parseLong(codeMin),
						Long.parseLong(codeMax));

		return new EntityResponse<Page<StandardTableItemPO>>(page, "*", "content.*", "content.currency.*",
				"!content.standardTableItemList");
	}

	@GetMapping("/currency/")
	public EntityResponse<List<CurrencyPO>> findAllCurrency() {
		return new EntityResponse<List<CurrencyPO>>(currencyFacade.findAll(), "*");
	}

}
