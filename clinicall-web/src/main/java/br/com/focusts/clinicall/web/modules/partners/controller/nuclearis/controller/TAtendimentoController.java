package br.com.focusts.clinicall.web.modules.partners.controller.nuclearis.controller;

import br.com.focusts.clinicall.fw.constants.GlobalKeyMessagesConstants;
import br.com.focusts.clinicall.fw.response.EntityResponse;
import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.fw.response.SuccessResponse;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPO;
import br.com.focusts.clinicall.service.modules.operational.reception.service.OrderService;
import br.com.focusts.clinicall.service.modules.partners.service.PartnersNuclearisService;
import br.com.focusts.clinicall.service.modules.partners.to.nuclearisTO.*;
import br.com.focusts.clinicall.service.modules.reports.service.ReportService;
import br.com.focusts.clinicall.service.modules.reports.to.ReportResultTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/nuclearis/tatendimentocontroller")
public class TAtendimentoController {

    @Autowired
    private PartnersNuclearisService partnerNuclearisService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private ReportService reportService;

    @PostMapping("/guiatiss")
    public EntityResponse<Response> generateOrder(@RequestBody NuclearisAtendimentoTO atendimento) {

        OrderPO order = partnerNuclearisService.insertOrderNuclearis(atendimento);

        ReportResultTO reportResult = null;
        try {
            reportResult = reportService.generateGuideOrder(order.getId());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        NuclearisGuiaTO nuclearisGuia = new NuclearisGuiaTO();
        if (reportResult != null){
            nuclearisGuia.setArquivoPDF(reportResult.getPdf());
            nuclearisGuia.setNumeroGuiaSADT(order.getId().toString());
            nuclearisGuia.setNumeroOS(order.getId().toString());
            nuclearisGuia.setChaveAgendamentos(atendimento.getChaveAgendamento());
        }

        SuccessResponse<NuclearisGuiaTO> successResponse = new SuccessResponse<NuclearisGuiaTO>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, nuclearisGuia);
        return new EntityResponse<Response>(successResponse);
    }

    @PostMapping("/pagamento")
    public EntityResponse<Response> generageOrderParticular(@RequestBody NuclearisAtendimentoTO atendimento) {
        NuclearisVagaPacienteTO nuclearisVagaPaciente = partnerNuclearisService.insertPaymentNuclearis(atendimento);
        SuccessResponse<NuclearisVagaPacienteTO> successResponse = new SuccessResponse<NuclearisVagaPacienteTO>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, nuclearisVagaPaciente);
        return new EntityResponse<Response>(successResponse);
    }

    @DeleteMapping("/cancelarGuiaPorNumeroOs/{orderId}")
    public EntityResponse<Response> deleteGuide(@PathVariable Long orderId) {
        partnerNuclearisService.deleteGuide(orderId);
        SuccessResponse<Page<NuclearisSolicitanteTO>> successResponse = new SuccessResponse<Page<NuclearisSolicitanteTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS);
        return new EntityResponse<Response>(successResponse);
    }

    @DeleteMapping("/cancelarGuia/{cdAtendimentoNuclearis}")
    public EntityResponse<Response> deleteGuideByCdAtendimentoNuclearis(@PathVariable Long cdAtendimentoNuclearis) {
        partnerNuclearisService.deleteGuideByCdAtendimentoNuclearis(cdAtendimentoNuclearis);
        SuccessResponse<Page<NuclearisSolicitanteTO>> successResponse = new SuccessResponse<Page<NuclearisSolicitanteTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS);
        return new EntityResponse<Response>(successResponse);
    }

    @GetMapping(value={"/statusguia/{osNumber}"})
    public EntityResponse<Response> listStatusGuide(@PathVariable Long osNumber){
        List<StatusGuiaTO> statusGuide = partnerNuclearisService.listStatusGuideByOS(osNumber);
        SuccessResponse<List<StatusGuiaTO>> successResponse =
                new SuccessResponse<List<StatusGuiaTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, statusGuide);
        return new EntityResponse<Response>(successResponse);
    }

    @PostMapping(value={"/statusguia"})
    public EntityResponse<Response> updateStatusGuide(@RequestBody StatusGuiaTO statusGuiaTO){
        partnerNuclearisService.updateStatusGuideByOS(statusGuiaTO);
        SuccessResponse<List<StatusGuiaTO>> successResponse =
                new SuccessResponse<List<StatusGuiaTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS);
        return new EntityResponse<Response>(successResponse);
    }

    @GetMapping(value={"/guiatiss/{chaveAgendamento}"})
    public EntityResponse<Response> findGuideByChaveAgendamento(@PathVariable String chaveAgendamento){
        List<NuclearisInformacaoAtendimentoTO> infoGuide = partnerNuclearisService.findGuideByChaveAgendamento(chaveAgendamento);
        SuccessResponse<List<NuclearisInformacaoAtendimentoTO>> successResponse =
                new SuccessResponse<List<NuclearisInformacaoAtendimentoTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, infoGuide);
        return new EntityResponse<Response>(successResponse);
    }

    @GetMapping(value={"/visualizarGuia/{numOS}"})
    public EntityResponse<Response> findGuideByNumOs(@PathVariable Long numOS){
        NuclearisGuiaTO infoGuide = partnerNuclearisService.findGuideByNumOs(numOS);
        SuccessResponse<NuclearisGuiaTO> successResponse =
                new SuccessResponse<NuclearisGuiaTO>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, infoGuide);
        return new EntityResponse<Response>(successResponse);
    }

    @GetMapping(value={"/guiatiss"})
    public EntityResponse<Response> findReportGuide(@RequestParam Map<String, String> params){
        List<NuclearisRelatorioGuiaTO> infoGuide = partnerNuclearisService.findReportGuide(params);
        SuccessResponse<List<NuclearisRelatorioGuiaTO>> successResponse =
                new SuccessResponse<List<NuclearisRelatorioGuiaTO>>(Response.INFO, GlobalKeyMessagesConstants.OPERATION_SUCCESS, infoGuide);
        return new EntityResponse<Response>(successResponse);
    }
}
