/********************************************************************************
 * Motivation: Adiciona estrutura para chamada de pacientes ou senhas
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 * Created at: 2023-12-14
 ********************************************************************************/
CREATE TABLE IF NOT EXISTS schedule_call(
   schedule_call_id   INT NOT NULL AUTO_INCREMENT COMMENT 'ID Chamada',
   company_id         INT NOT NULL COMMENT 'Id Empresa',
   environment_id     INT NOT NULL COMMENT 'Id do Ambiente',
   schedule_id        INT COMMENT 'ID da Agenda',
   `password`         VARCHAR(10) COMMENT 'Senha Incremental',
   `status`           VARCHAR(1) NOT NULL COMMENT 'Status do Chamado',
   created_at         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data e Hora  de Criação do Registro - Interno',
   created_by         INT NOT NULL COMMENT 'Usuário de Criação do Registro - Interno',
   updated_at         TIMESTAMP COMMENT 'Data e Hora de Atualização do Registro - Interno',
   updated_by         INT COMMENT 'Usuário de Atualização do Registro - Interno',
   PRIMARY KEY (schedule_call_id),
   CONSTRAINT fk_schedule_call__environment FOREIGN KEY (environment_id) REFERENCES environment(environment_id),
   CONSTRAINT fk_schedule_call__company FOREIGN KEY (company_id) REFERENCES company(company_id),
   CONSTRAINT fk_schedule_call__schedule FOREIGN KEY (schedule_id) REFERENCES schedule(schedule_id),
   CONSTRAINT ck_schedule_call__status CHECK (`status` IN ('F', 'A')),
   CONSTRAINT ck_schedule_call__password__schedule_id CHECK ((schedule_id IS NOT NULL AND `password` IS NULL) OR (schedule_id IS NULL AND `password` IS NOT NULL ))
) COMMENT 'Chamada da Agenda';