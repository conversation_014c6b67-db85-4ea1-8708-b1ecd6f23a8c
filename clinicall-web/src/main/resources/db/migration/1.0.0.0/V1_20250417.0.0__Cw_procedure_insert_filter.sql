/****************************
 * Motivation: Update insert filter
 * Author: <PERSON><PERSON> <<EMAIL>>
 * Created at: 2025-04-15 16:20
 ****************************/
DROP PROCEDURE IF EXISTS InsertFilter;
DELIMITER //
CREATE DEFINER= 'user_clinicall' PROCEDURE InsertFilter(
    IN p_key VARCHAR(255),
    IN p_type CHAR(1),
    IN p_alias VARCHAR(255),
    IN p_value VARCHAR(255),
    IN p_mandatory INT,
    IN p_scope VARCHAR(255),
    IN p_component VARCHAR(255)
)
BEGIN
    INSERT INTO `filter` (filter_id, `key`, `type`, alias, value, mandatory, `scope`, component)
    SELECT NULL, p_key, p_type, p_alias, p_value, p_mandatory, p_scope, p_component
    FROM DUAL
    WHERE NOT EXISTS (SELECT 1 FROM `filter` WHERE `filter`.component = p_component  AND `filter`.`key` = p_key);

END //
DELIMITER ;