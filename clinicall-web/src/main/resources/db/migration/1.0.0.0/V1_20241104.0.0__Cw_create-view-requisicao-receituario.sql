/********************************************************************************
 * Motivation: Atualização das Funções e Views - Flyway (https://flywaydb.org/)
 * Author: <PERSON> <<EMAIL>>
 * Created at: 2023-09-15 11:10
 ********************************************************************************/
DROP VIEW IF EXISTS `vw_editor_requisicao`;
CREATE OR REPLACE DEFINER=`user_clinicall` VIEW `vw_editor_requisicao` AS
	SELECT 
		`request`.`request_id`   AS `#{requisicao.codigo}`,
		COALESCE (`request`.`note`, " ") AS `#{requisicao.observacaoSolic}`,
		COALESCE (`request`.`request` , " ") AS `#{requisicao.requisicao}`,
		GROUP_CONCAT(DISTINCT `cid`.`code` ORDER BY `cid`.`code` SEPARATOR ', ') AS `#{requisicao.cid}`
	FROM `request`
	LEFT JOIN `request_diagnosis` ON `request_diagnosis`.request_id  = `request`.`request_id`
	LEFT JOIN `cid` ON `cid`.`cid_id` = `request_diagnosis`.`cid_id`
GROUP BY 1;
	
	
	
