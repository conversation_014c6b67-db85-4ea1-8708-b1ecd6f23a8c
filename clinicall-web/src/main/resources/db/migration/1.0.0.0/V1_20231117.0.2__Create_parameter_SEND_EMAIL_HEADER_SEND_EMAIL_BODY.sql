/********************************************************************************
 * Motivation: Parametros para Cabeçalho e Corpo do Email;
 * Author: <PERSON><PERSON>  <<EMAIL>>
 * Created at: 2023-11-17 15:23
 ********************************************************************************/

INSERT INTO `parameter`
(name, `type`, description, context, `group`, value, send_front_end, created_at, created_by, updated_at, updated_by, info)
SELECT 'SEND_EMAIL_HEADER', 'IT', 'Texto no Cabeçalho no email', 'SYSTEM', 'EMAIL', '', 1, '2023-11-06 19:52:57', NULL,
'2023-11-17 09:52:02', 1, '<p>Destinado à rotina de envio de guias para o e-mail do especialista cadastrado no parâmetro do paciente.</p>'
WHERE NOT EXISTS (SELECT 1 FROM `parameter` WHERE `parameter`.name = 'SEND_EMAIL_HEADER');
INSERT INTO `parameter`
(name, `type`, description, context, `group`, value, send_front_end, created_at, created_by, updated_at, updated_by, info)
select 'SEND_EMAIL_BODY', 'IT', 'Texto para o corpo do email', 'SYSTEM', 'EMAIL', '', 1, '2023-11-06 19:52:57', NULL,
'2023-11-14 21:04:41', 1, '<p>Destinado à rotina de envio de guias para o e-mail do especialista cadastrado no parâmetro do paciente.</p'
WHERE NOT EXISTS (SELECT 1 FROM `parameter` WHERE `parameter`.name = 'SEND_EMAIL_BODY');