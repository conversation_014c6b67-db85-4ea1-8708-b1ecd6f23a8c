
 DROP PROCEDURE IF EXISTS InsertRestrictableComponent;
DELIMITER //
CREATE DEFINER= 'user_clinicall' PROCEDURE InsertRestrictableComponent(
    IN p_type VARCHAR(255),
    IN p_name VARCHAR(255),
    IN p_description VARCHAR(255),
    IN p_view_code VA<PERSON>HAR(255),
    IN p_code VARCHAR(255)
)
BEGIN
    INSERT INTO restrictable_component
        (type, name, description, view_id, code)
    SELECT p_type, 
        p_name, 
        p_description, 
        (SELECT `view`.view_id FROM `view` WHERE `view`.code = p_view_code), 
        p_code
    FROM DUAL
   WHERE NOT EXISTS (SELECT 1 FROM restrictable_component rc WHERE rc.code = p_code);
  
END //
DELIMITER ;


