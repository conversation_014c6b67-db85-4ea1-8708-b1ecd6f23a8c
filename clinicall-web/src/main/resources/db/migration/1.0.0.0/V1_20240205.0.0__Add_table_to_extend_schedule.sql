/********************************************************************************
 * Motivation: <PERSON><PERSON><PERSON><PERSON> tabela para estender agendamento
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 * Created at: 2024-02-01
 * Released at:2024-02-05
 ********************************************************************************/
CREATE TABLE IF NOT EXISTS `schedule_custom`(
   `schedule_custom_id` INT NOT NULL AUTO_INCREMENT COMMENT 'Id da Customização da Agenda',
   `schedule_id`        INT COMMENT 'ID da Agenda',
   `key`                VARCHAR(100) NOT NULL COMMENT 'Chave para Customização',
   `value`              VARCHAR(100) NOT NULL COMMENT '<PERSON>or para Customização',
   `created_at`         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data e Hora  de Criação do Registro - Interno',
   `created_by`         INT COMMENT 'Usuário de Criação do Registro - Interno',
   `updated_at`         TIMESTAMP COMMENT 'Data e Hora de Atualização do Registro - Interno',
   `updated_by`         INT COMMENT 'Usuário de Atualização do Registro - Interno',
   PRIMARY KEY (`schedule_custom_id`),
   CONSTRAINT `fk_schedule_custom__schedule` FOREIGN KEY (`schedule_id`) REFERENCES `schedule` (`schedule_id`)
) COMMENT 'Tabela para estentder Agendamento';
