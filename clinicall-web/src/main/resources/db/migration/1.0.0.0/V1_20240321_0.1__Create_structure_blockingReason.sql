/********************************************************************************
 * Motivation: Adicionar estrutura de dados para Bloqueios de Notas
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 * Created at: 2024-03-19
 * Released at:2024-03-23
 ********************************************************************************/
 CREATE TABLE IF NOT EXISTS `block`(
   `block_id`           INT NOT NULL AUTO_INCREMENT COMMENT 'ID Bloqueio',
   `user_id`            INT NOT NULL COMMENT 'Id Usuário',
   `name`               VARCHAR(250) NOT NULL COMMENT 'Des<PERSON>ri<PERSON> do bloqueio',
   `date`               DATE NOT NULL COMMENT 'Data de bloqueio',
   `hour`               TIME NOT NULL COMMENT 'Horario de bloqueio',
   `note`               text NOT NULL COMMENT 'Observação do cancelamento',
   `created_at`         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data e Hora  de Criação do Registro - Interno',
   `created_by`         INT COMMENT 'Usuário de Criação do Registro - Interno',
   `updated_at`         TIMESTAMP COMMENT 'Data e Hora de Atualização do Registro - Interno',
   `updated_by`         INT COMMENT 'Usuário de Atualização do Registro - Interno',
   PRIMARY KEY (`block_id`),
   CONSTRAINT `fk_block__user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`)
) COMMENT 'Dados do Bloqueio';

CREATE TABLE IF NOT EXISTS `schedule_block`(
   `schedule_block_id`  INT NOT NULL AUTO_INCREMENT COMMENT 'ID Bloqueio de Agenda',
   `block_id`           INT NOT NULL COMMENT 'ID Bloqueio',
   `schedule_id`        INT NOT NULL COMMENT 'ID da Agenda',
   `created_at`         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data e Hora  de Criação do Registro - Interno',
   `created_by`         INT COMMENT 'Usuário de Criação do Registro - Interno',
   `updated_at`         TIMESTAMP COMMENT 'Data e Hora de Atualização do Registro - Interno',
   `updated_by`         INT COMMENT 'Usuário de Atualização do Registro - Interno',
   PRIMARY KEY (`schedule_block_id`),
   CONSTRAINT `fk_schedule_block__block` FOREIGN KEY (`block_id`) REFERENCES `block` (`block_id`),
   CONSTRAINT `fk_schedule_block__schedule` FOREIGN KEY (`schedule_id`) REFERENCES `schedule` (`schedule_id`)
) COMMENT 'Bloqueios de Agenda';