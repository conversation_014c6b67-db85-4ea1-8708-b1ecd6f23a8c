/********************************************************************************
 * Motivation: Adicionado estrutura para cadastro de especficações do contrato
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 * Created at: 2023-10-04 16:30
 ********************************************************************************/
CREATE TABLE IF NOT EXISTS agreement_spec(
	agreement_spec_id  INT NOT NULL AUTO_INCREMENT COMMENT 'Id Especificações do Contrato',
	agreement_id       INT NOT NULL COMMENT 'Id Contrato',
	firm_id            INT NOT NULL COMMENT 'Id da Firma',
	guide_number       VARCHAR(20) COMMENT 'Nº Maquineta para Guias de Consulta',
	sadt_number        VARCHAR(20) COMMENT 'Nº Maquineta para Guias SADT',
	created_at         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data e Hora  de Criação do Registro - Interno',
	created_by         INT COMMENT 'Usuário de Criação do Registro - Interno',
	updated_at         TIMESTAMP COMMENT 'Data e Hora de Atualização do Registro - Interno',
	updated_by         INT COMMENT 'Usuário de Atualização do Registro - Interno',
	PRIMARY KEY (agreement_spec_id),
	UNIQUE KEY ak_aggrement_spec__aggrement_id__firm_id(agreement_id, firm_id),
	CONSTRAINT fk_agreement_spec__firm FOREIGN KEY (firm_id) REFERENCES firm(firm_id),
	CONSTRAINT fk_agreement_spec__agreement FOREIGN KEY (agreement_id) REFERENCES agreement(agreement_id)
) COMMENT 'Especificações do Contrato';