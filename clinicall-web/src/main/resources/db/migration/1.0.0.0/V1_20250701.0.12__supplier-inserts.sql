
/*******************************************************************************
 * Motivation: Insert Permission 
 * Author: Gerador Clinic all
 * Created at: 
 ******************************************************************************/


-- ================================== MODULE ======================================
INSERT
	INTO
	module (name,
	parent_id,
	code,
	created_at,
	created_by)
SELECT
	'Financeiro',
	(SELECT module.module_id FROM module where module.code = "register"),
	'register-financial',
	'2022-06-09 13:41:38',
	1
WHERE
	NOT EXISTS (
	SELECT
		1
	FROM
		module
	WHERE
		code = 'register-financial'
);
-- ================================== VIEW ======================================
CALL InsertView("register-financial", "Fornecedor", "C", "/cadastro/financeiro/fornecedor", "register-financial-supplier", "Fornecedor");

-- ==============================================================================   

-- =============================== TRANSACTION ==================================

-- CRUD LIST - SEARCH REGISTERS
CALL InsertTransaction(
  "register-financial-supplier",
  "search-register-financial-supplier-list",
  "Listar Fornecedor",
  "Listar Fornecedor",
  1
);

-- CRUD EDIT - INSERT REGISTER
CALL InsertTransaction(
  "register-financial-supplier",
  "insert-register-financial-supplier-edit",
  "Incluir Fornecedor",
  "Incluir Fornecedor",    
  NULL
);

-- CRUD EDIT - UPDATE REGISTER
CALL InsertTransaction(
  "register-financial-supplier",
  "update-register-financial-supplier-edit",
  "Atualizar Fornecedor",
  "Atualizar Fornecedor",
  null
);

-- CRUD EDIT - DELETE REGISTER
CALL InsertTransaction(
  "register-financial-supplier",
  "delete-register-financial-supplier-edit",
  "Deletar Fornecedor",
  "Deletar Fornecedor",
  null
);


-- CRUD EDIT - FIND REGISTER
CALL InsertTransaction(
  "register-financial-supplier",
  "find-register-financial-supplier-edit",
  "Visualizar Fornecedor",
  "Visualizar Fornecedor",
  null
);
-- ==============================================================================

-- ============================ RESTRICTABLE COMPONENT ==========================

   

/*******************************************************************************
 * Motivation: Insert Restrictable Component 
 * Author: Gerador Clinic all
 * Created at: 
 ******************************************************************************/

CALL InsertRestrictableComponent(
  "IT",
  "Razão Social",
  "Campo Razão Social",
  (SELECT view_id FROM view WHERE code = "register-financial-supplier"),
  "register-financial-supplier-name-edit-form_it"
);

/*******************************************************************************
 * Motivation: Insert Restrictable Component 
 * Author: Gerador Clinic all
 * Created at: 
 ******************************************************************************/

CALL InsertRestrictableComponent(
  "IT",
  "Nome Fantasia",
  "Campo Nome Fantasia",
  (SELECT view_id FROM view WHERE code = "register-financial-supplier"),
  "register-financial-supplier-alias-edit-form_it"
);

-- ==============================================================================
