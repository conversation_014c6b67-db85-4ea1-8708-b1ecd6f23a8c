/********************************************************************************
 * Motivation: Adicionar estrutura de dados para Recibo
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 * Created at: 2024-03-18
 * Released at: 2024-03-19
 ********************************************************************************/
CREATE TABLE IF NOT EXISTS `receipt`(
   `receipt_id`         INT NOT NULL AUTO_INCREMENT COMMENT 'ID Recibo',
   `order_pay_id`       INT COMMENT 'Id do Pagamento da Ordem de Venda',
   `order_id`           INT COMMENT 'Id da Ordem de Serviço (Conta Hospitalar)',
   `company_id`         INT COMMENT 'Id Empresa',
   `performer_id`       INT COMMENT 'Id do Executante',
   `patient_id`         INT COMMENT 'Id do Paciente',
   `firm_id`            INT COMMENT 'Id da Firma',
   `person_id`          INT COMMENT 'Id da Pessoa',
   `number`             INT NOT NULL COMMENT 'Número do Recibo',
   `dated`              DATE NOT NULL COMMENT 'Data de Pagamento',
   `value`              DOUBLE NOT NULL COMMENT 'Valor do Recibo',
   `reference`          VARCHAR(100) NOT NULL COMMENT 'Referência',
   `created_at`         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data e Hora  de Criação do Registro - Interno',
   `created_by`         INT COMMENT 'Usuário de Criação do Registro - Interno',
   `updated_at`         TIMESTAMP COMMENT 'Data e Hora de Atualização do Registro - Interno',
   `updated_by`         INT COMMENT 'Usuário de Atualização do Registro - Interno',
   PRIMARY KEY (`receipt_id`),
   UNIQUE KEY `uk_receipt__company_id__number`(`company_id`, `number`),
   CONSTRAINT `fk_receipt__company` FOREIGN KEY (`company_id`) REFERENCES `company` (`company_id`),
   CONSTRAINT `fk_receipt__firm` FOREIGN KEY (`firm_id`) REFERENCES `firm` (`firm_id`),
   CONSTRAINT `fk_receipt__order` FOREIGN KEY (`order_id`) REFERENCES `order` (`order_id`),
   CONSTRAINT `fk_receipt__order_pay` FOREIGN KEY (`order_pay_id`) REFERENCES `order_pay` (`order_pay_id`),
   CONSTRAINT `fk_receipt__patient` FOREIGN KEY (`patient_id`) REFERENCES `patient` (`patient_id`),
   CONSTRAINT `fk_receipt__performer` FOREIGN KEY (`performer_id`) REFERENCES `performer` (`performer_id`),
   CONSTRAINT `fk_receipt__person` FOREIGN KEY (`person_id`) REFERENCES `person` (`person_id`)
) COMMENT 'Recibo';
