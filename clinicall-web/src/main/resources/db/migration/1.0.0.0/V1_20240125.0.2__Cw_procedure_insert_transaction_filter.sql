DROP PROCEDURE IF EXISTS InsertTransactionFilter;

DELIMITER //

CREATE DEFINER= 'user_clinicall' PROCEDURE InsertTransactionFilter(
    IN p_code VARCHAR(255),
    IN p_component VARCHAR(255),
    IN p_param_name VARCHAR(255)
)
BEGIN
    DECLARE transaction_id_val INT;
    DECLARE filter_id_val INT;

    -- Obter transaction_id
    SELECT transaction_id INTO transaction_id_val
    FROM `transaction`
    WHERE code = p_code;

    -- Obter filter_id
    SELECT filter_id INTO filter_id_val
    FROM `filter`
    WHERE component = p_component;

    -- Inserir apenas se não existir
    INSERT INTO transaction_filter (transaction_filter_id, transaction_id, filter_id, `order`, param_name)
    SELECT NULL, transaction_id_val, filter_id_val, 1, p_param_name
    FROM dual
    WHERE NOT EXISTS (
        SELECT 1
        FROM `transaction_filter`
        LEFT JOIN `transaction` ON `transaction`.transaction_id = `transaction_filter`.transaction_id
        LEFT JOIN `filter` ON `filter`.filter_id = `transaction_filter`.filter_id
        WHERE `filter`.component = p_component
        AND `transaction`.code = p_code
    );
END // 

DELIMITER ;