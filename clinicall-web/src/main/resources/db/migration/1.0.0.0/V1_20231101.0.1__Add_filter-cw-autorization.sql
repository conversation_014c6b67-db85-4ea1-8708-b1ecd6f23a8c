/********************************************************************************
 * Motivation: Filtro para o lançamento em lote
 * Author: <PERSON><PERSON>  <<EMAIL>>
 * Created at: 2023-11-01 21:06
 ********************************************************************************/


INSERT INTO `filter`
(`key`, `type`, value, mandatory, `scope`, created_at, created_by, updated_at, updated_by, component)
select 'Periodo', 'P', NULL, 1, 'authorization', '2022-06-09 13:41:29', NULL, NULL, NULL, 'cw-authorization'
WHERE
NOT  EXISTS (SELECT 1 FROM `filter` WHERE `filter`.scope = 'authorization' AND `filter`.key  = 'Periodo');

INSERT INTO `filter`
(`key`, `type`, value, mandatory, `scope`, created_at, created_by, updated_at, updated_by, component)
SELECT 'Status Authorization', 'S', NULL, 1, 'authorization', '2022-06-09 13:41:29', NULL, NULL, NULL, 'cw-authorization'
WHERE
NOT  EXISTS (SELECT 1 FROM `filter` WHERE `filter`.scope = 'authorization' AND `filter`.key  = 'Status Authorization');


INSERT INTO `filter`
(`key`, `type`, value, mandatory, `scope`, created_at, created_by, updated_at, updated_by, component)
SELECT 'BATCH_ENTRY', 'M', NULL, 1, 'authorization', '2022-06-09 13:41:29', NULL, NULL, NULL, 'cw-authorization'
WHERE
NOT  EXISTS (SELECT 1 FROM `filter` WHERE `filter`.scope = 'authorization' AND `filter`.key  = 'BATCH_ENTRY');

INSERT INTO `filter`
(`key`, `type`, value, mandatory, `scope`, created_at, created_by, updated_at, updated_by, component)
SELECT 'Convênio', 'M', NULL, 1, 'authorization', '2022-07-06 20:29:43', NULL, NULL, NULL, 'cw-authorization'
WHERE
NOT  EXISTS (SELECT 1 FROM `filter` WHERE `filter`.scope = 'authorization' AND `filter`.key  = 'Convênio');