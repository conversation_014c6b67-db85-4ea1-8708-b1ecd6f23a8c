/****************************
 * Motivation: Novo campo para parent_id para múltiplos procedimentos na agenda
 * Author: <PERSON><PERSON> <<EMAIL>>
 * Created at: 2025-02-06 16:12
 ****************************/


CALL flyway_add_column(
		'schedule',		-- cw_table_name  = Tabela
		'parent_id', 		-- cw_column_name = Nome do Campo
		'INT', 		-- cw_column_type = Tipo do Dado
		CONCAT(			-- cw_additional  = Dados adicionais ao novo atributo
			' COMMENT ',
	        '''', 'ID da Agenda Parente', '''',
	        ' AFTER `schedule_id`'),
	@msg_status		-- cw_status_msg = Mensagem de Retorno da operação
    );

CALL flyway_add_column(
		'schedule_aud',		-- cw_table_name  = Tabela
		'parent_id', 		-- cw_column_name = Nome do Campo
		'INT', 		-- cw_column_type = Tipo do Dado
		CONCAT(			-- cw_additional  = Dados adicionais ao novo atributo
			'COMMENT ',
	        '''', 'ID da Agenda Parente', '''',
	        ' AFTER `schedule_id`'),
	@msg_status		-- cw_status_msg = Mensagem de Retorno da operação
    );

CALL flyway_add_fk('schedule',
					'fk_schedule_self',
					'parent_id',
					'schedule',
					'schedule_id',
					null,
					null,
		@msg_status);