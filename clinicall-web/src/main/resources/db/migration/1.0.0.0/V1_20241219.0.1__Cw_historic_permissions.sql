
/*******************************************************************************
 * Motivation: Insert Permission
 * Author: Gerador Clinic all
 * Created at:
 ******************************************************************************/

-- ================================== VIEW ======================================
CALL InsertView("tables-financial", "Histórico", "C", "/tabelas/financeiro/historico", "tables-financial-historic", "Histórico");

-- ==============================================================================

-- =============================== TRANSACTION ==================================

-- CRUD LIST - SEARCH REGISTERS
CALL InsertTransaction(
  "tables-financial-historic",
  "search-tables-financial-historic-list",
  "Listar Histórico",
  "Listar Histórico",
  1
);

-- CRUD EDIT - INSERT REGISTER
CALL InsertTransaction(
  "tables-financial-historic",
  "insert-tables-financial-historic-edit",
  "Incluir Histórico",
  "Incluir Histórico",
  NULL
);

-- CRUD EDIT - UPDATE REGISTER
CALL InsertTransaction(
  "tables-financial-historic",
  "update-tables-financial-historic-edit",
  "Atualizar Histórico",
  "Atualizar Histórico",
  null
);

-- CRUD EDIT - DELETE REGISTER
CALL InsertTransaction(
  "tables-financial-historic",
  "delete-tables-financial-historic-edit",
  "Deletar Histórico",
  "Deletar Histórico",
  null
);


-- CRUD EDIT - FIND REGISTER
CALL InsertTransaction(
  "tables-financial-historic",
  "find-tables-financial-historic-edit",
  "Visualizar Histórico",
  "Visualizar Histórico",
  null
);
-- ==============================================================================

-- ============================ RESTRICTABLE COMPONENT ==========================



/*******************************************************************************
 * Motivation: Insert Restrictable Component
 * Author: Gerador Clinic all
 * Created at:
 ******************************************************************************/

CALL InsertRestrictableComponent(
  "IT",
  " Histórico",
  "Campo  Histórico",
  (SELECT view_id FROM view WHERE code = "tables-financial-historic"),
  "tables-financial-historic-name-edit-form_it"
);

-- ==============================================================================
