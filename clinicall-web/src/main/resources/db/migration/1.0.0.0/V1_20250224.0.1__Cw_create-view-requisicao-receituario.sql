/********************************************************************************
 * Motivation: Atualização das Funções e Views - Flyway (https://flywaydb.org/)
 * Author: <PERSON> <<EMAIL>>
 * Created at: 2023-09-15 11:10
 ********************************************************************************/

DROP FUNCTION IF EXISTS `date_to_text`;
DELIMITER //
CREATE DEFINER=`user_clinicall` 
-- IF NOT EXISTS -- MySQL 8.0.29
FUNCTION `date_to_text`() 
RETURNS varchar(30) 
BEGIN
	DECLARE month_name VARCHAR(20);
	SET month_name = CASE EXTRACT(MONTH FROM CURRENT_DATE) 
						WHEN  1 THEN 'Janeiro'
						WHEN  2 THEN 'Fevereiro'
						WHEN  3 THEN 'Março'
						WHEN  4 THEN 'Abril'
						WHEN  5 THEN 'Maio'
						WHEN  6 THEN 'Junho'
						WHEN  7 THEN 'Julho'
						WHEN  8 THEN 'Agosto'
						WHEN  9 THEN 'Setembro'
						WHEN 10 THEN 'Outubro'
						WHEN 11 THEN 'Novembro'
						WHEN 12 THEN 'Dezembro'
					 END;
	RETURN 
			CONCAT(CAST(EXTRACT(DAY FROM CURRENT_DATE) AS CHAR), 
			' de ', 
            month_name,
			' de ', 
            CAST(EXTRACT(YEAR FROM CURRENT_DATE) AS CHAR));
END//
DELIMITER ;
--
DROP VIEW IF EXISTS `vw_editor_grupo`;
CREATE OR REPLACE DEFINER=`user_clinicall` VIEW `vw_editor_grupo`
AS
	SELECT `schema_views`.`TABLE_NAME` AS `grupo`,
       REPLACE(`schema_views`.`TABLE_NAME`, 'vw_editor_', '') AS `apelido`,
       CAST(now() AS datetime) AS `created_at`,
       CAST(now() AS datetime) AS `updated_at`,
       CAST(1 AS signed) AS `created_by`,
       CAST(1 AS signed) AS `updated_by`
	FROM `information_schema`.`VIEWS` `schema_views`
	WHERE `schema_views`.`TABLE_SCHEMA` = database()
		AND `schema_views`.`TABLE_NAME` LIKE 'vw_editor%'
		AND `schema_views`.`TABLE_NAME` NOT IN ('vw_editor_grupo',
                                                'vw_editor_variavel');
--
DROP VIEW IF EXISTS `vw_editor_medico`;
CREATE OR REPLACE DEFINER=`user_clinicall` VIEW `vw_editor_medico` AS
    SELECT 
        `performer`.`performer_id` AS `#{medico.codigo}`,
        `person`.`name` AS `#{medico.nome}`,
        `person`.`alias` AS `#{medico.apelido}`,
        `person`.`social_name` AS `#{medico.nome social}`,
        `professional`.`council_number` AS `#{medico.número do conselho}`,
        `council`.`initials` AS `#{medico.sigla do conselho}`,
        `state`.`initials` AS `#{medico.uf do conselho}`,
        `performer`.`rqe_number` AS `#{medico.rqe}`,
        `speciality`.`name` AS `#{medico.especialidade}`
    FROM `professional`
	JOIN `performer` ON `performer`.`professional_id` = `professional`.`professional_id`
	JOIN `council` ON `council`.`council_id` = `professional`.`council_id`
	JOIN `person` ON `person`.`person_id` = `professional`.`person_id`
	LEFT JOIN `state` ON `state`.`state_id` = `professional`.`state_id`
    LEFT JOIN `speciality` ON `speciality`.`speciality_id` = `professional`.`speciality_id`
    ;
--
DROP VIEW IF EXISTS `vw_editor_paciente`;
CREATE OR REPLACE DEFINER=`user_clinicall` VIEW `vw_editor_paciente` AS
    SELECT 
        `patient`.`patient_id` AS `#{paciente.codigo}`,
        `patient`.`medical_record` AS `#{paciente.prontuario}`,
        `person`.`name` AS `#{paciente.nome}`,
        `person`.`alias` AS `#{paciente.apelido}`,
        `person`.`social_name` AS `#{paciente.nome social}`,
        `person`.`cpf` AS `#{paciente.cpf}`,
        `person`.`cns` AS `#{paciente.cns}`,
        `address_type`.`code` AS `#{paciente.endereco-tipo}`,
        `address`.`address` AS `#{paciente.endereco-logradouro}`,
        `address`.`district` AS `#{paciente.endereco-bairro}`,
        `city`.`name` AS `#{paciente.endereco-cidade}`,
        `city`.`code` AS `#{paciente.endereco-ibge}`,
        `address`.`zipcode` AS `#{paciente.endereco-cep}`,
        `address`.`addon` AS `#{paciente.endereco-complemento}`,
        `address`.`number` AS `#{paciente.endereco-numero}`,
        DATE_FORMAT(`patient`.`dated`, '%d/%m/%Y') AS `#{paciente.data-cadastro}`,
        DATE_FORMAT(`person`.`birthday`, '%d/%m/%Y') AS `#{paciente.data-nascimento}`,
        DATE_FORMAT(`patient`.`validity`, '%d/%m/%Y') AS `#{paciente.validade-carteira}`,
        CONCAT(FLOOR(DATEDIFF(CURDATE(), `person`.`birthday`) / 365.25), 
			' anos e ',
				FLOOR((DATEDIFF(CURDATE(), `person`.`birthday`) % 365.25) / 30.44),
					' meses') AS `#{paciente.idade}`,        
        `contact_list_phone`.`phone` AS `#{paciente.telefone}`,
        `contact_list_email`.`email` AS `#{paciente.email}`,
        `insurance`.`name` AS `#{paciente.convenio}`,
        `gender`.`name` AS `#{paciente.genero}`,
        `schooling`.`name` AS `#{paciente.escolaridade}`,
        `civil_status`.`name` AS `#{paciente.estadocivil}`,
        `person`.`mother` AS `#{paciente.mae}`,
        `person`.`dad` AS `#{paciente.pai}`,
        `patient`.`enrollment` AS `#{paciente.matricula}`,
        `occupation`.`name` AS `#{paciente.profissao}`,
        `person_identity`.`number` AS `#{paciente.rg}`,
        `person_identity`.`emitter` AS `#{paciente.rg.orgao}`,
        `person_identity`.`dated` AS `#{paciente.rg.emisao}`        
    FROM `patient`
	JOIN `person` ON `person`.`person_id` = `patient`.`person_id`
	LEFT JOIN `person_identity` ON `person_identity`.`person_id` = `person`.`person_id`
	LEFT JOIN `address` ON `address`.`address_id` = `person`.`address_id`
	LEFT JOIN `address_type` ON `address_type`.`address_type_id` = `address`.`address_type_id`
	LEFT JOIN `city` ON `city`.`city_id` = `address`.`city_id`
	LEFT JOIN `insurance` ON `insurance`.`insurance_id` = `patient`.`insurance_id`
	LEFT JOIN `gender` ON `gender`.`gender_id` = `person`.`gender_id`
	LEFT JOIN `occupation` ON `occupation`.`occupation_id` = `person`.`occupation_id`
	LEFT JOIN `schooling` ON `schooling`.`schooling_id` = `person`.`schooling_id`
	LEFT JOIN `civil_status` ON `civil_status`.`civil_status_id` = `person`.`civil_status_id`
	LEFT JOIN `contact_list` ON `contact_list`.`contact_list_id` = `person`.`contact_list_id` AND `contact_list`.`type` = 'G'
	LEFT JOIN (
		SELECT  `contact_list_phone`.`contact_list_id` AS `contact_list_id`,
                `contact_list_phone`.`parent_id` AS `parent_id`,
                `contact_list_phone`.`description` AS `description`,
                `contact_list_phone`.`phone` AS `phone`,
                `contact_list_phone`.`email` AS `email`,
                `contact_list_phone`.`others` AS `others`,
                `contact_list_phone`.`type` AS `type`
        FROM `contact_list` `contact_list_phone`
        WHERE `contact_list_phone`.`type` = 'S' -- Phone
        ORDER BY `contact_list_phone`.`description`
        ) AS `contact_list_phone` ON `contact_list_phone`.`parent_id` = `contact_list`.`contact_list_id`
	LEFT JOIN (
		SELECT  `contact_list_email`.`parent_id` AS `parent_id`,
                MAX(`contact_list_email`.`contact_list_id`) AS `contact_list_id`
        FROM `contact_list` `contact_list_email`
        WHERE `contact_list_email`.`type` = 'M'  -- EMail
        GROUP BY 1        
        ) AS `contact_list_email_max` ON `contact_list_email_max`.`parent_id` = `contact_list`.`contact_list_id`
	LEFT JOIN (
		SELECT	`contact_list_email`.`contact_list_id`,
				`contact_list_email`.`email`
		FROM `contact_list` `contact_list_email`
		WHERE `contact_list_email`.`type` = 'M'  -- EMail
    ) AS `contact_list_email` ON `contact_list_email`.`contact_list_id` = `contact_list_email_max`.`contact_list_id`;
--         
DROP VIEW IF EXISTS `vw_editor_sistema`;
CREATE OR REPLACE DEFINER=`user_clinicall` VIEW `vw_editor_sistema` AS
    SELECT 
		CAST(1 AS SIGNED) as `#{sistema.codigo}`,
        DATE_FORMAT(CURDATE(), '%d/%m/%Y') AS `#{sistema.data-corrente}`,
        DATE_FORMAT(CURTIME(), '%h:%i')    AS `#{sistema.hora-corrente}`,
        DATE_FORMAT(CURRENT_TIMESTAMP(), '%d/%m/%Y %h:%i') AS `#{sistema.data-hora-corrente}`,
        EXTRACT(DAY FROM CURDATE())   AS `#{sistema.dia-corrente}`,
        EXTRACT(MONTH FROM CURDATE()) AS `#{sistema.mes-corrente}`,
        EXTRACT(YEAR FROM CURDATE())  AS `#{sistema.ano-corrente}`,
        `date_to_text`() AS `#{sistema.data-extenso}`;
-- 
DROP VIEW IF EXISTS `vw_editor_variavel`;
CREATE OR REPLACE DEFINER=`user_clinicall` VIEW `vw_editor_variavel` AS
    SELECT DISTINCT
        `schema_views`.`TABLE_NAME` AS `grupo`,
        REPLACE(`schema_views`.`TABLE_NAME`, 'vw_editor_', '') AS `apelido`,
        `schema_columns`.`COLUMN_NAME` AS `variavel`,
        CAST(CURRENT_TIMESTAMP AS DATETIME) AS `created_at`,
        CAST(CURRENT_TIMESTAMP AS DATETIME) AS `updated_at`,
        CAST(1 AS SIGNED) AS `created_by`,
        CAST(1 AS SIGNED) AS `updated_by`
    FROM `information_schema`.`views` AS `schema_views`
	JOIN `information_schema`.`columns` AS `schema_columns` ON `schema_columns`.`TABLE_NAME` = `schema_views`.`TABLE_NAME`
    WHERE `schema_views`.`TABLE_SCHEMA` = DATABASE() AND 
		`schema_views`.`TABLE_NAME` NOT IN ('vw_editor_grupo' , 'vw_editor_variavel');
-- 
DROP VIEW IF EXISTS `vw_util_month`;
CREATE OR REPLACE DEFINER=`user_clinicall` VIEW `vw_util_month` AS
    SELECT 
        CAST('01' AS CHAR (2) CHARSET UTF8MB4) AS `month_id`,
        CAST('Janeiro' AS CHAR (20) CHARSET UTF8MB4) AS `name`,
        CAST('jan' AS CHAR (3) CHARSET UTF8MB4) AS `initials`
    
    UNION SELECT 
        CAST('02' AS CHAR (2) CHARSET UTF8MB4) AS `month_id`,
        CAST('Fevereiro' AS CHAR (20) CHARSET UTF8MB4) AS `name`,
        CAST('fev' AS CHAR (3) CHARSET UTF8MB4) AS `initials`
    
    UNION SELECT 
        CAST('03' AS CHAR (2) CHARSET UTF8MB4) AS `month_id`,
        CAST('Março' AS CHAR (20) CHARSET UTF8MB4) AS name,
        CAST('mar' AS CHAR (3) CHARSET UTF8MB4) AS initials
    
    UNION SELECT 
        CAST('04' AS CHAR (2) CHARSET UTF8MB4) AS month_id,
        CAST('Abril' AS CHAR (20) CHARSET UTF8MB4) AS name,
        CAST('abr' AS CHAR (3) CHARSET UTF8MB4) AS initials
    
    UNION SELECT 
        CAST('05' AS CHAR (2) CHARSET UTF8MB4) AS month_id,
        CAST('Maio' AS CHAR (20) CHARSET UTF8MB4) AS name,
        CAST('mai' AS CHAR (3) CHARSET UTF8MB4) AS initials
    
    UNION SELECT 
        CAST('06' AS CHAR (2) CHARSET UTF8MB4) AS month_id,
        CAST('Junho' AS CHAR (20) CHARSET UTF8MB4) AS name,
        CAST('Jun' AS CHAR (3) CHARSET UTF8MB4) AS initials
    
    UNION SELECT 
        CAST('07' AS CHAR (2) CHARSET UTF8MB4) AS month_id,
        CAST('Julho' AS CHAR (20) CHARSET UTF8MB4) AS name,
        CAST('jul' AS CHAR (3) CHARSET UTF8MB4) AS initials
    
    UNION SELECT 
        CAST('08' AS CHAR (2) CHARSET UTF8MB4) AS month_id,
        CAST('Agosto' AS CHAR (20) CHARSET UTF8MB4) AS name,
        CAST('ago' AS CHAR (3) CHARSET UTF8MB4) AS initials
    
    UNION SELECT 
        CAST('09' AS CHAR (2) CHARSET UTF8MB4) AS month_id,
        CAST('Setembro' AS CHAR (20) CHARSET UTF8MB4) AS name,
        CAST('set' AS CHAR (3) CHARSET UTF8MB4) AS initials
    
    UNION SELECT 
        CAST('10' AS CHAR (2) CHARSET UTF8MB4) AS month_id,
        CAST('Outubro' AS CHAR (20) CHARSET UTF8MB4) AS name,
        CAST('out' AS CHAR (3) CHARSET UTF8MB4) AS initials
    
    UNION SELECT 
        CAST('11' AS CHAR (2) CHARSET UTF8MB4) AS month_id,
        CAST('Novembro' AS CHAR (20) CHARSET UTF8MB4) AS name,
        CAST('nov' AS CHAR (3) CHARSET UTF8MB4) AS initials
    
    UNION SELECT 
        CAST('12' AS CHAR (2) CHARSET UTF8MB4) AS month_id,
        CAST('Dezembro' AS CHAR (20) CHARSET UTF8MB4) AS name,
        CAST('dez' AS CHAR (3) CHARSET UTF8MB4) AS initials;
-- 
DROP VIEW IF EXISTS `vw_editor_receituario`;
CREATE OR REPLACE DEFINER=`user_clinicall` VIEW `vw_editor_receituario` AS
	SELECT    
		`request_item`.`request_id`   AS `#{receituario.codigo}`,
		`request_item`.`name`         AS `#{receituario.nome}`,
		ROW_NUMBER() OVER (PARTITION BY `request_item`.`request_id` ) AS `#{receituario.indice}`,
		`request_item`.`quantity`     AS `#{receituario.qtd}`,
		`request_item`.`dosage`       AS `#{receituario.posologia}`,
		`request_item`.`note`         AS `#{receituario.obs}`,
		`request_item`.`via`          AS `#{receituario.via}`,
		`request_item`.`unit_measurement` AS `#{receituario.unidade}`,
		CAST('Uso:' AS CHAR CHARSET utf8mb4) AS `#{receituario.labeluso}`,
		CAST('Via:' AS CHAR CHARSET utf8mb4) AS `#{receituario.labelvia}`,
		CAST('.....................' AS CHAR CHARSET utf8mb4) AS `#{receituario.delimitador}`
	FROM `request_item`
	LEFT JOIN `request` ON `request`.`request_id` = `request_item`.`request_id`
	LEFT JOIN `product` ON `product`.`product_id` = `request_item`.`product_id`
	LEFT JOIN `def` ON `def`.`product_id` = `product`.`product_id`
	LEFT JOIN `request_diagnosis` ON `request_diagnosis`.request_id  = `request_item`.`request_id`
	LEFT JOIN `cid` ON `cid`.`cid_id` = `request_diagnosis`.`cid_id`
	GROUP BY 1,2,4,5,6,7,8,9,10;
-- 
DROP VIEW IF EXISTS `vw_editor_requisicao`;
CREATE OR REPLACE DEFINER=`user_clinicall` VIEW `vw_editor_requisicao` AS
	SELECT 
		`request`.`request_id`   AS `#{requisicao.codigo}`,
		COALESCE (`request`.`note`, " ") AS `#{requisicao.observacaoSolic}`,
		COALESCE (`request`.`request` , " ") AS `#{requisicao.requisicao}`,
		COALESCE (`request`.`date_request`, " ") AS `#{requisicao.data}`,
		GROUP_CONCAT(DISTINCT `cid`.`code` ORDER BY `cid`.`code` SEPARATOR ', ') AS `#{requisicao.cid}`
	FROM `request`
	LEFT JOIN `request_diagnosis` ON `request_diagnosis`.request_id  = `request`.`request_id`
	LEFT JOIN `cid` ON `cid`.`cid_id` = `request_diagnosis`.`cid_id`
GROUP BY 1;
	
	
	