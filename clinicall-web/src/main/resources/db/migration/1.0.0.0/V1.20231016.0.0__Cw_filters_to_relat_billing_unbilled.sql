
/********************************************************************************
 * Motivation: Relatório a Faturar Filtro de Bloeados - Flyway (https://flywaydb.org/)
 * Author: <PERSON> <<EMAIL>>
 * Created at: 2023-10-16 11:41
 ********************************************************************************/


INSERT INTO `transaction`
(view_id, code, name, description, main, created_at, created_by, updated_at, updated_by)
SELECT
`view`.view_id, 
'application-report-billing-unbilled', 
'A faturar', 
'A faturar', 
NULL, 
'2023-09-22 10:11:15', NULL, NULL, NULL FROM `view` 
 WHERE  `view`.code = 'application-report-billing' AND NOT EXISTS (SELECT 1 FROM `transaction` WHERE code ="application-report-billing-unbilled");


INSERT INTO transaction_filter (transaction_id,filter_id,`order`,param_name)
SELECT (SELECT `transaction`.transaction_id FROM `transaction` WHERE  `transaction`.code = 'application-report-billing-unbilled'),(SELECT `filter`.filter_id FROM `filter` WHERE `filter`.component = 'cw-filter-report-printBlocked'),1,'Bloqueadas'
WHERE NOT EXISTS (SELECT 1 FROM transaction_filter WHERE transaction_id = (SELECT `transaction`.transaction_id FROM `transaction` 
WHERE  `transaction`.code = 'application-report-billing-unbilled') AND filter_id = (SELECT `filter`.filter_id FROM `filter` WHERE `filter`.component = 'cw-filter-report-printBlocked'));

