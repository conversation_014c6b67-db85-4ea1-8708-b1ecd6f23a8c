/********************************************************************************
 * Motivation: <PERSON><PERSON><PERSON> das estruturas de dados para rotina de formula
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 * Created at: 2023-09-05
 ********************************************************************************/
CREATE TABLE IF NOT EXISTS `expression`
(
	`expression_id`      INT NOT NULL AUTO_INCREMENT COMMENT 'ID do Item de Documento',
	`parent_id`          INT COMMENT 'ID do Item de Documento',
	`name`               VARCHAR(250) NOT NULL COMMENT 'Nome do Objeto',
	`title`              VARCHAR(250) NOT NULL COMMENT 'Titulo usado na entrada de dados',
	`type`               VARCHAR(1) NOT NULL COMMENT 'Tipo de dado do elemento',
	`calc`               BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'É cálculo',
	`size`               SMALLINT NOT NULL DEFAULT 0 COMMENT 'Tamanho na Interface Grafica',
	`expression`         TEXT COMMENT 'Expressão para calculo',
	`created_at`         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data e Hora  de Criação do Registro - Interno',
	`created_by`         INT NOT NULL COMMENT 'Usuário de Criação do Registro - Interno',
	`updated_at`         TIMESTAMP COMMENT 'Data e Hora de Atualização do Registro - Interno',
	`updated_by`         INT COMMENT 'Usuário de Atualização do Registro - Interno',
	PRIMARY KEY (`expression_id`),
	CONSTRAINT `fk_expression__self`
		FOREIGN KEY (`parent_id`)
			REFERENCES `expression` (`expression_id`)
				ON DELETE RESTRICT ON UPDATE RESTRICT
);

ALTER TABLE `expression` COMMENT 'Itens dinâmicos do documento - Variáveis e Fórmulas';

CREATE TABLE IF NOT EXISTS `document_model_expression`
(
	`document_model_expression_id` INT NOT NULL AUTO_INCREMENT COMMENT 'ID da Expressão do Modelo de Documento',
	`expression_id`      INT NOT NULL COMMENT 'ID do Item de Documento',
	`document_model_id`  INT NOT NULL COMMENT 'Id do Modelo do Documento',
	`active`             BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Ativo',
	`default_value`      TEXT COMMENT 'Valor Padrão',
	`created_at`         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data e Hora  de Criação do Registro - Interno',
	`created_by`         INT NOT NULL COMMENT 'Usuário de Criação do Registro - Interno',
	`updated_at`         TIMESTAMP COMMENT 'Data e Hora de Atualização do Registro - Interno',
	`updated_by`         INT COMMENT 'Usuário de Atualização do Registro - Interno',
	PRIMARY KEY (`document_model_expression_id`),
	CONSTRAINT `fk_document_model_expression__expression`
		FOREIGN KEY (`expression_id`)
			REFERENCES `expression` (`expression_id`)
				ON DELETE RESTRICT ON UPDATE RESTRICT,
	CONSTRAINT `fk_document_model_expression__document_model`
		FOREIGN KEY (`document_model_id`)
			REFERENCES `document_model` (`id`)
				ON DELETE RESTRICT ON UPDATE RESTRICT
);

ALTER TABLE `document_model_expression` COMMENT 'Expressões do Modelo de Documento';

CREATE TABLE IF NOT EXISTS `document_expression`
(
	`document_expression_id` INT NOT NULL AUTO_INCREMENT COMMENT 'ID das Expressões do Documento',
	`document_model_expression_id` INT COMMENT 'ID da Expressão do Modelo de Documento',
	`document_id`        INT COMMENT 'ID do Documento',
	`value`              TEXT NOT NULL COMMENT 'Valor ou Resultado da Expressão',
	`created_at`         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data e Hora  de Criação do Registro - Interno',
	`created_by`         INT NOT NULL COMMENT 'Usuário de Criação do Registro - Interno',
	`updated_at`         TIMESTAMP COMMENT 'Data e Hora de Atualização do Registro - Interno',
	`updated_by`         INT COMMENT 'Usuário de Atualização do Registro - Interno',
	PRIMARY KEY (`document_expression_id`),
	CONSTRAINT `fk_document_expression__document`
		FOREIGN KEY (`document_id`)
			REFERENCES `document` (`document_id`)
				ON DELETE RESTRICT ON UPDATE RESTRICT,
	CONSTRAINT `fk_document_expression__document_model_expression`
		FOREIGN KEY (`document_model_expression_id`)
			REFERENCES `document_model_expression` (`document_model_expression_id`)
				ON DELETE RESTRICT ON UPDATE RESTRICT
);

ALTER TABLE `document_expression` COMMENT 'Conteudo e respostas para as expressões programadas';
