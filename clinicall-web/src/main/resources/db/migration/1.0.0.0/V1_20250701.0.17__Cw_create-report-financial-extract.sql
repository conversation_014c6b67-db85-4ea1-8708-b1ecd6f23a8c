/****************************
 * Motivation: Script do relatório "extrato"
 * Author: <PERSON> <<EMAIL>>
 * Created at: 2025-06-30 09:20
 ****************************/

CALL InsertTransaction('application-report-financial', 'application-report-financial-extract', 'Extrato Bancário', 'Extrato Bancário', 0);
CALL InsertTransactionFilter('application-report-financial-extract', 'cw-filter-report-date', 'Data');
CALL InsertTransactionFilter('application-report-financial-extract', 'cw-filter-report-cost-center', 'Centro de Custo');

-- CALL InsertTransactionFilter('application-report-patient-register-by-date', 'cw-filter-report-performer', 'Profissional');
-- CALL InsertTransactionFilter('application-report-patient-register-by-date', 'cw-filter-report-patient', 'Paciente');

-- CALL InsertTransactionFilter('application-report-order-os-by-attendance', 'cw-filter-report-performer', 'Profissional');
-- CALL InsertTransactionFilter('application-report-order-os-by-attendance', 'cw-filter-report-company', 'Empresa/Filial');
-- CALL InsertTransactionFilter('application-report-order-attendance-procedure', 'cw-filter-report-insurance', 'Convênio');
-- CALL InsertTransactionFilter('application-report-order-attendance-procedure', 'cw-filter-report-date', 'Data');
-- CALL InsertTransactionFilter('application-report-order-attendance-procedure', 'cw-filter-report-performer', 'Profissional');
-- CALL InsertTransactionFilter('application-report-order-attendance-procedure', 'cw-filter-report-company', 'Empresa/Filial');
-- CALL InsertTransactionFilter('application-report-order-attendance-procedure', 'cw-filter-report-group-procedure', 'Grupo de Serviço');
-- CALL InsertTransactionFilter('application-report-order-attendance-procedure', 'cw-filter-report-procedure', 'Procedimento');
-- CALL InsertTransactionFilter('application-report-order-attendance-procedure', 'cw-filter-report-user', 'Usuário');
-- CALL InsertTransactionFilter('application-report-schedule-status', 'cw-filter-report-patientStatus', 'Status do Paciente');
-- CALL InsertTransactionFilter('application-report-schedule-status', 'cw-filter-report-scheduleStatus', 'Status da Agenda');
-- CALL InsertTransactionFilter('application-report-schedule-status', 'cw-filter-report-speciality', 'Especialidade');
-- CALL InsertTransactionFilter('application-report-transfer-detailed-transfer-by-production', 'cw-filter-report-onlyGerarateCash', 'Check(Apenas Gera Caixa)');
-- CALL InsertTransactionFilter('application-report-transfer-detailed-transfer-by-production', 'cw-filter-report-notGerarateCash', 'Check(Exceto Gera Caixa)');
-- CALL InsertTransactionFilter('application-report-schedule-status', 'cw-filter-report-cost-center', 'Centro de Custo');