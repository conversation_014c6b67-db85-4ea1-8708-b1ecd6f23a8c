
/*******************************************************************************
 * Motivation: Insert Permission 
 * Author: Gerador Clinic all
 * Created at: 
 ******************************************************************************/

-- ================================== VIEW ======================================
CALL InsertView("financial-bonds", "Contas a Receber", "C", "/financeiro/titulos/contas-a-receber", "financial-bonds-accounts_receivable", "Contas a Receber");

-- ==============================================================================   

-- =============================== TRANSACTION ==================================

-- CRUD LIST - SEARCH REGISTERS
CALL InsertTransaction(
  "financial-bonds-accounts_receivable",
  "search-financial-bonds-accounts_receivable-list",
  "Listar Contas a Receber",
  "Listar Contas a Receber",
  1
);

-- CRUD EDIT - INSERT REGISTER
CALL InsertTransaction(
  "financial-bonds-accounts_receivable",
  "insert-financial-bonds-accounts_receivable-edit",
  "Incluir Contas a Receber",
  "Incluir Contas a Receber",    
  NULL
);

-- CRUD EDIT - UPDATE REGISTER
CALL InsertTransaction(
  "financial-bonds-accounts_receivable",
  "update-financial-bonds-accounts_receivable-edit",
  "Atualizar Contas a Receber",
  "Atualizar Contas a Receber",
  null
);

-- CRUD EDIT - DELETE REGISTER
CALL InsertTransaction(
  "financial-bonds-accounts_receivable",
  "delete-financial-bonds-accounts_receivable-edit",
  "Deletar Contas a Receber",
  "Deletar Contas a Receber",
  null
);


-- CRUD EDIT - FIND REGISTER
CALL InsertTransaction(
  "financial-bonds-accounts_receivable",
  "find-financial-bonds-accounts_receivable-edit",
  "Visualizar Contas a Receber",
  "Visualizar Contas a Receber",
  null
);
-- ==============================================================================

-- ============================ RESTRICTABLE COMPONENT ==========================

   

-- ==============================================================================
