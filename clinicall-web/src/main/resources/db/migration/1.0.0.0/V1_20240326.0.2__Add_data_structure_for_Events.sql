/********************************************************************************
 * Motivation: Adicionar estrutura de dados para Eventos
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 * Created at: 2024-03-19
 * Released at:
 ********************************************************************************/
CREATE TABLE IF NOT EXISTS `event`(
   `event_id`           INT NOT NULL AUTO_INCREMENT COMMENT 'Id do Evento ou Mensagem de Agenda',
   `parent_id`          INT COMMENT 'Id do Evento ou Mensagem de Agenda',
   `user_id`            INT COMMENT 'Id Usuário',
   `company_id`         INT COMMENT 'Id Empresa',
   `performer_id`       INT COMMENT 'Id do Executante',
   `patient_id`         INT COMMENT 'Id do Paciente',
   `name`               VARCHAR(50) NOT NULL COMMENT 'Nome do Evento',
   `content`            BLOB COMMENT 'Conteúdo Textual do Evento',
   `type`		CHAR(1) NOT NULL COMMENT 'Tipo do Evento',
   `color` 		VARCHAR(50) COMMENT'Cor para o Evento',
   `dated`              DATE COMMENT 'Data do Evento',
   `hour`               TIME NOT NULL COMMENT 'Hora do Evento',
   `created_at`         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data e Hora  de Criação do Registro - Interno',
   `created_by`         INT COMMENT 'Usuário de Criação do Registro - Interno',
   `updated_at`         TIMESTAMP COMMENT 'Data e Hora de Atualização do Registro - Interno',
   `updated_by`         INT COMMENT 'Usuário de Atualização do Registro - Interno',
   PRIMARY KEY (`event_id`),
   CONSTRAINT `fk_event__company` FOREIGN KEY (`company_id`) REFERENCES `company` (`company_id`),
   CONSTRAINT `fk_event__patient` FOREIGN KEY (`patient_id`) REFERENCES `patient` (`patient_id`),
   CONSTRAINT `fk_event__self` FOREIGN KEY (`parent_id`) REFERENCES `event` (`event_id`),
   CONSTRAINT `fk_event__performer` FOREIGN KEY (`performer_id`) REFERENCES `performer` (`performer_id`),
   CONSTRAINT `fk_event__user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`)
) COMMENT 'Eventos e Mensagens da Agenda';

