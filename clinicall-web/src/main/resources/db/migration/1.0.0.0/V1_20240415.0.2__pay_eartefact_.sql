/********************************************************************************
 * Motivation: Baixa automática de fatura
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 * Created at: 2024-02-06
 * Released at:
 ********************************************************************************/
 CREATE TABLE IF NOT EXISTS `pay_eartefact`(
   `pay_eartefact_id`   INT NOT NULL AUTO_INCREMENT COMMENT 'ID Artefatos Pagamento',
   `eartefact_id`       INT COMMENT 'ID do Artefato',
   `pay_id`             INT COMMENT 'ID do Pagamento de Fatura',
   `created_at`         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data e Hora  de Criação do Registro - Interno',
   `created_by`         INT COMMENT 'Usuário de Criação do Registro - Interno',
   `updated_at`         TIMESTAMP COMMENT 'Data e Hora de Atualização do Registro - Interno',
   `updated_by`         INT COMMENT 'Usuário de Atualização do Registro - Interno',
   PRIMARY KEY (`pay_eartefact_id`),
   CONSTRAINT `fk_pay_eartefact__eartefact` FOREIGN KEY (`eartefact_id`) REFERENCES `eartefact` (`eartefact_id`),
   CONSTRAINT `fk_pay_eartefact__pay` FOREIGN KEY (`pay_id`) REFERENCES `pay` (`pay_id`)
) COMMENT 'Artefatos do Pagamento';
