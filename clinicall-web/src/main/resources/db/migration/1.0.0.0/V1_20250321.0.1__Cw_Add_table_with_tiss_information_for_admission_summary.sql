/********************************************************************************
 * Motivation: Tabela com informações para Guia de Resumo de Internação
 * 
 * Rule: 
 * 
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 * 
 * Created at: 2025-02-27
 ********************************************************************************/

CREATE TABLE IF NOT EXISTS order_tiss_declaration (
    order_tiss_declaration_id INT NOT NULL AUTO_INCREMENT COMMENT 'Id Declaração Order Tiss',
    order_tiss_id             INT NOT NULL COMMENT 'ID dados do TISS para Ordem de Serviço',
    cid_id                    INT NULL COMMENT 'ID do CID (Campo 31 - CID 10 óbito)',
    birth_declaration         VARCHAR(20) NULL COMMENT 'Campo 30 - Número da Declaração de Nascidos Vivos',
    death_declaration         VARCHAR(20) NULL COMMENT 'Campo 32 - Número da declaração de óbito',
    dorn_indicator            VARCHAR(1) NULL COMMENT 'Campo 33 - Indicador Declaração de Obitos de Recém Nascido',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data e Hora de Criação do Registro - Interno',
    `created_by` INT DEFAULT NULL COMMENT 'Usuário de Criação do Registro - Interno',
    `updated_at` TIMESTAMP NULL DEFAULT NULL COMMENT 'Data e Hora de Atualização do Registro - Interno',
    `updated_by` INT DEFAULT NULL COMMENT 'Usuário de Atualização do Registro - Interno',
    PRIMARY KEY (order_tiss_declaration_id),
    CONSTRAINT fk_order_tiss_declaration__cid
        FOREIGN KEY (cid_id)
        REFERENCES cid (cid_id) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT fk_order_tiss_declaration__order_tiss
        FOREIGN KEY (order_tiss_id)
        REFERENCES order_tiss (order_tiss_id) ON DELETE RESTRICT ON UPDATE RESTRICT
) COMMENT='Dados do TISS - Declarações';

