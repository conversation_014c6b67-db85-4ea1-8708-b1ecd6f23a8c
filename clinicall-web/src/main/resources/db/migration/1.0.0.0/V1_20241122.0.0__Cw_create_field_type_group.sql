/********************************************************************************
 * Motivation: Adici<PERSON>r campo type em group
 * Author: <PERSON> <<EMAIL>>
 * Created at: 2024-11-22
 * Released at:
 ********************************************************************************/
CALL flyway_add_column('group', 
	'type', 'VARCHAR(1)', 
    CONCAT('COMMENT ', '''', 'Tipo do Grupo', '''', ' AFTER code'), 
    @msg_status);
   
CALL flyway_add_column('group_aud', 
	'type', 'VARCHAR(1)', 
    CONCAT('COMMENT ', '''', 'Tipo do Grupo', '''', ' AFTER name'), 
    @msg_status);   

   
UPDATE `group`
SET `type` = CASE
    WHEN (`name` LIKE '%PROC%' OR `name` LIKE '%EXA%' OR `name` LIKE '%SES%' OR `name` LIKE '%TER%' OR `name` LIKE '%LAB%') THEN 'P'
    WHEN (`name` LIKE '%AVA%' OR `name` LIKE '%CON%') THEN 'C'
    WHEN (`name` LIKE '%RET%' OR `name` LIKE '%REV%') THEN 'R'
    WHEN `name` LIKE '%CIR%' THEN 'S'
    ELSE 'N'
END
WHERE `type` IS NULL;