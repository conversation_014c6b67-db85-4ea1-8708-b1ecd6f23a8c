/********************************************************************************
 * Motivation: Adicionar campos para periodo de hora no faturamento
 * 
 * Rule: 
 * 
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 * 
 * Created at: 2025-02-27
 ********************************************************************************/

CALL flyway_add_column('order_tiss',
	'billing_start_time', 'TIME NULL',
    CONCAT('COMMENT ', '''', 'Hora Inicial de Faturamento', '''', 'AFTER billing_start_date'),
    @msg_status);

CALL flyway_add_column('order_tiss',
	'billing_end_time', 'TIME NULL',
    CONCAT('COMMENT ', '''', 'Hora Final de Faturamento', '''', 'AFTER billing_end_date'),
    @msg_status);
