/********************************************************************************
 * Motivation: Adiconando campo de unidade de medida na tabela de taxa
 * Author: <PERSON><PERSON> <<EMAIL>>
 * Created at: 2023-12-21 16:00
 ********************************************************************************/
CALL flyway_add_column('fee', 'measure_tiss_id', 'INT', CONCAT('COMMENT ', '''', 'Id Unidade de Medida do TISS', '''', ' AFTER fee_id'), @msg_status);
CALL flyway_add_fk('fee', 'fk_fee__measure_tiss', 'measure_tiss_id', 'measure_tiss', 'measure_tiss_id', 'RESTRICT', 'RESTRICT', @msg_status);