/********************************************************************************
 * Motivation: Adicionar estrutura de dados para Controle de Cotas e Carência
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 * Created at: 2024-04-12
 * Released at: 2024-05-03 - Adicionado importação de dados de accreditation_spec
 ********************************************************************************/
CREATE TABLE IF NOT EXISTS `quota_lack_control`(
   `quota_lack_control_id` INT NOT NULL AUTO_INCREMENT COMMENT 'ID Controle de Cota e Carência',
   `name`                  VARCHAR(250) NOT NULL COMMENT 'Descrição',
   `priority`              SMALLINT NOT NULL COMMENT 'Prioridade',
   `company_id`            INT COMMENT 'Id Empresa',
   `insurance_id`          INT COMMENT 'Id do Convenio',
   `procedure_id`          INT COMMENT 'Id Procedimento',
   `performer_id`          INT COMMENT 'Id do Executante',
   `speciality_id`         INT COMMENT 'Id Especialidade',
   `group_id`              INT COMMENT 'Id Grupo',
   `parent_id`             INT COMMENT 'ID Controle de Cota e Carência',
   `day`                   SMALLINT COMMENT 'Dia da Semana',
   `quota`                 SMALLINT COMMENT 'Quantidade Cota',
   `lack`                  SMALLINT COMMENT 'Quantidade Carência',
   `is_schedule`           BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Usado na Agenda',
   `is_order`              BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Usado no Atendimento',
   `period`                VARCHAR(1) NOT NULL DEFAULT 'N' COMMENT 'Frequencia (Não se Aplica, Diaria, Mensal)',
   `shift`                 VARCHAR(1) NOT NULL DEFAULT 'N' COMMENT 'Turno (Não se Aplica, Matutino, Vespertino, Noturno)',
   `is_only_warning`       BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Somente Avisa - permitindo o lançamento',
   `created_at`            TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Data e Hora  de Criação do Registro - Interno',
   `created_by`            INT COMMENT 'Usuário de Criação do Registro - Interno',
   `updated_at`            TIMESTAMP COMMENT 'Data e Hora de Atualização do Registro - Interno',
   `updated_by`            INT COMMENT 'Usuário de Atualização do Registro - Interno',
   PRIMARY KEY (`quota_lack_control_id`),
   CONSTRAINT `fk_quota_lack_control__company` FOREIGN KEY (`company_id`) REFERENCES `company` (`company_id`),
   CONSTRAINT `fk_quota_lack_control__group` FOREIGN KEY (`group_id`) REFERENCES `group` (`group_id`),
   CONSTRAINT `fk_quota_lack_control__insurance` FOREIGN KEY (`insurance_id`) REFERENCES `insurance` (`insurance_id`),
   CONSTRAINT `fk_quota_lack_control__performer` FOREIGN KEY (`performer_id`) REFERENCES `performer` (`performer_id`),
   CONSTRAINT `fk_quota_lack_control__procedure` FOREIGN KEY (`procedure_id`) REFERENCES `procedure` (`procedure_id`),
   CONSTRAINT `fk_quota_lack_control__self` FOREIGN KEY (`parent_id`) REFERENCES `quota_lack_control` (`quota_lack_control_id`),
   CONSTRAINT `fk_quota_lack_control__speciality` FOREIGN KEY (`speciality_id`) REFERENCES `speciality` (`speciality_id`)
) COMMENT 'Central de Controle de Cota e Carência';

-- Importa dados de accreditation_spec
INSERT INTO quota_lack_control(
	name,
    priority,
    insurance_id,
    procedure_id,
    lack,
    is_schedule,
    is_order,
    period,
    shift,
    is_only_warning,
    created_at,
    created_by
)
SELECT 
	CONCAT('Carência ', insurance.name) AS `name`,    -- varchar(250) not null comment 'Descrição',
	CAST(1 AS SIGNED) AS priority,                    -- smallint not null comment 'Prioridade',
	agreement.insurance_id,                           -- int comment 'Id do Convenio',
    accreditation.procedure_id,                       -- int comment 'Id Procedimento',
	accreditation_spec.lack,                          -- smallint comment 'Quantidade Carência',
	FALSE AS is_schedule,                             -- boolean not null default false comment 'Usado na Agenda',
	TRUE AS is_order,                                 -- boolean not null default false comment 'Usado no Atendimento',
	CAST('D' AS CHAR) AS period,                      -- varchar(1) not null default 'N' comment 'Frequencia (Não se Aplica, Diaria, Mensal)',
	CAST('N' AS CHAR) AS shift,                       -- varchar(1) not null default 'N' comment 'Turno (Não se Aplica, Matutino, Vespertino, Noturno)',
	FALSE AS is_only_warning,                         -- boolean not null default false comment 'Somente Avisa - permitindo o lançamento',
	CURRENT_TIMESTAMP as created_at,                -- timestamp not null default current_timestamp comment 'Data e Hora  de Criação do Registro - Interno',
	CAST(1 AS SIGNED) AS created_by                 -- int comment 'Usuário de Criação do Registro - Interno',
FROM accreditation_spec 
JOIN accreditation ON accreditation.accreditation_id = accreditation_spec.accreditation_id 
JOIN agreement ON agreement.agreement_id = accreditation.agreement_id
JOIN insurance ON insurance.insurance_id = agreement.insurance_id
WHERE accreditation_spec.lack IS NOT NULL;
