/****************************
 * Motivation: Criação de parametros para bloquear gerações de rebibo e guias;"
 * Author: <PERSON> <<EMAIL>>
 * Created at: 2024-05-03 09:20
 ****************************/

CALL InsertParameter('BLOCK_RECEIPT_ATTENDANCE', 'ST', 'Bloquear receita do atendimento.', 'SYSTEM', 'GENERAL', '0', 0,  CURRENT_TIMESTAMP());
CALL InsertParameter('BLOCK_GUIDE_ATTENDANCE', 'ST', 'Bloquear guias no atendimento.', 'SYSTEM', 'GENERAL', '0', 0,  CURRENT_TIMESTAMP());
