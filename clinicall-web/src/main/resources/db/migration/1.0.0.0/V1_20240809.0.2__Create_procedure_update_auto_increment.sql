/******************************************************************************
 * Motivation: Criação de novos campos em order_tiss data inicio faturamento e data fim faturamento;
 * Author: <PERSON><PERSON> <<EMAIL>>
 * Created at: 2024-08-08
 ******************************************************************************/
DROP PROCEDURE IF EXISTS update_auto_increment;
DELIMITER //
CREATE DEFINER=`user_clinicall`@`%` PROCEDURE `update_auto_increment`(
    IN cw_table_name VARCHAR(100),
    IN cw_table_source VARCHAR(100),
    IN fieldname VARCHAR(100)
)
    READS SQL DATA
BEGIN
    DECLARE maxId INTEGER;

    -- Construindo a query para obter o valor máximo
    SET @sql_query1 = CONCAT('SELECT MAX(`', fieldname, '`) INTO @maxId FROM `', cw_table_source, '`');
    PREPARE sql_script1 FROM @sql_query1;
    EXECUTE sql_script1;
    DEALLOCATE PREPARE sql_script1;

    -- Construindo a query para alterar o auto incremento
    SET @sql_query2 = CONCAT('SET @maxId = ', '@maxId');
    PREPARE sql_script2 FROM @sql_query2;
    EXECUTE sql_script2;
    DEALLOCATE PREPARE sql_script2;

    -- Verificando se @maxId é NULL, se for, atribui 1
    IF @maxId IS NULL THEN
        SET @maxId = 1;
    END IF;

    -- Construindo a query para alterar o auto incremento
    SET @sql_query3 = CONCAT('ALTER TABLE `', cw_table_name, '` AUTO_INCREMENT = ', @maxId);

    -- Preparando e executando a query
    PREPARE sql_script3 FROM @sql_query3;
    EXECUTE sql_script3;

    -- Liberando a query preparada
    DEALLOCATE PREPARE sql_script3;
END //

DELIMITER ;