/****************************
 * Motivation: Script do relatório "OS por atendente"
 * Author: <PERSON> <<EMAIL>>
 * Created at: 2024-08-16 15:20
 ****************************/

CALL InsertTransaction('application-report-order', 'application-report-order-os-by-attendance', 'OS por atendente', 'OS por atendente', 0);
CALL InsertTransactionFilter('application-report-order-os-by-attendance', 'cw-filter-report-insurance', 'Convênio');
CALL InsertTransactionFilter('application-report-order-os-by-attendance', 'cw-filter-report-date', 'Data');
CALL InsertTransactionFilter('application-report-order-os-by-attendance', 'cw-filter-report-performer', 'Profissional');
CALL InsertTransactionFilter('application-report-order-os-by-attendance', 'cw-filter-report-company', 'Empresa/Filial');