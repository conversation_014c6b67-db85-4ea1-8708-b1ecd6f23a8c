<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="capture" columnCount="2" printOrder="Horizontal" pageWidth="620" pageHeight="842" columnWidth="290" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="ab7a2538-927f-4b7c-b210-99ae38035759">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_demo"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="document_id" class="java.lang.Long"/>
	<queryString>
		<![CDATA[SELECT 
eartefact.content AS image,
attachment.name AS name
FROM 
attachment 
LEFT JOIN eartefact ON eartefact.eartefact_id = attachment.eartefact_id 
LEFT JOIN document ON document.document_id = attachment.document_id 
WHERE 
document.document_id = $P{document_id} AND attachment.checked = true]]>
	</queryString>
	<field name="image" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="content"/>
		<property name="com.jaspersoft.studio.field.label" value="image"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="eartefact"/>
	</field>
	<field name="name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="attachment"/>
		<fieldDescription><![CDATA[Descrição ou nome para artefato]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="283" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout"/>
			<frame>
				<reportElement x="0" y="4" width="280" height="279" uuid="aedb764d-09bd-42b7-8f5c-558e449c33f8">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="14" y="12" width="261" height="229" uuid="3a3d28c3-090b-4c1f-bd53-1e43edebfd4d">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<box>
						<pen lineWidth="1.0" lineStyle="Dashed" lineColor="#0F7394"/>
						<topPen lineWidth="1.0" lineStyle="Dashed" lineColor="#0F7394"/>
						<leftPen lineWidth="1.0" lineStyle="Dashed" lineColor="#0F7394"/>
						<bottomPen lineWidth="1.0" lineStyle="Dashed" lineColor="#0F7394"/>
						<rightPen lineWidth="1.0" lineStyle="Dashed" lineColor="#0F7394"/>
					</box>
					<imageExpression><![CDATA[new ByteArrayInputStream(
    java.util.Base64.getDecoder().decode(
        $F{image}.replaceFirst("data:image/[^;]+;base64,", "")
    )
)]]></imageExpression>
				</image>
				<textField>
					<reportElement x="19" y="244" width="250" height="30" uuid="ff95f14b-b3b5-4dc8-8cef-d2f1cd591a63">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="12"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{name} != null && !$F{name}.startsWith("capture") ? $F{name} : ""
]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
