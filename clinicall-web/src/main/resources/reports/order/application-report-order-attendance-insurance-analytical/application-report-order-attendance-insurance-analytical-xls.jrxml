<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="application-report-order-attendance-insurance-analytical" pageWidth="595" pageHeight="841" whenNoDataType="NoDataSection" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4ff84453-1320-4ac5-a9ad-242cce5098f5">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_demo"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="0"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="1000"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<subDataset name="Dataset1" uuid="7fe0c814-75e0-454d-bbae-8410466db122">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MySql "/>
		<queryString language="SQL">
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="costCenter" class="java.util.ArrayList"/>
	<parameter name="performer" class="java.util.ArrayList"/>
	<parameter name="insurance" class="java.util.ArrayList"/>
	<parameter name="dataInicio" class="java.lang.String"/>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="filters" class="java.util.ArrayList"/>
	<parameter name="nameReport" class="java.lang.String"/>
	<parameter name="user" class="java.util.ArrayList"/>
	<parameter name="costCenterStart" class="java.lang.String"/>
	<parameter name="costCenterEnd" class="java.lang.String"/>
	<parameter name="performerStart" class="java.lang.String"/>
	<parameter name="performerEnd" class="java.lang.String"/>
	<parameter name="performerRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "$X{IN,performer.performer_id, performer} " :
"person_performer.`name`  >= " +"$P" +"{performerStart}"+" AND person_performer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="costCenterRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{costCenterStart} == null || $P{costCenterEnd} == null ? "and $X{IN,cost_center.cost_center_id, costCenter} " :
"and cost_center.`name`  >= " +"$P" +"{costCenterStart}"+" AND cost_center.`name` <= " + "$P" + "{costCenterEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{userStart} == null || $P{userEnd} == null ? "and $X{IN,user.user_id, user} " :
"and personUser.`name`  >= " +"$P" +"{userStart}"+" AND personUser.`name` <= " + "$P" + "{userEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userEnd" class="java.lang.String"/>
	<parameter name="userStart" class="java.lang.String"/>
	<parameter name="dateRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{dataInicio} == null || $P{dataFim} == null ? " " :
"AND order_item.`date` BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="firm" class="java.util.ArrayList"/>
	<parameter name="firmStart" class="java.lang.String"/>
	<parameter name="firmEnd" class="java.lang.String"/>
	<parameter name="firmRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{firmStart} == null || $P{firmEnd} == null ? "and  $X{IN,company.company_id, firm} " :
"and company.`name`  >= " +"$P" +"{firmStart}"+" AND company.`name` <= " + "$P" + "{firmEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="onlyGerarateCash" class="java.util.ArrayList"/>
	<parameter name="notGerarateCash" class="java.util.ArrayList"/>
	<parameter name="onlyGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{onlyGerarateCash} ==  null ?  " " : "and  $X{IN,insurance.payment_model, onlyGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="notGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{notGerarateCash} ==  null ? " " : "and  $X{IN,insurance.payment_model, notGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="printValue" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="printValueClinical" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="printCalculationBasis" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="insuranceStart" class="java.lang.String"/>
	<parameter name="insuranceEnd" class="java.lang.String"/>
	<parameter name="insuranceRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{insuranceStart} == null || $P{insuranceEnd} == null ? "and  $X{IN,`insurance`.`insurance_id`, insurance} " :
"and `insurance`.`name`  >= " +"$P" +"{insuranceStart}"+" AND `insurance`.`name` <= " + "$P" + "{insuranceEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="patientRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{patientStart} == null || $P{patientEnd} == null ? "and  $X{IN,`patient`.`patient_id`, patient} " :
"and `person_patient`.`name`  >= " +"$P" +"{patientStart}"+" AND `person_patient`.`name` <= " + "$P" + "{patientEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="patientStart" class="java.lang.String"/>
	<parameter name="patientEnd" class="java.lang.String"/>
	<parameter name="patient" class="java.util.ArrayList"/>
	<parameter name="companyImage" class="java.lang.String" isForPrompting="false"/>
	<parameter name="companyName" class="java.lang.String"/>
	<parameter name="auxiliaryRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "AND $X{IN,performer_auxiliary.performer_id, performer} " :
" AND personPerformer.`name`  >= " +"$P" +"{performerStart}"+" AND personPerformer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[WITH 
	cte_parameters AS (
			SELECT
				parameter.name,
				COALESCE(parameter_value.value, NULLIF(parameter.value, 0)) AS value,
				CAST(parameter_value.key AS SIGNED) AS `key`
			FROM parameter
			JOIN parameter_value ON parameter_value.parameter_id = parameter.parameter_id
			WHERE parameter.name IN (
				'DISCOUNT_TAX_ON_INSURANCE',
				'DISCOUNT_TAX_ON_PAY_TYPE',
				'DISCOUNT_FEE_ON_PAY_TYPE'
			)
			AND parameter_value.key IS NOT NULL
	)
	-- SELECT * FROM cte_parameters; -- Testes dos parametros
	, 
		cte_product AS (
			SELECT
				order_item_product.order_item_id AS order_item_id,			
				SUM(CASE 
                WHEN order_item_product.participation_type = '%' THEN	
                    (order_item_product.total - 
                    (order_item_product.total * COALESCE(order_item_product.participation_value, 0) / 100)) 
                WHEN order_item_product.participation_type = '$' THEN	
                    order_item_product.total
                ELSE 
                    .0 
            	END) AS value_product
			FROM order_item_product 
			GROUP BY 1
			
	)
	-- SELECT * FROM cte_parameters; -- Testes dos parametros
	, 
	cte_base AS (
		SELECT 
	company.company_id,
	insurance.insurance_id,
	patient.patient_id,
	order_item.order_item_id,
    `order`.order_id,
	`order`.`number` AS guideNumber,
	person_performer.name AS performer_name,
	company.name AS company_name,
	insurance.name AS insuranre_name,
	order_item.`date` AS date_launch,
	person_patient.name AS patient_name,
	order_item.quantity AS procedure_quantity,
	order_item.name AS procedure_name,
	order_item.code AS procedure_code,
	order_authorization.release_password AS password,
	insurance.cnpj AS insurance_cnpj,
	currency.name AS currency,
	(SELECT 1 FROM order_item_fee WHERE  order_item_fee.order_item_id = order_item.order_item_id 
		UNION 
	 SELECT 1 FROM order_item_product WHERE order_item_product.order_item_id = order_item.order_item_id
		UNION 
	 SELECT 1 FROM order_item_auxiliary WHERE order_item_auxiliary.order_item_id = order_item.order_item_id) AS isProduct,
			-- Valor Total 		
			CASE
				WHEN order_pay.value IS NULL THEN order_item.value
				WHEN order_pay.value = `order`.value THEN order_item.`value`
				ELSE (order_item.`value` / 100) * ((order_pay.value / `order`.value) * 100)
			END AS value,
			
			CASE
				WHEN order_pay.value IS NULL THEN order_item.value
				WHEN order_pay.value = `order`.value THEN order_item.`value`
				ELSE (order_item.`value` / 100) * ((order_pay.value / `order`.value) * 100)
			END + COALESCE(cte_product.value_product,0)    AS total,
			
			-- Inicio dos campos Calculados
			CASE 
				WHEN vw_tax_insurance.value > 0
				AND vw_tax_discount_on_pay_type.value > 0
				AND vw_fee_discount_on_pay_type.value > 0
				AND pay_type.pay_type_id
				
				-- IMPOSTO CONVENIO / IMPOSTO PAGAMENTO / TAXA
			    THEN CASE
					WHEN `order_pay`.value IS NULL THEN order_item.value
					WHEN `order_pay`.value = `order`.value THEN order_item.`value`
					ELSE (order_item.`value` / 100) * ((`order_pay`.value / `order`.value) * 100)
				END - (CASE
					WHEN `order_pay`.value IS NULL THEN order_item.value
					WHEN `order_pay`.value = `order`.value THEN order_item.`value`
					ELSE (order_item.`value` / 100) * ((`order_pay`.value / `order`.value) * 100)
				END * (insurance_tax.medic + pay_type.tax + pay_type.fee)) / 100
				WHEN vw_tax_discount_on_pay_type.value > 0
				AND vw_fee_discount_on_pay_type.value > 0
				AND pay_type.pay_type_id
				
				--  IMPOSTO PAGAMENTO / TAXA
			    THEN CASE
					WHEN `order_pay`.value IS NULL THEN order_item.value
					WHEN `order_pay`.value = `order`.value THEN order_item.`value`
					ELSE (order_item.`value` / 100) * ((`order_pay`.value / `order`.value) * 100)
				END - (CASE
					WHEN `order_pay`.value IS NULL THEN order_item.value
					WHEN `order_pay`.value = `order`.value THEN order_item.`value`
					ELSE (order_item.`value` / 100) * ((`order_pay`.value / `order`.value) * 100)
				END * (pay_type.tax + pay_type.fee)) / 100
				WHEN vw_tax_discount_on_pay_type.value > 0
				AND vw_tax_insurance.value > 0
				AND pay_type.pay_type_id
				
				-- IMPOSTO PAGAMENTO / IMPOSTO CONVENIO
			    THEN CASE
					WHEN `order_pay`.value IS NULL THEN order_item.value
					WHEN `order_pay`.value = `order`.value THEN order_item.`value`
					ELSE (order_item.`value` / 100) * ((`order_pay`.value / `order`.value) * 100)
				END - (CASE
					WHEN `order_pay`.value IS NULL THEN order_item.value
					WHEN `order_pay`.value = `order`.value THEN order_item.`value`
					ELSE (order_item.`value` / 100) * ((`order_pay`.value / `order`.value) * 100)
				END * (pay_type.tax + insurance_tax.medic )) / 100
				WHEN vw_fee_discount_on_pay_type.value > 0
				AND vw_tax_insurance.value > 0
				AND pay_type.pay_type_id
				
				-- IMPOSTO CONVENIO / TAXA
			    THEN CASE
					WHEN `order_pay`.value IS NULL THEN order_item.value
					WHEN `order_pay`.value = `order`.value THEN order_item.`value`
					ELSE (order_item.`value` / 100) * ((`order_pay`.value / `order`.value) * 100)
				END - (CASE
					WHEN `order_pay`.value IS NULL THEN order_item.value
					WHEN `order_pay`.value = `order`.value THEN order_item.`value`
					ELSE (order_item.`value` / 100) * ((`order_pay`.value / `order`.value) * 100)
				END * (pay_type.fee + insurance_tax.medic )) / 100
				WHEN vw_tax_insurance.value > 0 
			    THEN CASE
					WHEN `order_pay`.value IS NULL THEN order_item.value
					WHEN `order_pay`.value = `order`.value THEN order_item.`value`
					ELSE (order_item.`value` / 100) * ((`order_pay`.value / `order`.value) * 100)
				END - (CASE
					WHEN `order_pay`.value IS NULL THEN order_item.value
					WHEN `order_pay`.value = `order`.value THEN order_item.`value`
					ELSE (order_item.`value` / 100) * ((`order_pay`.value / `order`.value) * 100)
				END * insurance_tax.medic ) / 100
			
				-- IMPOSTO CONVENIO
				WHEN vw_tax_discount_on_pay_type.value > 0
				AND pay_type.pay_type_id 
				THEN CASE
					WHEN `order_pay`.value IS NULL THEN order_item.value
					WHEN `order_pay`.value = `order`.value THEN order_item.`value`
					ELSE (order_item.`value` / 100) * ((`order_pay`.value / `order`.value) * 100)
				END - (CASE
					WHEN `order_pay`.value IS NULL THEN order_item.value
					WHEN `order_pay`.value = `order`.value THEN order_item.`value`
					ELSE (order_item.`value` / 100) * ((`order_pay`.value / `order`.value) * 100)
				END * pay_type.tax ) / 100
		
				-- IMPOSTO PAGAMENTO
				WHEN vw_fee_discount_on_pay_type.value > 0
				AND pay_type.pay_type_id 
				THEN CASE
					WHEN `order_pay`.value IS NULL THEN order_item.value
					WHEN `order_pay`.value = `order`.value THEN order_item.`value`
					ELSE (order_item.`value` / 100) * ((`order_pay`.value / `order`.value) * 100)
				END - (CASE
					WHEN `order_pay`.value IS NULL THEN order_item.value
					WHEN `order_pay`.value = `order`.value THEN order_item.`value`
					ELSE (order_item.`value` / 100) * ((`order_pay`.value / `order`.value) * 100)
				END * pay_type.fee ) / 100
			
				-- TAXA
				ELSE CASE
					WHEN `order_pay`.value IS NULL THEN order_item.value
					WHEN `order_pay`.value = `order`.value THEN order_item.`value`
					ELSE (order_item.`value` / 100) * ((`order_pay`.value / `order`.value) * 100)
				END
			END + COALESCE(cte_product.value_product,0)  AS baseCalculo,
			
			CASE
				WHEN order_item.participation_type = '%' THEN	
				CASE
					WHEN vw_tax_insurance.value > 0
					AND vw_tax_discount_on_pay_type.value > 0
					AND vw_fee_discount_on_pay_type.value > 0
					AND pay_type.pay_type_id
					-- IMPOSTO CONVENIO / IMPOSTO PAGAMENTO / TAXA
			    THEN CASE
						WHEN `order_pay`.value IS NULL THEN order_item.value
						WHEN `order_pay`.value = `order`.value THEN order_item.value
						ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100)
					END - (CASE
						WHEN `order_pay`.value IS NULL THEN order_item.value
						WHEN `order_pay`.value = `order`.value THEN order_item.value
						ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100)
					END * (insurance_tax.medic + pay_type.tax + pay_type.fee)) / 100
					WHEN vw_tax_discount_on_pay_type.value > 0
					AND vw_fee_discount_on_pay_type.value > 0
					AND pay_type.pay_type_id
					--  IMPOSTO PAGAMENTO / TAXA
			    THEN CASE
						WHEN `order_pay`.value IS NULL THEN order_item.value
						WHEN `order_pay`.value = `order`.value THEN order_item.value
						ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100)
					END - (CASE
						WHEN `order_pay`.value IS NULL THEN order_item.value
						WHEN `order_pay`.value = `order`.value THEN order_item.value
						ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100)
					END * (pay_type.tax + pay_type.fee)) / 100
					WHEN vw_tax_discount_on_pay_type.value > 0
					AND vw_tax_insurance.value > 0
					AND pay_type.pay_type_id
					-- IMPOSTO PAGAMENTO / IMPOSTO CONVENIO
			    THEN CASE
						WHEN `order_pay`.value IS NULL THEN order_item.value
						WHEN `order_pay`.value = `order`.value THEN order_item.value
						ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100)
					END - (CASE
						WHEN `order_pay`.value IS NULL THEN order_item.value
						WHEN `order_pay`.value = `order`.value THEN order_item.value
						ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100)
					END * (pay_type.tax + insurance_tax.medic )) / 100
					WHEN vw_fee_discount_on_pay_type.value > 0
					AND vw_tax_insurance.value > 0
					AND pay_type.pay_type_id
					-- IMPOSTO CONVENIO / TAXA
			    THEN CASE
						WHEN `order_pay`.value IS NULL THEN order_item.value
						WHEN `order_pay`.value = `order`.value THEN order_item.value
						ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100)
					END - (CASE
						WHEN `order_pay`.value IS NULL THEN order_item.value
						WHEN `order_pay`.value = `order`.value THEN order_item.value
						ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100)
					END * (pay_type.fee + insurance_tax.medic )) / 100
					WHEN vw_tax_insurance.value > 0
			    THEN CASE
						WHEN `order_pay`.value IS NULL THEN order_item.value
						WHEN `order_pay`.value = `order`.value THEN order_item.value
						ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100)
					END - (CASE
						WHEN `order_pay`.value IS NULL THEN order_item.value
						WHEN `order_pay`.value = `order`.value THEN order_item.value
						ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100)
					END * insurance_tax.medic ) / 100
					-- IMPOSTO CONVENIO
					WHEN vw_tax_discount_on_pay_type.value > 0
					AND pay_type.pay_type_id
				THEN CASE
						WHEN `order_pay`.value IS NULL THEN order_item.value
						WHEN `order_pay`.value = `order`.value THEN order_item.value
						ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100)
					END - (CASE
						WHEN `order_pay`.value IS NULL THEN order_item.value
						WHEN `order_pay`.value = `order`.value THEN order_item.value
						ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100)
					END * pay_type.tax ) / 100
					-- IMPOSTO PAGAMENTO
					WHEN vw_fee_discount_on_pay_type.value > 0
					AND pay_type.pay_type_id
				THEN CASE
						WHEN `order_pay`.value IS NULL THEN order_item.value
						WHEN `order_pay`.value = `order`.value THEN order_item.value
						ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100)
					END - (CASE
						WHEN `order_pay`.value IS NULL THEN order_item.value
						WHEN `order_pay`.value = `order`.value THEN order_item.value
						ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100)
					END * pay_type.fee ) / 100
					-- TAXA
					ELSE
				(CASE
						WHEN `order_pay`.value IS NULL THEN order_item.value
						WHEN `order_pay`.value = `order`.value THEN order_item.value
						ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100)
					END) +  COALESCE(cte_product.value_product,0)
				END  * (order_item.participation_value / 100)
				WHEN order_item.participation_type = '$' THEN ((CASE 
				WHEN vw_tax_insurance.value > 0 AND  vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id  -- IMPOSTO CONVENIO / IMPOSTO PAGAMENTO / TAXA
			    THEN CASE
				WHEN `order_pay`.value IS NULL THEN order_item.value
				WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
				WHEN `order_pay`.value IS NULL THEN order_item.value
				WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * (insurance_tax.medic + pay_type.tax + pay_type.fee)) / 100 
			    WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id  --  IMPOSTO PAGAMENTO / TAXA
			    THEN CASE
				WHEN `order_pay`.value IS NULL THEN order_item.value
				WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
				WHEN `order_pay`.value IS NULL THEN order_item.value
				WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * (pay_type.tax + pay_type.fee)) / 100 
			    WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_tax_insurance.value > 0 AND pay_type.pay_type_id  -- IMPOSTO PAGAMENTO / IMPOSTO CONVENIO
			    THEN CASE
				WHEN `order_pay`.value IS NULL THEN order_item.value
				WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
				WHEN `order_pay`.value IS NULL THEN order_item.value
				WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * (pay_type.tax + insurance_tax.medic )) / 100 
			    WHEN vw_fee_discount_on_pay_type.value > 0 AND vw_tax_insurance.value > 0 AND pay_type.pay_type_id  -- IMPOSTO CONVENIO / TAXA
			    THEN CASE
				WHEN `order_pay`.value IS NULL THEN order_item.value
				WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
				WHEN `order_pay`.value IS NULL THEN order_item.value
				WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * (pay_type.fee + insurance_tax.medic )) / 100 
			    WHEN vw_tax_insurance.value > 0 
			    THEN CASE
				WHEN `order_pay`.value IS NULL THEN order_item.value
				WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
				WHEN `order_pay`.value IS NULL THEN order_item.value
				WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * insurance_tax.medic ) / 100 -- IMPOSTO CONVENIO
			    WHEN vw_tax_discount_on_pay_type.value > 0 AND pay_type.pay_type_id 
				THEN CASE
				WHEN `order_pay`.value IS NULL THEN order_item.value
				WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
				WHEN `order_pay`.value IS NULL THEN order_item.value
				WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * pay_type.tax ) / 100 -- IMPOSTO PAGAMENTO
				WHEN vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id 
				THEN CASE
				WHEN `order_pay`.value IS NULL THEN order_item.value
				WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
				WHEN `order_pay`.value IS NULL THEN order_item.value
				WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * pay_type.fee ) / 100  -- TAXA
				ELSE CASE
				WHEN `order_pay`.value IS NULL THEN order_item.value
				WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END END) *
				((`order_item`.participation_value / `order_item`.total) * 100 / 100)) ELSE .0 END  
				AS valorMedico
				
		
		-- Fim dos campos calculados
		
		FROM order_item
		
		LEFT JOIN `order` ON `order`.order_id = order_item.order_id
		LEFT JOIN company_cost_center ON company_cost_center.company_cost_center_id = `order`.company_cost_center_id
		LEFT JOIN company ON company.company_id = company_cost_center.company_id
		LEFT JOIN cost_center ON cost_center.cost_center_id = company_cost_center.cost_center_id
		LEFT JOIN performer ON performer.performer_id = order_item.performer_id
		LEFT JOIN professional ON professional.professional_id = performer.professional_id
		
		-- Parametros de Profissionais
		LEFT JOIN cte_parameters AS vw_tax_insurance ON vw_tax_insurance.name = 'DISCOUNT_TAX_ON_INSURANCE' AND vw_tax_insurance.key = professional.professional_id
		LEFT JOIN cte_parameters AS vw_tax_discount_on_pay_type ON vw_tax_discount_on_pay_type.name = 'DISCOUNT_TAX_ON_PAY_TYPE' AND vw_tax_discount_on_pay_type.key = professional.professional_id
		LEFT JOIN cte_parameters AS vw_fee_discount_on_pay_type ON vw_fee_discount_on_pay_type.name = 'DISCOUNT_FEE_ON_PAY_TYPE' AND vw_fee_discount_on_pay_type.key = professional.professional_id
		LEFT JOIN person person_performer ON person_performer.person_id = professional.person_id
		LEFT JOIN insurance ON insurance.insurance_id = `order`.insurance_id
		LEFT JOIN patient ON patient.patient_id = `order`.patient_id
		LEFT JOIN person person_patient ON person_patient.person_id = patient.person_id
						
		-- Verificar essa expressão acredito que vai projetar valores errados 
		LEFT JOIN insurance_tax ON insurance_tax.insurance_id = insurance.insurance_id
		LEFT JOIN speciality ON speciality.speciality_id = professional.speciality_id
		LEFT JOIN `procedure` ON `procedure`.procedure_id = order_item.procedure_id
		LEFT JOIN `group` ON `group`.group_id = `procedure`.group_id
		LEFT JOIN order_pay ON order_pay.order_id = `order`.`order_id`
		LEFT JOIN pay_type ON pay_type.pay_type_id = order_pay.pay_type_id
		LEFT JOIN session_item ON session_item.order_item_id = order_item.order_item_id 
		LEFT JOIN order_diagnosis ON order_diagnosis.order_id = `order`.order_id and order_diagnosis.main = 1
		LEFT JOIN cid ON cid.cid_id = order_diagnosis.cid_id
		LEFT JOIN cte_product ON cte_product.order_item_id = order_item.order_item_id 
		LEFT JOIN order_authorization on order_authorization.order_id = `order`.order_id
		LEFT JOIN currency ON currency.currency_id = order_item.currency_id
				WHERE 
		 $P!{performerRange}
 $P!{costCenterRange}
 $P!{firmRange}
  $P!{insuranceRange}
 $P!{userRange}
 $P!{dateRange}

		),
		
		cte_base_aux AS (
		
		SELECT 
	company.company_id,
	insurance.insurance_id,
	patient.patient_id,
	order_item.order_item_id,
    `order`.order_id,
	`order`.`number` AS guideNumber,
	person_performer.name AS performer_name,
	company.name AS company_name,
	insurance.name AS insuranre_name,
	order_item.`date` AS date_launch,
	person_patient.name AS patient_name,
	order_item.quantity AS procedure_quantity,
	order_item.name AS procedure_name,
	order_item.code AS procedure_code,
	order_authorization.release_password AS password,
	insurance.cnpj AS insurance_cnpj,
	currency.name AS currency,
	(SELECT 1 FROM order_item_fee WHERE  order_item_fee.order_item_id = order_item.order_item_id 
		UNION 
	 SELECT 1 FROM order_item_product WHERE order_item_product.order_item_id = order_item.order_item_id
		UNION 
	 SELECT 1 FROM order_item_auxiliary WHERE order_item_auxiliary.order_item_id = order_item.order_item_id) AS isProduct,
		-- Valor Total 			
		order_item_auxiliary.value AS value,
		order_item_auxiliary.value AS total,
			
		-- Inicio dos campos Calculados
		CASE 
        WHEN vw_tax_insurance.value > 0 THEN 
            CASE 
                WHEN order_item_auxiliary.tax_type = '%' THEN	
                    (order_item_auxiliary.value - 
                    (order_item_auxiliary.value * COALESCE(order_item_auxiliary.tax_value, 0) / 100)) 
                WHEN order_item_auxiliary.tax_type = '$' THEN	
                    order_item_auxiliary.value
                ELSE 
                    order_item_auxiliary.value 
            END
        ELSE 
            order_item_auxiliary.value  -- Ou outro valor padrão, se necessário
    END AS baseCalculo,	 
		
		CASE 
        WHEN vw_tax_insurance.value > 0 THEN 
            CASE 
                WHEN order_item_auxiliary.tax_type = '%' THEN	
                    (order_item_auxiliary.value - 
                    (order_item_auxiliary.value * COALESCE(order_item_auxiliary.tax_value, 0) / 100)) 
                WHEN order_item_auxiliary.tax_type = '$' THEN	
                    order_item_auxiliary.value
                ELSE 
                    order_item_auxiliary.value 
            END
        ELSE 
            order_item_auxiliary.value  -- Ou outro valor padrão, se necessário
    END AS valorMedico	
		
		-- Fim dos campos calculados
		
		FROM order_item_auxiliary
		
				-- Parametros de Auxiliares
		LEFT JOIN order_item ON order_item_auxiliary.order_item_id = order_item.order_item_id 
		LEFT JOIN auxiliary ON auxiliary.auxiliary_id = order_item_auxiliary.auxiliary_id 
		LEFT JOIN performer AS performer_auxiliary ON performer_auxiliary.performer_id = order_item_auxiliary.performer_id 
		LEFT JOIN professional AS professional_auxiliary ON professional_auxiliary.professional_id = performer_auxiliary.professional_id 
		LEFT JOIN person AS person_auxiliary ON person_auxiliary.person_id = professional_auxiliary.person_id 
		
		LEFT JOIN `order` ON `order`.order_id = order_item.order_id
		LEFT JOIN company_cost_center ON company_cost_center.company_cost_center_id = `order`.company_cost_center_id
		LEFT JOIN company ON company.company_id = company_cost_center.company_id
		LEFT JOIN cost_center ON cost_center.cost_center_id = company_cost_center.cost_center_id
		LEFT JOIN performer ON performer.performer_id = order_item.performer_id
		LEFT JOIN professional ON professional.professional_id = performer.professional_id
		
		-- Parametros de Profissionais
		LEFT JOIN cte_parameters AS vw_tax_insurance ON vw_tax_insurance.name = 'DISCOUNT_TAX_ON_INSURANCE' AND vw_tax_insurance.key = professional_auxiliary.professional_id
		LEFT JOIN cte_parameters AS vw_tax_discount_on_pay_type ON vw_tax_discount_on_pay_type.name = 'DISCOUNT_TAX_ON_PAY_TYPE' AND vw_tax_discount_on_pay_type.key = professional.professional_id
		LEFT JOIN cte_parameters AS vw_fee_discount_on_pay_type ON vw_fee_discount_on_pay_type.name = 'DISCOUNT_FEE_ON_PAY_TYPE' AND vw_fee_discount_on_pay_type.key = professional.professional_id
		LEFT JOIN person person_performer ON person_performer.person_id = professional.person_id
		LEFT JOIN insurance ON insurance.insurance_id = `order`.insurance_id
		LEFT JOIN patient ON patient.patient_id = `order`.patient_id
		LEFT JOIN person person_patient ON person_patient.person_id = patient.person_id
				
		-- Verificar essa expressão acredito que vai projetar valores errados 
		LEFT JOIN insurance_tax ON insurance_tax.insurance_id = insurance.insurance_id
		LEFT JOIN speciality ON speciality.speciality_id = professional.speciality_id
		LEFT JOIN `procedure` ON `procedure`.procedure_id = order_item.procedure_id
		LEFT JOIN `group` ON `group`.group_id = `procedure`.group_id
		LEFT JOIN order_pay ON order_pay.order_id = `order`.`order_id`
		LEFT JOIN pay_type ON pay_type.pay_type_id = order_pay.pay_type_id
		LEFT JOIN session_item ON session_item.order_item_id = order_item.order_item_id 
		LEFT JOIN order_diagnosis ON order_diagnosis.order_id = `order`.order_id and order_diagnosis.main = 1
		LEFT JOIN cid ON cid.cid_id = order_diagnosis.cid_id
		LEFT JOIN order_authorization on order_authorization.order_id = `order`.order_id
		LEFT JOIN currency ON currency.currency_id = order_item.currency_id
 		WHERE 
		 $P!{performerRange}
 $P!{costCenterRange}
 $P!{firmRange}
  $P!{insuranceRange}
 $P!{userRange}
 $P!{dateRange}

		),
		
		cte_base_third_party AS (
		
		SELECT 
	company.company_id,
	insurance.insurance_id,
	patient.patient_id,
	order_item.order_item_id,
    `order`.order_id,
	`order`.`number` AS guideNumber,
	person_performer.name AS performer_name,
	company.name AS company_name,
	insurance.name AS insuranre_name,
	order_item.`date` AS date_launch,
	person_patient.name AS patient_name,
	order_item.quantity AS procedure_quantity,
	order_item.name AS procedure_name,
	order_item.code AS procedure_code,
	order_authorization.release_password AS password,
	insurance.cnpj AS insurance_cnpj,
	currency.name AS currency,
	(SELECT 1 FROM order_item_fee WHERE  order_item_fee.order_item_id = order_item.order_item_id 
		UNION 
	 SELECT 1 FROM order_item_product WHERE order_item_product.order_item_id = order_item.order_item_id
		UNION 
	 SELECT 1 FROM order_item_auxiliary WHERE order_item_auxiliary.order_item_id = order_item.order_item_id) AS isProduct,
		-- Valor Total 			
		NULL AS value,
		NULL AS total,
			
		-- Inicio dos campos Calculados
		NULL AS baseCalculo,	 
		
		order_item_third_party.value AS valorMedico
		
		-- Fim dos campos calculados
		
		FROM order_item_third_party
		
				-- Parametros de Auxiliares
		LEFT JOIN order_item ON order_item.order_item_id = order_item_third_party.order_item_id 
		LEFT JOIN third_party ON third_party.third_party_id = order_item_third_party.third_party_id 
		LEFT JOIN performer ON performer.performer_id = third_party.performer_id 
		LEFT JOIN professional AS professional_third_party ON professional_third_party.professional_id = performer.professional_id 
		LEFT JOIN person AS person_third_party ON person_third_party.person_id = professional_third_party.person_id 
		
		LEFT JOIN `order` ON `order`.order_id = order_item.order_id
		LEFT JOIN company_cost_center ON company_cost_center.company_cost_center_id = `order`.company_cost_center_id
		LEFT JOIN company ON company.company_id = company_cost_center.company_id
		LEFT JOIN cost_center ON cost_center.cost_center_id = company_cost_center.cost_center_id
		LEFT JOIN professional ON professional.professional_id = performer.professional_id
		
		-- Parametros de Profissionais
		LEFT JOIN cte_parameters AS vw_tax_insurance ON vw_tax_insurance.name = 'DISCOUNT_TAX_ON_INSURANCE' AND vw_tax_insurance.key = professional.professional_id
		LEFT JOIN cte_parameters AS vw_tax_discount_on_pay_type ON vw_tax_discount_on_pay_type.name = 'DISCOUNT_TAX_ON_PAY_TYPE' AND vw_tax_discount_on_pay_type.key = professional.professional_id
		LEFT JOIN cte_parameters AS vw_fee_discount_on_pay_type ON vw_fee_discount_on_pay_type.name = 'DISCOUNT_FEE_ON_PAY_TYPE' AND vw_fee_discount_on_pay_type.key = professional.professional_id
		LEFT JOIN person person_performer ON person_performer.person_id = professional.person_id
		LEFT JOIN insurance ON insurance.insurance_id = `order`.insurance_id
		LEFT JOIN patient ON patient.patient_id = `order`.patient_id
		LEFT JOIN person person_patient ON person_patient.person_id = patient.person_id
				
		-- Verificar essa expressão acredito que vai projetar valores errados 
		LEFT JOIN insurance_tax ON insurance_tax.insurance_id = insurance.insurance_id
		LEFT JOIN speciality ON speciality.speciality_id = professional.speciality_id
		LEFT JOIN `procedure` ON `procedure`.procedure_id = order_item.procedure_id
		LEFT JOIN `group` ON `group`.group_id = `procedure`.group_id
		LEFT JOIN order_pay ON order_pay.order_id = `order`.`order_id`
		LEFT JOIN pay_type ON pay_type.pay_type_id = order_pay.pay_type_id
		LEFT JOIN session_item ON session_item.order_item_id = order_item.order_item_id 
		LEFT JOIN order_diagnosis ON order_diagnosis.order_id = `order`.order_id and order_diagnosis.main = 1
		LEFT JOIN cid ON cid.cid_id = order_diagnosis.cid_id
		LEFT JOIN order_authorization on order_authorization.order_id = `order`.order_id
		LEFT JOIN currency ON currency.currency_id = order_item.currency_id
		WHERE 
		 $P!{performerRange}
 $P!{costCenterRange}
 $P!{firmRange}
  $P!{insuranceRange}
 $P!{userRange}
 $P!{dateRange}


		)
		
	SELECT 
		cte_base.*
	FROM cte_base
	
	UNION ALL
	
	SELECT 
		cte_base_aux.*
	FROM cte_base_aux
	
	UNION ALL
	
	SELECT 
		cte_base_third_party.*
	FROM cte_base_third_party
	
	  
 	ORDER BY 1,2,3,4]]>
	</queryString>
	<field name="company_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="company_id"/>
		<property name="com.jaspersoft.studio.field.label" value="company_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="company"/>
		<fieldDescription><![CDATA[Id Empresa]]></fieldDescription>
	</field>
	<field name="insurance_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="insurance_id"/>
		<property name="com.jaspersoft.studio.field.label" value="insurance_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="insurance"/>
		<fieldDescription><![CDATA[Id do Convênio]]></fieldDescription>
	</field>
	<field name="patient_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="patient_id"/>
		<property name="com.jaspersoft.studio.field.label" value="patient_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="patient"/>
		<fieldDescription><![CDATA[Id do Paciente]]></fieldDescription>
	</field>
	<field name="order_item_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="order_item_id"/>
		<property name="com.jaspersoft.studio.field.label" value="order_item_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
		<fieldDescription><![CDATA[Id do Item da Conta]]></fieldDescription>
	</field>
	<field name="order_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="order_id"/>
		<property name="com.jaspersoft.studio.field.label" value="order_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
		<fieldDescription><![CDATA[Id da Ordem de Serviço (Conta Hospitalar)]]></fieldDescription>
	</field>
	<field name="guideNumber" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="guideNumber"/>
		<property name="com.jaspersoft.studio.field.label" value="guideNumber"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
	</field>
	<field name="performer_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="performer_name"/>
		<property name="com.jaspersoft.studio.field.label" value="performer_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="company_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="company_name"/>
		<property name="com.jaspersoft.studio.field.label" value="company_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="company"/>
	</field>
	<field name="insuranre_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="insuranre_name"/>
		<property name="com.jaspersoft.studio.field.label" value="insuranre_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="insurance"/>
	</field>
	<field name="date_launch" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.name" value="date_launch"/>
		<property name="com.jaspersoft.studio.field.label" value="date_launch"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="patient_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="patient_name"/>
		<property name="com.jaspersoft.studio.field.label" value="patient_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="procedure_quantity" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="procedure_quantity"/>
		<property name="com.jaspersoft.studio.field.label" value="procedure_quantity"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="procedure_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="procedure_name"/>
		<property name="com.jaspersoft.studio.field.label" value="procedure_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="procedure_code" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="procedure_code"/>
		<property name="com.jaspersoft.studio.field.label" value="procedure_code"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="password" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="password"/>
		<property name="com.jaspersoft.studio.field.label" value="password"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_authorization"/>
	</field>
	<field name="insurance_cnpj" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="insurance_cnpj"/>
		<property name="com.jaspersoft.studio.field.label" value="insurance_cnpj"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="insurance"/>
	</field>
	<field name="currency" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="currency"/>
		<property name="com.jaspersoft.studio.field.label" value="currency"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="currency"/>
	</field>
	<field name="isProduct" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.name" value="isProduct"/>
		<property name="com.jaspersoft.studio.field.label" value="isProduct"/>
	</field>
	<field name="value" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="value"/>
		<property name="com.jaspersoft.studio.field.label" value="value"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
		<fieldDescription><![CDATA[Valor Unitário]]></fieldDescription>
	</field>
	<field name="total" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="total"/>
		<property name="com.jaspersoft.studio.field.label" value="total"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
		<fieldDescription><![CDATA[Total do Item]]></fieldDescription>
	</field>
	<field name="baseCalculo" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="baseCalculo"/>
		<property name="com.jaspersoft.studio.field.label" value="baseCalculo"/>
	</field>
	<field name="valorMedico" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="valorMedico"/>
		<property name="com.jaspersoft.studio.field.label" value="valorMedico"/>
	</field>
	<variable name="total1" class="java.lang.Double" resetType="Group" resetGroup="patient" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="total2" class="java.lang.Double" resetType="Group" resetGroup="insurance" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="total3" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="total4" class="java.lang.Double" resetType="Group" resetGroup="company" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="total5" class="java.lang.Double" resetType="Group" resetGroup="patient" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="total6" class="java.lang.Double" resetType="Group" resetGroup="insurance" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="total7" class="java.lang.Double" resetType="Group" resetGroup="company" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="total8" class="java.lang.Double" resetType="Group" resetGroup="order_item" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="total9" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="value1" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{value}]]></variableExpression>
	</variable>
	<group name="company" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{company_id}]]></groupExpression>
		<groupHeader>
			<band height="19">
				<line>
					<reportElement x="0" y="1" width="555" height="1" uuid="bfdc87ba-ade3-4c77-995c-2d34af90f7c2">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<staticText>
					<reportElement x="4" y="5" width="56" height="14" uuid="d72303f2-736b-4b8c-bee3-d8c44799fef5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Unidade:]]></text>
				</staticText>
				<textField>
					<reportElement x="60" y="5" width="468" height="14" uuid="54f0aedd-6153-455a-afaa-c2b0752bbc6c">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{company_name}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="13">
				<staticText>
					<reportElement x="4" y="3" width="96" height="10" uuid="efcb0784-fe3c-45e3-999d-4a6fe47db862">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Total da Unidade:]]></text>
				</staticText>
				<line>
					<reportElement x="0" y="1" width="555" height="1" uuid="e858be0e-fcbf-4025-8754-0b6f2cb3b44c">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="510" y="2" width="41" height="9" uuid="6e970e38-418b-4c61-971e-a45bef9d6c7a">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{total7}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="insurance" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{insurance_id}]]></groupExpression>
		<groupHeader>
			<band height="37">
				<line>
					<reportElement x="0" y="1" width="555" height="1" uuid="5f786713-4970-413a-afe8-b092a49b31a6">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<staticText>
					<reportElement x="4" y="3" width="56" height="10" uuid="2e887e07-8b05-4817-b82f-45506a469a61">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Convênio:]]></text>
				</staticText>
				<textField>
					<reportElement x="60" y="3" width="454" height="10" uuid="f7686787-27d1-4fa9-942e-971831d937b8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{insuranre_name}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="4" y="15" width="56" height="10" uuid="ef8c31b1-e4d4-41f6-b3aa-fdf217415d42">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[CNPJ:]]></text>
				</staticText>
				<staticText>
					<reportElement x="4" y="27" width="32" height="10" uuid="5b372dd9-1a84-4508-84af-3d6081aa556b">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Data]]></text>
				</staticText>
				<staticText>
					<reportElement x="39" y="27" width="50" height="10" uuid="65d50393-e5cc-4637-aacd-2f9ff545b65f">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Codigo]]></text>
				</staticText>
				<staticText>
					<reportElement x="93" y="27" width="157" height="10" uuid="c362f59d-40ed-48aa-b459-087459330f30">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Serviço]]></text>
				</staticText>
				<staticText>
					<reportElement x="252" y="27" width="116" height="10" uuid="d3e2d439-fd7d-4b86-a4f4-4903fbfe6c8b">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Médico]]></text>
				</staticText>
				<staticText>
					<reportElement x="370" y="27" width="40" height="10" uuid="658fab67-3085-4b7c-9efd-6bea3d1c35d9">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Tipo]]></text>
				</staticText>
				<staticText>
					<reportElement x="433" y="27" width="48" height="10" uuid="9bb21f3f-a674-42a0-b8e8-0093a62dd472">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<printWhenExpression><![CDATA[$P{printCalculationBasis}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[/- Indexador -\]]></text>
				</staticText>
				<staticText>
					<reportElement x="515" y="27" width="36" height="10" uuid="78a0352c-7964-4cbd-9071-d37e833f4a17">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Valor(R$)]]></text>
				</staticText>
				<staticText>
					<reportElement x="483" y="27" width="30" height="10" uuid="d760d93f-9c16-4e8d-8fe3-1b68d2cd8857">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<printWhenExpression><![CDATA[$P{printValueClinical}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Vl.unit.]]></text>
				</staticText>
				<staticText>
					<reportElement x="412" y="27" width="19" height="10" uuid="1a891bb3-553c-4e27-b3c5-3349d06ed1c0">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Qtd.]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="13">
				<staticText>
					<reportElement x="4" y="3" width="96" height="10" uuid="b3bdace5-b627-420e-b27a-5b481282cbd6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Total do Convênio:]]></text>
				</staticText>
				<line>
					<reportElement x="0" y="1" width="555" height="1" uuid="81a83dc8-cb3a-44ef-b6d2-3598694aaadb">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="510" y="2" width="41" height="9" uuid="b02fe839-66bc-4efa-9eed-e5a887ad9ae2">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{total6}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="patient" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{patient_id}]]></groupExpression>
		<groupHeader>
			<band height="16">
				<staticText>
					<reportElement x="4" y="2" width="34" height="14" uuid="7482fac4-3c1f-468a-b675-494b1f07fdfa">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Paciente:]]></text>
				</staticText>
				<textField>
					<reportElement x="38" y="2" width="188" height="14" uuid="1f37b7ba-d89b-4a79-b399-22dd838338d5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{patient_name}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="0" width="555" height="1" uuid="a9661ec0-8a97-48ac-a110-ada66f6e5574">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
				</line>
				<staticText>
					<reportElement x="282" y="2" width="28" height="14" uuid="0d6a72a4-f113-493b-9ee5-d862deb17e16">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Senha:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="310" y="2" width="140" height="14" uuid="2acd03c1-8ee0-400f-8dfe-ce9e3b001a26"/>
					<textElement>
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{password}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="13">
				<staticText>
					<reportElement x="4" y="3" width="96" height="10" uuid="9d6c9fac-5677-4631-923d-0a314b526b00">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Total do Paciente:]]></text>
				</staticText>
				<line>
					<reportElement x="0" y="1" width="555" height="1" uuid="d111de0f-7416-493d-a60f-b9f4e3e8ae36">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="510" y="2" width="41" height="9" uuid="6f872846-d9cf-4b55-a782-83aa4ae221e1">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{total5}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="order_item" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{order_item_id}]]></groupExpression>
		<groupHeader>
			<band height="12">
				<textField isBlankWhenNull="true">
					<reportElement x="412" y="2" width="19" height="9" uuid="0056e0d0-f741-4e7e-b2fc-342f9786074f">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="6" isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{procedure_quantity}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="93" y="2" width="157" height="9" uuid="a033d999-0f1b-4835-85db-d3eee04f8c8b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="6" isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{procedure_name}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="252" y="2" width="116" height="9" uuid="6cef5fd7-3950-4f21-94f9-32372b739da8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="6" isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{performer_name}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="39" y="2" width="50" height="9" uuid="37e2cefd-41c1-4615-b88b-312bf8fa280e">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="6" isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{procedure_code}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="4" y="2" width="32" height="9" uuid="249a44b1-33fe-4750-a7d8-bc3b8208609e">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="5" isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{date_launch}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="515" y="2" width="36" height="10" uuid="55f05e83-f123-4e53-9016-b28a3837e3b4">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{total}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="433" y="2" width="48" height="10" uuid="553b426d-1541-49a8-ba4c-c9d2b1135e0c"/>
					<textElement>
						<font fontName="Arial" size="6" isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{currency}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="483" y="2" width="30" height="10" uuid="d5733840-115c-4408-bd98-d3f95acd4132">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{value}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<pageHeader>
		<band height="94">
			<textField isBlankWhenNull="true">
				<reportElement x="86" y="31" width="339" height="25" uuid="321f1f63-b46c-439c-a430-8165e08bf9f1">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{nameReport}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy">
				<reportElement x="491" y="11" width="60" height="15" uuid="e11903a3-920d-48d0-b89c-80f2ee64d61c"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="521" y="39" width="30" height="15" uuid="cb3aa02c-7575-4b86-87c8-9000228b885f"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="428" y="11" width="58" height="14" uuid="ee7dd153-6548-48cc-b06c-cefec483ceb3"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Data:]]></text>
			</staticText>
			<staticText>
				<reportElement x="428" y="39" width="58" height="14" uuid="ed407f09-8cb1-4d5a-9121-132a6a2aad1b"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Página:]]></text>
			</staticText>
			<image hAlign="Center" vAlign="Middle">
				<reportElement x="-9" y="4" width="85" height="40" uuid="59c84930-55aa-4a7a-af69-2e386701470d"/>
				<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64(new String($P{companyImage}).getBytes("UTF-8")))]]></imageExpression>
			</image>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="6" y="75" width="542" height="16" uuid="6402bda8-77f9-4af2-9180-b51b332e81fc"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{filters}.isEmpty() ? null : $P{filters}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="86" y="62" width="339" height="13" uuid="7e73f7dc-2685-4299-b5ab-d4d53f2963a1">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Filtros Selecionados]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="86" y="2" width="339" height="30" uuid="10c97d4d-01dd-4674-bd38-c91df8d39112">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{companyName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="428" y="25" width="58" height="14" uuid="5caec54e-2731-4c4a-a32d-a4fac0113823"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Hora:]]></text>
			</staticText>
			<textField pattern="HH:mm">
				<reportElement x="491" y="24" width="60" height="15" uuid="f0d82f9d-3c9a-45b4-9db8-4ea0b4bd592e"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="19">
			<printWhenExpression><![CDATA[$F{isProduct} == 1]]></printWhenExpression>
			<subreport>
				<reportElement x="0" y="0" width="555" height="19" isPrintWhenDetailOverflows="true" uuid="e727fb31-a7d0-49c3-beb4-e0565d49afd5"/>
				<subreportParameter name="orderItemId">
					<subreportParameterExpression><![CDATA[$F{order_item_id}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["reports/order/application-report-order-attendance-insurance-analytical/sub-reportjrxml.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<summary>
		<band height="13">
			<staticText>
				<reportElement x="4" y="3" width="96" height="10" uuid="2440aecd-4f02-4d53-802e-aa4b5b49dfd8">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Geral:]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="1" width="555" height="1" uuid="c8e6595c-5061-440f-b39a-4e7eeb3865fc">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<textField pattern="#,##0.00">
				<reportElement x="510" y="2" width="41" height="9" uuid="cf798cc1-d0cf-4b6a-bad9-43e8f4a5c159">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{total9}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
	<noData>
		<band height="79">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="555" height="79" uuid="818316e3-96c5-4073-b58e-30943ab6120b"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="26" isBold="true"/>
				</textElement>
				<text><![CDATA[Não há dados para os filtros selecionados.]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
