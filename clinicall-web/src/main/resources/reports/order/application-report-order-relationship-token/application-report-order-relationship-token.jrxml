<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="application-report-order-relationship-token" pageWidth="841" pageHeight="595" orientation="Landscape" whenNoDataType="NoDataSection" columnWidth="801" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4ff84453-1320-4ac5-a9ad-242cce5098f5">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="0"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="1000"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_mundus_psi"/>
	<style name="RedTextStyle" forecolor="#FF0000"/>
	<subDataset name="Dataset1" uuid="7fe0c814-75e0-454d-bbae-8410466db122">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MySql "/>
		<queryString language="SQL">
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="filters" class="java.util.ArrayList"/>
	<parameter name="nameReport" class="java.lang.String"/>
	<parameter name="costCenter" class="java.util.ArrayList"/>
	<parameter name="performer" class="java.util.ArrayList"/>
	<parameter name="dataInicio" class="java.lang.String"/>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="user" class="java.util.ArrayList"/>
	<parameter name="costCenterStart" class="java.lang.String"/>
	<parameter name="costCenterEnd" class="java.lang.String"/>
	<parameter name="performerStart" class="java.lang.String"/>
	<parameter name="performerEnd" class="java.lang.String"/>
	<parameter name="performerRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "$X{IN,performer.performer_id, performer} " :
"person_performer.`name`  >= " +"$P" +"{performerStart}"+" AND person_performer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="costCenterRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{costCenterStart} == null || $P{costCenterEnd} == null ? "and $X{IN,cost_center.cost_center_id, costCenter} " :
"and cost_center.`name`  >= " +"$P" +"{costCenterStart}"+" AND cost_center.`name` <= " + "$P" + "{costCenterEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{userStart} == null || $P{userEnd} == null ? "and $X{IN,user_order.user_id, user} " :
"and person_user.`name`  >= " +"$P" +"{userStart}"+" AND person_user.`name` <= " + "$P" + "{userEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userEnd" class="java.lang.String"/>
	<parameter name="userStart" class="java.lang.String"/>
	<parameter name="dateRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{dataInicio} == null || $P{dataFim} == null ? " " :
"AND `order`.`date` BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="firm" class="java.util.ArrayList"/>
	<parameter name="firmStart" class="java.lang.String"/>
	<parameter name="firmEnd" class="java.lang.String"/>
	<parameter name="firmRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{firmStart} == null || $P{firmEnd} == null ? "and  $X{IN,company.company_id, firm} " :
"and company.`name`  >= " +"$P" +"{firmStart}"+" AND company.`name` <= " + "$P" + "{firmEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="insurance" class="java.util.ArrayList"/>
	<parameter name="insuranceStart" class="java.lang.String"/>
	<parameter name="insuranceEnd" class="java.lang.String"/>
	<parameter name="insuranceRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{insuranceStart} == null || $P{insuranceEnd} == null ? "and  $X{IN,`insurance`.`insurance_id`, insurance} " :
"and `insurance`.`name`  >= " +"$P" +"{insuranceStart}"+" AND `insurance`.`name` <= " + "$P" + "{insuranceEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="onlyGerarateCash" class="java.util.ArrayList"/>
	<parameter name="notGerarateCash" class="java.util.ArrayList"/>
	<parameter name="onlyGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{onlyGerarateCash} ==  null ?  " " : "and  $X{IN,insurance.payment_model, onlyGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="notGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{notGerarateCash} ==  null ? " " : "and  $X{IN,insurance.payment_model, notGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="groupProcedure" class="java.util.ArrayList"/>
	<parameter name="groupProcedureStart" class="java.lang.String"/>
	<parameter name="groupProcedureEnd" class="java.lang.String"/>
	<parameter name="groupProcedureRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{groupProcedureStart} == null || $P{groupProcedureEnd} == null ? "and  $X{IN, `group`.group_id, groupProcedure} " :
"and `group`.`name`  >= " +"$P" +"{groupProcedureStart}"+" AND `group`.`name` <= " + "$P" + "{groupProcedureEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="patient" class="java.util.ArrayList"/>
	<parameter name="patientStart" class="java.lang.String"/>
	<parameter name="patientEnd" class="java.lang.String"/>
	<parameter name="patientRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{patientStart} == null || $P{patientEnd} == null ? "and $X{IN,patient.patient_id, patient} " :
"and person_patient.`name`  >= " +"$P" +"{patientStart}"+" AND person_patient.`name` <= " + "$P" + "{patientEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="procedureEnd" class="java.lang.String"/>
	<parameter name="procedureStart" class="java.lang.String"/>
	<parameter name="procedure" class="java.util.ArrayList"/>
	<parameter name="procedureRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{procedureStart} == null || $P{procedureEnd} == null ? "and $X{IN,`procedure`.procedure_id, procedure} " :
"and `procedure`.`name`  >= " +"$P" +"{procedureStart}"+" AND `procedure`.`name` <= " + "$P" + "{procedureEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="blocked" class="java.util.ArrayList"/>
	<parameter name="pending" class="java.util.ArrayList"/>
	<parameter name="blockedRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{blocked} == null ? " " : "and  $X{IN,`order`.state, blocked} "]]></defaultValueExpression>
	</parameter>
	<parameter name="pendingRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{pending} == null ?  " " : "and  $X{IN,`order`.state, pending} "]]></defaultValueExpression>
	</parameter>
	<parameter name="normal" class="java.util.ArrayList"/>
	<parameter name="normalRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{normal} == null ?  " " : "and  $X{IN,`order`.state, normal} "]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT 
  `person`.`name`	      								as 'Paciente'		-- Nome do Paciente
, `order`.`number`		 								as 'Num Guia'		-- Número da Guia 
,DATE_FORMAT( `order_authorization`.`release_date`, '%d-%m-%Y')	 				as 'Dt Auth' 	    -- Data da autorização
, sum(`order_item`.quantity) 		 					as 'Qtd'		    -- Quantidades de Consultas da guia
, case 
	when `order_item_tk1`.`date` is not null and `tk1`.token is null then 'Não possui'
	when `order_item_tk1`.`date` is not null and `tk1`.token is not null then DATE_FORMAT( `order_item_tk1`.`date`, '%d-%m-%Y')
	when `order_item_tk1`.`date` is null then 'Não precisa'
  end
							 							as 'Dt tk1'			-- Dt. TK 1
, case 
	when `order_item_tk2`.`date` is not null and `tk2`.token is null then 'Não possui'
	when `order_item_tk2`.`date` is not null and `tk2`.token is not null then DATE_FORMAT( `order_item_tk2`.`date`, '%d-%m-%Y')
	when `order_item_tk2`.`date` is null then 'Não precisa'
  end
  														as 'Dt tk2'			-- Dt. TK 2
, case 
	when `order_item_tk3`.`date` is not null and `tk3`.token is null then 'Não possui'
	when `order_item_tk3`.`date` is not null and `tk3`.token is not null then DATE_FORMAT( `order_item_tk3`.`date`, '%d-%m-%Y')
	when `order_item_tk3`.`date` is null then 'Não precisa'
  end
  														as 'Dt tk3'			-- Dt. TK 3
, case 
	when `order_item_tk4`.`date` is not null and `tk4`.token is null then 'Não possui'
	when `order_item_tk4`.`date` is not null and `tk4`.token is not null then DATE_FORMAT( `order_item_tk4`.`date`, '%d-%m-%Y')
	when `order_item_tk4`.`date` is null then 'Não precisa'
  end
  														as 'Dt tk4'			-- Dt. TK 4
, case 
	when `order_item_tk5`.`date` is not null and `tk5`.token is null then 'Não possui'
	when `order_item_tk5`.`date` is not null and `tk5`.token is not null then DATE_FORMAT( `order_item_tk5`.`date`, '%d-%m-%Y')
	when `order_item_tk5`.`date` is null then 'Não precisa'
  end
  														as 'Dt tk5'			-- Dt. TK 5
, `person_professional`.`name`							as 'Especialista'	-- Nome do especialista
, `person_user`.`name` 	 								as 'Administrador'  -- Nome do estagiário
, `insurance`.name						    			as 'Convênio'		-- Plano de Saúde
From `order`
LEFT JOIN `order_item` 				on `order_item`.`order_id`  = `order`.`order_id`
--
LEFT JOIN `order_item` as `order_item_tk1` on `order_item_tk1`.order_id = `order`.`order_id` and `order_item_tk1`.`sequence` = 1
LEFT JOIN `order_item_tiss` as `tk1`	   on `tk1`.`order_item_id` = `order_item_tk1`.`order_item_id`
--
LEFT JOIN `order_item` as `order_item_tk2` on `order_item_tk2`.order_id = `order`.`order_id` and `order_item_tk2`.`sequence` = 2
LEFT JOIN `order_item_tiss` as `tk2`	   on `tk2`.`order_item_id` = `order_item_tk2`.`order_item_id`
--
LEFT JOIN `order_item` as `order_item_tk3` on `order_item_tk3`.order_id = `order`.`order_id` and `order_item_tk3`.`sequence` = 3
LEFT JOIN `order_item_tiss` as `tk3`	   on `tk3`.`order_item_id` = `order_item_tk3`.`order_item_id`
--
LEFT JOIN `order_item` as `order_item_tk4` on `order_item_tk4`.order_id = `order`.`order_id` and `order_item_tk4`.`sequence` = 4
LEFT JOIN `order_item_tiss` as `tk4`	   on `tk4`.`order_item_id` = `order_item_tk4`.`order_item_id`
--
LEFT JOIN `order_item` as `order_item_tk5` on `order_item_tk5`.order_id = `order`.`order_id` and `order_item_tk5`.`sequence` = 5
LEFT JOIN `order_item_tiss` as `tk5`	   on `tk5`.`order_item_id` = `order_item_tk5`.`order_item_id`
--
LEFT JOIN `order_authorization` 		   on `order_authorization`.`order_id` = `order`.`order_id`
LEFT JOIN `patient` 					   on `patient`.patient_id = `order`.patient_id
LEFT JOIN `person` 						   on `patient`.person_id = `person`.person_id
LEFT JOIN `user`						   on `person`.person_id = `user`.person_id 
LEFT JOIN `insurance` 					   on `order`.insurance_id = `insurance`.insurance_id 
LEFT JOIN `performer`				   	   on `order_item`.performer_id = `performer`.performer_id
LEFT JOIN `professional`				   on `performer`.professional_id = `professional`.professional_id 
--
LEFT JOIN `parameter` 					   on `parameter`.`name` = 'USAGE_TOKEN' 
LEFT JOIN `parameter_value`				   on `parameter`.`parameter_id` = `parameter_value`.`parameter_id` and `parameter_value`.`key` = `insurance`.insurance_id  
--
LEFT JOIN `parameter` as `parameter_specialist` 				on `parameter_specialist`.`name` = 'SPECIALIST'
LEFT JOIN `parameter_value` as `parameter_value_specialist` 	on `parameter_value_specialist`.parameter_id = `parameter_specialist`.parameter_id and `parameter_value_specialist`.key = `patient`.patient_id
LEFT JOIN `professional` as `professional_specialist`			on `parameter_value_specialist`.`value` = `professional_specialist`.professional_id 
LEFT JOIN `person` as `person_professional` 			    	on `professional_specialist`.`person_id` = `person_professional`.person_id
--
LEFT JOIN `parameter` as `parameter_administrator` 				on `parameter_administrator`.`name` = 'ADMINISTRATOR'
LEFT JOIN `parameter_value` as `parameter_value_administrator`  on `parameter_value_administrator`.parameter_id = `parameter_administrator`.parameter_id and `parameter_value_administrator`.key = `patient`.patient_id
LEFT JOIN `user` as `user_estagiario` 			    			on `parameter_value_administrator`.`value` = `user_estagiario`.user_id
LEFT JOIN `person` as `person_user`								on `user_estagiario`.person_id = `person_user`.person_id
-- 
where  `parameter_value`.value = 1 
 $P!{insuranceRange}
 $P!{dateRange}  
group by 2,1,3,5,6,7,8,9,10,11,12;]]>
	</queryString>
	<field name="Paciente" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="Paciente"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="Num Guia" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="number"/>
		<property name="com.jaspersoft.studio.field.label" value="Num Guia"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
	</field>
	<field name="Dt Auth" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="Dt Auth"/>
		<property name="com.jaspersoft.studio.field.label" value="Dt Auth"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_authorization"/>
	</field>
	<field name="Qtd" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="Qtd"/>
		<property name="com.jaspersoft.studio.field.label" value="Qtd"/>
	</field>
	<field name="Dt tk1" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="Dt tk1"/>
		<property name="com.jaspersoft.studio.field.label" value="Dt tk1"/>
	</field>
	<field name="Dt tk2" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="Dt tk2"/>
		<property name="com.jaspersoft.studio.field.label" value="Dt tk2"/>
	</field>
	<field name="Dt tk3" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="Dt tk3"/>
		<property name="com.jaspersoft.studio.field.label" value="Dt tk3"/>
	</field>
	<field name="Dt tk4" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="Dt tk4"/>
		<property name="com.jaspersoft.studio.field.label" value="Dt tk4"/>
	</field>
	<field name="Dt tk5" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="Dt tk5"/>
		<property name="com.jaspersoft.studio.field.label" value="Dt tk5"/>
	</field>
	<field name="Especialista" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="Especialista"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="Administrador" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="Administrador"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="Convênio" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="Convênio"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="insurance"/>
	</field>
	<pageHeader>
		<band height="91">
			<textField isBlankWhenNull="true">
				<reportElement x="89" y="31" width="541" height="25" uuid="321f1f63-b46c-439c-a430-8165e08bf9f1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="17" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Relatório de Tokens"]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy">
				<reportElement x="730" y="17" width="60" height="15" uuid="e11903a3-920d-48d0-b89c-80f2ee64d61c"/>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="760" y="45" width="30" height="15" uuid="cb3aa02c-7575-4b86-87c8-9000228b885f"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="644" y="17" width="81" height="14" uuid="ee7dd153-6548-48cc-b06c-cefec483ceb3"/>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" isBold="true"/>
				</textElement>
				<text><![CDATA[Data:]]></text>
			</staticText>
			<staticText>
				<reportElement x="644" y="45" width="81" height="14" uuid="ed407f09-8cb1-4d5a-9121-132a6a2aad1b"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Página:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="89" y="2" width="541" height="30" uuid="10c97d4d-01dd-4674-bd38-c91df8d39112"/>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="20" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["NOME DA EMPRESA"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="644" y="31" width="81" height="14" uuid="5caec54e-2731-4c4a-a32d-a4fac0113823"/>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" isBold="true"/>
				</textElement>
				<text><![CDATA[Hora:]]></text>
			</staticText>
			<textField pattern="HH:mm">
				<reportElement x="730" y="30" width="60" height="15" uuid="f0d82f9d-3c9a-45b4-9db8-4ea0b4bd592e"/>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="23">
			<line>
				<reportElement x="1" y="6" width="800" height="1" uuid="e16b8545-e76d-4347-874c-855e7d702e48">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="2" y="8" width="93" height="12" uuid="f3accf36-8a65-4461-9b7a-3fb60c6ef8df">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Paciente]]></text>
			</staticText>
			<line>
				<reportElement x="1" y="22" width="800" height="1" uuid="d0a1bd55-ebfc-4287-b45c-ff8bc633d963">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="99" y="8" width="37" height="12" uuid="1b55c1da-31fd-424e-9e49-6c607d7cc574">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="faeee5f3-c66c-42c1-9a25-fb994e7cdaf1"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Num Guia]]></text>
			</staticText>
			<staticText>
				<reportElement x="140" y="8" width="55" height="12" uuid="d47bb5f2-df6a-4589-97c6-f37dff6596a2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Dt. Autorização]]></text>
			</staticText>
			<staticText>
				<reportElement x="199" y="8" width="55" height="12" uuid="bd6c178e-8046-4b1a-81c2-d7ca42a62b0c">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Qtd. cons/guia]]></text>
			</staticText>
			<staticText>
				<reportElement x="258" y="8" width="50" height="12" uuid="cee07299-4f28-4ac7-adea-018f580002d6">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Dt. Token 1]]></text>
			</staticText>
			<staticText>
				<reportElement x="309" y="8" width="50" height="12" uuid="1c3dff13-d39f-46bf-85f5-2a56b4ddd55f">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Dt. Token 2]]></text>
			</staticText>
			<staticText>
				<reportElement x="361" y="8" width="50" height="12" uuid="2690d740-af30-4f33-95d3-7267ec2e2155">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Dt. Token 3]]></text>
			</staticText>
			<staticText>
				<reportElement x="413" y="8" width="50" height="12" uuid="427f91f0-e4d9-4dab-bb94-33501f70cdb6">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Dt. Token 4]]></text>
			</staticText>
			<staticText>
				<reportElement x="467" y="8" width="50" height="12" uuid="d1ab8724-ae5f-4f35-a437-40980ea4d2c6">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Dt. Token 5]]></text>
			</staticText>
			<staticText>
				<reportElement x="524" y="8" width="100" height="12" uuid="aba45d45-929c-4847-89be-a5917bb4bd49">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Especialista]]></text>
			</staticText>
			<staticText>
				<reportElement x="627" y="8" width="100" height="12" uuid="b0021e01-3a6e-422e-9f0d-50b015c9de9a">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Administrador]]></text>
			</staticText>
			<staticText>
				<reportElement x="733" y="8" width="67" height="12" uuid="58a1b92f-8e74-44c0-9301-b753d154f8ff">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Convênio]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="15">
			<textField isBlankWhenNull="true">
				<reportElement x="2" y="3" width="93" height="12" uuid="a3b6f38f-f646-41b3-8040-5774c6fb964a">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Paciente}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="99" y="3" width="37" height="12" uuid="f988117a-2967-4394-8fa5-995169d8e090">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="faeee5f3-c66c-42c1-9a25-fb994e7cdaf1"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Num Guia}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="140" y="3" width="55" height="12" uuid="b90da789-58b7-4d49-a2d3-950453f9d6a3">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Dt Auth}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="199" y="3" width="55" height="12" uuid="ac4e6540-339f-40d9-8e45-dee8e02824c8">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Qtd}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="RedTextStyle" mode="Opaque" x="258" y="3" width="50" height="12" uuid="bad9a998-3d6a-4393-a189-5648dd0c5e81">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$F{Dt tk1}.equals("Não possui")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" markup="none">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Dt tk1}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="RedTextStyle" x="309" y="3" width="50" height="12" uuid="319470ea-8b43-4d84-8251-5075e57c43c2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$F{Dt tk2}.equals("Não possui")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Dt tk2}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="RedTextStyle" x="361" y="3" width="50" height="12" uuid="6280a09b-fa96-4229-9dd4-1b3899b5ce42">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$F{Dt tk3}.equals("Não possui")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Dt tk3}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="RedTextStyle" x="413" y="3" width="50" height="12" uuid="18a3d89c-5cc1-4d7f-a769-5476e3e3ba7f">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$F{Dt tk4}.equals("Não possui")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Dt tk4}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="RedTextStyle" x="467" y="3" width="50" height="12" uuid="779d4004-a6da-48de-b2db-570ae52535a7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[$F{Dt tk5}.equals("Não possui")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Dt tk5}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="524" y="3" width="100" height="12" uuid="967b3b0a-0e48-4335-9199-62b09649c564">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Especialista}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="627" y="3" width="100" height="12" uuid="07bae6e0-376f-4fbc-8491-804563c5b2c6">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Administrador}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="733" y="3" width="67" height="12" uuid="590485a7-970e-4a9d-b3dd-f0bb424305af">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Convênio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="258" y="3" width="50" height="12" uuid="bd091872-4af6-4b8a-ab21-6238e1109702">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!$F{Dt tk1}.equals("Não possui")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" markup="none">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Dt tk1}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="309" y="3" width="50" height="12" uuid="bd89fd47-88e6-4de4-997d-7a137be6a41b">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!$F{Dt tk2}.equals("Não possui")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Dt tk2}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="361" y="3" width="50" height="12" uuid="5715ee5c-**************-d01ea89228d8">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!$F{Dt tk3}.equals("Não possui")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Dt tk3}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="413" y="3" width="50" height="12" uuid="ccdcc815-a1e7-4dd7-97c5-c48b3d9f976b">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!$F{Dt tk4}.equals("Não possui")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Dt tk4}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="467" y="3" width="50" height="12" uuid="54412c66-d3cd-4ffc-924c-5385a53fc670">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="1f34d236-15e8-4c49-af0d-4bf7e8009175"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!$F{Dt tk5}.equals("Não possui")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Dt tk5}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="96">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<line>
				<reportElement x="0" y="-1" width="800" height="1" uuid="49f63265-1473-42c6-adae-e98b528e7b02">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<subreport>
				<reportElement x="1" y="10" width="370" height="86" uuid="6cc1f64b-91cc-4011-aae2-83b3e695afda"/>
				<subreportParameter name="dataInicio">
					<subreportParameterExpression><![CDATA[$P{dataInicio}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataFim">
					<subreportParameterExpression><![CDATA[$P{dataFim}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["reports/order/application-report-order-relationship-token/totalizador_especialista.jasper"]]></subreportExpression>
			</subreport>
			<subreport>
				<reportElement x="413" y="10" width="387" height="86" uuid="7dbec649-**************-9eb4598b1477"/>
				<subreportParameter name="dataInicio">
					<subreportParameterExpression><![CDATA[$P{dataInicio}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataFim">
					<subreportParameterExpression><![CDATA[$P{dataFim}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["reports/order/application-report-order-relationship-token/totalizador_estagiario.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</summary>
	<noData>
		<band height="79">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="801" height="79" uuid="f54daa93-7c74-49ca-b93b-86d091ab637a"/>
				<textElement textAlignment="Center">
					<font size="26" isBold="true"/>
				</textElement>
				<text><![CDATA[Não há dados para os filtros selecionados.]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
