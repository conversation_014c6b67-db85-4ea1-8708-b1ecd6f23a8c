<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="application-report-operational-daily-service" pageWidth="841" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="801" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4ff84453-1320-4ac5-a9ad-242cce5098f5">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_climagem"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="0"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="1000"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<subDataset name="Dataset1" uuid="7fe0c814-75e0-454d-bbae-8410466db122">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MySql "/>
		<queryString language="SQL">
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="filters" class="java.util.ArrayList"/>
	<parameter name="nameReport" class="java.lang.String"/>
	<parameter name="costCenter" class="java.util.ArrayList"/>
	<parameter name="performer" class="java.util.ArrayList"/>
	<parameter name="dataInicio" class="java.lang.String"/>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="user" class="java.util.ArrayList"/>
	<parameter name="costCenterStart" class="java.lang.String"/>
	<parameter name="costCenterEnd" class="java.lang.String"/>
	<parameter name="performerStart" class="java.lang.String"/>
	<parameter name="performerEnd" class="java.lang.String"/>
	<parameter name="performerRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "$X{IN,performer.performer_id, performer} " :
"person_performer.`name`  >= " +"$P" +"{performerStart}"+" AND person_performer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="costCenterRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{costCenterStart} == null || $P{costCenterEnd} == null ? "and $X{IN,cost_center.cost_center_id, costCenter} " :
"and cost_center.`name`  >= " +"$P" +"{costCenterStart}"+" AND cost_center.`name` <= " + "$P" + "{costCenterEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{userStart} == null || $P{userEnd} == null ? "and $X{IN,user.user_id, user} " :
"and person_user.`name`  >= " +"$P" +"{userStart}"+" AND person_user.`name` <= " + "$P" + "{userEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userEnd" class="java.lang.String"/>
	<parameter name="userStart" class="java.lang.String"/>
	<parameter name="dateRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{dataInicio} == null || $P{dataFim} == null ? " " :
"AND order_item.`date` BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="firm" class="java.util.ArrayList"/>
	<parameter name="firmStart" class="java.lang.String"/>
	<parameter name="firmEnd" class="java.lang.String"/>
	<parameter name="firmRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{firmStart} == null || $P{firmEnd} == null ? "and  $X{IN,company.company_id, firm} " :
"and company.`name`  >= " +"$P" +"{firmStart}"+" AND company.`name` <= " + "$P" + "{firmEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="insurance" class="java.util.ArrayList"/>
	<parameter name="insuranceStart" class="java.lang.String"/>
	<parameter name="insuranceEnd" class="java.lang.String"/>
	<parameter name="insuranceRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{insuranceStart} == null || $P{insuranceEnd} == null ? "and  $X{IN,`insurance`.`insurance_id`, insurance} " :
"and `insurance`.`name`  >= " +"$P" +"{insuranceStart}"+" AND `insurance`.`name` <= " + "$P" + "{insuranceEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[
SELECT 
	`order_item`.`date`,
	`company`.`name` as companyName,	
	`cost_center`.`name` as costCenterName,
	`image`.`content` as logo,
	`cost_center`.`cost_center_id`,
	
	-- Particulares Forma de Pagamento
    MIN(
    (SELECT SUM(`order_pay`.`value`) AS `total` FROM `order_pay` 
		JOIN `pay_type` ON `pay_type`.`pay_type_id` = `order_pay`.`pay_type_id`
		WHERE `order_pay`.`order_id` = `order`.`order_id` AND `pay_type`.`type` IN (1)
	)) AS paid_as_money,
    MIN((SELECT SUM(`order_pay`.`value`) AS `total` FROM `order_pay` 
		JOIN `pay_type` ON `pay_type`.`pay_type_id` = `order_pay`.`pay_type_id`
		WHERE `order_pay`.`order_id` = `order`.`order_id` AND `pay_type`.`type` IN (3, 4)
	)) AS paid_as_card,
    MIN((SELECT SUM(`order_pay`.`value`) AS `total` FROM `order_pay` 
		JOIN `pay_type` ON `pay_type`.`pay_type_id` = `order_pay`.`pay_type_id`
		WHERE `order_pay`.`order_id` = `order`.`order_id` AND `pay_type`.`type` NOT IN (1, 3, 4)
	)) AS paid_as_other,
	
	-- Quantidades
    SUM(CASE WHEN UPPER(`group`.`name`) LIKE '%CONS%' THEN `order_item`.`quantity` ELSE 0 END) AS `appointment_quantity`,
    SUM(CASE WHEN UPPER(`group`.`name`) NOT LIKE '%CONS%' THEN `order_item`.`quantity` ELSE 0 END) AS `prodedure_quantity`,
	
	-- Consultas por Convênios
    SUM(CASE WHEN insurance.payment_model NOT IN ('P') AND UPPER(`group`.`name`) LIKE '%CONS%' THEN 	CASE
		WHEN `order_pay`.value IS NULL THEN order_item.total
		WHEN `order_pay`.value = `order`.value THEN order_item.`total` ELSE (order_item.`total` / 100) * ((`order_pay`.value / `order`.value) * 100) END ELSE 0 END) AS `appointment_insurance_total`, 
    SUM(CASE WHEN insurance.payment_model NOT IN ('P') AND UPPER(`group`.`name`) NOT LIKE '%CONS%' THEN 	CASE
		WHEN `order_pay`.value IS NULL THEN order_item.total
		WHEN `order_pay`.value = `order`.value THEN order_item.`total` ELSE (order_item.`total` / 100) * ((`order_pay`.value / `order`.value) * 100) END ELSE 0 END) AS `prodedure_insurance_total`,
	
	-- Particulares
    SUM(CASE WHEN insurance.payment_model IN ('P') AND UPPER(`group`.`name`) LIKE '%CONS%' THEN 	CASE
		WHEN `order_pay`.value IS NULL THEN order_item.total
		WHEN `order_pay`.value = `order`.value THEN order_item.`total` ELSE (order_item.`total` / 100) * ((`order_pay`.value / `order`.value) * 100) END ELSE 0 END) AS `appointment_paid_total`,
    SUM(CASE WHEN insurance.payment_model IN ('P') AND UPPER(`group`.`name`) NOT LIKE '%CONS%' THEN 	CASE
		WHEN `order_pay`.value IS NULL THEN order_item.total
		WHEN `order_pay`.value = `order`.value THEN order_item.`total` ELSE (order_item.`total` / 100) * ((`order_pay`.value / `order`.value) * 100) END ELSE 0 END) AS `prodedure_paid_total`,
	
	-- Total
    SUM(CASE WHEN UPPER(`group`.`name`) LIKE '%CON%' THEN 	CASE
		WHEN `order_pay`.value IS NULL THEN order_item.total
		WHEN `order_pay`.value = `order`.value THEN order_item.`total` ELSE (order_item.`total` / 100) * ((`order_pay`.value / `order`.value) * 100) END ELSE 0 END) AS `appointment_total`,
    SUM(CASE WHEN UPPER(`group`.`name`) NOT LIKE '%CON%' THEN 	CASE
		WHEN `order_pay`.value IS NULL THEN order_item.total
		WHEN `order_pay`.value = `order`.value THEN order_item.`total` ELSE (order_item.`total` / 100) * ((`order_pay`.value / `order`.value) * 100) END ELSE 0 END) AS `prodedure_total`,
    SUM(CASE WHEN UPPER(`group`.`name`) LIKE '%CON%' THEN 	CASE
		WHEN `order_pay`.value IS NULL THEN order_item.total
		WHEN `order_pay`.value = `order`.value THEN order_item.`total` ELSE (order_item.`total` / 100) * ((`order_pay`.value / `order`.value) * 100) END ELSE 0 END) +
    SUM(CASE WHEN UPPER(`group`.`name`) NOT LIKE '%CON%' THEN 	CASE
		WHEN `order_pay`.value IS NULL THEN order_item.total
		WHEN `order_pay`.value = `order`.value THEN order_item.`total` ELSE (order_item.`total` / 100) * ((`order_pay`.value / `order`.value) * 100) END ELSE 0 END) AS `total`
	
FROM `order` 

LEFT JOIN order_item  ON order_item.order_id = `order`.order_id
LEFT JOIN company_cost_center ON company_cost_center.company_cost_center_id = `order`.company_cost_center_id
LEFT JOIN company  ON company.company_id = company_cost_center.company_id
LEFT JOIN image ON image.image_id = company.image_id
LEFT JOIN cost_center ON cost_center.cost_center_id = company_cost_center.cost_center_id
LEFT JOIN patient ON patient.patient_id = `order`.patient_id
LEFT JOIN person as personPatient ON personPatient.person_id = patient.person_id
LEFT JOIN order_diagnosis ON order_diagnosis.order_id = `order`.order_id and order_diagnosis.main = 1
LEFT JOIN cid ON cid.cid_id = order_diagnosis.cid_id
LEFT JOIN insurance  ON insurance.insurance_id = `order`.insurance_id
LEFT JOIN insurance_tax  ON insurance_tax.insurance_id = insurance.insurance_id
LEFT JOIN performer ON performer.performer_id = order_item.performer_id
LEFT JOIN professional ON professional.professional_id = performer.professional_id
LEFT JOIN person as personPerformer ON personPerformer.person_id = professional.person_id
LEFT JOIN user ON user.user_id = `order`.created_by
LEFT JOIN person as personUser ON user.person_id = personUser.person_id
LEFT JOIN `order_pay` ON `order_pay`.`order_id` = `order`.`order_id` 
LEFT JOIN `pay_type`  ON `pay_type`.`pay_type_id` = `order_pay`.`pay_type_id` 
LEFT JOIN billing_pay_order_item ON billing_pay_order_item.order_item_id = order_item.order_item_id 
LEFT JOIN gloss ON gloss.billing_pay_order_item_id = billing_pay_order_item.billing_pay_order_item_id 
LEFT JOIN `procedure` ON `procedure`.procedure_id = order_item.procedure_id 
LEFT JOIN `group` ON `group`.group_id = `procedure`.group_id
LEFT JOIN speciality ON speciality.speciality_id = professional.speciality_id
LEFT JOIN order_tiss ON order_tiss.order_id = `order`.order_id 
LEFT JOIN type_attendance ON type_attendance.type_attendance_id = order_tiss.type_attendance_id 

WHERE 

 $P!{performerRange}
 $P!{costCenterRange}
 $P!{firmRange}
 $P!{userRange}
 $P!{dateRange}

GROUP BY 1 , 2, 3, 4, 5
ORDER BY 5, `order_item`.`date`
;]]>
	</queryString>
	<field name="date" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.name" value="date"/>
		<property name="com.jaspersoft.studio.field.label" value="date"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
		<fieldDescription><![CDATA[Data de Lançamento do Item da Conta]]></fieldDescription>
	</field>
	<field name="companyName" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="companyName"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="company"/>
	</field>
	<field name="costCenterName" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="costCenterName"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="cost_center"/>
	</field>
	<field name="logo" class="byte[]">
		<property name="com.jaspersoft.studio.field.name" value="content"/>
		<property name="com.jaspersoft.studio.field.label" value="logo"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="image"/>
	</field>
	<field name="cost_center_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="cost_center_id"/>
		<property name="com.jaspersoft.studio.field.label" value="cost_center_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="cost_center"/>
		<fieldDescription><![CDATA[Id Centro de Resultado]]></fieldDescription>
	</field>
	<field name="paid_as_money" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="paid_as_money"/>
		<property name="com.jaspersoft.studio.field.label" value="paid_as_money"/>
	</field>
	<field name="paid_as_card" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="paid_as_card"/>
		<property name="com.jaspersoft.studio.field.label" value="paid_as_card"/>
	</field>
	<field name="paid_as_other" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="paid_as_other"/>
		<property name="com.jaspersoft.studio.field.label" value="paid_as_other"/>
	</field>
	<field name="appointment_quantity" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="appointment_quantity"/>
		<property name="com.jaspersoft.studio.field.label" value="appointment_quantity"/>
	</field>
	<field name="prodedure_quantity" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="prodedure_quantity"/>
		<property name="com.jaspersoft.studio.field.label" value="prodedure_quantity"/>
	</field>
	<field name="appointment_insurance_total" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="appointment_insurance_total"/>
		<property name="com.jaspersoft.studio.field.label" value="appointment_insurance_total"/>
	</field>
	<field name="prodedure_insurance_total" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="prodedure_insurance_total"/>
		<property name="com.jaspersoft.studio.field.label" value="prodedure_insurance_total"/>
	</field>
	<field name="appointment_paid_total" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="appointment_paid_total"/>
		<property name="com.jaspersoft.studio.field.label" value="appointment_paid_total"/>
	</field>
	<field name="prodedure_paid_total" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="prodedure_paid_total"/>
		<property name="com.jaspersoft.studio.field.label" value="prodedure_paid_total"/>
	</field>
	<field name="appointment_total" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="appointment_total"/>
		<property name="com.jaspersoft.studio.field.label" value="appointment_total"/>
	</field>
	<field name="prodedure_total" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="prodedure_total"/>
		<property name="com.jaspersoft.studio.field.label" value="prodedure_total"/>
	</field>
	<field name="total" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="total"/>
		<property name="com.jaspersoft.studio.field.label" value="total"/>
	</field>
	<variable name="subtotal-insurance" class="java.lang.Double">
		<variableExpression><![CDATA[$F{appointment_insurance_total}+$F{prodedure_insurance_total}]]></variableExpression>
	</variable>
	<variable name="subtotal-paid" class="java.lang.Double">
		<variableExpression><![CDATA[$F{appointment_paid_total}+$F{prodedure_paid_total}]]></variableExpression>
	</variable>
	<variable name="paid_as_money1" class="java.lang.Double" resetType="Group" resetGroup="Group1" calculation="Sum">
		<variableExpression><![CDATA[$F{paid_as_money}]]></variableExpression>
	</variable>
	<variable name="paid_as_card1" class="java.lang.Double" resetType="Group" resetGroup="Group1" calculation="Sum">
		<variableExpression><![CDATA[$F{paid_as_card}]]></variableExpression>
	</variable>
	<variable name="paid_as_other1" class="java.lang.Double" resetType="Group" resetGroup="Group1" calculation="Sum">
		<variableExpression><![CDATA[$F{paid_as_other}]]></variableExpression>
	</variable>
	<variable name="appointment_quantity1" class="java.lang.Double" resetType="Group" resetGroup="Group1" calculation="Sum">
		<variableExpression><![CDATA[$F{appointment_quantity}]]></variableExpression>
	</variable>
	<variable name="prodedure_quantity1" class="java.lang.Double" resetType="Group" resetGroup="Group1" calculation="Sum">
		<variableExpression><![CDATA[$F{prodedure_quantity}]]></variableExpression>
	</variable>
	<variable name="appointment_insurance_total1" class="java.lang.Double" resetType="Group" resetGroup="Group1" calculation="Sum">
		<variableExpression><![CDATA[$F{appointment_insurance_total}]]></variableExpression>
	</variable>
	<variable name="prodedure_insurance_total1" class="java.lang.Double" resetType="Group" resetGroup="Group1" calculation="Sum">
		<variableExpression><![CDATA[$F{prodedure_insurance_total}]]></variableExpression>
	</variable>
	<variable name="appointment_paid_total1" class="java.lang.Double" resetType="Group" resetGroup="Group1" calculation="Sum">
		<variableExpression><![CDATA[$F{appointment_paid_total}]]></variableExpression>
	</variable>
	<variable name="prodedure_paid_total1" class="java.lang.Double" resetType="Group" resetGroup="Group1" calculation="Sum">
		<variableExpression><![CDATA[$F{prodedure_paid_total}]]></variableExpression>
	</variable>
	<variable name="appointment_total1" class="java.lang.Double" resetType="Group" resetGroup="Group1" calculation="Sum">
		<variableExpression><![CDATA[$F{appointment_total}]]></variableExpression>
	</variable>
	<variable name="prodedure_total1" class="java.lang.Double" resetType="Group" resetGroup="Group1" calculation="Sum">
		<variableExpression><![CDATA[$F{prodedure_total}]]></variableExpression>
	</variable>
	<variable name="total1" class="java.lang.Double" resetType="Group" resetGroup="Group1" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="subtotal-insurance-total" class="java.lang.Double">
		<variableExpression><![CDATA[$V{appointment_insurance_total1}+$V{prodedure_insurance_total1}]]></variableExpression>
	</variable>
	<variable name="subtotal-paid-total" class="java.lang.Double">
		<variableExpression><![CDATA[$V{appointment_paid_total1}+$V{prodedure_paid_total1}]]></variableExpression>
	</variable>
	<variable name="total2" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="prodedure_total2" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{prodedure_total}]]></variableExpression>
	</variable>
	<variable name="appointment_total2" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{appointment_total}]]></variableExpression>
	</variable>
	<variable name="paid_as_money2" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{paid_as_money}]]></variableExpression>
	</variable>
	<variable name="paid_as_card2" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{paid_as_card}]]></variableExpression>
	</variable>
	<variable name="paid_as_other2" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{paid_as_other}]]></variableExpression>
	</variable>
	<variable name="appointment_paid_total2" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{appointment_paid_total}]]></variableExpression>
	</variable>
	<variable name="prodedure_paid_total2" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{prodedure_paid_total}]]></variableExpression>
	</variable>
	<variable name="sub-total-paid-geral" class="java.lang.Double">
		<variableExpression><![CDATA[$V{appointment_paid_total2}+$V{prodedure_paid_total2}]]></variableExpression>
	</variable>
	<variable name="appointment_insurance_total2" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{appointment_insurance_total}]]></variableExpression>
	</variable>
	<variable name="prodedure_insurance_total2" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{prodedure_insurance_total}]]></variableExpression>
	</variable>
	<variable name="subtotal-insurance-total_geral" class="java.lang.Double">
		<variableExpression><![CDATA[$V{appointment_insurance_total2}+$V{prodedure_insurance_total2}]]></variableExpression>
	</variable>
	<variable name="appointment_quantity2" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{appointment_quantity}]]></variableExpression>
	</variable>
	<variable name="prodedure_quantity2" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{prodedure_quantity}]]></variableExpression>
	</variable>
	<group name="Group1" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{cost_center_id}]]></groupExpression>
		<groupHeader>
			<band height="49">
				<line>
					<reportElement x="0" y="15" width="800" height="1" uuid="c31bd223-b14a-449d-a452-5c5dfd017da8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<line>
					<reportElement x="-1" y="47" width="800" height="1" uuid="5c8e1b56-3f74-4542-8f97-9d96b70f631f">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<staticText>
					<reportElement x="0" y="36" width="40" height="9" uuid="30068c2b-8f29-431a-93da-d62c062cfe25">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Data]]></text>
				</staticText>
				<staticText>
					<reportElement x="53" y="36" width="40" height="9" uuid="bfed94f1-ddde-47d1-bf3d-19948c591be2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Cons.]]></text>
				</staticText>
				<staticText>
					<reportElement x="93" y="36" width="40" height="9" uuid="1c523acc-60bd-4963-a20f-1dcff36f5b82">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Proc.]]></text>
				</staticText>
				<staticText>
					<reportElement x="59" y="21" width="70" height="9" uuid="d496d392-ca06-426a-bc01-6e49b1f85102"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Quantidade]]></text>
				</staticText>
				<staticText>
					<reportElement x="158" y="37" width="40" height="9" uuid="426ec1c2-760b-4485-99fc-309781ab9331">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Cons.]]></text>
				</staticText>
				<staticText>
					<reportElement x="198" y="37" width="40" height="9" uuid="4360ab58-4bdc-4594-8e89-12fe4e4f3abe">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Proc.]]></text>
				</staticText>
				<staticText>
					<reportElement x="197" y="22" width="40" height="9" uuid="a1420600-f81e-4f0b-ad00-b5eb1d31efa4"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Convênios]]></text>
				</staticText>
				<staticText>
					<reportElement x="240" y="37" width="40" height="9" uuid="0afc749a-703b-42f6-be6f-af9d315ee627">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Subtotal]]></text>
				</staticText>
				<staticText>
					<reportElement x="299" y="37" width="40" height="9" uuid="18b41a33-ee2c-438e-b4d0-efbbbb22e868">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Cons.]]></text>
				</staticText>
				<staticText>
					<reportElement x="339" y="37" width="40" height="9" uuid="ef58b1c9-150d-44f2-a863-c6cc4aef6cf2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Proc.]]></text>
				</staticText>
				<staticText>
					<reportElement x="336" y="22" width="40" height="9" uuid="45add042-9055-4ae5-b20a-838e5960bc85"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Particular]]></text>
				</staticText>
				<staticText>
					<reportElement x="381" y="37" width="40" height="9" uuid="8ed34fbd-9768-461d-8f5d-110f441c412a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Subtotal]]></text>
				</staticText>
				<staticText>
					<reportElement x="452" y="37" width="40" height="9" uuid="9d892b55-b8ad-4c4c-9e5e-0c888dca4d32">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Dinheiro]]></text>
				</staticText>
				<staticText>
					<reportElement x="494" y="37" width="40" height="9" uuid="c6194bc5-0e17-41db-8044-b840adc730c9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[C. Crédito]]></text>
				</staticText>
				<staticText>
					<reportElement x="444" y="22" width="139" height="9" uuid="e868b108-48d3-4968-a691-687b8e41f1c0"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Forma de pagamento particular]]></text>
				</staticText>
				<staticText>
					<reportElement x="536" y="37" width="40" height="9" uuid="54feed57-cc37-4f20-aebd-85706a182298">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Outros]]></text>
				</staticText>
				<staticText>
					<reportElement x="635" y="37" width="40" height="9" uuid="0a11d834-8e64-4b37-b591-451658b0f46d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Cons.]]></text>
				</staticText>
				<staticText>
					<reportElement x="681" y="37" width="40" height="9" uuid="664715f9-35c4-43cd-8c45-3c3b833418b3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Proc.]]></text>
				</staticText>
				<staticText>
					<reportElement x="680" y="22" width="40" height="9" uuid="d386ce84-580b-4281-b850-3eb67d1721e0"/>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Total]]></text>
				</staticText>
				<staticText>
					<reportElement x="727" y="37" width="40" height="9" uuid="c968354b-092e-4cb7-a52c-5184df9c37c5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Geral]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="3" width="70" height="9" uuid="4dcdfe9f-316b-4cc6-b9a1-c77dbdd1627f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Centro de Custo:]]></text>
				</staticText>
				<textField>
					<reportElement x="80" y="3" width="412" height="9" uuid="9f1f7d82-1a86-4842-913b-f73066768b50">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{costCenterName}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="13">
				<line>
					<reportElement x="0" y="1" width="800" height="1" uuid="969c3989-0d3b-455f-a228-3f73886e3b6f">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<textField pattern="#,##0.00">
					<reportElement x="452" y="4" width="40" height="9" uuid="f5d4eda9-c10c-490a-804d-e74414b992bb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{paid_as_money1} == null ? 0 : $V{paid_as_money1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="494" y="4" width="40" height="9" uuid="42c2dfc3-0275-42bc-8ebe-8eced1b17f1f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{paid_as_card1} == null ? 0 : $V{paid_as_card1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="536" y="4" width="40" height="9" uuid="a7e0637b-2d81-4370-aeed-9ffccb4bd8c6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{paid_as_other1} == null ? 0 : $V{paid_as_other1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0">
					<reportElement x="53" y="4" width="40" height="9" uuid="c51343d9-1326-42d8-86e7-e888e1ad8f9b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{appointment_quantity1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0">
					<reportElement x="93" y="4" width="40" height="9" uuid="b065232a-725e-408b-9fb1-2e0cfa4b9d46">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{prodedure_quantity1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="158" y="4" width="40" height="9" uuid="52fcc7f6-27b3-47a9-bc36-f37e5609d4f5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{appointment_insurance_total1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="198" y="4" width="40" height="9" uuid="c83a105f-0308-4b49-9e1d-732708c4aa8e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{prodedure_insurance_total1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="299" y="4" width="40" height="9" uuid="6a496923-5f10-4e3b-8fe7-b339469b860c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{appointment_paid_total1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="339" y="4" width="40" height="9" uuid="d38197e0-f2e3-4cb2-b986-026529396f3f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{prodedure_paid_total1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="635" y="4" width="40" height="9" uuid="1c522b5c-214d-4c19-ba56-2dbb6ad332e8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{appointment_total1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="681" y="4" width="40" height="9" uuid="b6789c00-36b5-4b6b-aad1-18256fedb733">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{prodedure_total1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="727" y="4" width="40" height="9" uuid="00d60f22-25ba-4407-8b58-9ecfb440ee87">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{total1}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="4" y="4" width="40" height="9" uuid="d6b8b63e-03e1-443e-9ccd-5c3dfa0ed180">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Total]]></text>
				</staticText>
				<textField pattern="#,##0.00">
					<reportElement x="240" y="4" width="40" height="9" uuid="31a4b86a-a5d9-4607-95eb-e371c8681a3f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{subtotal-insurance-total}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="381" y="4" width="40" height="9" uuid="4864b859-befa-4dcf-a865-fab788aceafb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{subtotal-paid-total}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<pageHeader>
		<band height="94">
			<textField isBlankWhenNull="true">
				<reportElement x="89" y="31" width="541" height="25" uuid="9699d274-ced7-4a7e-b19f-96d092d7fcfa">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="17" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{nameReport}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy">
				<reportElement x="730" y="17" width="60" height="15" uuid="49e2d1c1-f864-42d0-acf0-a6e3769657e4"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="760" y="45" width="30" height="15" uuid="c9926094-c7fd-431c-9f85-d49be6f2d1fa"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="644" y="17" width="81" height="14" uuid="5ab8c034-e148-4371-9d9b-54b0ef01d5d0"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Data:]]></text>
			</staticText>
			<staticText>
				<reportElement x="644" y="45" width="81" height="14" uuid="2b79ddb7-71c7-40ba-a143-2370c2befaf6"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Página:]]></text>
			</staticText>
			<image hAlign="Center" vAlign="Middle">
				<reportElement x="-9" y="4" width="85" height="40" uuid="6bf03931-b8e6-4841-8ddc-eaf79c163e69"/>
				<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64(new String($F{logo}).getBytes("UTF-8")))]]></imageExpression>
			</image>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="6" y="70" width="784" height="20" uuid="c691608c-9fd6-4503-a9b2-78afca5f440a"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{filters}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="293" y="58" width="181" height="17" uuid="4dd2d497-7da2-4eac-a93c-1a19884fa3ff"/>
				<textElement textAlignment="Center">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Filtros Selecionados]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="89" y="2" width="541" height="30" uuid="fdb0b444-4539-4b39-9851-2f2e59e4d9fb"/>
				<textElement textAlignment="Center">
					<font size="20" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{companyName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="644" y="31" width="81" height="14" uuid="12469056-5b7c-430f-990f-e95f19e441c1"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Hora:]]></text>
			</staticText>
			<textField pattern="HH:mm">
				<reportElement x="730" y="30" width="60" height="15" uuid="31bc7538-0cdc-4ca6-9993-713f642830b8"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="11">
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="false">
				<reportElement x="0" y="1" width="46" height="9" uuid="6b6f934a-2058-4252-a883-e7e59c042991">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{date}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement x="452" y="2" width="40" height="9" uuid="f805f16e-348c-410e-a47c-09bc20ba6e66">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{paid_as_money} == null ? 0 : $F{paid_as_money}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement x="494" y="2" width="40" height="9" uuid="45b78151-49c4-4377-aa7f-aeebe2694ed1">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{paid_as_card} == null ? 0 : $F{paid_as_card}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement x="536" y="2" width="40" height="9" uuid="79202404-db15-44a8-b4b5-a461315e8f36">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{paid_as_other} == null ? 0 : $F{paid_as_other}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="false">
				<reportElement x="53" y="2" width="40" height="9" uuid="f3e39422-a450-43a1-9eaa-e67c05ca7375">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{appointment_quantity}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="false">
				<reportElement x="93" y="2" width="40" height="9" uuid="adbffd66-6751-4970-ab3d-b0f0b7bc29e0">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{prodedure_quantity}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement x="158" y="2" width="40" height="9" uuid="85841929-54cd-422a-91c8-c99f054171a3">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{appointment_insurance_total}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement x="198" y="2" width="40" height="9" uuid="aede32c8-d115-40cc-a97c-dd68e2468d50">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{prodedure_insurance_total}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement x="299" y="2" width="40" height="9" uuid="c8740a0e-344a-4b94-9e6e-904bf789009e">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{appointment_paid_total}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement x="339" y="2" width="40" height="9" uuid="8eead878-3cc6-465d-9b47-4d2cb25e51b9">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{prodedure_paid_total}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement x="635" y="2" width="40" height="9" uuid="f7e8e3b6-f48e-4068-8f83-973de0bdd352">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{appointment_total}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement x="681" y="2" width="40" height="9" uuid="aa8b9e25-ed18-4e50-825e-768d00c97667">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{prodedure_total}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement x="727" y="2" width="40" height="9" uuid="e717397b-9770-4c28-aee3-297f27064473">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{total}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement x="240" y="2" width="40" height="9" uuid="41a09d7c-a1f9-46fa-a030-a4b96a5418b9">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{subtotal-insurance}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement x="381" y="2" width="40" height="9" uuid="f65852dd-097f-4c55-a49f-695290ebfd37">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{subtotal-paid}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="15">
			<textField pattern="#,##0.00">
				<reportElement x="727" y="4" width="40" height="9" uuid="36a01e99-c5c3-4d13-a329-43c26c83ca8f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{total2}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="4" width="49" height="9" uuid="80c659e6-eff9-4916-a295-e79dfcf06070">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Geral]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="1" width="800" height="1" uuid="54c7116b-e06f-48ac-9b04-95d9df701c7e">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="0" y="14" width="800" height="1" uuid="09aadf1d-15f0-4a1a-a856-0e84e31eead8">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<textField pattern="#,##0.00">
				<reportElement x="681" y="4" width="40" height="9" uuid="8083bda4-b8d1-4e97-b723-22b2e27a96d1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{prodedure_total2}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="635" y="4" width="40" height="9" uuid="6d1d3e26-50fa-4707-ab5d-890240b4ff64">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{appointment_total2}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="452" y="4" width="40" height="9" uuid="473d2e28-8473-4270-87f4-49a25b77615e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{paid_as_money2} == null ? 0 : $V{paid_as_money2}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="494" y="4" width="40" height="9" uuid="3873d03b-d2d3-4199-a3f3-008346216a34">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{paid_as_card2} == null ? 0 : $V{paid_as_card2}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="536" y="4" width="40" height="9" uuid="537f8d7b-8964-4783-8ee1-837a61442c19">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{paid_as_other2} == null ? 0 : $V{paid_as_other2}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="299" y="4" width="40" height="9" uuid="4952226c-b47b-44a9-85b1-dccf47408ea1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{appointment_paid_total2}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="339" y="4" width="40" height="9" uuid="6d94432d-f9ff-46ea-bec0-4d4405b3718d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{prodedure_paid_total2}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="381" y="4" width="40" height="9" uuid="4a1e471f-2885-48e0-8330-5f74e84d04c7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{sub-total-paid-geral}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="158" y="4" width="40" height="9" uuid="f89a67d9-45a7-4779-ade5-01243d26f00d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{appointment_insurance_total2}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="198" y="4" width="40" height="9" uuid="21fdf80e-fe84-49d5-87bc-84c3216fbd76">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{prodedure_insurance_total2}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="240" y="4" width="40" height="9" uuid="84ca0a55-5c91-49e5-9a53-c630303f7515">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{subtotal-insurance-total_geral}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0">
				<reportElement x="53" y="4" width="40" height="9" uuid="53789a7e-c086-43b2-8da9-00781de76d69">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{appointment_quantity2}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0">
				<reportElement x="93" y="4" width="40" height="9" uuid="d6e8c7e3-f33d-42fe-98d0-ff0764c48758">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{prodedure_quantity2}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
