<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="application-report-order-attendance-performer-insurance-analytical" pageWidth="595" pageHeight="841" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4ff84453-1320-4ac5-a9ad-242cce5098f5">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_demo"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="0"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="1000"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<subDataset name="Dataset1" uuid="7fe0c814-75e0-454d-bbae-8410466db122">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MySql "/>
		<queryString language="SQL">
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="costCenter" class="java.util.ArrayList"/>
	<parameter name="performer" class="java.util.ArrayList"/>
	<parameter name="insurance" class="java.util.ArrayList"/>
	<parameter name="dataInicio" class="java.lang.String"/>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="filters" class="java.util.ArrayList"/>
	<parameter name="nameReport" class="java.lang.String"/>
	<parameter name="user" class="java.util.ArrayList"/>
	<parameter name="costCenterStart" class="java.lang.String"/>
	<parameter name="costCenterEnd" class="java.lang.String"/>
	<parameter name="performerStart" class="java.lang.String"/>
	<parameter name="performerEnd" class="java.lang.String"/>
	<parameter name="performerRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "$X{IN,performer.performer_id, performer} " :
"person_performer.`name`  >= " +"$P" +"{performerStart}"+" AND person_performer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="costCenterRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{costCenterStart} == null || $P{costCenterEnd} == null ? "and $X{IN,cost_center.cost_center_id, costCenter} " :
"and cost_center.`name`  >= " +"$P" +"{costCenterStart}"+" AND cost_center.`name` <= " + "$P" + "{costCenterEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{userStart} == null || $P{userEnd} == null ? "and $X{IN,user.user_id, user} " :
"and personUser.`name`  >= " +"$P" +"{userStart}"+" AND personUser.`name` <= " + "$P" + "{userEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userEnd" class="java.lang.String"/>
	<parameter name="userStart" class="java.lang.String"/>
	<parameter name="dateRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{dataInicio} == null || $P{dataFim} == null ? " " :

$P{sessionHeld} == true ? "AND session_item.dated BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " " : "AND order_item.`date` BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="firm" class="java.util.ArrayList"/>
	<parameter name="firmStart" class="java.lang.String"/>
	<parameter name="firmEnd" class="java.lang.String"/>
	<parameter name="firmRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{firmStart} == null || $P{firmEnd} == null ? "and  $X{IN,company.company_id, firm} " :
"and company.`name`  >= " +"$P" +"{firmStart}"+" AND company.`name` <= " + "$P" + "{firmEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="onlyGerarateCash" class="java.util.ArrayList"/>
	<parameter name="notGerarateCash" class="java.util.ArrayList"/>
	<parameter name="onlyGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{onlyGerarateCash} ==  null ?  " " : "and  $X{IN,insurance.payment_model, onlyGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="notGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{notGerarateCash} ==  null ? " " : "and  $X{IN,insurance.payment_model, notGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="printValue" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="printValueClinical" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="printCalculationBasis" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="speciality" class="java.util.ArrayList"/>
	<parameter name="specialityStart" class="java.lang.String"/>
	<parameter name="specialityEnd" class="java.lang.String"/>
	<parameter name="specialityRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{specialityStart} == null || $P{specialityEnd} == null ? "and  $X{IN,speciality.speciality_id, speciality} " :
"and speciality.`name`  >= " +"$P" +"{specialityStart}"+" AND speciality.`name` <= " + "$P" + "{specialityEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="insuranceStart" class="java.lang.String"/>
	<parameter name="insuranceEnd" class="java.lang.String"/>
	<parameter name="insuranceRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{insuranceStart} == null || $P{insuranceEnd} == null ? "and  $X{IN,`insurance`.`insurance_id`, insurance} " :
"and `insurance`.`name`  >= " +"$P" +"{insuranceStart}"+" AND `insurance`.`name` <= " + "$P" + "{insuranceEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="groupProcedure" class="java.util.ArrayList"/>
	<parameter name="groupProcedureStart" class="java.lang.String"/>
	<parameter name="groupProcedureEnd" class="java.lang.String"/>
	<parameter name="groupProcedureRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{groupProcedureStart} == null || $P{groupProcedureEnd} == null ? "and  $X{IN, `group`.group_id, groupProcedure} " :
"and `group`.`name`  >= " +"$P" +"{groupProcedureStart}"+" AND `group`.`name` <= " + "$P" + "{groupProcedureEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="horaFim" class="java.lang.String"/>
	<parameter name="horaInicio" class="java.lang.String"/>
	<parameter name="horaRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{horaInicio} == null || $P{horaFim} == null ? "" :
"AND `order`.`hour` BETWEEN " +"$P" +"{horaInicio}"+" AND $P" + "{horaFim}" + "  "]]></defaultValueExpression>
	</parameter>
	<parameter name="procedureEnd" class="java.lang.String"/>
	<parameter name="procedureStart" class="java.lang.String"/>
	<parameter name="procedure" class="java.util.ArrayList"/>
	<parameter name="procedureRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{procedureStart} == null || $P{procedureEnd} == null ? "and $X{IN,`procedure`.procedure_id, procedure} " :
"and `procedure`.`name`  >= " +"$P" +"{procedureStart}"+" AND `procedure`.`name` <= " + "$P" + "{procedureEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="printPatient" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="sessionHeld" class="java.lang.Boolean"/>
	<parameter name="sessionHeldCondition" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{sessionHeld} == true ? "  session_item.dated IS NOT NULL" :  "1=1"]]></defaultValueExpression>
	</parameter>
	<parameter name="hideCoparticipation" class="java.lang.Boolean"/>
	<parameter name="hideCoparticipationCondition" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{hideCoparticipation} == true ? "  insurance.payment_model <> 'O' " :  "1=1"]]></defaultValueExpression>
	</parameter>
	<parameter name="companyImage" class="java.lang.String" isForPrompting="false"/>
	<parameter name="companyName" class="java.lang.String"/>
	<parameter name="auxiliaryRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "AND $X{IN,performer_auxiliary.performer_id, performer} " :
" AND personPerformer.`name`  >= " +"$P" +"{performerStart}"+" AND personPerformer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[-- Script otimimizado para relatorio
-- Em 04/09/2024
WITH 
	cte_parameters AS (
			SELECT
				parameter.name,
				COALESCE(parameter_value.value, NULLIF(parameter.value, 0)) AS value,
				CAST(parameter_value.key AS SIGNED) AS `key`
			FROM parameter
			JOIN parameter_value ON parameter_value.parameter_id = parameter.parameter_id
			WHERE parameter.name IN (
				'DISCOUNT_TAX_ON_INSURANCE',
				'DISCOUNT_TAX_ON_PAY_TYPE',
				'DISCOUNT_FEE_ON_PAY_TYPE'
			)
			AND parameter_value.key IS NOT NULL
	), 
		cte_product AS (
			SELECT
				order_item_product.order_item_id AS order_item_id,			
				SUM(CASE 
                WHEN order_item_product.participation_type = '%' THEN	
                    (           (order_item_product.total * COALESCE(order_item_product.participation_value, 0) / 100)) 
                WHEN order_item_product.participation_type = '$' THEN	
                    order_item_product.total
                ELSE 
                    .0 
            	END) AS value_product
			FROM order_item_product 
			GROUP BY 1
			
	)
	-- SELECT * FROM cte_parameters; -- Testes dos parametros
	, 
	cte_base AS (
		SELECT 
			performer.performer_id AS performer_id,
			insurance.insurance_id,
			NULL AS type_auxiliary,
			person_performer.name AS performer_name,
			company.name AS company_name,
			insurance.name AS insuranre_name,
			speciality.name AS speciality_name,
			order_item.`date` AS date_launch,
			`order`.hour AS hour_launch,
			`order`.number AS guideNumber,
			order_item.name AS procedure_name,
			person_patient.name AS patient_name,
			order_item.participation_value AS percent,
			order_item.tax_value AS tax,
			pay_type.pay_type_id,
			performer.professional_id,
			order_item.quantity AS procedure_quantity,
			-- Valor Total 		
			SUM((CASE WHEN `order_pay`.value IS NULL THEN order_item.total WHEN `order_pay`.value = `order`.value THEN order_item.total ELSE (order_item.total / 100) * ((`order_pay`.value / `order`.value) * 100) END)) AS `total`,
			
			-- Inicio dos campos Calculados
			SUM((CASE 
    WHEN vw_tax_insurance.value > 0
        AND vw_tax_discount_on_pay_type.value > 0
        AND vw_fee_discount_on_pay_type.value > 0
        AND pay_type.pay_type_id THEN 
        ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
        - (((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount)) * (insurance_tax.medic + pay_type.tax + pay_type.fee)) / 100
    WHEN vw_tax_discount_on_pay_type.value > 0
        AND vw_fee_discount_on_pay_type.value > 0
        AND pay_type.pay_type_id THEN 
        ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
        - (((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount)) * (pay_type.tax + pay_type.fee)) / 100
    WHEN vw_tax_discount_on_pay_type.value > 0
        AND vw_tax_insurance.value > 0
        AND pay_type.pay_type_id THEN 
        ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
        - (((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount)) * (pay_type.tax + insurance_tax.medic)) / 100
    WHEN vw_fee_discount_on_pay_type.value > 0
        AND vw_tax_insurance.value > 0
        AND pay_type.pay_type_id THEN 
        ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
        - (((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount)) * (pay_type.fee + insurance_tax.medic)) / 100
    WHEN vw_tax_insurance.value > 0 THEN 
        ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
        - (((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount)) * insurance_tax.medic) / 100
    WHEN vw_tax_discount_on_pay_type.value > 0
        AND pay_type.pay_type_id THEN 
        ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
        - (((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount)) * pay_type.tax) / 100
    WHEN vw_fee_discount_on_pay_type.value > 0
        AND pay_type.pay_type_id THEN 
        ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
        - (((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount)) * pay_type.fee) / 100
    ELSE 
        ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
END) + COALESCE(cte_product.value_product,0)) AS base_calculo,
			
			SUM((CASE
    WHEN order_item.participation_type = '%' THEN
        CASE
            WHEN vw_tax_insurance.value > 0 AND vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id
            THEN (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
            ) - (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount)) 
                * (insurance_tax.medic + pay_type.tax + pay_type.fee)) / 100
            WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id
            THEN (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
            ) - (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
                * (pay_type.tax + pay_type.fee)) / 100
            WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_tax_insurance.value > 0 AND pay_type.pay_type_id
            THEN (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
            ) - (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
                * (pay_type.tax + insurance_tax.medic)) / 100
            WHEN vw_fee_discount_on_pay_type.value > 0 AND vw_tax_insurance.value > 0 AND pay_type.pay_type_id
            THEN (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
            ) - (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
                * (pay_type.fee + insurance_tax.medic)) / 100
            WHEN vw_tax_insurance.value > 0
            THEN (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
            ) - (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
                * insurance_tax.medic) / 100
            WHEN vw_tax_discount_on_pay_type.value > 0 AND pay_type.pay_type_id
            THEN (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
            ) - (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
                * pay_type.tax) / 100
            WHEN vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id
            THEN (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
            ) - (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
                * pay_type.fee) / 100
            ELSE (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
            )
        END * (order_item.participation_value / 100)
    WHEN order_item.participation_type = '$' THEN ((CASE 
		WHEN vw_tax_insurance.value > 0 AND  vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id  -- IMPOSTO CONVENIO / IMPOSTO PAGAMENTO / TAXA
	    THEN CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * (insurance_tax.medic + pay_type.tax + pay_type.fee)) / 100 
	   
	    WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id  --  IMPOSTO PAGAMENTO / TAXA
	    THEN CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * (pay_type.tax + pay_type.fee)) / 100 
	     
	    WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_tax_insurance.value > 0 AND pay_type.pay_type_id  -- IMPOSTO PAGAMENTO / IMPOSTO CONVENIO
	    THEN CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * (pay_type.tax + insurance_tax.medic )) / 100 
	     
	    WHEN vw_fee_discount_on_pay_type.value > 0 AND vw_tax_insurance.value > 0 AND pay_type.pay_type_id  -- IMPOSTO CONVENIO / TAXA
	    THEN CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * (pay_type.fee + insurance_tax.medic )) / 100 
	     
	    WHEN vw_tax_insurance.value > 0 
	    THEN CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * insurance_tax.medic ) / 100 -- IMPOSTO CONVENIO
	    
	    WHEN vw_tax_discount_on_pay_type.value > 0 AND pay_type.pay_type_id 
		THEN CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * pay_type.tax ) / 100 -- IMPOSTO PAGAMENTO
		 
		WHEN vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id 
		THEN CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * pay_type.fee ) / 100  -- TAXA
	     
		ELSE CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END END) *
		
		((`order_item`.participation_value / order_item.value) * 100 / 100)) ELSE .0 END) + COALESCE(cte_product.value_product,0)) 
				AS value_performer,	 
				
			 	SUM((CASE 
    WHEN vw_tax_insurance.value > 0
        AND vw_tax_discount_on_pay_type.value > 0
        AND vw_fee_discount_on_pay_type.value > 0
        AND pay_type.pay_type_id THEN 
        ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
        - (((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount)) * (insurance_tax.medic + pay_type.tax + pay_type.fee)) / 100
    WHEN vw_tax_discount_on_pay_type.value > 0
        AND vw_fee_discount_on_pay_type.value > 0
        AND pay_type.pay_type_id THEN 
        ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
        - (((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount)) * (pay_type.tax + pay_type.fee)) / 100
    WHEN vw_tax_discount_on_pay_type.value > 0
        AND vw_tax_insurance.value > 0
        AND pay_type.pay_type_id THEN 
        ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
        - (((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount)) * (pay_type.tax + insurance_tax.medic)) / 100
    WHEN vw_fee_discount_on_pay_type.value > 0
        AND vw_tax_insurance.value > 0
        AND pay_type.pay_type_id THEN 
        ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
        - (((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount)) * (pay_type.fee + insurance_tax.medic)) / 100
    WHEN vw_tax_insurance.value > 0 THEN 
        ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
        - (((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount)) * insurance_tax.medic) / 100
    WHEN vw_tax_discount_on_pay_type.value > 0
        AND pay_type.pay_type_id THEN 
        ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
        - (((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount)) * pay_type.tax) / 100
    WHEN vw_fee_discount_on_pay_type.value > 0
        AND pay_type.pay_type_id THEN 
        ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
        - (((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount)) * pay_type.fee) / 100
    ELSE 
        ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
END
- CASE
    WHEN order_item.participation_type = '%' THEN
        CASE
            WHEN vw_tax_insurance.value > 0 AND vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id
            THEN (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
            ) - (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount)) 
                * (insurance_tax.medic + pay_type.tax + pay_type.fee)) / 100
            WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id
            THEN (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
            ) - (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
                * (pay_type.tax + pay_type.fee)) / 100
            WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_tax_insurance.value > 0 AND pay_type.pay_type_id
            THEN (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
            ) - (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
                * (pay_type.tax + insurance_tax.medic)) / 100
            WHEN vw_fee_discount_on_pay_type.value > 0 AND vw_tax_insurance.value > 0 AND pay_type.pay_type_id
            THEN (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
            ) - (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
                * (pay_type.fee + insurance_tax.medic)) / 100
            WHEN vw_tax_insurance.value > 0
            THEN (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
            ) - (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
                * insurance_tax.medic) / 100
            WHEN vw_tax_discount_on_pay_type.value > 0 AND pay_type.pay_type_id
            THEN (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
            ) - (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
                * pay_type.tax) / 100
            WHEN vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id
            THEN (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
            ) - (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
                * pay_type.fee) / 100
            ELSE (
                ((CASE WHEN `order_pay`.value IS NULL THEN order_item.value WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - order_item.discount))
            )
        END * (order_item.participation_value / 100)
    WHEN order_item.participation_type = '$' THEN ((CASE 
		WHEN vw_tax_insurance.value > 0 AND  vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id  -- IMPOSTO CONVENIO / IMPOSTO PAGAMENTO / TAXA
	    THEN CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * (insurance_tax.medic + pay_type.tax + pay_type.fee)) / 100 
	   
	    WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id  --  IMPOSTO PAGAMENTO / TAXA
	    THEN CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * (pay_type.tax + pay_type.fee)) / 100 
	     
	    WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_tax_insurance.value > 0 AND pay_type.pay_type_id  -- IMPOSTO PAGAMENTO / IMPOSTO CONVENIO
	    THEN CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * (pay_type.tax + insurance_tax.medic )) / 100 
	     
	    WHEN vw_fee_discount_on_pay_type.value > 0 AND vw_tax_insurance.value > 0 AND pay_type.pay_type_id  -- IMPOSTO CONVENIO / TAXA
	    THEN CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * (pay_type.fee + insurance_tax.medic )) / 100 
	     
	    WHEN vw_tax_insurance.value > 0 
	    THEN CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * insurance_tax.medic ) / 100 -- IMPOSTO CONVENIO
	    
	    WHEN vw_tax_discount_on_pay_type.value > 0 AND pay_type.pay_type_id 
		THEN CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * pay_type.tax ) / 100 -- IMPOSTO PAGAMENTO
		 
		WHEN vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id 
		THEN CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END - (CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END * pay_type.fee ) / 100  -- TAXA
	     
		ELSE CASE
		WHEN `order_pay`.value IS NULL THEN order_item.value
		WHEN `order_pay`.value = `order`.value THEN order_item.value ELSE (order_item.value / 100) * ((`order_pay`.value / `order`.value) * 100) END END) *
		
		((`order_item`.participation_value / order_item.value) * 100 / 100)) ELSE .0 END) + COALESCE(cte_product.value_product,0)) AS value_company			
		
		-- Fim dos campos calculados
		
		FROM order_item
		
		JOIN `order` ON `order`.order_id = order_item.order_id
		LEFT JOIN company_cost_center ON company_cost_center.company_cost_center_id = `order`.company_cost_center_id
		LEFT JOIN company ON company.company_id = company_cost_center.company_id
		LEFT JOIN cost_center ON cost_center.cost_center_id = company_cost_center.cost_center_id
		LEFT JOIN performer ON performer.performer_id = order_item.performer_id
		LEFT JOIN professional ON professional.professional_id = performer.professional_id
		
		-- Parametros de Profissionais
		LEFT JOIN cte_parameters AS vw_tax_insurance ON vw_tax_insurance.name = 'DISCOUNT_TAX_ON_INSURANCE' AND vw_tax_insurance.key = professional.professional_id
		LEFT JOIN cte_parameters AS vw_tax_discount_on_pay_type ON vw_tax_discount_on_pay_type.name = 'DISCOUNT_TAX_ON_PAY_TYPE' AND vw_tax_discount_on_pay_type.key = professional.professional_id
		LEFT JOIN cte_parameters AS vw_fee_discount_on_pay_type ON vw_fee_discount_on_pay_type.name = 'DISCOUNT_FEE_ON_PAY_TYPE' AND vw_fee_discount_on_pay_type.key = professional.professional_id
		LEFT JOIN person person_performer ON person_performer.person_id = professional.person_id
		LEFT JOIN insurance ON insurance.insurance_id = `order`.insurance_id
		LEFT JOIN patient ON patient.patient_id = `order`.patient_id
		LEFT JOIN person person_patient ON person_patient.person_id = patient.person_id
						
		-- Verificar essa expressão acredito que vai projetar valores errados 
		LEFT JOIN insurance_tax ON insurance_tax.insurance_id = insurance.insurance_id
		LEFT JOIN speciality ON speciality.speciality_id = professional.speciality_id
		LEFT JOIN `procedure` ON `procedure`.procedure_id = order_item.procedure_id
		LEFT JOIN `group` ON `group`.group_id = `procedure`.group_id
		LEFT JOIN order_pay ON order_pay.order_id = `order`.`order_id`
		LEFT JOIN pay_type ON pay_type.pay_type_id = order_pay.pay_type_id
		LEFT JOIN session_item ON session_item.order_item_id = order_item.order_item_id 
		LEFT JOIN cte_product ON cte_product.order_item_id = order_item.order_item_id 
		
		WHERE 

				$P!{performerRange}
				$P!{costCenterRange}
				$P!{insuranceRange}
				$P!{firmRange}
				$P!{specialityRange}
				$P!{groupProcedureRange}
				$P!{procedureRange}
				$P!{userRange}
				$P!{onlyGerarateCashRange}
				$P!{notGerarateCashRange}
				$P!{horaRange}
				$P!{dateRange}  	and
 				$P!{sessionHeldCondition}  and 
 				$P!{hideCoparticipationCondition}

 GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17

		),
		
		cte_base_aux AS (
		
		SELECT 
			performer_auxiliary.performer_id AS performer_id,
			insurance.insurance_id,
			auxiliary.name AS type_auxiliary,
			person_auxiliary.name AS performer_name,
			company.name AS company_name,
			insurance.name AS insuranre_name,
			speciality.name AS speciality_name,
			`order`.`date` AS date_launch,
			`order`.hour AS hour_launch,
			`order`.number AS guideNumber,
			order_item.name AS procedure_name,
			person_patient.name AS patient_name,
			order_item.participation_value AS percent,
			order_item.tax_value AS tax,			
			pay_type.pay_type_id,
			performer.professional_id,
			order_item.quantity AS procedure_quantity,
		-- Valor Total 			
		SUM(order_item_auxiliary.value) AS `total`,
			
		-- Inicio dos campos Calculados
			SUM(CASE 
        WHEN vw_tax_insurance.value > 0 THEN 
            CASE 
                WHEN order_item_auxiliary.tax_type = '%' THEN	
                    (order_item_auxiliary.value - 
                    (order_item_auxiliary.value * COALESCE(order_item_auxiliary.tax_value, 0) / 100)) 
                WHEN order_item_auxiliary.tax_type = '$' THEN	
                    order_item_auxiliary.value
                ELSE 
                    order_item_auxiliary.value 
            END
        ELSE 
            order_item_auxiliary.value  -- Ou outro valor padrão, se necessário
    END) AS base_calculo,	 
		
		SUM(CASE 
        WHEN vw_tax_insurance.value > 0 THEN 
            CASE 
                WHEN order_item_auxiliary.tax_type = '%' THEN	
                    (order_item_auxiliary.value - 
                    (order_item_auxiliary.value * COALESCE(order_item_auxiliary.tax_value, 0) / 100)) 
                WHEN order_item_auxiliary.tax_type = '$' THEN	
                    order_item_auxiliary.value
                ELSE 
                    order_item_auxiliary.value 
            END
        ELSE 
            order_item_auxiliary.value  -- Ou outro valor padrão, se necessário
    END) AS value_performer,	 
				
		SUM(order_item_auxiliary.value - CASE 
        WHEN vw_tax_insurance.value > 0 THEN 
            CASE 
                WHEN order_item_auxiliary.tax_type = '%' THEN	
                    (order_item_auxiliary.value - 
                    (order_item_auxiliary.value * COALESCE(order_item_auxiliary.tax_value, 0) / 100)) 
                WHEN order_item_auxiliary.tax_type = '$' THEN	
                    order_item_auxiliary.value
                ELSE 
                    order_item_auxiliary.value 
            END
        ELSE 
            order_item_auxiliary.value  -- Ou outro valor padrão, se necessário
    END) AS value_company	
		
		-- Fim dos campos calculados
		
		FROM order_item_auxiliary
		
				-- Parametros de Auxiliares
		LEFT JOIN order_item ON order_item_auxiliary.order_item_id = order_item.order_item_id 
		LEFT JOIN auxiliary ON auxiliary.auxiliary_id = order_item_auxiliary.auxiliary_id 
		LEFT JOIN performer AS performer_auxiliary ON performer_auxiliary.performer_id = order_item_auxiliary.performer_id 
		LEFT JOIN professional AS professional_auxiliary ON professional_auxiliary.professional_id = performer_auxiliary.professional_id 
		LEFT JOIN person AS person_auxiliary ON person_auxiliary.person_id = professional_auxiliary.person_id 
		
		LEFT JOIN `order` ON `order`.order_id = order_item.order_id
		LEFT JOIN company_cost_center ON company_cost_center.company_cost_center_id = `order`.company_cost_center_id
		LEFT JOIN company ON company.company_id = company_cost_center.company_id
		LEFT JOIN cost_center ON cost_center.cost_center_id = company_cost_center.cost_center_id
		LEFT JOIN performer ON performer.performer_id = order_item.performer_id
		LEFT JOIN professional ON professional.professional_id = performer.professional_id
		
		-- Parametros de Profissionais
		LEFT JOIN cte_parameters AS vw_tax_insurance ON vw_tax_insurance.name = 'DISCOUNT_TAX_ON_INSURANCE' AND vw_tax_insurance.key = professional.professional_id
		LEFT JOIN cte_parameters AS vw_tax_discount_on_pay_type ON vw_tax_discount_on_pay_type.name = 'DISCOUNT_TAX_ON_PAY_TYPE' AND vw_tax_discount_on_pay_type.key = professional.professional_id
		LEFT JOIN cte_parameters AS vw_fee_discount_on_pay_type ON vw_fee_discount_on_pay_type.name = 'DISCOUNT_FEE_ON_PAY_TYPE' AND vw_fee_discount_on_pay_type.key = professional.professional_id
		LEFT JOIN person person_performer ON person_performer.person_id = professional.person_id
		LEFT JOIN insurance ON insurance.insurance_id = `order`.insurance_id
		LEFT JOIN patient ON patient.patient_id = `order`.patient_id
		LEFT JOIN person person_patient ON person_patient.person_id = patient.person_id
				
		-- Verificar essa expressão acredito que vai projetar valores errados 
		LEFT JOIN insurance_tax ON insurance_tax.insurance_id = insurance.insurance_id
		LEFT JOIN speciality ON speciality.speciality_id = professional.speciality_id
		LEFT JOIN `procedure` ON `procedure`.procedure_id = order_item.procedure_id
		LEFT JOIN `group` ON `group`.group_id = `procedure`.group_id
		LEFT JOIN order_pay ON order_pay.order_id = `order`.`order_id`
		LEFT JOIN pay_type ON pay_type.pay_type_id = order_pay.pay_type_id
		LEFT JOIN session_item ON session_item.order_item_id = order_item.order_item_id 
		
		WHERE 
		
				$P!{performerRange}
				$P!{auxiliaryRange}
				$P!{costCenterRange}
				$P!{insuranceRange}
				$P!{firmRange}
				$P!{specialityRange}
				$P!{groupProcedureRange}
				$P!{procedureRange}
				$P!{userRange}
				$P!{onlyGerarateCashRange}
				$P!{notGerarateCashRange}
				$P!{horaRange}
				$P!{dateRange}  	and
 				$P!{sessionHeldCondition}  and 
 				$P!{hideCoparticipationCondition}

 GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17
		),
		
		cte_base_third_party AS (
		
		SELECT 
			performer.performer_id AS performer_id,
			insurance.insurance_id,
			"Terceiro" AS type_auxiliary,
			person_third_party.name AS performer_name,
			company.name AS company_name,
			insurance.name AS insuranre_name,
			speciality.name AS speciality_name,
			`order`.`date` AS date_launch,
			`order`.hour AS hour_launch,
			`order`.number AS guideNumber,
			order_item.name AS procedure_name,
			person_patient.name AS patient_name,
			order_item.participation_value AS percent,
			order_item.tax_value AS tax,
			pay_type.pay_type_id,
			performer.professional_id,
			0 AS procedure_quantity,
		-- Valor Total 			
		NULL AS `total`,
			
		-- Inicio dos campos Calculados
		NULL AS base_calculo,	 
		
		SUM(order_item_third_party.value) AS value_performer,	 
				
		NULL AS value_company	
		
		-- Fim dos campos calculados
		
		FROM order_item_third_party
		
				-- Parametros de Auxiliares
		LEFT JOIN order_item ON order_item.order_item_id = order_item_third_party.order_item_id 
		LEFT JOIN third_party ON third_party.third_party_id = order_item_third_party.third_party_id 
		LEFT JOIN performer ON performer.performer_id = third_party.performer_id 
		LEFT JOIN professional AS professional_third_party ON professional_third_party.professional_id = performer.professional_id 
		LEFT JOIN person AS person_third_party ON person_third_party.person_id = professional_third_party.person_id 
		
		LEFT JOIN `order` ON `order`.order_id = order_item.order_id
		LEFT JOIN company_cost_center ON company_cost_center.company_cost_center_id = `order`.company_cost_center_id
		LEFT JOIN company ON company.company_id = company_cost_center.company_id
		LEFT JOIN cost_center ON cost_center.cost_center_id = company_cost_center.cost_center_id
		LEFT JOIN professional ON professional.professional_id = performer.professional_id
		
		-- Parametros de Profissionais
		LEFT JOIN cte_parameters AS vw_tax_insurance ON vw_tax_insurance.name = 'DISCOUNT_TAX_ON_INSURANCE' AND vw_tax_insurance.key = professional.professional_id
		LEFT JOIN cte_parameters AS vw_tax_discount_on_pay_type ON vw_tax_discount_on_pay_type.name = 'DISCOUNT_TAX_ON_PAY_TYPE' AND vw_tax_discount_on_pay_type.key = professional.professional_id
		LEFT JOIN cte_parameters AS vw_fee_discount_on_pay_type ON vw_fee_discount_on_pay_type.name = 'DISCOUNT_FEE_ON_PAY_TYPE' AND vw_fee_discount_on_pay_type.key = professional.professional_id
		LEFT JOIN person person_performer ON person_performer.person_id = professional.person_id
		LEFT JOIN insurance ON insurance.insurance_id = `order`.insurance_id
		LEFT JOIN patient ON patient.patient_id = `order`.patient_id
		LEFT JOIN person person_patient ON person_patient.person_id = patient.person_id
				
		-- Verificar essa expressão acredito que vai projetar valores errados 
		LEFT JOIN insurance_tax ON insurance_tax.insurance_id = insurance.insurance_id
		LEFT JOIN speciality ON speciality.speciality_id = professional.speciality_id
		LEFT JOIN `procedure` ON `procedure`.procedure_id = order_item.procedure_id
		LEFT JOIN `group` ON `group`.group_id = `procedure`.group_id
		LEFT JOIN order_pay ON order_pay.order_id = `order`.`order_id`
		LEFT JOIN pay_type ON pay_type.pay_type_id = order_pay.pay_type_id
		LEFT JOIN session_item ON session_item.order_item_id = order_item.order_item_id 
		
		WHERE 
		
				$P!{performerRange}
				$P!{costCenterRange}
				$P!{insuranceRange}
				$P!{firmRange}
				$P!{specialityRange}
				$P!{groupProcedureRange}
				$P!{procedureRange}
				$P!{userRange}
				$P!{onlyGerarateCashRange}
				$P!{notGerarateCashRange}
				$P!{horaRange}
				$P!{dateRange}  	and
 				$P!{sessionHeldCondition}  and 
 				$P!{hideCoparticipationCondition}
 
GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17
		)
		
	SELECT 
		cte_base.*
	FROM cte_base
	
	UNION ALL
	
	SELECT 
		cte_base_aux.*
	FROM cte_base_aux
	
	UNION ALL
	
	SELECT 
		cte_base_third_party.*
	FROM cte_base_third_party
	
	 ORDER BY
		4,
		6,
		8,
		9]]>
	</queryString>
	<field name="performer_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="performer_id"/>
		<property name="com.jaspersoft.studio.field.label" value="performer_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="performer"/>
		<fieldDescription><![CDATA[Id do Executante]]></fieldDescription>
	</field>
	<field name="insurance_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="insurance_id"/>
		<property name="com.jaspersoft.studio.field.label" value="insurance_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="insurance"/>
		<fieldDescription><![CDATA[Id do Convênio]]></fieldDescription>
	</field>
	<field name="type_auxiliary" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="type_auxiliary"/>
		<property name="com.jaspersoft.studio.field.label" value="type_auxiliary"/>
	</field>
	<field name="performer_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="performer_name"/>
		<property name="com.jaspersoft.studio.field.label" value="performer_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="company_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="company_name"/>
		<property name="com.jaspersoft.studio.field.label" value="company_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="company"/>
	</field>
	<field name="insuranre_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="insuranre_name"/>
		<property name="com.jaspersoft.studio.field.label" value="insuranre_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="insurance"/>
	</field>
	<field name="speciality_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="speciality_name"/>
		<property name="com.jaspersoft.studio.field.label" value="speciality_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="speciality"/>
	</field>
	<field name="date_launch" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.name" value="date_launch"/>
		<property name="com.jaspersoft.studio.field.label" value="date_launch"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
	</field>
	<field name="hour_launch" class="java.sql.Time">
		<property name="com.jaspersoft.studio.field.name" value="hour_launch"/>
		<property name="com.jaspersoft.studio.field.label" value="hour_launch"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
	</field>
	<field name="guideNumber" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="guideNumber"/>
		<property name="com.jaspersoft.studio.field.label" value="guideNumber"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
	</field>
	<field name="procedure_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="procedure_name"/>
		<property name="com.jaspersoft.studio.field.label" value="procedure_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="patient_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="patient_name"/>
		<property name="com.jaspersoft.studio.field.label" value="patient_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="percent" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="percent"/>
		<property name="com.jaspersoft.studio.field.label" value="percent"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="tax" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="tax"/>
		<property name="com.jaspersoft.studio.field.label" value="tax"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="pay_type_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="pay_type_id"/>
		<property name="com.jaspersoft.studio.field.label" value="pay_type_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="pay_type"/>
		<fieldDescription><![CDATA[Id Forma de Pagamento]]></fieldDescription>
	</field>
	<field name="professional_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="professional_id"/>
		<property name="com.jaspersoft.studio.field.label" value="professional_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="performer"/>
		<fieldDescription><![CDATA[Id Profissional]]></fieldDescription>
	</field>
	<field name="procedure_quantity" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="procedure_quantity"/>
		<property name="com.jaspersoft.studio.field.label" value="procedure_quantity"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="total" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="total"/>
		<property name="com.jaspersoft.studio.field.label" value="total"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
		<fieldDescription><![CDATA[Total do Item]]></fieldDescription>
	</field>
	<field name="base_calculo" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="base_calculo"/>
		<property name="com.jaspersoft.studio.field.label" value="base_calculo"/>
	</field>
	<field name="value_performer" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="value_performer"/>
		<property name="com.jaspersoft.studio.field.label" value="value_performer"/>
	</field>
	<field name="value_company" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="value_company"/>
		<property name="com.jaspersoft.studio.field.label" value="value_company"/>
	</field>
	<variable name="total1" class="java.lang.Double" resetType="Group" resetGroup="insurance" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="base_calculo1" class="java.lang.Double" resetType="Group" resetGroup="insurance" calculation="Sum">
		<variableExpression><![CDATA[$F{base_calculo}]]></variableExpression>
	</variable>
	<variable name="value_performer1" class="java.lang.Double" resetType="Group" resetGroup="insurance" calculation="Sum">
		<variableExpression><![CDATA[$F{value_performer}]]></variableExpression>
	</variable>
	<variable name="value_company1" class="java.lang.Double" resetType="Group" resetGroup="insurance" calculation="Sum">
		<variableExpression><![CDATA[$F{base_calculo}-$F{value_performer}]]></variableExpression>
	</variable>
	<variable name="procedure_quantity1" class="java.lang.Double" resetType="Group" resetGroup="insurance" calculation="Sum">
		<variableExpression><![CDATA[$F{procedure_quantity}]]></variableExpression>
	</variable>
	<variable name="procedure_quantity2" class="java.lang.Double" resetType="Group" resetGroup="professional" calculation="Sum">
		<variableExpression><![CDATA[$F{procedure_quantity}]]></variableExpression>
	</variable>
	<variable name="total2" class="java.lang.Double" resetType="Group" resetGroup="professional" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="base_calculo2" class="java.lang.Double" resetType="Group" resetGroup="professional" calculation="Sum">
		<variableExpression><![CDATA[$F{base_calculo}]]></variableExpression>
	</variable>
	<variable name="value_performer2" class="java.lang.Double" resetType="Group" resetGroup="professional" calculation="Sum">
		<variableExpression><![CDATA[$F{value_performer}]]></variableExpression>
	</variable>
	<variable name="value_company2" class="java.lang.Double" resetType="Group" resetGroup="professional" calculation="Sum">
		<variableExpression><![CDATA[$F{base_calculo}-$F{value_performer}]]></variableExpression>
	</variable>
	<variable name="procedure_quantity3" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{procedure_quantity}]]></variableExpression>
	</variable>
	<variable name="total3" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="base_calculo3" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{base_calculo}]]></variableExpression>
	</variable>
	<variable name="value_company3" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{value_company}]]></variableExpression>
	</variable>
	<variable name="value_performer3" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{value_performer}]]></variableExpression>
	</variable>
	<variable name="procedure_quantity4" class="java.lang.Double" resetType="Group" resetGroup="professional" calculation="Sum">
		<variableExpression><![CDATA[$F{procedure_quantity}]]></variableExpression>
	</variable>
	<variable name="total4" class="java.lang.Double" resetType="Group" resetGroup="professional" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="base_calculo4" class="java.lang.Double" resetType="Group" resetGroup="professional" calculation="Sum">
		<variableExpression><![CDATA[$F{base_calculo}]]></variableExpression>
	</variable>
	<variable name="value_performer4" class="java.lang.Double" resetType="Group" resetGroup="professional" calculation="Sum">
		<variableExpression><![CDATA[$F{value_performer}]]></variableExpression>
	</variable>
	<variable name="value_company4" class="java.lang.Double" resetType="Group" resetGroup="professional" calculation="Sum">
		<variableExpression><![CDATA[$F{value_company}]]></variableExpression>
	</variable>
	<group name="professional" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{performer_id}]]></groupExpression>
		<groupHeader>
			<band height="17">
				<textField isBlankWhenNull="true">
					<reportElement x="30" y="2" width="290" height="10" uuid="d446760a-8268-446b-aeb7-1534d2f09c9a"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{performer_name}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="2" width="30" height="10" uuid="88d093ff-ab0f-43cb-9402-f34b8969d898">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Dr(a).:]]></text>
				</staticText>
				<line>
					<reportElement x="0" y="1" width="555" height="1" uuid="5f786713-4970-413a-afe8-b092a49b31a6">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<line>
					<reportElement x="0" y="12" width="555" height="1" uuid="327c33e4-f4f3-4662-88f2-26d7d9331fc3">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="9">
				<staticText>
					<reportElement x="240" y="1" width="110" height="8" uuid="8f1d6514-bb42-4372-aea8-68095ce94275">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Total do Profissional:]]></text>
				</staticText>
				<textField pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="351" y="1" width="19" height="8" uuid="660bc92b-de78-4713-8df4-8986ed9ecfda">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{procedure_quantity4}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="371" y="1" width="45" height="8" uuid="f41c6119-76bc-45da-b7f0-48cbb77ce7d5">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$P{printValue}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{total4}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="417" y="1" width="45" height="8" uuid="b168533b-2478-4192-83eb-75793d114222">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$P{printCalculationBasis}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{base_calculo4}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="509" y="1" width="45" height="8" uuid="febf7c9f-566d-4604-b98b-ef5963c2d219">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{value_performer4}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="463" y="1" width="45" height="8" uuid="2e6052bf-9e56-43ee-bac8-db6e4ffa8ba8">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$P{printValueClinical}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{value_company4}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="insurance" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{insurance_id}]]></groupExpression>
		<groupHeader>
			<band height="27">
				<staticText>
					<reportElement x="0" y="14" width="35" height="8" uuid="5b372dd9-1a84-4508-84af-3d6081aa556b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Data]]></text>
				</staticText>
				<staticText>
					<reportElement x="36" y="14" width="25" height="8" uuid="65d50393-e5cc-4637-aacd-2f9ff545b65f">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Hora]]></text>
				</staticText>
				<staticText>
					<reportElement x="63" y="14" width="32" height="8" uuid="56425de1-ec16-4222-b6c1-4878e66a2df2">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Guia]]></text>
				</staticText>
				<staticText>
					<reportElement x="96" y="14" width="121" height="8" uuid="c362f59d-40ed-48aa-b459-087459330f30">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Serviço]]></text>
				</staticText>
				<staticText>
					<reportElement x="219" y="14" width="96" height="8" uuid="d3e2d439-fd7d-4b86-a4f4-4903fbfe6c8b">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<printWhenExpression><![CDATA[$P{printPatient}]]></printWhenExpression>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Paciente]]></text>
				</staticText>
				<staticText>
					<reportElement x="351" y="14" width="19" height="8" uuid="658fab67-3085-4b7c-9efd-6bea3d1c35d9">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Qtd.]]></text>
				</staticText>
				<staticText>
					<reportElement x="371" y="14" width="45" height="8" uuid="8d03fbd8-9aaf-477b-8c3b-d6d266d687f6">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$P{printValue}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Valor]]></text>
				</staticText>
				<staticText>
					<reportElement x="417" y="14" width="45" height="8" uuid="9bb21f3f-a674-42a0-b8e8-0093a62dd472">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$P{printCalculationBasis}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[B.Calc]]></text>
				</staticText>
				<staticText>
					<reportElement x="509" y="14" width="45" height="8" uuid="78a0352c-7964-4cbd-9071-d37e833f4a17">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Med(R$)]]></text>
				</staticText>
				<staticText>
					<reportElement x="463" y="14" width="45" height="8" uuid="d760d93f-9c16-4e8d-8fe3-1b68d2cd8857">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$P{printValueClinical}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Clinica]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="2" width="58" height="10" uuid="2e887e07-8b05-4817-b82f-45506a469a61">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Convênio:]]></text>
				</staticText>
				<textField>
					<reportElement x="66" y="2" width="474" height="10" uuid="f7686787-27d1-4fa9-942e-971831d937b8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{insuranre_name}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="22" width="555" height="1" uuid="a9661ec0-8a97-48ac-a110-ada66f6e5574">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="9">
				<staticText>
					<reportElement x="240" y="1" width="110" height="8" uuid="dabe026b-7f15-4071-99cf-b25dcbb5466d">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Total do Convênio:]]></text>
				</staticText>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="371" y="1" width="45" height="8" uuid="e2a9c8b2-6cf9-4b88-ac38-3f7d12a7cf98">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$P{printValue}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{total1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="417" y="1" width="45" height="8" uuid="fef1e0d8-9048-4cde-a949-d16b8e872f2b">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$P{printCalculationBasis}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{base_calculo1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="509" y="1" width="45" height="8" uuid="477aa37b-ad09-43f0-9280-0a1197f61ce1">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{value_performer1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="463" y="1" width="45" height="8" uuid="73503fe4-f402-44f5-9f56-614be965307c">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$P{printValueClinical}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{value_company1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="351" y="1" width="19" height="8" uuid="7eb214d5-e6aa-4aba-a581-2ad12b9fcf4f">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{procedure_quantity1}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<pageHeader>
		<band height="94">
			<textField isBlankWhenNull="true">
				<reportElement x="86" y="31" width="339" height="25" uuid="321f1f63-b46c-439c-a430-8165e08bf9f1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Atendimento médico/convênio analítico"]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy">
				<reportElement x="491" y="11" width="60" height="15" uuid="e11903a3-920d-48d0-b89c-80f2ee64d61c"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="521" y="39" width="30" height="15" uuid="cb3aa02c-7575-4b86-87c8-9000228b885f"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="428" y="11" width="58" height="14" uuid="ee7dd153-6548-48cc-b06c-cefec483ceb3"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Data:]]></text>
			</staticText>
			<staticText>
				<reportElement x="428" y="39" width="58" height="14" uuid="ed407f09-8cb1-4d5a-9121-132a6a2aad1b"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Página:]]></text>
			</staticText>
			<image hAlign="Center" vAlign="Middle">
				<reportElement x="-9" y="4" width="85" height="40" uuid="59c84930-55aa-4a7a-af69-2e386701470d"/>
				<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64(new String($P{companyImage}).getBytes("UTF-8")))]]></imageExpression>
			</image>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="6" y="75" width="542" height="16" uuid="6402bda8-77f9-4af2-9180-b51b332e81fc"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{filters}.isEmpty() ? null : $P{filters}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="219" y="62" width="181" height="13" uuid="7e73f7dc-2685-4299-b5ab-d4d53f2963a1"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Filtros Selecionados]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="86" y="2" width="339" height="30" uuid="10c97d4d-01dd-4674-bd38-c91df8d39112">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{companyName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="428" y="25" width="58" height="14" uuid="5caec54e-2731-4c4a-a32d-a4fac0113823"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Hora:]]></text>
			</staticText>
			<textField pattern="HH:mm">
				<reportElement x="491" y="24" width="60" height="15" uuid="f0d82f9d-3c9a-45b4-9db8-4ea0b4bd592e"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="9">
			<textField pattern="dd/MM/yyyy">
				<reportElement x="0" y="1" width="35" height="8" uuid="a4728605-8d96-4489-a893-40020ddfa6e0">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{date_launch}]]></textFieldExpression>
			</textField>
			<textField pattern="HH:mm">
				<reportElement x="36" y="1" width="25" height="8" uuid="0223b965-b097-4330-9529-3c7bb3d8557d">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{hour_launch}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="63" y="1" width="32" height="8" uuid="d63c84be-dd2d-44fe-aa0d-7495d3ab5a36">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{guideNumber}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="96" y="1" width="121" height="8" uuid="6e8518ca-0428-46b0-8bee-4c0653feacc0">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{procedure_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="219" y="1" width="96" height="8" uuid="9f86e551-1e2e-40e3-950a-2eb534758db0">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{printPatient}]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{patient_name}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="351" y="1" width="19" height="8" uuid="43882952-9c6f-4cd2-b3d6-5a74df520129">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{procedure_quantity}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="371" y="1" width="45" height="8" uuid="d09059a2-455e-453c-92e2-c640324a6812">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{printValue}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{total} != null ? $F{total} : "T"]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="417" y="1" width="45" height="8" uuid="30604f06-da99-4d73-abbb-be700a0b905f">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$P{printCalculationBasis}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{base_calculo}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="509" y="1" width="45" height="8" uuid="5e3ee4f7-cf7c-4543-b4ec-1e4470d212c7">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{value_performer}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="463" y="1" width="45" height="8" uuid="90981d45-a009-4873-a45e-f7b7b1b0854f">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$P{printValueClinical}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{base_calculo} - $F{value_performer}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="316" y="1" width="34" height="8" uuid="4cda28db-4b29-4e14-80fa-cfe99edef0b8">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="5" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{type_auxiliary}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="9">
			<textField isBlankWhenNull="true">
				<reportElement x="351" y="1" width="19" height="8" uuid="e9f3531a-1642-4f63-87d5-35a5d3ebb75e">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{procedure_quantity3}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="371" y="1" width="45" height="8" uuid="2bd834aa-68df-4227-a400-939fa8eb582e">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{printValue}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{total3}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="240" y="1" width="110" height="8" uuid="3440295c-20c8-4858-ae7f-7593679137ca">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Total do Geral:]]></text>
			</staticText>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="417" y="1" width="45" height="8" uuid="935ffbea-1d26-4084-89ca-7f91f60bf171">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{printCalculationBasis}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{base_calculo3}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="463" y="1" width="45" height="8" uuid="c686a65a-ba09-4421-8a21-70f03dbe93aa">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{printValueClinical}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{value_company3}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="509" y="1" width="45" height="8" uuid="9ef6f9ae-3232-4c19-b09b-bbbb69060939">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{value_performer3}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
