<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="application-report-order-attendance-performer-insurance-analytical" pageWidth="595" pageHeight="841" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4ff84453-1320-4ac5-a9ad-242cce5098f5">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_vida_psi"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="0"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="1000"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<subDataset name="Dataset1" uuid="7fe0c814-75e0-454d-bbae-8410466db122">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MySql "/>
		<queryString language="SQL">
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="costCenter" class="java.util.ArrayList"/>
	<parameter name="performer" class="java.util.ArrayList"/>
	<parameter name="insurance" class="java.util.ArrayList"/>
	<parameter name="dataInicio" class="java.lang.String"/>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="filters" class="java.util.ArrayList"/>
	<parameter name="nameReport" class="java.lang.String"/>
	<parameter name="user" class="java.util.ArrayList"/>
	<parameter name="costCenterStart" class="java.lang.String"/>
	<parameter name="costCenterEnd" class="java.lang.String"/>
	<parameter name="performerStart" class="java.lang.String"/>
	<parameter name="performerEnd" class="java.lang.String"/>
	<parameter name="performerRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "$X{IN,performer.performer_id, performer} " :
"person_performer.`name`  >= " +"$P" +"{performerStart}"+" AND person_performer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="costCenterRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{costCenterStart} == null || $P{costCenterEnd} == null ? "and $X{IN,cost_center.cost_center_id, costCenter} " :
"and cost_center.`name`  >= " +"$P" +"{costCenterStart}"+" AND cost_center.`name` <= " + "$P" + "{costCenterEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{userStart} == null || $P{userEnd} == null ? "and $X{IN,user.user_id, user} " :
"and personUser.`name`  >= " +"$P" +"{userStart}"+" AND personUser.`name` <= " + "$P" + "{userEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userEnd" class="java.lang.String"/>
	<parameter name="userStart" class="java.lang.String"/>
	<parameter name="dateRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{dataInicio} == null || $P{dataFim} == null ? " " :
"AND order_item.`date` BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="firm" class="java.util.ArrayList"/>
	<parameter name="firmStart" class="java.lang.String"/>
	<parameter name="firmEnd" class="java.lang.String"/>
	<parameter name="firmRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{firmStart} == null || $P{firmEnd} == null ? "and  $X{IN,company.company_id, firm} " :
"and company.`name`  >= " +"$P" +"{firmStart}"+" AND company.`name` <= " + "$P" + "{firmEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="onlyGerarateCash" class="java.util.ArrayList"/>
	<parameter name="notGerarateCash" class="java.util.ArrayList"/>
	<parameter name="onlyGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{onlyGerarateCash} ==  null ?  " " : "and  $X{IN,insurance.payment_model, onlyGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="notGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{notGerarateCash} ==  null ? " " : "and  $X{IN,insurance.payment_model, notGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="printValue" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="printValueClinical" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="printCalculationBasis" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="speciality" class="java.util.ArrayList"/>
	<parameter name="specialityStart" class="java.lang.String"/>
	<parameter name="specialityEnd" class="java.lang.String"/>
	<parameter name="specialityRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{specialityStart} == null || $P{specialityEnd} == null ? "and  $X{IN,speciality.speciality_id, speciality} " :
"and speciality.`name`  >= " +"$P" +"{specialityStart}"+" AND speciality.`name` <= " + "$P" + "{specialityEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="insuranceStart" class="java.lang.String"/>
	<parameter name="insuranceEnd" class="java.lang.String"/>
	<parameter name="insuranceRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{insuranceStart} == null || $P{insuranceEnd} == null ? "and  $X{IN,`insurance`.`insurance_id`, insurance} " :
"and `insurance`.`name`  >= " +"$P" +"{insuranceStart}"+" AND `insurance`.`name` <= " + "$P" + "{insuranceEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="groupProcedure" class="java.util.ArrayList"/>
	<parameter name="groupProcedureStart" class="java.lang.String"/>
	<parameter name="groupProcedureEnd" class="java.lang.String"/>
	<parameter name="groupProcedureRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{groupProcedureStart} == null || $P{groupProcedureEnd} == null ? "and  $X{IN, `group`.group_id, groupProcedure} " :
"and `group`.`name`  >= " +"$P" +"{groupProcedureStart}"+" AND `group`.`name` <= " + "$P" + "{groupProcedureEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT 
`order_item`.`order_item_id`,
performer.performer_id,
insurance.insurance_id,
image.content AS logo,
person_performer.name AS performer_name,
company.name AS company_name,
insurance.name AS insuranre_name,
speciality.name AS speciality_name,
`order`.`date` AS date_launch,
`order`.`hour` AS hour_launch,
`order`.`number` AS guideNumber,
order_item.name AS procedure_name,
person_patient.name AS patient_name,
order_item.participation_value AS percent,
order_item.tax_value AS tax,
order_item.quantity AS procedure_quantity,
order_item.total AS total,


		CASE WHEN `order_item`.`tax_value` IS NOT NULL AND  vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id  -- IMPOSTO CONVENIO / IMPOSTO PAGAMENTO / TAXA
	     THEN `order_item`.`total` - (`order_item`.`total` * (`order_item`.`tax_value` + pay_type.tax + pay_type.fee)) / 100 
	   
	     WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id  --  IMPOSTO PAGAMENTO / TAXA
	     THEN `order_item`.`total` - (`order_item`.`total` * (pay_type.tax + pay_type.fee)) / 100 
	     
	     WHEN vw_tax_discount_on_pay_type.value > 0 AND `order_item`.`tax_value` IS NOT NULL AND pay_type.pay_type_id  -- IMPOSTO PAGAMENTO / IMPOSTO CONVENIO
	     THEN `order_item`.`total` - (`order_item`.`total` * (pay_type.tax + `order_item`.`tax_value` )) / 100 
	     
	     WHEN vw_fee_discount_on_pay_type.value > 0 AND `order_item`.`tax_value` IS NOT NULL AND pay_type.pay_type_id  -- IMPOSTO CONVENIO / TAXA
	     THEN `order_item`.`total` - (`order_item`.`total` * (pay_type.fee + `order_item`.`tax_value` )) / 100 
	     
	     WHEN `order_item`.`tax_value` IS NOT NULL 
	     THEN `order_item`.`total` - (`order_item`.`total` * `order_item`.`tax_value` ) / 100 -- IMPOSTO CONVENIO
	    
	     WHEN vw_tax_discount_on_pay_type.value > 0 AND pay_type.pay_type_id 
		 THEN `order_item`.`total` - (`order_item`.`total` * pay_type.tax ) / 100 -- IMPOSTO PAGAMENTO
		 
		 WHEN vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id 
		 THEN `order_item`.`total` - (`order_item`.`total` * pay_type.fee ) / 100  -- TAXA
	     
		 ELSE `order_item`.`total` END AS base_calculo,
		 
		 CASE WHEN order_item.participation_type = '%' THEN	
		 CASE WHEN `order_item`.`tax_value` IS NOT NULL AND  vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id -- IMPOSTO CONVENIO / IMPOSTO PAGAMENTO / TAXA
	     THEN `order_item`.`total` - (`order_item`.`total` * (`order_item`.`tax_value` + pay_type.tax + pay_type.fee)) / 100 
	   
	     WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id --  IMPOSTO PAGAMENTO / TAXA
	     THEN `order_item`.`total` - (`order_item`.`total` * (pay_type.tax + pay_type.fee)) / 100 
	     
	     WHEN vw_tax_discount_on_pay_type.value > 0 AND `order_item`.`tax_value` IS NOT NULL AND pay_type.pay_type_id -- IMPOSTO PAGAMENTO / IMPOSTO CONVENIO
	     THEN `order_item`.`total` - (`order_item`.`total` * (pay_type.tax + `order_item`.`tax_value` )) / 100 
	     
	     WHEN vw_fee_discount_on_pay_type.value > 0 AND `order_item`.`tax_value` IS NOT NULL AND pay_type.pay_type_id -- IMPOSTO CONVENIO / TAXA
	     THEN `order_item`.`total` - (`order_item`.`total` * (pay_type.fee + `order_item`.`tax_value` )) / 100 
	     
	     WHEN `order_item`.`tax_value` IS NOT NULL
	     THEN `order_item`.`total` - (`order_item`.`total` * `order_item`.`tax_value` ) / 100 -- IMPOSTO CONVENIO
	    
	     WHEN vw_tax_discount_on_pay_type.value > 0 AND pay_type.pay_type_id
		 THEN `order_item`.`total` - (`order_item`.`total` * pay_type.tax ) / 100 -- IMPOSTO PAGAMENTO
		 
		 WHEN vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id
		 THEN `order_item`.`total` - (`order_item`.`total` * pay_type.fee ) / 100  -- TAXA
	     
		 ELSE `order_item`.`total` END * (order_item.participation_value / 100) 
		 WHEN  order_item.participation_type = '$' THEN order_item.participation_value ELSE .0 END
		 AS value_performer,
		 
		 
		 order_item.total - CASE WHEN `order_item`.`tax_value` IS NOT NULL AND  vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id -- IMPOSTO CONVENIO / IMPOSTO PAGAMENTO / TAXA
	     THEN `order_item`.`total` - (`order_item`.`total` * (`order_item`.`tax_value` + pay_type.tax + pay_type.fee)) / 100 
	   
	     WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id --  IMPOSTO PAGAMENTO / TAXA
	     THEN `order_item`.`total` - (`order_item`.`total` * (pay_type.tax + pay_type.fee)) / 100 
	     
	     WHEN vw_tax_discount_on_pay_type.value > 0 AND `order_item`.`tax_value` IS NOT NULL AND pay_type.pay_type_id -- IMPOSTO PAGAMENTO / IMPOSTO CONVENIO
	     THEN `order_item`.`total` - (`order_item`.`total` * (pay_type.tax + `order_item`.`tax_value` )) / 100 
	     
	     WHEN vw_fee_discount_on_pay_type.value > 0 AND `order_item`.`tax_value` IS NOT NULL AND pay_type.pay_type_id -- IMPOSTO CONVENIO / TAXA
	     THEN `order_item`.`total` - (`order_item`.`total` * (pay_type.fee + `order_item`.`tax_value` )) / 100 
	     
	     WHEN `order_item`.`tax_value` IS NOT NULL
	     THEN `order_item`.`total` - ((`order_item`.`total` * `order_item`.`tax_value`) / 100) -- IMPOSTO CONVENIO
	    
	     WHEN vw_tax_discount_on_pay_type.value > 0 AND pay_type.pay_type_id
		 THEN `order_item`.`total` - (`order_item`.`total` * pay_type.tax ) / 100 -- IMPOSTO PAGAMENTO
		 
		 WHEN vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id
		 THEN `order_item`.`total` - (`order_item`.`total` * pay_type.fee ) / 100  -- TAXA
	     
		 ELSE `order_item`.`total` END * (order_item.participation_value / 100) AS value_company

FROM 

order_item

LEFT JOIN `order` ON `order`.order_id = order_item.order_id 
LEFT JOIN company_cost_center ON company_cost_center.company_cost_center_id = `order`.company_cost_center_id 
LEFT JOIN company ON company.company_id = company_cost_center.company_id
LEFT JOIN cost_center ON cost_center.cost_center_id = company_cost_center.cost_center_id
LEFT JOIN performer ON performer.performer_id = order_item.performer_id 
LEFT JOIN professional ON professional.professional_id = performer.professional_id 
LEFT JOIN person person_performer ON person_performer.person_id = professional.person_id 
LEFT JOIN insurance ON insurance.insurance_id = `order`.insurance_id
LEFT JOIN patient ON patient.patient_id = `order`.patient_id
LEFT JOIN person person_patient ON person_patient.person_id = patient.person_id
LEFT JOIN order_pay ON order_pay.order_id = `order`.order_id 
LEFT JOIN pay_type ON pay_type.pay_type_id = order_pay.pay_type_id 
LEFT JOIN insurance_tax  ON insurance_tax.insurance_id = insurance.insurance_id
LEFT JOIN image ON image.image_id = company.image_id
LEFT JOIN speciality ON speciality.speciality_id = professional.speciality_id
LEFT JOIN `procedure` ON`procedure`.procedure_id = order_item.procedure_id
LEFT JOIN `group` ON `group`.group_id = `procedure`.group_id

LEFT JOIN (SELECT COALESCE(parameter_value.value, parameter.value) AS value,
            parameter_value.key 
            FROM parameter 
            LEFT JOIN parameter_value ON parameter_value.parameter_id = parameter.parameter_id 
            WHERE parameter.name = "DISCOUNT_TAX_ON_INSURANCE"
            ) AS vw_tax_insurance ON vw_tax_insurance.key = professional.professional_id 

LEFT JOIN (SELECT COALESCE(parameter_value.value, parameter.value) AS value,
            parameter_value.key 
            FROM parameter 
            LEFT JOIN parameter_value ON parameter_value.parameter_id = parameter.parameter_id 
            WHERE parameter.name = "DISCOUNT_TAX_ON_PAY_TYPE"
            ) AS vw_tax_discount_on_pay_type ON vw_tax_discount_on_pay_type.key = professional.professional_id
            
LEFT JOIN (SELECT COALESCE(parameter_value.value, parameter.value) AS value,
            parameter_value.key 
            FROM parameter 
            LEFT JOIN parameter_value ON parameter_value.parameter_id = parameter.parameter_id 
            WHERE parameter.name = "DISCOUNT_FEE_ON_PAY_TYPE"
            ) AS vw_fee_discount_on_pay_type ON vw_fee_discount_on_pay_type.key = professional.professional_id  


 WHERE 
 
 $P!{performerRange}
 $P!{costCenterRange}
 $P!{insuranceRange}
  $P!{firmRange}
  $P!{specialityRange}
  $P!{groupProcedureRange}
 $P!{userRange}
  $P!{onlyGerarateCashRange}
  $P!{notGerarateCashRange}
 $P!{dateRange}  
 
GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20
ORDER BY 2,5,9]]>
	</queryString>
	<field name="order_item_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="order_item_id"/>
		<property name="com.jaspersoft.studio.field.label" value="order_item_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
		<fieldDescription><![CDATA[Id do Item da Conta]]></fieldDescription>
	</field>
	<field name="performer_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="performer_id"/>
		<property name="com.jaspersoft.studio.field.label" value="performer_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="performer"/>
		<fieldDescription><![CDATA[Id do Executante]]></fieldDescription>
	</field>
	<field name="insurance_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="insurance_id"/>
		<property name="com.jaspersoft.studio.field.label" value="insurance_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="insurance"/>
		<fieldDescription><![CDATA[Id do Convênio]]></fieldDescription>
	</field>
	<field name="logo" class="byte[]">
		<property name="com.jaspersoft.studio.field.name" value="content"/>
		<property name="com.jaspersoft.studio.field.label" value="logo"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="image"/>
	</field>
	<field name="performer_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="performer_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="company_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="company_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="company"/>
	</field>
	<field name="insuranre_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="insuranre_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="insurance"/>
	</field>
	<field name="speciality_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="speciality_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="speciality"/>
	</field>
	<field name="date_launch" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.name" value="date"/>
		<property name="com.jaspersoft.studio.field.label" value="date_launch"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
	</field>
	<field name="hour_launch" class="java.sql.Time">
		<property name="com.jaspersoft.studio.field.name" value="hour"/>
		<property name="com.jaspersoft.studio.field.label" value="hour_launch"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
	</field>
	<field name="guideNumber" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="number"/>
		<property name="com.jaspersoft.studio.field.label" value="guideNumber"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
	</field>
	<field name="procedure_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="procedure_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="patient_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="patient_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="percent" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="participation_value"/>
		<property name="com.jaspersoft.studio.field.label" value="percent"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="tax" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="tax_value"/>
		<property name="com.jaspersoft.studio.field.label" value="tax"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="procedure_quantity" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="quantity"/>
		<property name="com.jaspersoft.studio.field.label" value="procedure_quantity"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="total" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="total"/>
		<property name="com.jaspersoft.studio.field.label" value="total"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
		<fieldDescription><![CDATA[Total do Item]]></fieldDescription>
	</field>
	<field name="base_calculo" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="base_calculo"/>
		<property name="com.jaspersoft.studio.field.label" value="base_calculo"/>
	</field>
	<field name="value_performer" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="value_performer"/>
		<property name="com.jaspersoft.studio.field.label" value="value_performer"/>
	</field>
	<field name="value_company" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="value_company"/>
		<property name="com.jaspersoft.studio.field.label" value="value_company"/>
	</field>
	<variable name="total1" class="java.lang.Double" resetType="Group" resetGroup="insurance" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="base_calculo1" class="java.lang.Double" resetType="Group" resetGroup="insurance" calculation="Sum">
		<variableExpression><![CDATA[$F{base_calculo}]]></variableExpression>
	</variable>
	<variable name="value_performer1" class="java.lang.Double" resetType="Group" resetGroup="insurance" calculation="Sum">
		<variableExpression><![CDATA[$F{value_performer}]]></variableExpression>
	</variable>
	<variable name="value_company1" class="java.lang.Double" resetType="Group" resetGroup="insurance" calculation="Sum">
		<variableExpression><![CDATA[$F{base_calculo}-$F{value_performer}]]></variableExpression>
	</variable>
	<variable name="procedure_quantity1" class="java.lang.Double" resetType="Group" resetGroup="insurance" calculation="Sum">
		<variableExpression><![CDATA[$F{procedure_quantity}]]></variableExpression>
	</variable>
	<variable name="procedure_quantity2" class="java.lang.Double" resetType="Group" resetGroup="professional" calculation="Sum">
		<variableExpression><![CDATA[$F{procedure_quantity}]]></variableExpression>
	</variable>
	<variable name="total2" class="java.lang.Double" resetType="Group" resetGroup="professional" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="base_calculo2" class="java.lang.Double" resetType="Group" resetGroup="professional" calculation="Sum">
		<variableExpression><![CDATA[$F{base_calculo}]]></variableExpression>
	</variable>
	<variable name="value_performer2" class="java.lang.Double" resetType="Group" resetGroup="professional" calculation="Sum">
		<variableExpression><![CDATA[$F{value_performer}]]></variableExpression>
	</variable>
	<variable name="value_company2" class="java.lang.Double" resetType="Group" resetGroup="professional" calculation="Sum">
		<variableExpression><![CDATA[$F{base_calculo}-$F{value_performer}]]></variableExpression>
	</variable>
	<variable name="procedure_quantity3" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{procedure_quantity}]]></variableExpression>
	</variable>
	<variable name="total3" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="base_calculo3" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{base_calculo}]]></variableExpression>
	</variable>
	<variable name="value_performer3" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{value_performer}]]></variableExpression>
	</variable>
	<variable name="value_company3" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{value_company}]]></variableExpression>
	</variable>
	<group name="professional" isStartNewPage="true" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{performer_id}]]></groupExpression>
		<groupHeader>
			<band height="25">
				<textField isBlankWhenNull="true">
					<reportElement x="54" y="8" width="491" height="14" uuid="d446760a-8268-446b-aeb7-1534d2f09c9a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{performer_name}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="5" y="8" width="48" height="14" uuid="88d093ff-ab0f-43cb-9402-f34b8969d898">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Dr(a).:]]></text>
				</staticText>
				<line>
					<reportElement x="0" y="1" width="555" height="1" uuid="5f786713-4970-413a-afe8-b092a49b31a6">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<line>
					<reportElement x="0" y="24" width="555" height="1" uuid="327c33e4-f4f3-4662-88f2-26d7d9331fc3">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="17">
				<staticText>
					<reportElement x="290" y="3" width="100" height="14" uuid="081ccd2f-7040-4828-90df-04bc79f49881">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Total do Profissional:]]></text>
				</staticText>
				<textField pattern="#,##0">
					<reportElement x="394" y="2" width="19" height="14" uuid="fb13f4c9-77c6-49c7-88da-5ec5900bc15f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{procedure_quantity2}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="416" y="2" width="30" height="14" uuid="97da9eb8-3d3f-43cb-b41d-e045d5368cee">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$P{printValue}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{total2}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="449" y="2" width="30" height="14" uuid="cb83fa5c-9e05-4469-9d72-641639efe54e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$P{printCalculationBasis}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{base_calculo2}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="515" y="2" width="30" height="14" uuid="5abd8abb-f029-4afe-a362-69af25101b63">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{value_performer2}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="482" y="2" width="30" height="14" uuid="437c2407-4f24-48b4-989c-9d338bab8be2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$P{printValueClinical}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{value_company2}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="insurance" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{insurance_id}]]></groupExpression>
		<groupHeader>
			<band height="35">
				<staticText>
					<reportElement x="4" y="20" width="40" height="14" uuid="5b372dd9-1a84-4508-84af-3d6081aa556b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Data]]></text>
				</staticText>
				<staticText>
					<reportElement x="47" y="20" width="24" height="14" uuid="65d50393-e5cc-4637-aacd-2f9ff545b65f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Hora]]></text>
				</staticText>
				<staticText>
					<reportElement x="73" y="20" width="30" height="14" uuid="56425de1-ec16-4222-b6c1-4878e66a2df2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Guia]]></text>
				</staticText>
				<staticText>
					<reportElement x="105" y="20" width="147" height="14" uuid="c362f59d-40ed-48aa-b459-087459330f30">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Serviço]]></text>
				</staticText>
				<staticText>
					<reportElement x="254" y="20" width="136" height="14" uuid="d3e2d439-fd7d-4b86-a4f4-4903fbfe6c8b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Paciente]]></text>
				</staticText>
				<staticText>
					<reportElement x="394" y="20" width="19" height="14" uuid="658fab67-3085-4b7c-9efd-6bea3d1c35d9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Qtd.]]></text>
				</staticText>
				<staticText>
					<reportElement x="416" y="20" width="30" height="14" uuid="8d03fbd8-9aaf-477b-8c3b-d6d266d687f6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$P{printValue}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Valor]]></text>
				</staticText>
				<staticText>
					<reportElement x="449" y="20" width="30" height="14" uuid="9bb21f3f-a674-42a0-b8e8-0093a62dd472">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$P{printCalculationBasis}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[B.Calc]]></text>
				</staticText>
				<staticText>
					<reportElement x="515" y="20" width="30" height="14" uuid="78a0352c-7964-4cbd-9071-d37e833f4a17">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Med(R$)]]></text>
				</staticText>
				<staticText>
					<reportElement x="482" y="20" width="30" height="14" uuid="d760d93f-9c16-4e8d-8fe3-1b68d2cd8857">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<printWhenExpression><![CDATA[$P{printValueClinical}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Clinica]]></text>
				</staticText>
				<staticText>
					<reportElement x="4" y="2" width="58" height="14" uuid="2e887e07-8b05-4817-b82f-45506a469a61">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Convênio:]]></text>
				</staticText>
				<textField>
					<reportElement x="70" y="2" width="474" height="14" uuid="f7686787-27d1-4fa9-942e-971831d937b8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{insuranre_name}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="31" width="555" height="1" uuid="a9661ec0-8a97-48ac-a110-ada66f6e5574">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="17">
				<staticText>
					<reportElement x="302" y="3" width="88" height="14" uuid="dabe026b-7f15-4071-99cf-b25dcbb5466d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Total do Convênio:]]></text>
				</staticText>
				<textField pattern="#,##0.00">
					<reportElement x="416" y="2" width="30" height="14" uuid="e2a9c8b2-6cf9-4b88-ac38-3f7d12a7cf98">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<printWhenExpression><![CDATA[$P{printValue}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{total1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="449" y="2" width="30" height="14" uuid="fef1e0d8-9048-4cde-a949-d16b8e872f2b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<printWhenExpression><![CDATA[$P{printCalculationBasis}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{base_calculo1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="515" y="2" width="30" height="14" uuid="477aa37b-ad09-43f0-9280-0a1197f61ce1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{value_performer1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="482" y="2" width="30" height="14" uuid="73503fe4-f402-44f5-9f56-614be965307c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<printWhenExpression><![CDATA[$P{printValueClinical}]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{value_company1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0">
					<reportElement x="394" y="2" width="19" height="14" uuid="7eb214d5-e6aa-4aba-a581-2ad12b9fcf4f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{procedure_quantity1}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<pageHeader>
		<band height="94">
			<textField isBlankWhenNull="true">
				<reportElement x="86" y="31" width="339" height="25" uuid="321f1f63-b46c-439c-a430-8165e08bf9f1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{nameReport}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy">
				<reportElement x="491" y="11" width="60" height="15" uuid="e11903a3-920d-48d0-b89c-80f2ee64d61c"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="521" y="39" width="30" height="15" uuid="cb3aa02c-7575-4b86-87c8-9000228b885f"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="428" y="11" width="58" height="14" uuid="ee7dd153-6548-48cc-b06c-cefec483ceb3"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Data:]]></text>
			</staticText>
			<staticText>
				<reportElement x="428" y="39" width="58" height="14" uuid="ed407f09-8cb1-4d5a-9121-132a6a2aad1b"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Página:]]></text>
			</staticText>
			<image hAlign="Center" vAlign="Middle">
				<reportElement x="-9" y="4" width="85" height="40" uuid="59c84930-55aa-4a7a-af69-2e386701470d"/>
				<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64(new String($F{logo}).getBytes("UTF-8")))]]></imageExpression>
			</image>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="6" y="75" width="542" height="16" uuid="6402bda8-77f9-4af2-9180-b51b332e81fc"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{filters}.isEmpty() ? null : $P{filters}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="219" y="62" width="181" height="21" uuid="7e73f7dc-2685-4299-b5ab-d4d53f2963a1"/>
				<textElement textAlignment="Center">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Filtros Selecionados]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="86" y="2" width="339" height="30" uuid="10c97d4d-01dd-4674-bd38-c91df8d39112">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{company_name}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="428" y="25" width="58" height="14" uuid="5caec54e-2731-4c4a-a32d-a4fac0113823"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Hora:]]></text>
			</staticText>
			<textField pattern="HH:mm">
				<reportElement x="491" y="24" width="60" height="15" uuid="f0d82f9d-3c9a-45b4-9db8-4ea0b4bd592e"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="16">
			<textField pattern="dd/MM/yyyy">
				<reportElement x="4" y="2" width="40" height="14" uuid="a4728605-8d96-4489-a893-40020ddfa6e0">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{date_launch}]]></textFieldExpression>
			</textField>
			<textField pattern="hh:mm">
				<reportElement x="47" y="2" width="24" height="14" uuid="0223b965-b097-4330-9529-3c7bb3d8557d">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{hour_launch}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="73" y="2" width="30" height="14" uuid="d63c84be-dd2d-44fe-aa0d-7495d3ab5a36">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{guideNumber}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="105" y="2" width="147" height="14" uuid="6e8518ca-0428-46b0-8bee-4c0653feacc0">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{procedure_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="254" y="2" width="136" height="14" uuid="9f86e551-1e2e-40e3-950a-2eb534758db0">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{patient_name}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0">
				<reportElement x="394" y="2" width="19" height="14" uuid="43882952-9c6f-4cd2-b3d6-5a74df520129">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{procedure_quantity}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="416" y="2" width="30" height="14" uuid="d09059a2-455e-453c-92e2-c640324a6812">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$P{printValue}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{total}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="449" y="2" width="30" height="14" uuid="30604f06-da99-4d73-abbb-be700a0b905f">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$P{printCalculationBasis}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{base_calculo}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="515" y="2" width="30" height="14" uuid="5e3ee4f7-cf7c-4543-b4ec-1e4470d212c7">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{value_performer}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="482" y="2" width="30" height="14" uuid="90981d45-a009-4873-a45e-f7b7b1b0854f">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[$P{printValueClinical}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{base_calculo}-$F{value_performer}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="17">
			<staticText>
				<reportElement x="312" y="3" width="78" height="14" uuid="a6a61b2c-2260-47de-9810-5e172eba4fbf">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Geral:]]></text>
			</staticText>
			<textField pattern="#,##0">
				<reportElement x="394" y="2" width="19" height="14" uuid="34c79ba8-89fe-460f-8ee6-7e77ad0118b7">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{procedure_quantity3}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="416" y="2" width="30" height="14" uuid="474c5244-ba8f-4900-81d6-5335ab4c8bde">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[$P{printValue}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{total3}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="449" y="2" width="30" height="14" uuid="caa62dcc-9a84-4cd2-baad-5970e8a9f6ed">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{printCalculationBasis}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{base_calculo3}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="515" y="2" width="30" height="14" uuid="6076b6b2-6700-4456-acee-9931b37e58f5">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{value_performer3}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="482" y="2" width="30" height="14" uuid="dc6d3027-42a6-4a86-8c32-e648403d102d">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{printValueClinical}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{value_company3}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
