<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="application-report-order-attendance-insurance-synthetic" pageWidth="801" pageHeight="555" orientation="Landscape" whenNoDataType="NoDataSection" columnWidth="801" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="4ff84453-1320-4ac5-a9ad-242cce5098f5">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_unidor"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="0"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="1000"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<subDataset name="Dataset1" uuid="7fe0c814-75e0-454d-bbae-8410466db122">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MySql "/>
		<queryString language="SQL">
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="filters" class="java.util.ArrayList"/>
	<parameter name="nameReport" class="java.lang.String"/>
	<parameter name="costCenter" class="java.util.ArrayList"/>
	<parameter name="performer" class="java.util.ArrayList"/>
	<parameter name="dataInicio" class="java.lang.String"/>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="user" class="java.util.ArrayList"/>
	<parameter name="costCenterStart" class="java.lang.String"/>
	<parameter name="costCenterEnd" class="java.lang.String"/>
	<parameter name="performerStart" class="java.lang.String"/>
	<parameter name="performerEnd" class="java.lang.String"/>
	<parameter name="performerRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "$X{IN,performer.performer_id, performer} " :
"person_performer.`name`  >= " +"$P" +"{performerStart}"+" AND person_performer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="costCenterRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{costCenterStart} == null || $P{costCenterEnd} == null ? "and $X{IN,cost_center.cost_center_id, costCenter} " :
"and cost_center.`name`  >= " +"$P" +"{costCenterStart}"+" AND cost_center.`name` <= " + "$P" + "{costCenterEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{userStart} == null || $P{userEnd} == null ? "and $X{IN,user.user_id, user} " :
"and person_user.`name`  >= " +"$P" +"{userStart}"+" AND person_user.`name` <= " + "$P" + "{userEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userEnd" class="java.lang.String"/>
	<parameter name="userStart" class="java.lang.String"/>
	<parameter name="dateRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{dataInicio} == null || $P{dataFim} == null ? " " :
"AND order_authorization.release_date BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="firm" class="java.util.ArrayList"/>
	<parameter name="firmStart" class="java.lang.String"/>
	<parameter name="firmEnd" class="java.lang.String"/>
	<parameter name="firmRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{firmStart} == null || $P{firmEnd} == null ? "and  $X{IN,company.company_id, firm} " :
"and company.`name`  >= " +"$P" +"{firmStart}"+" AND company.`name` <= " + "$P" + "{firmEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="insurance" class="java.util.ArrayList"/>
	<parameter name="insuranceStart" class="java.lang.String"/>
	<parameter name="insuranceEnd" class="java.lang.String"/>
	<parameter name="insuranceRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{insuranceStart} == null || $P{insuranceEnd} == null ? "and  $X{IN,`insurance`.`insurance_id`, insurance} " :
"and `insurance`.`name`  >= " +"$P" +"{insuranceStart}"+" AND `insurance`.`name` <= " + "$P" + "{insuranceEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="patientStart" class="java.lang.String"/>
	<parameter name="patientEnd" class="java.lang.String"/>
	<parameter name="patientRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{patientStart} == null || $P{patientEnd} == null ? " and $X{IN,patient.patient_id, patient} " :
" and person.`name`  >= " +"$P" +"{patientStart}"+" AND person.`name` <= " + "$P" + "{patientEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="patient" class="java.util.ArrayList"/>
	<queryString>
		<![CDATA[SELECT 

    `order`.`number`,
    GROUP_CONCAT(DATE_FORMAT(order_item.`date`, '%d/%m') ORDER BY order_item.date ASC SEPARATOR ', ') AS item_dates,
    order_authorization.release_date  AS authorization_date,
    `order`.value,
    insurance.name AS insurance_name,
    billing.dateat AS billing_date,
    person.name AS person_name,
    person_performer.name AS name_performer
    
FROM `order`

LEFT JOIN order_item ON `order`.order_id = order_item.order_id
LEFT JOIN order_authorization ON order_authorization.order_id = `order`.order_id
LEFT JOIN insurance ON insurance.insurance_id  = `order`.insurance_id 
LEFT JOIN billing_order_item ON billing_order_item.order_item_id = order_item.order_item_id 
LEFT JOIN billing ON billing.billing_id = billing_order_item.billing_id 
LEFT JOIN patient ON patient.patient_id = `order`.patient_id
LEFT JOIN person ON person.person_id = patient.person_id
LEFT JOIN order_custon ON order_custon.order_id = `order`.order_id 
LEFT JOIN `professional` AS `professional_specialist` ON `order_custon`.`value` = `professional_specialist`.professional_id 
LEFT JOIN `performer`	on `professional_specialist`.professional_id  = `performer`.professional_id 
LEFT JOIN person AS person_performer ON person_performer.person_id = `professional_specialist`.person_id

WHERE 
order_custon.`key` = 'S' and
    $P!{performerRange} -- ESPECIALISTA
 $P!{insuranceRange} -- PLANO DE SAUDE
 $P!{patientRange} -- PACIENTE
 $P!{dateRange}  -- DATA DE AUTORIZAÇÃO

    
GROUP BY 

    1,3,4,5,6,7,8;]]>
	</queryString>
	<field name="number" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="number"/>
		<property name="com.jaspersoft.studio.field.label" value="number"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
		<fieldDescription><![CDATA[Numero da Guia]]></fieldDescription>
	</field>
	<field name="item_dates" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="item_dates"/>
		<property name="com.jaspersoft.studio.field.label" value="item_dates"/>
	</field>
	<field name="authorization_date" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.name" value="release_date"/>
		<property name="com.jaspersoft.studio.field.label" value="authorization_date"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_authorization"/>
	</field>
	<field name="value" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="value"/>
		<property name="com.jaspersoft.studio.field.label" value="value"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
		<fieldDescription><![CDATA[Valor Ordem De Venda]]></fieldDescription>
	</field>
	<field name="insurance_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="insurance_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="insurance"/>
	</field>
	<field name="billing_date" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.name" value="dateat"/>
		<property name="com.jaspersoft.studio.field.label" value="billing_date"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="billing"/>
	</field>
	<field name="person_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="person_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="name_performer" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="name_performer"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<group name="Group1">
		<groupHeader>
			<band height="12">
				<staticText>
					<reportElement x="154" y="0" width="70" height="12" uuid="0236dc28-e20b-4644-b0d4-e343e813e7aa">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Valor da guia]]></text>
				</staticText>
				<staticText>
					<reportElement x="224" y="0" width="80" height="12" uuid="09884242-2e9f-445f-8719-33bf489e201b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Plano de saúde]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="0" width="83" height="12" uuid="f1f8f942-2891-4af6-bd35-678496b19308">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Nome do paciente]]></text>
				</staticText>
				<staticText>
					<reportElement x="304" y="0" width="275" height="12" uuid="060779cc-4f6c-4f93-b9dd-2353da671c6c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Data dos atendimentos]]></text>
				</staticText>
				<staticText>
					<reportElement x="730" y="0" width="71" height="12" uuid="30ff5e26-d09b-45ba-9d45-62c8dd8e3a1a">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Data do faturamento]]></text>
				</staticText>
				<staticText>
					<reportElement x="659" y="0" width="71" height="12" uuid="7cc2b645-708c-474d-baa0-ecaa1a10808d">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Data de autorização]]></text>
				</staticText>
				<staticText>
					<reportElement x="83" y="0" width="71" height="12" uuid="a3f0c9e7-7be1-477a-8db0-6fac9eea4b5c">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[N da guia]]></text>
				</staticText>
				<staticText>
					<reportElement x="579" y="0" width="80" height="12" uuid="924a625a-f154-468c-8c0e-e5f49157e2cb">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Especialista]]></text>
				</staticText>
			</band>
		</groupHeader>
	</group>
	<detail>
		<band height="12">
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="83" height="12" uuid="28498395-423f-486e-87ea-4ff3a166bd04">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{person_name}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="154" y="0" width="70" height="12" uuid="ac82cbd0-469c-4b17-888d-c5b8dbc6b2c5">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{value}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="224" y="0" width="80" height="12" uuid="8016edc8-ab66-4dd7-af1b-f9d89c71a42e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{insurance_name}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="659" y="0" width="71" height="12" uuid="1fb8e71d-de1b-4886-a3c8-8ac637bdc281">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{authorization_date}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="304" y="0" width="275" height="12" uuid="d3d44262-d798-41bb-bc2b-380f534e7f63">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{item_dates}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="83" y="0" width="71" height="12" uuid="f01058f0-618e-43ca-8c75-d035ac710aea">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{number}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="730" y="0" width="71" height="12" uuid="74e03b1f-c96c-4bc9-8622-224a14243df2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{billing_date} == null ? "Não faturada" : $F{billing_date}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="579" y="0" width="80" height="12" uuid="a781be3c-cc97-45a4-9dfc-a3b8011c6199">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{name_performer}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
