<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="application-report-operational-closing-managerial-sub-user" pageWidth="802" pageHeight="555" orientation="Landscape" columnWidth="802" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="79f1ccd0-bd77-4262-9486-7ad4a029ad6c">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_demo"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="283"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="711"/>
	<property name="com.jaspersoft.studio.report.description" value=""/>
	<parameter name="request_id" class="java.lang.Integer" isForPrompting="false"/>
	<parameter name="costCenter" class="java.util.ArrayList"/>
	<parameter name="performer" class="java.util.ArrayList"/>
	<parameter name="dataInicio" class="java.lang.String"/>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="user" class="java.util.ArrayList"/>
	<parameter name="costCenterStart" class="java.lang.String"/>
	<parameter name="costCenterEnd" class="java.lang.String"/>
	<parameter name="performerStart" class="java.lang.String"/>
	<parameter name="performerEnd" class="java.lang.String"/>
	<parameter name="performerRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "$X{IN,performer.performer_id, performer} " :
"person_performer.`name`  >= " +"$P" +"{performerStart}"+" AND person_performer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="costCenterRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{costCenterStart} == null || $P{costCenterEnd} == null ? "and $X{IN,cost_center.cost_center_id, costCenter} " :
"and cost_center.`name`  >= " +"$P" +"{costCenterStart}"+" AND cost_center.`name` <= " + "$P" + "{costCenterEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{userStart} == null || $P{userEnd} == null ? "and $X{IN,user.user_id, user} " :
"and person_user.`name`  >= " +"$P" +"{userStart}"+" AND person_user.`name` <= " + "$P" + "{userEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userEnd" class="java.lang.String"/>
	<parameter name="userStart" class="java.lang.String"/>
	<parameter name="dateRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{dataInicio} == null || $P{dataFim} == null ? " " :
"AND order_item.`date` BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="firm" class="java.util.ArrayList"/>
	<parameter name="firmStart" class="java.lang.String"/>
	<parameter name="firmEnd" class="java.lang.String"/>
	<parameter name="firmRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{firmStart} == null || $P{firmEnd} == null ? "and  $X{IN,company.company_id, firm} " :
"and company.`name`  >= " +"$P" +"{firmStart}"+" AND company.`name` <= " + "$P" + "{firmEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="insurance" class="java.util.ArrayList"/>
	<parameter name="insuranceStart" class="java.lang.String"/>
	<parameter name="insuranceEnd" class="java.lang.String"/>
	<parameter name="insuranceRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{insuranceStart} == null || $P{insuranceEnd} == null ? "and  $X{IN,`insurance`.`insurance_id`, insurance} " :
"and `insurance`.`name`  >= " +"$P" +"{insuranceStart}"+" AND `insurance`.`name` <= " + "$P" + "{insuranceEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[-- Relatório Fechamento de Caixa Gerencial
WITH cte_cashier_manager AS (
	SELECT
			`user`.`user_id`,
			`person_user`.`name` AS `user_name`,
			`order`.`order_id`,
			`order`.value AS value,
			SUM(CASE
		WHEN `order_pay`.value IS NULL THEN order_item.total
		WHEN `order_pay`.value = `order`.value THEN order_item.`total` ELSE (order_item.`total` / 100) * ((`order_pay`.value / `order`.value) * 100) END) AS `valuepay`

	FROM `order`
	JOIN `order_item` ON `order_item`.`order_id` = `order`.`order_id`
	JOIN `user` ON `user`.`user_id` = `order`.`created_by`
	JOIN `person` AS `person_user` ON `person_user`.`person_id` = `user`.`person_id`
	JOIN `insurance` ON `insurance`.`insurance_id` = `order`.`insurance_id` AND `insurance`.`payment_model` = "P"

	LEFT JOIN `order_pay` ON `order_pay`.`order_id` = `order`.`order_id`

	-- Sentença para Eventual Filtragem
	LEFT JOIN `company_cost_center` ON `company_cost_center`.`company_cost_center_id` = `order`.`company_cost_center_id`
	LEFT JOIN `company` ON `company`.`company_id` = `company_cost_center`.`company_id` 
	LEFT JOIN `cost_center` ON `cost_center`.`cost_center_id` = `company_cost_center`.`cost_center_id`

WHERE 

 $P!{performerRange}
 $P!{costCenterRange}
  $P!{firmRange}
 $P!{userRange}
 $P!{dateRange}


	GROUP BY 1, 2, 3, 4
),
cte_cashier_manager_total AS (
	SELECT cte_cashier_manager.*,
		   SUM(cte_cashier_manager.value) OVER(PARTITION BY user_id) AS `total`,
		   SUM(cte_cashier_manager.valuepay) OVER(PARTITION BY user_id) AS `totalpay`,
		   SUM(cte_cashier_manager.value) OVER(PARTITION BY user_id) -
		   SUM(cte_cashier_manager.valuepay) OVER(PARTITION BY user_id) AS `totalopen`
	FROM cte_cashier_manager
)
SELECT cte_cashier_manager_total.user_id,
       cte_cashier_manager_total.user_name,
       cte_cashier_manager_total.total,
       cte_cashier_manager_total.totalpay,
       cte_cashier_manager_total.totalopen,
       CASE WHEN cte_cashier_manager_total.totalopen > 0 THEN "SIM" ELSE "NÃO" END AS status
       
FROM cte_cashier_manager_total
GROUP BY 1, 2, 3, 4, 5;]]>
	</queryString>
	<field name="user_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="user_id"/>
		<property name="com.jaspersoft.studio.field.label" value="user_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="user"/>
		<fieldDescription><![CDATA[Id Usuário]]></fieldDescription>
	</field>
	<field name="user_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="user_name"/>
		<property name="com.jaspersoft.studio.field.label" value="user_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="total" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="total"/>
		<property name="com.jaspersoft.studio.field.label" value="total"/>
	</field>
	<field name="totalpay" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="totalpay"/>
		<property name="com.jaspersoft.studio.field.label" value="totalpay"/>
	</field>
	<field name="totalopen" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="totalopen"/>
		<property name="com.jaspersoft.studio.field.label" value="totalopen"/>
	</field>
	<field name="status" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="status"/>
		<property name="com.jaspersoft.studio.field.label" value="status"/>
	</field>
	<variable name="alert" class="java.lang.String" incrementType="Report">
		<variableExpression><![CDATA[$V{total_pay_sum} != $V{total_sum} ? "Caixa do periodo: " + NumberFormat.getCurrencyInstance(new Locale("pt","BR")).format($V{total_sum} ) + " diferente dos pagamentos recebidos: " + NumberFormat.getCurrencyInstance(new Locale("pt","BR")).format($V{total_pay_sum})  : ""]]></variableExpression>
	</variable>
	<variable name="total_sum" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{total}]]></variableExpression>
	</variable>
	<variable name="total_pay_sum" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{totalpay}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="37">
			<line>
				<reportElement x="-2" y="33" width="801" height="1" uuid="89c34a32-e43e-4eec-910d-ea580a010cbe">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="4" y="1" width="175" height="12" uuid="bcc229fe-bab4-475e-b152-95718438dc1c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Fechamento dos caixas:]]></text>
			</staticText>
			<staticText>
				<reportElement x="13" y="20" width="237" height="13" uuid="924c9fcf-4d99-4b79-86a7-777751bf5c4a">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Usuário]]></text>
			</staticText>
			<staticText>
				<reportElement x="294" y="20" width="89" height="12" uuid="ac0cacb8-e906-4026-85a7-60b75db499c6">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Rec. em aberto]]></text>
			</staticText>
			<staticText>
				<reportElement x="402" y="20" width="89" height="12" uuid="da627254-ce5e-4350-a601-d04659d79ee9">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor em aberto]]></text>
			</staticText>
			<staticText>
				<reportElement x="507" y="20" width="89" height="12" uuid="c72743bb-d615-4735-a08b-1c1beb2244a3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor a repassar]]></text>
			</staticText>
			<staticText>
				<reportElement x="609" y="20" width="89" height="12" uuid="158090fa-d44f-4631-bff7-f3beb21739ed">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Status do caixa]]></text>
			</staticText>
			<staticText>
				<reportElement x="706" y="21" width="89" height="12" uuid="d599a304-9d1a-4050-80b0-4a5b9d9767cf">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor Repassado]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="14" splitType="Stretch">
			<textField pattern="#,##0.00##" isBlankWhenNull="true">
				<reportElement x="402" y="1" width="89" height="12" uuid="7928c542-9dfe-4576-ad8d-d451c6ebc07b">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="cde6d998-3c70-4dd7-826c-c120b229c701"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{totalopen}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="13" y="1" width="237" height="13" uuid="eedc1157-d2d8-4cda-bc99-5acbaa3928d8">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9b115df6-512d-4d33-a95a-467299ad43f8"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{user_name}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="294" y="2" width="89" height="12" uuid="e132f1ba-4581-4084-8ff3-0749d75b0409">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="cb307b0c-8804-4b34-8fa9-eb76a6e7c24d"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{status}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00##" isBlankWhenNull="true">
				<reportElement x="507" y="2" width="89" height="12" uuid="0deee22d-de11-4aad-9b14-614997738fa2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="e6a449f4-8f1b-49c8-ad47-2aaf21d39e9f"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{totalpay}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
