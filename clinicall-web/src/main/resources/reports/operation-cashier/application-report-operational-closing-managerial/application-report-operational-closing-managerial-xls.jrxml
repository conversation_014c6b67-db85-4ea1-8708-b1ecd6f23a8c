<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="application-report-operational-closing-payment-type" pageWidth="841" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="801" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4ff84453-1320-4ac5-a9ad-242cce5098f5">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_demo"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="0"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="1000"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<subDataset name="Dataset1" uuid="7fe0c814-75e0-454d-bbae-8410466db122">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MySql "/>
		<queryString language="SQL">
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="filters" class="java.util.ArrayList"/>
	<parameter name="nameReport" class="java.lang.String"/>
	<parameter name="costCenter" class="java.util.ArrayList"/>
	<parameter name="performer" class="java.util.ArrayList"/>
	<parameter name="dataInicio" class="java.lang.String"/>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="user" class="java.util.ArrayList"/>
	<parameter name="costCenterStart" class="java.lang.String"/>
	<parameter name="costCenterEnd" class="java.lang.String"/>
	<parameter name="performerStart" class="java.lang.String"/>
	<parameter name="performerEnd" class="java.lang.String"/>
	<parameter name="performerRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "$X{IN,performer.performer_id, performer} " :
"person_performer.`name`  >= " +"$P" +"{performerStart}"+" AND person_performer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="costCenterRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{costCenterStart} == null || $P{costCenterEnd} == null ? "and $X{IN,cost_center.cost_center_id, costCenter} " :
"and cost_center.`name`  >= " +"$P" +"{costCenterStart}"+" AND cost_center.`name` <= " + "$P" + "{costCenterEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{userStart} == null || $P{userEnd} == null ? "and $X{IN,user.user_id, user} " :
"and person_user.`name`  >= " +"$P" +"{userStart}"+" AND person_user.`name` <= " + "$P" + "{userEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userEnd" class="java.lang.String"/>
	<parameter name="userStart" class="java.lang.String"/>
	<parameter name="dateRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{dataInicio} == null || $P{dataFim} == null ? " " :
"AND order_item.`date` BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="firm" class="java.util.ArrayList"/>
	<parameter name="firmStart" class="java.lang.String"/>
	<parameter name="firmEnd" class="java.lang.String"/>
	<parameter name="firmRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{firmStart} == null || $P{firmEnd} == null ? "and  $X{IN,company.company_id, firm} " :
"and company.`name`  >= " +"$P" +"{firmStart}"+" AND company.`name` <= " + "$P" + "{firmEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="insurance" class="java.util.ArrayList"/>
	<parameter name="insuranceStart" class="java.lang.String"/>
	<parameter name="insuranceEnd" class="java.lang.String"/>
	<parameter name="insuranceRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{insuranceStart} == null || $P{insuranceEnd} == null ? "and  $X{IN,`insurance`.`insurance_id`, insurance} " :
"and `insurance`.`name`  >= " +"$P" +"{insuranceStart}"+" AND `insurance`.`name` <= " + "$P" + "{insuranceEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="companyImage" class="java.lang.String" isForPrompting="false"/>
	<parameter name="companyName" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT 
SUM(CASE
		WHEN `order_pay`.value IS NULL THEN order_item.total
		WHEN `order_pay`.value = `order`.value THEN order_item.`total` ELSE (order_item.`total` / 100) * ((`order_pay`.value / `order`.value) * 100) END) AS value,
`company`.`name`as company_name,
`image`.`content` as logo 

FROM `order`

LEFT JOIN `order_item` ON `order_item`.`order_id` = `order`.`order_id`
LEFT JOIN `order_pay` ON `order_pay`.`order_id` = `order`.`order_id` 
LEFT JOIN `order_pay_detail` ON `order_pay_detail`.`order_pay_id` = `order_pay`.`order_pay_id` 
LEFT JOIN `brand` ON `brand`.`brand_id` = `order_pay`.`brand_id`
LEFT JOIN `user` ON `user`.`user_id` = `order_item`.`created_by`
LEFT JOIN `person` AS `person_user` ON `person_user`.`person_id` = `user`.`person_id`
LEFT JOIN `company_cost_center` ON `company_cost_center`.`company_cost_center_id` = `order`.`company_cost_center_id` 
JOIN `company` ON `company`.`company_id` = `company_cost_center`.`company_id` 
JOIN `cost_center` ON `cost_center`.`cost_center_id` = `company_cost_center`.`cost_center_id`
LEFT JOIN `image` ON `image`.`image_id` = `company`.`image_id`
JOIN `insurance` ON `insurance`.`insurance_id`= `order`.`insurance_id` AND `insurance`.`payment_model` = "P"

WHERE 

 $P!{performerRange}
 $P!{costCenterRange}
  $P!{firmRange}
 $P!{userRange}
 $P!{dateRange}

GROUP BY 2,3]]>
	</queryString>
	<field name="value" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="value"/>
		<property name="com.jaspersoft.studio.field.label" value="value"/>
	</field>
	<field name="company_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="company_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="company"/>
	</field>
	<field name="logo" class="byte[]">
		<property name="com.jaspersoft.studio.field.name" value="content"/>
		<property name="com.jaspersoft.studio.field.label" value="logo"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="image"/>
	</field>
	<variable name="value1" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{value}]]></variableExpression>
	</variable>
	<group name="main">
		<groupHeader>
			<band height="57">
				<staticText>
					<reportElement x="5" y="4" width="102" height="12" uuid="ee04ddfd-3f87-42b8-a6e4-64447b6f2e35">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Caixa do periodo:]]></text>
				</staticText>
				<staticText>
					<reportElement x="6" y="23" width="162" height="12" uuid="b8a0da3b-0776-48a4-bf08-1af78ebc6c65">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Pagamentos Recebidos:]]></text>
				</staticText>
				<textField evaluationTime="Auto" pattern="#,##0.00##">
					<reportElement x="402" y="4" width="100" height="12" uuid="88de6043-9988-45a1-8114-8810dfd08261">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Right"/>
					<textFieldExpression><![CDATA[$V{value1}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="-2" y="1" width="801" height="1" uuid="d2e2833a-a9ce-4c0b-8f7e-f5a53ec7bd34">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
				</line>
			</band>
		</groupHeader>
	</group>
	<group name="pay-type">
		<groupHeader>
			<band height="26">
				<subreport>
					<reportElement x="0" y="0" width="801" height="26" uuid="1631f99b-0835-4aaf-ba71-b6ec5744f54b">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<subreportParameter name="performer">
						<subreportParameterExpression><![CDATA[$P{performer}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="performerStart">
						<subreportParameterExpression><![CDATA[$P{performerStart}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="performerEnd">
						<subreportParameterExpression><![CDATA[$P{performerEnd}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="performerRange">
						<subreportParameterExpression><![CDATA[$P{performerRange}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="costCenter">
						<subreportParameterExpression><![CDATA[$P{costCenter}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="costCenterStart">
						<subreportParameterExpression><![CDATA[$P{costCenterStart}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="costCenterEnd">
						<subreportParameterExpression><![CDATA[$P{costCenterEnd}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="costCenterRange">
						<subreportParameterExpression><![CDATA[$P{costCenterRange}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="firm">
						<subreportParameterExpression><![CDATA[$P{firm}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="firmStart">
						<subreportParameterExpression><![CDATA[$P{firmStart}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="firmEnd">
						<subreportParameterExpression><![CDATA[$P{firmEnd}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="firmRange">
						<subreportParameterExpression><![CDATA[$P{firmRange}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="user">
						<subreportParameterExpression><![CDATA[$P{user}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="userStart">
						<subreportParameterExpression><![CDATA[$P{userStart}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="userEnd">
						<subreportParameterExpression><![CDATA[$P{userEnd}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="userRange">
						<subreportParameterExpression><![CDATA[$P{userRange}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="insurance">
						<subreportParameterExpression><![CDATA[$P{insurance}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="insuranceStart">
						<subreportParameterExpression><![CDATA[$P{insuranceStart}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="insuranceEnd">
						<subreportParameterExpression><![CDATA[$P{insuranceEnd}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="insuranceRange">
						<subreportParameterExpression><![CDATA[$P{insuranceRange}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="dataInicio">
						<subreportParameterExpression><![CDATA[$P{dataInicio}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="dataFim">
						<subreportParameterExpression><![CDATA[$P{dataFim}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="dateRange">
						<subreportParameterExpression><![CDATA[$P{dateRange}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA["reports/operation-cashier/application-report-operational-closing-managerial/application-report-operational-closing-managerial-sub-paytype.jasper"]]></subreportExpression>
				</subreport>
			</band>
		</groupHeader>
	</group>
	<group name="user">
		<groupHeader>
			<band height="32">
				<subreport>
					<reportElement x="0" y="0" width="801" height="26" uuid="ff0260dd-b028-4d7a-86bd-897912cb7884">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<subreportParameter name="performer">
						<subreportParameterExpression><![CDATA[$P{performer}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="performerStart">
						<subreportParameterExpression><![CDATA[$P{performerStart}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="performerEnd">
						<subreportParameterExpression><![CDATA[$P{performerEnd}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="performerRange">
						<subreportParameterExpression><![CDATA[$P{performerRange}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="costCenter">
						<subreportParameterExpression><![CDATA[$P{costCenter}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="costCenterStart">
						<subreportParameterExpression><![CDATA[$P{costCenterStart}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="costCenterEnd">
						<subreportParameterExpression><![CDATA[$P{costCenterEnd}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="costCenterRange">
						<subreportParameterExpression><![CDATA[$P{costCenterRange}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="firm">
						<subreportParameterExpression><![CDATA[$P{firm}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="firmStart">
						<subreportParameterExpression><![CDATA[$P{firmStart}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="firmEnd">
						<subreportParameterExpression><![CDATA[$P{firmEnd}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="firmRange">
						<subreportParameterExpression><![CDATA[$P{firmRange}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="user">
						<subreportParameterExpression><![CDATA[$P{user}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="userStart">
						<subreportParameterExpression><![CDATA[$P{userStart}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="userEnd">
						<subreportParameterExpression><![CDATA[$P{userEnd}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="userRange">
						<subreportParameterExpression><![CDATA[$P{userRange}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="insurance">
						<subreportParameterExpression><![CDATA[$P{insurance}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="insuranceStart">
						<subreportParameterExpression><![CDATA[$P{insuranceStart}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="insuranceEnd">
						<subreportParameterExpression><![CDATA[$P{insuranceEnd}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="insuranceRange">
						<subreportParameterExpression><![CDATA[$P{insuranceRange}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="dataInicio">
						<subreportParameterExpression><![CDATA[$P{dataInicio}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="dataFim">
						<subreportParameterExpression><![CDATA[$P{dataFim}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="dateRange">
						<subreportParameterExpression><![CDATA[$P{dateRange}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA["reports/operation-cashier/application-report-operational-closing-managerial/application-report-operational-closing-managerial-sub-user.jasper"]]></subreportExpression>
				</subreport>
			</band>
		</groupHeader>
	</group>
	<group name="insurance">
		<groupHeader>
			<band height="26">
				<subreport>
					<reportElement x="0" y="0" width="801" height="26" uuid="3cc0287a-d19d-47cf-bca8-d9e6dddc3bfc">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<subreportParameter name="performer">
						<subreportParameterExpression><![CDATA[$P{performer}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="performerStart">
						<subreportParameterExpression><![CDATA[$P{performerStart}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="performerEnd">
						<subreportParameterExpression><![CDATA[$P{performerEnd}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="performerRange">
						<subreportParameterExpression><![CDATA[$P{performerRange}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="costCenter">
						<subreportParameterExpression><![CDATA[$P{costCenter}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="costCenterStart">
						<subreportParameterExpression><![CDATA[$P{costCenterStart}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="costCenterEnd">
						<subreportParameterExpression><![CDATA[$P{costCenterEnd}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="costCenterRange">
						<subreportParameterExpression><![CDATA[$P{costCenterRange}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="firm">
						<subreportParameterExpression><![CDATA[$P{firm}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="firmStart">
						<subreportParameterExpression><![CDATA[$P{firmStart}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="firmEnd">
						<subreportParameterExpression><![CDATA[$P{firmEnd}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="firmRange">
						<subreportParameterExpression><![CDATA[$P{firmRange}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="user">
						<subreportParameterExpression><![CDATA[$P{user}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="userStart">
						<subreportParameterExpression><![CDATA[$P{userStart}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="userEnd">
						<subreportParameterExpression><![CDATA[$P{userEnd}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="userRange">
						<subreportParameterExpression><![CDATA[$P{userRange}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="insurance">
						<subreportParameterExpression><![CDATA[$P{insurance}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="insuranceStart">
						<subreportParameterExpression><![CDATA[$P{insuranceStart}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="insuranceEnd">
						<subreportParameterExpression><![CDATA[$P{insuranceEnd}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="insuranceRange">
						<subreportParameterExpression><![CDATA[$P{insuranceRange}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="dataInicio">
						<subreportParameterExpression><![CDATA[$P{dataInicio}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="dataFim">
						<subreportParameterExpression><![CDATA[$P{dataFim}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="dateRange">
						<subreportParameterExpression><![CDATA[$P{dateRange}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA["reports/operation-cashier/application-report-operational-closing-managerial/application-report-operational-closing-managerial-sub-insurance.jasper"]]></subreportExpression>
				</subreport>
			</band>
		</groupHeader>
	</group>
	<pageHeader>
		<band height="92">
			<textField isBlankWhenNull="true">
				<reportElement x="157" y="31" width="463" height="25" uuid="321f1f63-b46c-439c-a430-8165e08bf9f1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{nameReport}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy">
				<reportElement x="728" y="17" width="60" height="15" uuid="e11903a3-920d-48d0-b89c-80f2ee64d61c"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="758" y="45" width="30" height="15" uuid="cb3aa02c-7575-4b86-87c8-9000228b885f"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="642" y="17" width="81" height="14" uuid="ee7dd153-6548-48cc-b06c-cefec483ceb3"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Data:]]></text>
			</staticText>
			<staticText>
				<reportElement x="642" y="45" width="81" height="14" uuid="ed407f09-8cb1-4d5a-9121-132a6a2aad1b"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Página:]]></text>
			</staticText>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="6" y="75" width="782" height="16" uuid="6402bda8-77f9-4af2-9180-b51b332e81fc"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{filters}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="308" y="62" width="181" height="13" uuid="7e73f7dc-2685-4299-b5ab-d4d53f2963a1"/>
				<textElement textAlignment="Center">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Filtros Selecionados]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="157" y="2" width="463" height="30" uuid="10c97d4d-01dd-4674-bd38-c91df8d39112">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="16" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{companyName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="642" y="31" width="81" height="14" uuid="5caec54e-2731-4c4a-a32d-a4fac0113823"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Hora:]]></text>
			</staticText>
			<textField pattern="HH:mm">
				<reportElement x="728" y="30" width="60" height="15" uuid="f0d82f9d-3c9a-45b4-9db8-4ea0b4bd592e"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<image hAlign="Center" vAlign="Middle">
				<reportElement x="10" y="5" width="85" height="40" uuid="d3a9810f-46f5-47b9-bf87-c0d05b05f93c"/>
				<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64(new String($P{companyImage}).getBytes("UTF-8")))]]></imageExpression>
			</image>
		</band>
	</pageHeader>
</jasperReport>
