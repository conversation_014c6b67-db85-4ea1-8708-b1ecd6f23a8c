<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="receipt-attendance" pageWidth="595" pageHeight="830" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="3192d9b2-2dfe-43e4-8333-5b41c7b28478">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_imeb"/>
	<parameter name="order_id" class="java.lang.Integer"/>
	<queryString>
		<![CDATA[SELECT

`order`.`date`,
COALESCE(person_dmed.name, `person`.name) AS patient_name,
`order`.value AS value,
`pay_type`.name AS pay_type,
`order_item`.name AS procedure_name,
`person_performer`.name AS professional,
`person_performer`.cpf AS cpf,
`professional`.council_number AS council_number,
`council`.initials AS council_name,
`person_user`.name AS `user`,
`address`.`address`,
`address`.district ,
`company`.name AS company_name,
`company`.cnpj AS company_cnpj,
`address`.zipcode ,
`address`.`number` AS address_number,
`state`.initials AS state,
eartefact.content AS signature,
`performer`.rqe_number,
`image_company`.content as logo,
`order`.`number` as number_guide,
`speciality`.`name` as speciality,
`city`.name AS city,
`contact_list_phone`.phone AS phone,
order_payer.invoice

from `order`

LEFT JOIN `patient` ON `patient`.patient_id = `order`.patient_id 
LEFT JOIN `person` ON `person`.person_id = `patient`.person_id 
LEFT JOIN `order_item` ON `order_item`.order_id = `order`.order_id 
LEFT JOIN `order_pay` ON `order_pay`.order_id = `order`.order_id 
LEFT JOIN `pay_type` ON `pay_type`.pay_type_id = `order_pay`.pay_type_id
LEFT JOIN `performer` ON `performer`.performer_id = `order_item`.performer_id
LEFT JOIN `professional` ON `professional`.professional_id = `performer`.professional_id
LEFT JOIN `speciality` ON `speciality`.`speciality_id`= `professional`.`speciality_id` 
LEFT JOIN `person` AS `person_performer` ON `person_performer`.person_id = `professional`.person_id
LEFT JOIN `person_image` ON `person_image`.person_id = `person_performer`.person_id AND `person_image`.type = 2 
LEFT JOIN `image` ON `image`.image_id = `person_image`.image_id
LEFT JOIN `user` ON `user`.user_id = `order`.created_by
LEFT JOIN `person` AS `person_user` ON `person_user`.person_id = `user`.person_id 
LEFT JOIN `council` ON `council`.council_id = `professional`.council_id
LEFT JOIN `company_cost_center` ON `company_cost_center`.company_cost_center_id = `order`.company_cost_center_id 
LEFT JOIN `company` ON `company`.company_id = `company_cost_center`.company_id 
LEFT JOIN `image` AS image_company ON `image_company`.image_id = `company`.image_id
LEFT JOIN `address` ON  `address`.address_id = `company`.address_id 
LEFT JOIN `city` ON `city`.city_id = `address`.city_id 
LEFT JOIN `state` ON `state`.state_id = `city`.state_id 
LEFT JOIN `contact_list` ON `contact_list`.`contact_list_id` = `company`.`contact_list_id`
LEFT JOIN `contact_list` AS `contact_list_phone` ON `contact_list_phone`.`parent_id` = `contact_list`.`contact_list_id` AND `contact_list_phone`.`type` = "S"
LEFT JOIN order_payer ON order_payer.order_id = `order`.order_id
LEFT JOIN person AS person_dmed ON person_dmed.person_id  = order_payer.person_id 
LEFT JOIN `parameter` ON `parameter`.name = 'COMPANY_SIGNARUTE_RECEIPT'
LEFT JOIN `parameter_value` ON `parameter_value`.parameter_id = `parameter`.parameter_id AND `parameter_value`.key = `company`.company_id
LEFT JOIN eartefact ON eartefact.eartefact_id = `parameter_value`.value


WHERE

`order`.order_id =  $P{order_id}

GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25
]]>
	</queryString>
	<field name="date" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.name" value="date"/>
		<property name="com.jaspersoft.studio.field.label" value="date"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
		<fieldDescription><![CDATA[Data de Abertura da Ordem de Serviço]]></fieldDescription>
	</field>
	<field name="patient_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="patient_name"/>
		<property name="com.jaspersoft.studio.field.label" value="patient_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="value" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="value"/>
		<property name="com.jaspersoft.studio.field.label" value="value"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
		<fieldDescription><![CDATA[Valor Ordem De Venda]]></fieldDescription>
	</field>
	<field name="pay_type" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="pay_type"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="pay_type"/>
	</field>
	<field name="procedure_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="procedure_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="professional" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="professional"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="cpf" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="cpf"/>
		<property name="com.jaspersoft.studio.field.label" value="cpf"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
		<fieldDescription><![CDATA[Numero do CPF]]></fieldDescription>
	</field>
	<field name="council_number" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="council_number"/>
		<property name="com.jaspersoft.studio.field.label" value="council_number"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="professional"/>
		<fieldDescription><![CDATA[Numero do Conselho]]></fieldDescription>
	</field>
	<field name="council_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="initials"/>
		<property name="com.jaspersoft.studio.field.label" value="council_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="council"/>
	</field>
	<field name="user" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="user"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="address" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="address"/>
		<property name="com.jaspersoft.studio.field.label" value="address"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="address"/>
		<fieldDescription><![CDATA[Nome do Logradouro]]></fieldDescription>
	</field>
	<field name="district" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="district"/>
		<property name="com.jaspersoft.studio.field.label" value="district"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="address"/>
		<fieldDescription><![CDATA[Bairro]]></fieldDescription>
	</field>
	<field name="company_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="company_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="company"/>
	</field>
	<field name="company_cnpj" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="cnpj"/>
		<property name="com.jaspersoft.studio.field.label" value="company_cnpj"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="company"/>
	</field>
	<field name="zipcode" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="zipcode"/>
		<property name="com.jaspersoft.studio.field.label" value="zipcode"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="address"/>
		<fieldDescription><![CDATA[CEP]]></fieldDescription>
	</field>
	<field name="address_number" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="number"/>
		<property name="com.jaspersoft.studio.field.label" value="address_number"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="address"/>
	</field>
	<field name="state" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="initials"/>
		<property name="com.jaspersoft.studio.field.label" value="state"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="state"/>
	</field>
	<field name="signature" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="content"/>
		<property name="com.jaspersoft.studio.field.label" value="signature"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="eartefact"/>
	</field>
	<field name="rqe_number" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="rqe_number"/>
		<property name="com.jaspersoft.studio.field.label" value="rqe_number"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="performer"/>
		<fieldDescription><![CDATA[Registro de Qualificação de Especialista]]></fieldDescription>
	</field>
	<field name="logo" class="byte[]">
		<property name="com.jaspersoft.studio.field.name" value="content"/>
		<property name="com.jaspersoft.studio.field.label" value="logo"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="image"/>
	</field>
	<field name="number_guide" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="number"/>
		<property name="com.jaspersoft.studio.field.label" value="number_guide"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
	</field>
	<field name="speciality" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="speciality"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="speciality"/>
	</field>
	<field name="city" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="city"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="city"/>
	</field>
	<field name="phone" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="phone"/>
		<property name="com.jaspersoft.studio.field.label" value="phone"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="contact_list"/>
		<fieldDescription><![CDATA[Telefone de Contato]]></fieldDescription>
	</field>
	<field name="invoice" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="invoice"/>
		<property name="com.jaspersoft.studio.field.label" value="invoice"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_payer"/>
		<fieldDescription><![CDATA[Numero da Nota Fiscal]]></fieldDescription>
	</field>
	<variable name="Variable_1" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="355">
			<staticText>
				<reportElement x="5" y="94" width="555" height="18" uuid="4f838c57-db7c-4486-b7ef-fe0d7c3aa89a"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[RECIBO]]></text>
			</staticText>
			<frame>
				<reportElement x="5" y="121" width="555" height="234" uuid="7db35d40-a568-47e3-99c1-811eb9f1b677"/>
				<line>
					<reportElement x="102" y="193" width="350" height="1" uuid="188c164c-dbfd-4390-8baf-1986e6dac1a4">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
				</line>
				<staticText>
					<reportElement x="5" y="4" width="85" height="12" uuid="23453ef2-837b-4f15-81d0-b56f1023fb74">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[Recebemos de: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="26" width="95" height="12" uuid="7a2b8688-be3a-4bda-9d47-7bbbef494763">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[a importância de:]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="48" width="65" height="12" uuid="aaa5d821-304b-4d42-a9ef-4f569cf2c2dc">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[referente a:]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="70" width="65" height="12" uuid="f8ae2a96-4b1a-43c4-ae82-6727f80550dc"/>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[Profissional:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="109" y="4" width="424" height="12" uuid="66662ab1-dcd6-4a2b-84c4-1945b6cbbeab">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{patient_name}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="74" y="48" width="476" height="12" uuid="c55c707e-2fe0-439d-aac3-56865d4938e5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{procedure_name}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="74" y="70" width="476" height="12" uuid="31b76891-68af-4416-b277-d57a984963b3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{professional} + " " + $F{council_name}+": "+ $F{council_number}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00" isBlankWhenNull="true">
					<reportElement x="170" y="26" width="350" height="12" uuid="40281159-503f-47f4-8f16-63243d814d67">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[br.com.focusts.clinicall.util.NumberUtil.getInstance().write(new BigDecimal($F{value})).toUpperCase()]]></textFieldExpression>
				</textField>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="210" y="140" width="95" height="50" uuid="05406cda-214a-4122-82fc-e31fa931ecbd"/>
					<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64(new String($F{signature}).getBytes("UTF-8")))]]></imageExpression>
				</image>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement x="93" y="26" width="100" height="12" uuid="231b4f74-2122-4645-8f24-25a66e8930a5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{value}]]></textFieldExpression>
				</textField>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="5" y="-100" width="95" height="50" uuid="6789fc75-1012-4e91-9e43-03464d3ebcf4">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64(new String($F{logo}).getBytes("UTF-8")))]]></imageExpression>
				</image>
				<staticText>
					<reportElement x="80" y="4" width="30" height="12" uuid="3c23e1c4-e4f1-4317-a2c5-1b00df052409"/>
					<textElement>
						<font size="9"/>
					</textElement>
					<text><![CDATA[Sr(a).]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="6" y="109" width="200" height="12" uuid="62e91c8e-d1fa-4964-bfb5-0675adee407e">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{city} + " - " + $F{state}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="39" y="195" width="476" height="12" uuid="eccb6e8b-756f-4667-bd17-89a78b4d4607">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{company_name}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="39" y="207" width="476" height="12" uuid="3227ea06-1524-4a78-810f-60a6204d00e3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{company_cnpj}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="6" y="90" width="54" height="12" uuid="26ffb06c-7814-4305-9cf2-a06c1617a900">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[Emitido em:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy">
					<reportElement x="60" y="90" width="97" height="12" uuid="4ee107f9-b67d-4724-bb8f-68e0cb43473d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement x="60" y="125" width="97" height="12" uuid="cc49f5cc-0f49-4855-bc59-03d885900fc4">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{invoice}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="6" y="125" width="54" height="12" uuid="8856fba2-b643-49e3-a5b9-e55c1fcabee3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[Nota Fiscal]]></text>
				</staticText>
			</frame>
			<staticText>
				<reportElement x="400" y="59" width="31" height="12" uuid="5b8705cc-53fa-4830-acd7-0b88e90dac86">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[No.:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="428" y="59" width="122" height="12" uuid="850cf5cd-0310-4436-9d3f-73eb2d878bb7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{number_guide}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<pageFooter>
		<band height="52">
			<textField isBlankWhenNull="true">
				<reportElement x="7" y="40" width="550" height="12" uuid="efc2b592-e999-415c-9f53-d9b9a9f22ad5">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{address} + ", nº" + $F{address_number} + " - " + $F{district}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
