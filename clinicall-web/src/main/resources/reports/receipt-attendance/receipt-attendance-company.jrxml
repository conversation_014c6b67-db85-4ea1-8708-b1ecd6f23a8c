<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="receipt-attendance-company" pageWidth="595" pageHeight="830" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="3192d9b2-2dfe-43e4-8333-5b41c7b28478">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_imeb"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="217"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="776"/>
	<parameter name="order_id" class="java.lang.Integer"/>
	<queryString>
		<![CDATA[SELECT

`order`.`date`,
COALESCE(person_dmed.name, `person`.name) AS patient_name,
`order`.value AS value,
`pay_type`.name AS pay_type,
`order_item`.name AS procedure_name,
`person_performer`.name AS professional,
`person_performer`.cpf AS cpf,
`professional`.council_number AS council_number,
`council`.initials AS council_name,
`person_user`.name AS `user`,
`address`.`address`,
`company`.name AS company_name,
`company`.cnpj AS company_cnpj,
`address`.district ,
`address`.zipcode ,
`address`.`number` AS address_number,
`state`.initials AS state,
`performer`.rqe_number,
`image_company`.content as logo,
`receipt`.`number` as number_registry,
`city`.name AS city,
eartefact.content AS signature,
order_payer.invoice

from `order`

LEFT JOIN `patient` ON `patient`.patient_id = `order`.patient_id 
LEFT JOIN `person` ON `person`.person_id = `patient`.person_id 
LEFT JOIN `order_item` ON `order_item`.order_id = `order`.order_id 
LEFT JOIN `order_pay` ON `order_pay`.order_id = `order`.order_id 
LEFT JOIN `pay_type` ON `pay_type`.pay_type_id = `order_pay`.pay_type_id
LEFT JOIN `performer` ON `performer`.performer_id = `order_item`.performer_id
LEFT JOIN `professional` ON `professional`.professional_id = `performer`.professional_id 
LEFT JOIN `person` AS `person_performer` ON `person_performer`.person_id = `professional`.person_id
LEFT JOIN `person_image` ON `person_image`.person_id = `person_performer`.person_id AND `person_image`.type = 2 
LEFT JOIN `image` ON `image`.image_id = `person_image`.image_id
LEFT JOIN `user` ON `user`.user_id = `order`.created_by
LEFT JOIN `person` AS `person_user` ON `person_user`.person_id = `user`.person_id 
LEFT JOIN `council` ON `council`.council_id = `professional`.council_id
LEFT JOIN `company_cost_center` ON `company_cost_center`.company_cost_center_id = `order`.company_cost_center_id 
LEFT JOIN `company` ON `company`.company_id = `company_cost_center`.company_id 
LEFT JOIN `image` AS image_company ON `image_company`.image_id = `company`.image_id
LEFT JOIN `address` ON  `address`.address_id = `company`.address_id 
LEFT JOIN `city` ON `city`.city_id = `address`.city_id 
LEFT JOIN `state` ON `state`.state_id = `city`.state_id 
LEFT JOIN `receipt` ON `receipt`.order_id = `order`.order_id 
LEFT JOIN order_payer ON order_payer.order_id = `order`.order_id
LEFT JOIN person AS person_dmed ON person_dmed.person_id  = order_payer.person_id 
LEFT JOIN `parameter` ON `parameter`.name = 'COMPANY_SIGNARUTE_RECEIPT'
LEFT JOIN `parameter_value` ON `parameter_value`.parameter_id = `parameter`.parameter_id AND `parameter_value`.key = `company`.company_id
LEFT JOIN eartefact ON eartefact.eartefact_id = `parameter_value`.value

where

`order`.order_id =  $P{order_id}
GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23
]]>
	</queryString>
	<field name="date" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.name" value="date"/>
		<property name="com.jaspersoft.studio.field.label" value="date"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
		<fieldDescription><![CDATA[Data de Abertura da Ordem de Serviço]]></fieldDescription>
	</field>
	<field name="patient_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="patient_name"/>
		<property name="com.jaspersoft.studio.field.label" value="patient_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="value" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="value"/>
		<property name="com.jaspersoft.studio.field.label" value="value"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
		<fieldDescription><![CDATA[Valor Ordem De Venda]]></fieldDescription>
	</field>
	<field name="pay_type" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="pay_type"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="pay_type"/>
	</field>
	<field name="procedure_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="procedure_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="professional" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="professional"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="cpf" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="cpf"/>
		<property name="com.jaspersoft.studio.field.label" value="cpf"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
		<fieldDescription><![CDATA[Numero do CPF]]></fieldDescription>
	</field>
	<field name="council_number" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="council_number"/>
		<property name="com.jaspersoft.studio.field.label" value="council_number"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="professional"/>
		<fieldDescription><![CDATA[Numero do Conselho]]></fieldDescription>
	</field>
	<field name="council_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="initials"/>
		<property name="com.jaspersoft.studio.field.label" value="council_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="council"/>
	</field>
	<field name="user" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="user"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="address" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="address"/>
		<property name="com.jaspersoft.studio.field.label" value="address"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="address"/>
		<fieldDescription><![CDATA[Nome do Logradouro]]></fieldDescription>
	</field>
	<field name="company_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="company_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="company"/>
	</field>
	<field name="company_cnpj" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="cnpj"/>
		<property name="com.jaspersoft.studio.field.label" value="company_cnpj"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="company"/>
	</field>
	<field name="district" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="district"/>
		<property name="com.jaspersoft.studio.field.label" value="district"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="address"/>
		<fieldDescription><![CDATA[Bairro]]></fieldDescription>
	</field>
	<field name="zipcode" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="zipcode"/>
		<property name="com.jaspersoft.studio.field.label" value="zipcode"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="address"/>
		<fieldDescription><![CDATA[CEP]]></fieldDescription>
	</field>
	<field name="address_number" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="number"/>
		<property name="com.jaspersoft.studio.field.label" value="address_number"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="address"/>
	</field>
	<field name="state" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="initials"/>
		<property name="com.jaspersoft.studio.field.label" value="state"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="state"/>
	</field>
	<field name="rqe_number" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="rqe_number"/>
		<property name="com.jaspersoft.studio.field.label" value="rqe_number"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="performer"/>
		<fieldDescription><![CDATA[Registro de Qualificação de Especialista]]></fieldDescription>
	</field>
	<field name="logo" class="byte[]">
		<property name="com.jaspersoft.studio.field.name" value="content"/>
		<property name="com.jaspersoft.studio.field.label" value="logo"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="image"/>
	</field>
	<field name="number_registry" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="number"/>
		<property name="com.jaspersoft.studio.field.label" value="number_registry"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="receipt"/>
	</field>
	<field name="city" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="city"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="city"/>
	</field>
	<field name="signature" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="content"/>
		<property name="com.jaspersoft.studio.field.label" value="signature"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="eartefact"/>
	</field>
	<field name="invoice" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="invoice"/>
		<property name="com.jaspersoft.studio.field.label" value="invoice"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_payer"/>
		<fieldDescription><![CDATA[Numero da Nota Fiscal]]></fieldDescription>
	</field>
	<variable name="Variable_1" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<summary>
		<band height="702" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="89" width="555" height="18" uuid="462d36e4-11bd-4c8f-84f8-513675097dac"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[RECIBO]]></text>
			</staticText>
			<frame>
				<reportElement x="0" y="116" width="555" height="218" uuid="9dcf6b8c-43d6-4fff-a7f6-3662942471e0"/>
				<line>
					<reportElement x="102" y="171" width="350" height="1" uuid="fa2cbffb-bda7-464d-8016-e5867abc5ef5">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
				</line>
				<staticText>
					<reportElement x="5" y="4" width="75" height="12" uuid="90a7b3c0-c8a8-4da8-9f2a-5cd803e11740">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[Recebemos de: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="20" width="85" height="12" uuid="c9367cf6-75ca-4e99-9131-7c31355eb547">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[a importância de:]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="52" width="120" height="12" uuid="3721f235-d113-463a-9f78-9ff7a034b848">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[Condições de pagamento:]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="68" width="58" height="12" uuid="91583e59-70f8-4635-b229-48726facae15">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[Profissional:]]></text>
				</staticText>
				<staticText>
					<reportElement x="6" y="84" width="40" height="12" uuid="132eaa40-b66c-4360-b87a-be41a1f907f7">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[Usuário: ]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="80" y="4" width="458" height="12" uuid="206a9aea-61c5-41bb-a2d7-9af066a4cffc">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{patient_name}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="65" y="68" width="476" height="12" uuid="bf7e27fa-ddee-4c58-b5c7-2a492e4f1ee6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{professional} + " (" + $F{council_name}+": "+ $F{council_number}+")"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="47" y="84" width="499" height="12" uuid="371e473c-312e-4a05-89f7-d671c1751894">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{user}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="39" y="174" width="476" height="12" uuid="62aa4ff0-d2cb-49df-9ee8-796e3b0044d9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{company_name}]]></textFieldExpression>
				</textField>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="5" y="-100" width="95" height="50" uuid="1a0891b6-bd2e-4cab-b53a-f768805eaa56">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64(new String($F{logo}).getBytes("UTF-8")))]]></imageExpression>
				</image>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="5" y="249" width="95" height="50" uuid="1600cf82-3a7f-46b5-8b94-2433052064de">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64(new String($F{logo}).getBytes("UTF-8")))]]></imageExpression>
				</image>
				<subreport>
					<reportElement x="121" y="52" width="416" height="12" uuid="61cfc9cb-8166-4746-b584-425b895903a5">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<subreportParameter name="order_id">
						<subreportParameterExpression><![CDATA[$P{order_id}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA["reports/receipt-attendance/sub-reports/pay.jasper"]]></subreportExpression>
				</subreport>
				<textField isBlankWhenNull="true">
					<reportElement x="39" y="186" width="476" height="12" uuid="a201eac0-02a4-40ea-926c-6e1923829476">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{company_cnpj}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="72" y="199" width="200" height="12" uuid="9bdd48a7-2dd7-4d6c-8322-7331507a79f5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{city}+", "]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy">
					<reportElement x="274" y="199" width="230" height="12" uuid="2205657a-5306-4bbf-aabb-b03e8c0d314d">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{date}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00" isBlankWhenNull="true">
					<reportElement x="169" y="20" width="350" height="12" uuid="87e95d2b-973e-41e9-8977-324c74494db7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA["(" +br.com.focusts.clinicall.util.NumberUtil.getInstance().write(new BigDecimal($F{value})).toUpperCase() +")"]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement x="90" y="20" width="75" height="12" uuid="ebafaf00-66a4-448f-aee3-a4a41d6208e1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{value}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="5" y="33" width="58" height="20" uuid="a6bfe2cf-50ea-4648-9722-846956600bb3">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[referente a:]]></text>
				</staticText>
				<subreport>
					<reportElement x="60" y="33" width="491" height="20" uuid="ac22400c-0585-4fd6-925f-fb40a30732b8">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<subreportParameter name="order_id">
						<subreportParameterExpression><![CDATA[$P{order_id}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA["reports/receipt-attendance/sub-reports/procedures.jasper"]]></subreportExpression>
				</subreport>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="210" y="118" width="95" height="50" uuid="e62a7703-7b06-4362-9c5c-aaf433457650"/>
					<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64(new String($F{signature}).getBytes("UTF-8")))]]></imageExpression>
				</image>
				<staticText>
					<reportElement x="6" y="100" width="54" height="12" uuid="c0b2c77e-d1bc-4d29-9658-9a54059e0476">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[Emitido em:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy">
					<reportElement x="60" y="100" width="97" height="12" uuid="a6a25680-57d4-47fb-9cd9-117868ac8594">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement x="60" y="114" width="97" height="12" uuid="ff89a411-da82-4c83-a5f0-160e4b6eb5c4">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{invoice}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="6" y="114" width="54" height="12" uuid="27943ef8-d1c4-45e8-9187-7b3c8c3df5a1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[Nota Fiscal]]></text>
				</staticText>
			</frame>
			<line>
				<reportElement x="0" y="349" width="555" height="1" uuid="349a0a06-c604-4190-ab81-64918b57214d">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</line>
			<frame>
				<reportElement x="351" y="4" width="200" height="61" uuid="f466f3e2-2372-4391-96c3-2bee2523bcbb">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textField isBlankWhenNull="true">
					<reportElement x="0" y="18" width="200" height="12" uuid="f4da6331-2079-4323-bc88-c00a9024b3c7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{address} + ", nº" + $F{address_number} + " - " + $F{district}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="0" y="32" width="200" height="12" uuid="97e7fedd-f6b5-413f-a67a-f188b666d58d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{city} + " - " + $F{state} + "    CEP: " + $F{zipcode}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="0" y="47" width="200" height="12" uuid="a02e5df7-536d-4f81-8b73-a099cab1df42">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA["N° de Registro: " +$F{number_registry}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="467" width="555" height="224" uuid="6da558bf-6b8d-420f-a4ca-0513e79b0fac"/>
				<line>
					<reportElement x="102" y="171" width="350" height="1" uuid="cb0be470-b260-480d-9861-8973383e48c9">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
				</line>
				<staticText>
					<reportElement x="5" y="4" width="75" height="12" uuid="322720a6-f04c-4715-8650-09860325a30a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[Recebemos de: ]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="20" width="85" height="12" uuid="8cf94483-bcc7-42db-98ff-22f97af1ad01">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[a importância de:]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="52" width="120" height="12" uuid="c79bbd94-97c7-43ff-a616-a50b40ad3b62"/>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[Condições de pagamento:]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="68" width="58" height="12" uuid="0dc561cb-d0dc-46fb-9f3a-4e59ca3343c8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[Profissional:]]></text>
				</staticText>
				<staticText>
					<reportElement x="6" y="84" width="40" height="12" uuid="b0726c88-1c24-4d1f-8882-700dca8f982a"/>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[Usuário: ]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="80" y="4" width="458" height="12" uuid="c21ab92f-16d7-4922-837d-3bfac4a4ded2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{patient_name}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="65" y="68" width="476" height="12" uuid="db858778-d732-4880-858e-c2de25db57f6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{professional} + " (" + $F{council_name}+": "+ $F{council_number}+")"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="47" y="84" width="499" height="12" uuid="dd403dc7-f2b7-44eb-88ec-2bdc34f24bb1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{user}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="39" y="174" width="476" height="12" uuid="fed7ec76-ebc1-43d1-bffd-7b48ba0580ab">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{company_name}]]></textFieldExpression>
				</textField>
				<subreport>
					<reportElement x="121" y="52" width="416" height="12" uuid="1b73b7a1-1908-4a4a-8e99-537f678a12e1">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<subreportParameter name="order_id">
						<subreportParameterExpression><![CDATA[$P{order_id}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA["reports/receipt-attendance/sub-reports/pay.jasper"]]></subreportExpression>
				</subreport>
				<textField isBlankWhenNull="true">
					<reportElement x="39" y="186" width="476" height="12" uuid="fb0ffc4b-242f-44c3-b9bc-3650157af3cb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{company_cnpj}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="72" y="200" width="200" height="12" uuid="7dda7dd3-0a7f-4744-9c3a-a76e94c10213">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{city}+", "]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy">
					<reportElement x="274" y="200" width="230" height="12" uuid="c33e5811-8e22-474c-9b69-7ca78a27485c">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{date}]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00" isBlankWhenNull="true">
					<reportElement x="169" y="20" width="350" height="12" uuid="9f3797e0-67e9-4d37-a803-dc777017d7d8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA["(" +br.com.focusts.clinicall.util.NumberUtil.getInstance().write(new BigDecimal($F{value})).toUpperCase() +")"]]></textFieldExpression>
				</textField>
				<textField pattern="¤#,##0.00;¤-#,##0.00">
					<reportElement x="90" y="20" width="75" height="12" uuid="e378e10f-2ea9-4af1-8b6d-7fb1ec51f510">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{value}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="5" y="33" width="58" height="20" uuid="2a6c263e-56c1-480b-948b-23b678c2fd71">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[referente a:]]></text>
				</staticText>
				<subreport>
					<reportElement x="60" y="33" width="491" height="20" uuid="e6ee10fb-3330-40f8-a90e-97a821ec9e13">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<subreportParameter name="order_id">
						<subreportParameterExpression><![CDATA[$P{order_id}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA["reports/receipt-attendance/sub-reports/procedures.jasper"]]></subreportExpression>
				</subreport>
				<image hAlign="Center" vAlign="Middle">
					<reportElement x="210" y="118" width="95" height="50" uuid="e227989c-4039-41ea-9e99-d58fc913654f"/>
					<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64(new String($F{signature}).getBytes("UTF-8")))]]></imageExpression>
				</image>
				<staticText>
					<reportElement x="6" y="100" width="54" height="12" uuid="7f44bd54-6961-4c34-b306-6cb615709393">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[Emitido em:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy">
					<reportElement x="60" y="100" width="97" height="12" uuid="e8f915e6-4e55-4163-aa0f-f87279d58a00">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="6" y="114" width="54" height="12" uuid="f03f0d39-8f59-4ad2-8048-10076dd6820b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="9"/>
					</textElement>
					<text><![CDATA[Nota Fiscal]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement x="60" y="114" width="97" height="12" uuid="75da6e18-26f5-4055-8e6c-8bd531c68bee">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{invoice}]]></textFieldExpression>
				</textField>
			</frame>
			<staticText>
				<reportElement x="0" y="442" width="555" height="18" uuid="5fda5cca-a9e7-47ac-a2cb-da9b576164c2"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[RECIBO]]></text>
			</staticText>
			<frame>
				<reportElement x="351" y="359" width="200" height="61" uuid="716215d8-9bd5-4d07-83e1-ecb8593e995a">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textField isBlankWhenNull="true">
					<reportElement x="0" y="17" width="200" height="12" uuid="7ad18cb7-9028-4f9e-8d87-************">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{address} + ", nº" + $F{address_number} + " - " + $F{district}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="0" y="31" width="200" height="12" uuid="1de04d53-ba02-4128-94d6-89f8a56a23b9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{city} + " - " + $F{state} + "    CEP: " + $F{zipcode}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="0" y="47" width="200" height="12" uuid="42d55d94-a1d7-42d3-bf15-e99f4f66b261">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA["N° de Registro: " +$F{number_registry}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</summary>
</jasperReport>
