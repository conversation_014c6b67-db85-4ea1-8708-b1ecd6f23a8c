<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="pay" columnCount="6" printOrder="Horizontal" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="120" columnSpacing="1" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="ccadd6de-1c65-4b47-bcc3-3b1b1051f7fd">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_demo"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="order_id" class="java.lang.Integer"/>
	<queryString>
		<![CDATA[SELECT

GROUP_CONCAT(`order_item`.name ORDER BY `order_item`.name SEPARATOR ', ') AS procedure_name

from `order`

LEFT JOIN `order_item` ON `order_item`.order_id = `order`.order_id 

where

`order`.order_id =  $P{order_id}]]>
	</queryString>
	<field name="procedure_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="procedure_name"/>
		<property name="com.jaspersoft.studio.field.label" value="procedure_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="20">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
			<textField>
				<reportElement x="0" y="0" width="842" height="20" uuid="a552d749-54bb-4b6f-9efb-628dfd9d1c76"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{procedure_name} ]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
