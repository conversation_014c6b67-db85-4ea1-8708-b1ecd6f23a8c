<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.3.final using JasperReports Library version 6.21.3-4a3078d20785ebe464f18037d738d12fc98c13cf  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="profissionais" pageWidth="802" pageHeight="555" orientation="Landscape" columnWidth="802" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="b53976ab-62c5-4f6c-9ae3-6a145d503f54">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="totalItens" class="java.lang.Integer"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="performerCouncilInitials" class="java.lang.String">
		<fieldDescription><![CDATA[performerCouncilInitials]]></fieldDescription>
	</field>
	<field name="degreeParticipation" class="java.lang.String">
		<fieldDescription><![CDATA[degreeParticipation]]></fieldDescription>
	</field>
	<field name="performerCouncilNumber" class="java.lang.String">
		<fieldDescription><![CDATA[performerCouncilNumber]]></fieldDescription>
	</field>
	<field name="performerCpf" class="java.lang.String">
		<fieldDescription><![CDATA[performerCpf]]></fieldDescription>
	</field>
	<field name="performerName" class="java.lang.String">
		<fieldDescription><![CDATA[performerName]]></fieldDescription>
	</field>
	<field name="performerStateInitials" class="java.lang.String">
		<fieldDescription><![CDATA[performerStateInitials]]></fieldDescription>
	</field>
	<field name="performerCbos" class="java.lang.String">
		<fieldDescription><![CDATA[performerCbos]]></fieldDescription>
	</field>
	<variable name="Variable_1" class="java.lang.Integer" resetType="Page">
		<variableExpression><![CDATA[$V{REPORT_COUNT}]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="11">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="-1" y="1" width="40" height="8" uuid="02dd62b3-40a2-479c-89e2-97a520e1185c">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="0b83c640-1089-49cf-99aa-aea73cc2b390"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="6"/>
				</textElement>
				<text><![CDATA[46 - Seq. Ref]]></text>
			</staticText>
			<staticText>
				<reportElement x="40" y="1" width="40" height="8" uuid="edc60c59-98b7-445f-9b7d-da0c6be159bb">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4c4a6a70-5142-4725-bbb7-e848487a5291"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="6"/>
				</textElement>
				<text><![CDATA[47 - Grau Part.]]></text>
			</staticText>
			<staticText>
				<reportElement x="82" y="1" width="128" height="8" uuid="ff04c37e-54ed-4235-9e7d-3ecd1e3cdd19">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4f550651-7db9-4a23-949c-cf7986b48d10"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="6"/>
				</textElement>
				<text><![CDATA[48 - Numero Operadora / CPF]]></text>
			</staticText>
			<staticText>
				<reportElement x="211" y="1" width="349" height="8" uuid="189983f1-f6fd-4323-802d-f719603b94f4">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="428c74a7-27d8-4560-becb-b1d0b37a838b"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="6"/>
				</textElement>
				<text><![CDATA[49 - Nome do Profissional]]></text>
			</staticText>
			<staticText>
				<reportElement x="563" y="1" width="78" height="8" uuid="6e7d3fd2-4a0f-4fbb-85fd-9cef173c50f8">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7597332a-d4c9-48ff-acca-2fa747e539dc"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="6"/>
				</textElement>
				<text><![CDATA[50 - Conselho Profissional]]></text>
			</staticText>
			<staticText>
				<reportElement x="643" y="1" width="74" height="8" uuid="7f5bd346-d723-4152-bd52-61067314b214">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="3e949878-a6a9-47fb-911c-0317a45ce3f3"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="6"/>
				</textElement>
				<text><![CDATA[51 - Número do Conselho]]></text>
			</staticText>
			<staticText>
				<reportElement x="719" y="1" width="25" height="8" uuid="5bef013e-1cd8-4272-a2b0-1cf0e1353338">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="46aa35b0-7c53-4e4d-b84d-d777491be61d"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="6"/>
				</textElement>
				<text><![CDATA[52 - UF]]></text>
			</staticText>
			<staticText>
				<reportElement x="746" y="1" width="46" height="8" uuid="32ef966f-69fb-4667-a463-5ecba1b073ad">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="98afa672-722a-471f-8a55-74700658c181"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="6"/>
				</textElement>
				<text><![CDATA[53 - Código CBO]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="1" y="0" width="38" height="10" uuid="c23c509a-1b54-4a54-9648-92ff0b20d4c9"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT} + "-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="40" y="0" width="40" height="10" uuid="280a7fa9-fb72-4152-8804-c8818ff5806a"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{degreeParticipation}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="82" y="0" width="128" height="10" uuid="cad3e689-2ec0-4903-8167-4f947e2dd44c"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{performerCpf}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="211" y="0" width="349" height="10" uuid="cbff65ee-bc74-4d7b-86db-ca4f9643c630"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{performerName}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="563" y="0" width="78" height="10" uuid="533b7dc5-9f1e-4c28-bfe3-808a9e4f5b10"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{performerCouncilInitials}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="643" y="0" width="74" height="10" uuid="4a2bc0e6-2381-458e-aa27-91255f4e043f"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{performerCouncilNumber}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="719" y="0" width="25" height="10" uuid="988558f5-9380-4fe4-a4bf-f5076e29513f"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{performerStateInitials}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="746" y="0" width="46" height="10" uuid="5da84172-5431-4d74-857b-248b5ee65cdb"/>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{performerCbos}]]></textFieldExpression>
			</textField>
			<break>
				<reportElement x="0" y="11" width="100" height="1" uuid="6d937d9d-03e4-4970-a4ac-49e83b92f87f">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[$V{REPORT_COUNT} % 4 == 0  && $V{Variable_1} < $P{totalItens}]]></printWhenExpression>
				</reportElement>
			</break>
		</band>
	</detail>
</jasperReport>
