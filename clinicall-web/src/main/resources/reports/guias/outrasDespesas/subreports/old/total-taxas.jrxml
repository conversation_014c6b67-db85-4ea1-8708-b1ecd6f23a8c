<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.0.final using JasperReports Library version 6.20.0-2bc7ab61c56f459e8176eb05c7705e145cd400ad  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="total-taxas" pageWidth="81" pageHeight="555" orientation="Landscape" columnWidth="81" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="ccadd6de-1c65-4b47-bcc3-3b1b1051f7fd">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Homologocao"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<parameter name="order_id" class="java.lang.Integer"/>
	<queryString>
		<![CDATA[select 
    	sum(case when fee.`type` = 'T' then order_item_fee.`total` else .0 end) as TotalTaxa   
	
FROM `order`

LEFT JOIN order_item  ON order_item.order_id = `order`.order_id
LEFT JOIN order_item_fee ON order_item_fee.order_item_id = order_item.order_item_id
LEFT JOIN fee ON fee.fee_id = order_item_fee.fee_id

WHERE `order`.order_id = $P{order_id}]]>
	</queryString>
	<field name="TotalTaxa" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="TotalTaxa"/>
		<property name="com.jaspersoft.studio.field.label" value="TotalTaxa"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<summary>
		<band height="7" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField pattern="#,##0.00##">
				<reportElement x="0" y="0" width="81" height="7" uuid="3335f410-d80c-4549-9043-0edbd50f1b8d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TotalTaxa}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
