<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.6.final using JasperReports Library version 6.20.6  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="orderdto" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="663403b8-c27f-4561-8b1d-bf3b3701bf9f">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="name" class="java.lang.String"/>
	<parameter name="subreportParameter" class="net.sf.jasperreports.engine.JasperReport"/>
	<parameter name="subreportParameterProcedures" class="net.sf.jasperreports.engine.JasperReport"/>
	<parameter name="subreportParameterProfessionals" class="net.sf.jasperreports.engine.JasperReport"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="guia" class="java.util.List">
		<fieldDescription><![CDATA[guia]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="50" splitType="Stretch">
			<subreport>
				<reportElement x="0" y="0" width="70" height="50" uuid="0c755032-d12f-4b07-bcc1-83aea968b95c"/>
				<subreportParameter name="subreportParameterProcedures">
					<subreportParameterExpression><![CDATA[$P{subreportParameterProcedures}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="subreportParameterProfessionals">
					<subreportParameterExpression><![CDATA[$P{subreportParameterProfessionals}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{guia})]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{subreportParameter}]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
