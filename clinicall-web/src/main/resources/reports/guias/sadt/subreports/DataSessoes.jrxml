<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DataSessoes" printOrder="Horizontal" pageWidth="802" pageHeight="555" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isTitleNewPage="true" isSummaryNewPage="true" isSummaryWithPageHeaderAndFooter="true" isFloatColumnFooter="true" uuid="15e4c26d-ea65-449c-b8c4-d97c797bd7ab">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_trilhar"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="net.sf.jasperreports.print.create.bookmarks" value="true"/>
	<parameter name="order_id" class="java.lang.String"/>
	<parameter name="page_number" class="java.lang.Integer"/>
	<queryString language="SQL">
		<![CDATA[SELECT
    MAX(ExibirDatas) AS ExibirData,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 1 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_1,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 2 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_2,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 3 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_3,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 4 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_4,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 5 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_5,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 6 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_6,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 7 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_7,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 8 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_8,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 9 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_9,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 10 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_10,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 11 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_11,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 12 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_12,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 13 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_13,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 14 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_14,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 15 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_15,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 16 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_16,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 17 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_17,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 18 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_18,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 19 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_19,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 20 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_20,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 21 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_21,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 22 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_22,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 23 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_23,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 24 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_24,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 25 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_25,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 26 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_26,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 27 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_27,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 28 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_28,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 29 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_29,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 30 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_30,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 31 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_31,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 32 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_32,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 33 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_33,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 34 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_34,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 35 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_35,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 36 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_36,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 37 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_37,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 38 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_38,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 39 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_39,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 40 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_40,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 41 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_41,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 42 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_42,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 43 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_43,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 44 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_44,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 45 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_45,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 46 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_46,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 47 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_47,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 48 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_48,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 49 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_49,
    IF(MAX(ExibirDatas) = 0, '___/___/______', MAX(CASE WHEN rn = 50 THEN DATE_FORMAT(`date`,'%d/%m/%Y')END)) AS date_50,
    COUNT(rn) AS countRow
FROM (
    SELECT
    	CASE WHEN COALESCE(INSURACE_SESSION_DATE_GUIDE_VALUE.value, INSURACE_SESSION_DATE_GUIDE.value) = "1" THEN true ELSE false END AS ExibirDatas,
        `order_item`.`date`,
        ROW_NUMBER() OVER (ORDER BY `order_item`.`date`) AS rn
    FROM
        `order`
    LEFT JOIN
        `order_item` ON `order_item`.order_id = `order`.order_id
    LEFT JOIN        
        insurance ON insurance.insurance_id =  `order`.insurance_id
    LEFT JOIN 
        `parameter` AS INSURACE_SESSION_DATE_GUIDE ON INSURACE_SESSION_DATE_GUIDE.name = 'INSURACE_SESSION_DATE_GUIDE'
    LEFT JOIN 
        parameter_value AS INSURACE_SESSION_DATE_GUIDE_VALUE ON INSURACE_SESSION_DATE_GUIDE_VALUE.`key` = insurance.insurance_id AND INSURACE_SESSION_DATE_GUIDE_VALUE.parameter_id = INSURACE_SESSION_DATE_GUIDE.parameter_id     
    WHERE
        `order`.order_id = $P{order_id}

	ORDER BY `order_item`.`date`
) sub;]]>
	</queryString>
	<field name="ExibirData" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="ExibirData"/>
		<property name="com.jaspersoft.studio.field.label" value="ExibirData"/>
	</field>
	<field name="date_1" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_1"/>
		<property name="com.jaspersoft.studio.field.label" value="date_1"/>
	</field>
	<field name="date_2" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_2"/>
		<property name="com.jaspersoft.studio.field.label" value="date_2"/>
	</field>
	<field name="date_3" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_3"/>
		<property name="com.jaspersoft.studio.field.label" value="date_3"/>
	</field>
	<field name="date_4" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_4"/>
		<property name="com.jaspersoft.studio.field.label" value="date_4"/>
	</field>
	<field name="date_5" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_5"/>
		<property name="com.jaspersoft.studio.field.label" value="date_5"/>
	</field>
	<field name="date_6" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_6"/>
		<property name="com.jaspersoft.studio.field.label" value="date_6"/>
	</field>
	<field name="date_7" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_7"/>
		<property name="com.jaspersoft.studio.field.label" value="date_7"/>
	</field>
	<field name="date_8" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_8"/>
		<property name="com.jaspersoft.studio.field.label" value="date_8"/>
	</field>
	<field name="date_9" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_9"/>
		<property name="com.jaspersoft.studio.field.label" value="date_9"/>
	</field>
	<field name="date_10" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_10"/>
		<property name="com.jaspersoft.studio.field.label" value="date_10"/>
	</field>
	<field name="date_11" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_11"/>
		<property name="com.jaspersoft.studio.field.label" value="date_11"/>
	</field>
	<field name="date_12" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_12"/>
		<property name="com.jaspersoft.studio.field.label" value="date_12"/>
	</field>
	<field name="date_13" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_13"/>
		<property name="com.jaspersoft.studio.field.label" value="date_13"/>
	</field>
	<field name="date_14" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_14"/>
		<property name="com.jaspersoft.studio.field.label" value="date_14"/>
	</field>
	<field name="date_15" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_15"/>
		<property name="com.jaspersoft.studio.field.label" value="date_15"/>
	</field>
	<field name="date_16" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_16"/>
		<property name="com.jaspersoft.studio.field.label" value="date_16"/>
	</field>
	<field name="date_17" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_17"/>
		<property name="com.jaspersoft.studio.field.label" value="date_17"/>
	</field>
	<field name="date_18" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_18"/>
		<property name="com.jaspersoft.studio.field.label" value="date_18"/>
	</field>
	<field name="date_19" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_19"/>
		<property name="com.jaspersoft.studio.field.label" value="date_19"/>
	</field>
	<field name="date_20" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_20"/>
		<property name="com.jaspersoft.studio.field.label" value="date_20"/>
	</field>
	<field name="date_21" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_21"/>
		<property name="com.jaspersoft.studio.field.label" value="date_21"/>
	</field>
	<field name="date_22" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_22"/>
		<property name="com.jaspersoft.studio.field.label" value="date_22"/>
	</field>
	<field name="date_23" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_23"/>
		<property name="com.jaspersoft.studio.field.label" value="date_23"/>
	</field>
	<field name="date_24" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_24"/>
		<property name="com.jaspersoft.studio.field.label" value="date_24"/>
	</field>
	<field name="date_25" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_25"/>
		<property name="com.jaspersoft.studio.field.label" value="date_25"/>
	</field>
	<field name="date_26" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_26"/>
		<property name="com.jaspersoft.studio.field.label" value="date_26"/>
	</field>
	<field name="date_27" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_27"/>
		<property name="com.jaspersoft.studio.field.label" value="date_27"/>
	</field>
	<field name="date_28" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_28"/>
		<property name="com.jaspersoft.studio.field.label" value="date_28"/>
	</field>
	<field name="date_29" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_29"/>
		<property name="com.jaspersoft.studio.field.label" value="date_29"/>
	</field>
	<field name="date_30" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_30"/>
		<property name="com.jaspersoft.studio.field.label" value="date_30"/>
	</field>
	<field name="date_31" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_31"/>
		<property name="com.jaspersoft.studio.field.label" value="date_31"/>
	</field>
	<field name="date_32" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_32"/>
		<property name="com.jaspersoft.studio.field.label" value="date_32"/>
	</field>
	<field name="date_33" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_33"/>
		<property name="com.jaspersoft.studio.field.label" value="date_33"/>
	</field>
	<field name="date_34" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_34"/>
		<property name="com.jaspersoft.studio.field.label" value="date_34"/>
	</field>
	<field name="date_35" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_35"/>
		<property name="com.jaspersoft.studio.field.label" value="date_35"/>
	</field>
	<field name="date_36" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_36"/>
		<property name="com.jaspersoft.studio.field.label" value="date_36"/>
	</field>
	<field name="date_37" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_37"/>
		<property name="com.jaspersoft.studio.field.label" value="date_37"/>
	</field>
	<field name="date_38" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_38"/>
		<property name="com.jaspersoft.studio.field.label" value="date_38"/>
	</field>
	<field name="date_39" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_39"/>
		<property name="com.jaspersoft.studio.field.label" value="date_39"/>
	</field>
	<field name="date_40" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_40"/>
		<property name="com.jaspersoft.studio.field.label" value="date_40"/>
	</field>
	<field name="date_41" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_41"/>
		<property name="com.jaspersoft.studio.field.label" value="date_41"/>
	</field>
	<field name="date_42" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_42"/>
		<property name="com.jaspersoft.studio.field.label" value="date_42"/>
	</field>
	<field name="date_43" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_43"/>
		<property name="com.jaspersoft.studio.field.label" value="date_43"/>
	</field>
	<field name="date_44" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_44"/>
		<property name="com.jaspersoft.studio.field.label" value="date_44"/>
	</field>
	<field name="date_45" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_45"/>
		<property name="com.jaspersoft.studio.field.label" value="date_45"/>
	</field>
	<field name="date_46" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_46"/>
		<property name="com.jaspersoft.studio.field.label" value="date_46"/>
	</field>
	<field name="date_47" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_47"/>
		<property name="com.jaspersoft.studio.field.label" value="date_47"/>
	</field>
	<field name="date_48" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_48"/>
		<property name="com.jaspersoft.studio.field.label" value="date_48"/>
	</field>
	<field name="date_49" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_49"/>
		<property name="com.jaspersoft.studio.field.label" value="date_49"/>
	</field>
	<field name="date_50" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="date_50"/>
		<property name="com.jaspersoft.studio.field.label" value="date_50"/>
	</field>
	<field name="countRow" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.name" value="countRow"/>
		<property name="com.jaspersoft.studio.field.label" value="countRow"/>
	</field>
	<variable name="Variable_1" class="java.lang.Integer">
		<variableExpression><![CDATA[$V{REPORT_COUNT}]]></variableExpression>
	</variable>
	<group name="Group1">
		<groupExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() <= 10)]]></groupExpression>
		<groupHeader>
			<band>
				<printWhenExpression><![CDATA[new Boolean($V{PAGE_COUNT}.intValue() <= 10)]]></printWhenExpression>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="35" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="5" y="2" width="18" height="12" uuid="bdbb9acc-a3c3-4f65-a2b8-2f174db923e7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" 1 - "]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="22" y="2" width="51" height="12" uuid="e1041fb8-abaf-4d24-a8fa-be2ba1790a80">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{page_number} == 1 && $F{date_1} != null ? $F{date_1} :
$P{page_number} == 2 && $F{date_11} != null ? $F{date_11} :
$P{page_number} == 3 && $F{date_21} != null ? $F{date_21} :
$P{page_number} == 4 && $F{date_31} != null ? $F{date_31} :
$P{page_number} == 5 && $F{date_41} != null ? $F{date_41} : "___/___/______"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="71" y="2" width="90" height="12" uuid="19bcdaa2-298c-4d4a-8a90-cd4d3af560d0">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[______________________________]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="22" y="19" width="51" height="12" uuid="bc15ef9b-5a5a-42f5-88dc-ef7c484bab7b">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{page_number} == 1 && $F{date_2} != null ? $F{date_2} :
$P{page_number} == 2 && $F{date_12} != null ? $F{date_12} :
$P{page_number} == 3 && $F{date_22} != null ? $F{date_22} :
$P{page_number} == 4 && $F{date_32} != null ? $F{date_32} :
$P{page_number} == 5 && $F{date_42} != null ? $F{date_42} : "___/___/______"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="71" y="19" width="90" height="12" uuid="b0aff09b-2e22-46e6-a2e4-ccd444b83d27">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[______________________________]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="164" y="2" width="18" height="12" uuid="c7eb0567-8175-46be-a738-f92d5e67306d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" 3 - "]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="181" y="2" width="51" height="12" uuid="63d5ea14-8987-4e72-92b0-1c850976c9c8">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{page_number} == 1 && $F{date_3} != null ? $F{date_3} :
$P{page_number} == 2 && $F{date_13} != null ? $F{date_13} :
$P{page_number} == 3 && $F{date_23} != null ? $F{date_23} :
$P{page_number} == 4 && $F{date_33} != null ? $F{date_33} :
$P{page_number} == 5 && $F{date_43} != null ? $F{date_43} : "___/___/______"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="230" y="2" width="90" height="12" uuid="8c0d4fb2-e8ce-4da7-b64f-1e2d574782d3"/>
				<textElement verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[______________________________]]></text>
			</staticText>
			<staticText>
				<reportElement x="230" y="19" width="90" height="12" uuid="cf0779ec-2800-4a2b-95b5-c1ba06c60ba4"/>
				<textElement verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[______________________________]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="181" y="19" width="51" height="12" uuid="36c98cb3-99dc-4d9b-b90e-45451ab0ea5a">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{page_number} == 1 && $F{date_4} != null ? $F{date_4} :
$P{page_number} == 2 && $F{date_14} != null ? $F{date_14} :
$P{page_number} == 3 && $F{date_24} != null ? $F{date_24} :
$P{page_number} == 4 && $F{date_34} != null ? $F{date_34} :
$P{page_number} == 5 && $F{date_44} != null ? $F{date_44} : "___/___/______"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="164" y="19" width="18" height="12" uuid="88bed764-1a63-4a79-8306-3d8b16141e3b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" 4 - "]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="323" y="2" width="18" height="12" uuid="147b262d-eb27-46b3-9ec8-3fe4b580aeed">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" 5 - "]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="340" y="2" width="51" height="12" uuid="3b4f6d41-175e-4ad9-af59-db0f200efe5b">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{page_number} == 1 && $F{date_5} != null ? $F{date_5} :
$P{page_number} == 2 && $F{date_15} != null ? $F{date_15} :
$P{page_number} == 3 && $F{date_25} != null ? $F{date_25} :
$P{page_number} == 4 && $F{date_35} != null ? $F{date_35} :
$P{page_number} == 5 && $F{date_45} != null ? $F{date_45} : "___/___/______"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="389" y="2" width="90" height="12" uuid="22d22aa3-8084-41cd-b0ca-a0bafac2d4d7"/>
				<textElement verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[______________________________]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="323" y="19" width="18" height="12" uuid="7791b137-4bc3-4d68-b177-17ba48a11458">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" 6 - "]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="340" y="19" width="51" height="12" uuid="d1ff7c4a-6ac6-495a-8cdb-1b4b14cf2348">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{page_number} == 1 && $F{date_6} != null ? $F{date_6} :
$P{page_number} == 2 && $F{date_16} != null ? $F{date_16} :
$P{page_number} == 3 && $F{date_26} != null ? $F{date_26} :
$P{page_number} == 4 && $F{date_36} != null ? $F{date_36} :
$P{page_number} == 5 && $F{date_46} != null ? $F{date_46} : "___/___/______"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="389" y="19" width="90" height="12" uuid="42633239-f590-4dcf-817f-6ff029147801"/>
				<textElement verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[______________________________]]></text>
			</staticText>
			<staticText>
				<reportElement x="548" y="19" width="90" height="12" uuid="262e155d-070c-434c-bbe4-a14aed87d80a"/>
				<textElement verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[______________________________]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="499" y="19" width="51" height="12" uuid="b4ec3ff8-a36d-4711-b950-b5dd756cf91e">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{page_number} == 1 && $F{date_8} != null ? $F{date_8} :
$P{page_number} == 2 && $F{date_18} != null ? $F{date_18} :
$P{page_number} == 3 && $F{date_28} != null ? $F{date_28} :
$P{page_number} == 4 && $F{date_38} != null ? $F{date_38} :
$P{page_number} == 5 && $F{date_48} != null ? $F{date_48} : "___/___/______"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="482" y="19" width="18" height="12" uuid="e214d628-72dd-4b15-b3c9-e60e5f4207b5">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" 8 - "]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="482" y="2" width="18" height="12" uuid="7abfe77c-0b6f-4604-ae24-c4d7f4a83cfc">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" 7 - "]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="499" y="2" width="51" height="12" uuid="eaf8d305-fb1e-4ce2-9a52-def0fbf7757a">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{page_number} == 1 && $F{date_7} != null ? $F{date_7} :
$P{page_number} == 2 && $F{date_17} != null ? $F{date_17} :
$P{page_number} == 3 && $F{date_27} != null ? $F{date_27} :
$P{page_number} == 4 && $F{date_37} != null ? $F{date_37} :
$P{page_number} == 5 && $F{date_47} != null ? $F{date_47} : "___/___/______"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="548" y="2" width="90" height="12" uuid="7acffab6-5a8b-48a9-a300-b31416ffe7a3"/>
				<textElement verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[______________________________]]></text>
			</staticText>
			<staticText>
				<reportElement x="707" y="19" width="90" height="12" uuid="7dac856c-efad-4ca3-8364-233c992af81f"/>
				<textElement verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[______________________________]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="658" y="19" width="51" height="12" uuid="6138ce77-05e5-4dc0-8062-1d98dbe41aac">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{page_number} == 1 && $F{date_10} != null ? $F{date_10} :
$P{page_number} == 2 && $F{date_20} != null ? $F{date_20} :
$P{page_number} == 3 && $F{date_30} != null ? $F{date_30} :
$P{page_number} == 4 && $F{date_40} != null ? $F{date_40} :
$P{page_number} == 5 && $F{date_50} != null ? $F{date_50} : "___/___/______"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="641" y="19" width="18" height="12" uuid="90aad014-8fd5-4251-8b3a-d1401f8852bb">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" 10 - "]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="641" y="2" width="18" height="12" uuid="d2a54198-dc65-4524-8d0c-fb64bf20a7e5">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" 9 - "]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="658" y="2" width="51" height="12" uuid="df9a7d30-ad21-44f6-9510-dd3f69886fb5">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{page_number} == 1 && $F{date_9} != null ? $F{date_9} :
$P{page_number} == 2 && $F{date_19} != null ? $F{date_19} :
$P{page_number} == 3 && $F{date_29} != null ? $F{date_29} :
$P{page_number} == 4 && $F{date_39} != null ? $F{date_39} :
$P{page_number} == 5 && $F{date_49} != null ? $F{date_49} : "___/___/______"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="707" y="2" width="90" height="12" uuid="b3d53534-5cc9-4f8b-a58a-45433d83492b"/>
				<textElement verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<text><![CDATA[______________________________]]></text>
			</staticText>
			<break>
				<reportElement x="0" y="33" width="555" height="1" uuid="1012f3fd-58a1-4ee3-8433-bcaf82115329"/>
			</break>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="5" y="19" width="18" height="12" uuid="ceda92b1-36e7-41b7-aa23-63191d51443f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" 2 - "]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
