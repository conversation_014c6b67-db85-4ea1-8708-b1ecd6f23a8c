<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ExamesSolicitados" pageWidth="802" pageHeight="555" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isTitleNewPage="true" isSummaryNewPage="true" isSummaryWithPageHeaderAndFooter="true" isFloatColumnFooter="true" uuid="95c20473-2772-4f38-8b88-cfecbda936fd">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_unidor"/>
	<property name="net.sf.jasperreports.print.create.bookmarks" value="true"/>
	<parameter name="codigo_order" class="java.lang.Integer"/>
	<queryString language="SQL">
		<![CDATA[#PROCEDIMENTO E EXAMES SOLICITADOS
select 
#Dados da Solicitação/Procedimentos e Exames Solictados
o.order_id,
oi.date AS dated,
COALESCE(tt.code, insurance_table_type.code) as TipoTabela, 
oi.code as CodigoProcedimento, 
oi.name as NomeProcedimento, 
oi.quantity as QtdSolicAut,
	(SELECT COUNT(*) FROM order_item where order_item.order_id = o.order_id) as qtd_detail

from 
`order` as o

LEFT JOIN order_item as oi ON oi.order_id = o.order_id
LEFT JOIN agreement as ag ON ag.agreement_id = o.agreement_id
LEFT JOIN accreditation as acc ON acc.agreement_id = ag.agreement_id and acc.procedure_id = oi.procedure_id and acc.active = 1
LEFT JOIN table_type as tt ON tt.table_type_id = acc.table_type_id
LEFT JOIN insurance ON insurance.insurance_id = o.insurance_id
LEFT JOIN insurance_tiss ON insurance_tiss.insurance_id = insurance.insurance_id
LEFT JOIN table_type AS insurance_table_type ON insurance_table_type.table_type_id = insurance_tiss.procedure_table_type_id


where o.order_id = $P{codigo_order}

ORDER BY 2, 6 ASC]]>
	</queryString>
	<field name="order_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="order_id"/>
		<property name="com.jaspersoft.studio.field.label" value="order_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
		<fieldDescription><![CDATA[Id da Ordem de Serviço (Conta Hospitalar)]]></fieldDescription>
	</field>
	<field name="dated" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.name" value="date"/>
		<property name="com.jaspersoft.studio.field.label" value="dated"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="TipoTabela" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="TipoTabela"/>
		<property name="com.jaspersoft.studio.field.label" value="TipoTabela"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="table_type"/>
	</field>
	<field name="CodigoProcedimento" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="code"/>
		<property name="com.jaspersoft.studio.field.label" value="CodigoProcedimento"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="NomeProcedimento" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="NomeProcedimento"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="QtdSolicAut" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="quantity"/>
		<property name="com.jaspersoft.studio.field.label" value="QtdSolicAut"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="qtd_detail" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.name" value="qtd_detail"/>
		<property name="com.jaspersoft.studio.field.label" value="qtd_detail"/>
	</field>
	<variable name="Variable_1" class="java.lang.Integer">
		<variableExpression><![CDATA[$V{REPORT_COUNT}]]></variableExpression>
	</variable>
	<columnHeader>
		<band height="7">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="10" y="-1" width="50" height="8" uuid="194ad633-1b6f-41a3-b33b-1faf994512ae">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="5" isBold="false"/>
				</textElement>
				<text><![CDATA[24 - Tabela]]></text>
			</staticText>
			<staticText>
				<reportElement x="61" y="-1" width="98" height="8" uuid="5dd4a09f-aeb6-4768-be3f-9633812a473c">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="5" isBold="false"/>
				</textElement>
				<text><![CDATA[25 - Codigo do Procedimento]]></text>
			</staticText>
			<staticText>
				<reportElement x="160" y="-1" width="415" height="8" uuid="f9656dcd-e4ef-42e9-aa28-09409ab1ab37">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="5" isBold="false"/>
				</textElement>
				<text><![CDATA[26 - Descrição]]></text>
			</staticText>
			<staticText>
				<reportElement x="576" y="-1" width="54" height="8" uuid="6df61618-d2f6-46f2-aa0a-97ae8f9e1e1e">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="5" isBold="false"/>
				</textElement>
				<text><![CDATA[27 - Qtd Solicitada]]></text>
			</staticText>
			<staticText>
				<reportElement x="631" y="-1" width="54" height="8" uuid="77a96784-bd77-4287-8da8-e5dc464debcb">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="5" isBold="false"/>
				</textElement>
				<text><![CDATA[28 - Qtd Autorizada]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="7" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField isBlankWhenNull="true">
				<reportElement x="10" y="-1" width="50" height="7" uuid="123728e3-b6b6-481a-a463-e7994b6996e7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="b991c632-09ef-45aa-b53f-f6324cdfd7d8"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="Arial" size="5" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TipoTabela}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="61" y="0" width="98" height="7" uuid="432caf12-f7dc-47a6-a5a1-15b9e03b322b">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="89e764e5-23f4-4700-a626-14b37fa53a8d"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="Arial" size="5" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CodigoProcedimento}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="160" y="0" width="415" height="7" uuid="8a0c6a09-08f8-4c7d-a193-ac48037de21d">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="f6fb7c77-cefb-4869-80e4-065ac975195d"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="Arial" size="5" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NomeProcedimento}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="576" y="0" width="54" height="7" uuid="608384d8-d45d-4480-945c-21aa503dd3b9">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="907573e2-4516-4780-8c12-939f670a9bf8"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="Arial" size="5" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QtdSolicAut}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="631" y="0" width="54" height="7" uuid="6b6d276e-e0a8-4b3c-9fcd-715a421c06ee">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="907573e2-4516-4780-8c12-939f670a9bf8"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="Arial" size="5" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QtdSolicAut}]]></textFieldExpression>
			</textField>
			<break>
				<reportElement x="0" y="6" width="100" height="1" uuid="58a614c8-104b-4e0a-9b80-40aacf34db3d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[$V{REPORT_COUNT} % 10 == 0  && $V{Variable_1} < $F{qtd_detail}]]></printWhenExpression>
				</reportElement>
			</break>
		</band>
	</detail>
</jasperReport>
