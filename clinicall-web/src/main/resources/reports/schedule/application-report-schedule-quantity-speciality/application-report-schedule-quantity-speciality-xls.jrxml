<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="application-report-billing-consolidated-synthetic" pageWidth="595" pageHeight="841" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4ff84453-1320-4ac5-a9ad-242cce5098f5">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_imeb"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="0"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="1000"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<subDataset name="Dataset1" uuid="7fe0c814-75e0-454d-bbae-8410466db122">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MySql "/>
		<queryString language="SQL">
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="costCenter" class="java.util.ArrayList"/>
	<parameter name="performer" class="java.util.ArrayList"/>
	<parameter name="insurance" class="java.util.ArrayList"/>
	<parameter name="dataInicio" class="java.lang.String"/>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="filters" class="java.util.ArrayList"/>
	<parameter name="nameReport" class="java.lang.String"/>
	<parameter name="user" class="java.util.ArrayList"/>
	<parameter name="costCenterStart" class="java.lang.String"/>
	<parameter name="costCenterEnd" class="java.lang.String"/>
	<parameter name="performerStart" class="java.lang.String"/>
	<parameter name="performerEnd" class="java.lang.String"/>
	<parameter name="performerRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "AND $X{IN,performer.performer_id, performer} " :
"AND person_performer.`name`  >= " +"$P" +"{performerStart}"+" AND person_performer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="costCenterRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{costCenterStart} == null || $P{costCenterEnd} == null ? "and $X{IN,cost_center.cost_center_id, costCenter} " :
"and cost_center.`name`  >= " +"$P" +"{costCenterStart}"+" AND cost_center.`name` <= " + "$P" + "{costCenterEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{userStart} == null || $P{userEnd} == null ? "and $X{IN,user.user_id, user} " :
"and personUser.`name`  >= " +"$P" +"{userStart}"+" AND personUser.`name` <= " + "$P" + "{userEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userEnd" class="java.lang.String"/>
	<parameter name="userStart" class="java.lang.String"/>
	<parameter name="firm" class="java.util.ArrayList"/>
	<parameter name="firmStart" class="java.lang.String"/>
	<parameter name="firmEnd" class="java.lang.String"/>
	<parameter name="firmRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{firmStart} == null || $P{firmEnd} == null ? "and  $X{IN,company.company_id, firm} " :
"and company.`name`  >= " +"$P" +"{firmStart}"+" AND company.`name` <= " + "$P" + "{firmEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="onlyGerarateCash" class="java.util.ArrayList"/>
	<parameter name="notGerarateCash" class="java.util.ArrayList"/>
	<parameter name="onlyGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{onlyGerarateCash} ==  null ?  " " : "and  $X{IN,insurance.payment_model, onlyGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="notGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{notGerarateCash} ==  null ? " " : "and  $X{IN,insurance.payment_model, notGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="printValue" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="printValueClinical" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="printCalculationBasis" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="insuranceStart" class="java.lang.String"/>
	<parameter name="insuranceEnd" class="java.lang.String"/>
	<parameter name="insuranceRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{insuranceStart} == null || $P{insuranceEnd} == null ? "and  $X{IN,`insurance`.`insurance_id`, insurance} " :
"and `insurance`.`name`  >= " +"$P" +"{insuranceStart}"+" AND `insurance`.`name` <= " + "$P" + "{insuranceEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="dateRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{dataInicio} == null || $P{dataFim} == null ?  " $X{IN, schedule.date , dataInicio}" :
"schedule.date BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="cover" class="java.util.ArrayList"/>
	<parameter name="coverRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{cover} == null  ? " " : "and $X{IN,`billing`.`cover` , cover} "]]></defaultValueExpression>
	</parameter>
	<parameter name="typeGuide" class="java.util.ArrayList"/>
	<parameter name="typeGuideStart" class="java.lang.String"/>
	<parameter name="typeGuideEnd" class="java.lang.String"/>
	<parameter name="typeGuideRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{typeGuideStart}== null || $P{typeGuideEnd} == null ? "and  $X{IN,`order_tiss`.`type_guide_id`, typeGuide} " :
"and `type_guide`.`name`  >= " +"$P" +"{typeGuideStart}"+" AND `type_guide`.`name` <= " + "$P" + "{typeGuideEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="speciality" class="java.util.ArrayList"/>
	<parameter name="specialityStart" class="java.lang.String"/>
	<parameter name="specialityEnd" class="java.lang.String"/>
	<parameter name="specialityRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{specialityStart} == null || $P{specialityEnd} == null ? "and  $X{IN,speciality.speciality_id, speciality} " :
"and speciality.`name`  >= " +"$P" +"{specialityStart}"+" AND speciality.`name` <= " + "$P" + "{specialityEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT 

`session`.`session_id`,
`company`.`name` AS company_name,
`person_performer`.`name` AS `performer_name`,
`person_patient`.`name` AS `patient_name`,
`speciality`.`name` AS especialidade,
CASE WHEN `schedule`.schedulestatus = "V" THEN 1 ELSE 0 END AS first_status,
CASE WHEN `schedule`.schedulestatus IN("A","X","P","E","R") THEN 1 ELSE 0 END AS appear,
CASE WHEN `schedule`.schedulestatus IN("F","Z","W") THEN 1 ELSE 0 END AS absence

FROM `schedule`

LEFT JOIN `session` ON `session`.`session_id` = `schedule`.`session_id`
LEFT JOIN `company` ON `company`.`company_id` = `schedule`.`company_id`
LEFT JOIN `image` ON `image`.`image_id` = `company`.`image_id`
 JOIN `patient` ON `patient`.`patient_id` = `schedule`.`patient_id`
LEFT JOIN `person` AS `person_patient` ON `person_patient`.`person_id` = `patient`.`person_id`
LEFT JOIN `performer` ON `performer`.`performer_id` = `schedule`.`performer_id`
LEFT JOIN `professional` ON `professional`.`professional_id` = `performer`.`professional_id`
LEFT JOIN `person` AS `person_performer` ON `person_performer`.`person_id` = `professional`.`person_id`
LEFT JOIN `speciality` ON `speciality`.`speciality_id` = `professional`.`speciality_id`


WHERE 

 $P!{dateRange}
 $P!{performerRange}
  $P!{firmRange}
  $P!{insuranceRange}
 $P!{specialityRange}

GROUP BY 1,2,3,4,5,6,7,8
ORDER BY 3]]>
	</queryString>
	<field name="session_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="session_id"/>
		<property name="com.jaspersoft.studio.field.label" value="session_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="session"/>
		<fieldDescription><![CDATA[Id da Sessão]]></fieldDescription>
	</field>
	<field name="company_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="company_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="company"/>
	</field>
	<field name="performer_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="performer_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="patient_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="patient_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="especialidade" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="especialidade"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="speciality"/>
	</field>
	<field name="first_status" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="first_status"/>
		<property name="com.jaspersoft.studio.field.label" value="first_status"/>
	</field>
	<field name="appear" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="appear"/>
		<property name="com.jaspersoft.studio.field.label" value="appear"/>
	</field>
	<field name="absence" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="absence"/>
		<property name="com.jaspersoft.studio.field.label" value="absence"/>
	</field>
	<variable name="patient_name1" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{patient_name}]]></variableExpression>
	</variable>
	<variable name="appear1" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{appear}]]></variableExpression>
	</variable>
	<variable name="absence1" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{absence}]]></variableExpression>
	</variable>
	<variable name="first_status1" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{first_status}]]></variableExpression>
	</variable>
	<columnHeader>
		<band height="15">
			<staticText>
				<reportElement x="5" y="1" width="174" height="14" uuid="b382b005-5136-4b8d-879f-e7e96891e59c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Paciente]]></text>
			</staticText>
			<staticText>
				<reportElement x="179" y="1" width="175" height="14" uuid="56820a70-969a-4cd9-91cb-0bf0664bc741">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Profissional]]></text>
			</staticText>
			<staticText>
				<reportElement x="354" y="1" width="112" height="14" uuid="a752781d-c58f-4681-a447-8219c9cda164">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Especialidade]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="14">
			<textField>
				<reportElement x="179" y="0" width="175" height="14" uuid="77f2702f-b101-4d53-b843-4c11f37aa2c9">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="2839c285-95f9-4e77-a90e-4097d544682a"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{performer_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="0" width="174" height="14" uuid="3519603b-3145-4165-b648-9e471ef83824">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="85a77634-4915-4bbe-83f4-0d228b7a10e1"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{patient_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="354" y="0" width="112" height="14" uuid="34fe4a0f-9f0a-4a9d-a337-ff28a04ec163">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="383c972d-e27a-4286-943c-e4d501d7584e"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{especialidade}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="56">
			<staticText>
				<reportElement x="5" y="0" width="174" height="14" uuid="4eb5cdd2-cae8-4fc6-b7ce-4904e66a8f59">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Total de Agendamentos:]]></text>
			</staticText>
			<textField>
				<reportElement x="179" y="0" width="175" height="14" uuid="6f587d3f-0b0e-4005-a366-eef324e493cb">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$V{patient_name1}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="5" y="42" width="174" height="14" uuid="0105da09-ace6-4111-845f-02b14bdbd4b3">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Total de Agendamentos Primeira Vez:]]></text>
			</staticText>
			<staticText>
				<reportElement x="5" y="14" width="174" height="14" uuid="a19acd91-7460-4fbb-88a5-2768e873377d">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Comparecidos:]]></text>
			</staticText>
			<staticText>
				<reportElement x="5" y="28" width="174" height="14" uuid="637539b9-6b7e-41a5-ab75-4f56191d3887">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Cancelados:]]></text>
			</staticText>
			<textField>
				<reportElement x="179" y="14" width="175" height="14" uuid="3446d85b-7b50-4b71-ba93-e20e10f4b7bd">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$V{appear1}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="179" y="28" width="175" height="14" uuid="f6b33e4c-b9eb-4f38-972d-67115fefcc92">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$V{absence1}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="179" y="42" width="175" height="14" uuid="2ab03907-dd14-4107-b0f7-f45f782a3f5b">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center"/>
				<textFieldExpression><![CDATA[$V{first_status1}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
	<noData>
		<band height="20">
			<staticText>
				<reportElement x="0" y="0" width="555" height="20" uuid="a3b4df7e-b79b-4f79-9c57-aa045a148094"/>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[NÃO HÁ DADOS PARA OS FILTROS SELECIONADOS.]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
