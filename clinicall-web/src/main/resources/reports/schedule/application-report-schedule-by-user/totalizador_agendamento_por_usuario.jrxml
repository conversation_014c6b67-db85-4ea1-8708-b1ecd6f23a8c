<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="totalizador_agendamento_por_usuario" pageWidth="802" pageHeight="555" orientation="Landscape" columnWidth="802" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="4eda491a-8a9b-4f09-a0bb-50ae31ceea13">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_demo"/>
	<parameter name="filters" class="java.util.ArrayList"/>
	<parameter name="nameReport" class="java.lang.String"/>
	<parameter name="costCenter" class="java.util.ArrayList"/>
	<parameter name="performer" class="java.util.ArrayList"/>
	<parameter name="dataInicio" class="java.lang.String"/>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="user" class="java.util.ArrayList"/>
	<parameter name="costCenterStart" class="java.lang.String"/>
	<parameter name="costCenterEnd" class="java.lang.String"/>
	<parameter name="performerStart" class="java.lang.String"/>
	<parameter name="performerEnd" class="java.lang.String"/>
	<parameter name="performerRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "$X{IN,performer.performer_id, performer} " :
"person_performer.`name`  >= " +"$P" +"{performerStart}"+" AND person_performer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="costCenterRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{costCenterStart} == null || $P{costCenterEnd} == null ? "and $X{IN,cost_center.cost_center_id, costCenter} " :
"and cost_center.`name`  >= " +"$P" +"{costCenterStart}"+" AND cost_center.`name` <= " + "$P" + "{costCenterEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{userStart} == null || $P{userEnd} == null ? "and $X{IN, `user`.user_id, user} " :
"and person_user.`name`  >= " +"$P" +"{userStart}"+" AND person_user.`name` <= " + "$P" + "{userEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userEnd" class="java.lang.String"/>
	<parameter name="userStart" class="java.lang.String"/>
	<parameter name="dateRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{dataInicio} == null || $P{dataFim} == null ? " " :
"AND schedule.`scheduled` BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="firm" class="java.util.ArrayList"/>
	<parameter name="firmStart" class="java.lang.String"/>
	<parameter name="firmEnd" class="java.lang.String"/>
	<parameter name="firmRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{firmStart} == null || $P{firmEnd} == null ? "and  $X{IN,company.company_id, firm} " :
"and company.`name`  >= " +"$P" +"{firmStart}"+" AND company.`name` <= " + "$P" + "{firmEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="insurance" class="java.util.ArrayList"/>
	<parameter name="insuranceStart" class="java.lang.String"/>
	<parameter name="insuranceEnd" class="java.lang.String"/>
	<parameter name="insuranceRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{insuranceStart} == null || $P{insuranceEnd} == null ? "and  $X{IN,`insurance`.`insurance_id`, insurance} " :
"and `insurance`.`name`  >= " +"$P" +"{insuranceStart}"+" AND `insurance`.`name` <= " + "$P" + "{insuranceEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="onlyGerarateCash" class="java.util.ArrayList"/>
	<parameter name="notGerarateCash" class="java.util.ArrayList"/>
	<parameter name="onlyGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{onlyGerarateCash} ==  null ?  " " : "and  $X{IN,insurance.payment_model, onlyGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="notGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{notGerarateCash} ==  null ? " " : "and  $X{IN,insurance.payment_model, notGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="groupProcedure" class="java.util.ArrayList"/>
	<parameter name="groupProcedureStart" class="java.lang.String"/>
	<parameter name="groupProcedureEnd" class="java.lang.String"/>
	<parameter name="groupProcedureRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{groupProcedureStart} == null || $P{groupProcedureEnd} == null ? "and  $X{IN, `group`.group_id, groupProcedure} " :
"and `group`.`name`  >= " +"$P" +"{groupProcedureStart}"+" AND `group`.`name` <= " + "$P" + "{groupProcedureEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="patient" class="java.util.ArrayList"/>
	<parameter name="patientStart" class="java.lang.String"/>
	<parameter name="patientEnd" class="java.lang.String"/>
	<parameter name="patientRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{patientStart} == null || $P{patientEnd} == null ? "and $X{IN,patient.patient_id, patient} " :
"and person_patient.`name`  >= " +"$P" +"{patientStart}"+" AND person_patient.`name` <= " + "$P" + "{patientEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="procedureEnd" class="java.lang.String"/>
	<parameter name="procedureStart" class="java.lang.String"/>
	<parameter name="procedure" class="java.util.ArrayList"/>
	<parameter name="procedureRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{procedureStart} == null || $P{procedureEnd} == null ? "and $X{IN,`procedure`.procedure_id, procedure} " :
"and `procedure`.`name`  >= " +"$P" +"{procedureStart}"+" AND `procedure`.`name` <= " + "$P" + "{procedureEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="blocked" class="java.util.ArrayList"/>
	<parameter name="pending" class="java.util.ArrayList"/>
	<parameter name="blockedRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{blocked} == null ? " " : "and  $X{IN,`order`.state, blocked} "]]></defaultValueExpression>
	</parameter>
	<parameter name="pendingRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{pending} == null ?  " " : "and  $X{IN,`order`.state, pending} "]]></defaultValueExpression>
	</parameter>
	<parameter name="normal" class="java.util.ArrayList"/>
	<parameter name="normalRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{normal} == null ?  " " : "and  $X{IN,`order`.state, normal} "]]></defaultValueExpression>
	</parameter>
	<parameter name="companyImage" class="java.lang.String" isForPrompting="false"/>
	<parameter name="companyName" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[SELECT
    COUNT(patient.patient_id) + COUNT(person_notPat.person_id) AS `personOuPatient`,
    -- COUNT(person_notPat.person_id) AS `personNotPatId`,
    person_user.name AS `usuario`
FROM
    schedule
LEFT JOIN patient ON patient.patient_id = schedule.patient_id
LEFT JOIN person AS person_pat ON person_pat.person_id = patient.person_id 
LEFT JOIN person AS person_notPat ON person_notPat.person_id = schedule.person_id    
LEFT JOIN `user` ON schedule.user_id = `user`.user_id
LEFT JOIN person AS person_user ON person_user.person_id = `user`.person_id 
LEFT JOIN insurance ON insurance.insurance_id = schedule.insurance_id
WHERE 
    schedule.scheduled IS NOT NULL
    AND (schedule.person_id IS NOT NULL OR schedule.patient_id IS NOT NULL)
    AND     $P!{performerRange}
  $P!{userRange}
      $P!{insuranceRange}
 $P!{dateRange}
GROUP BY 
2
ORDER BY 2;]]>
	</queryString>
	<field name="personOuPatient" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.name" value="personOuPatient"/>
		<property name="com.jaspersoft.studio.field.label" value="personOuPatient"/>
	</field>
	<field name="usuario" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="usuario"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="26" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="1" y="12" width="100" height="12" uuid="f51fd36e-7840-4226-823c-3c6007e506cc">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="dddcad10-1362-40fc-9942-a348d40af5a3"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Usuário]]></text>
			</staticText>
			<staticText>
				<reportElement x="101" y="12" width="54" height="12" uuid="c8824db9-5f2b-4a15-b2ff-e7e29fb1245d">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="a104b836-557a-48f6-a948-9b6b1c521957"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<frame>
				<reportElement x="1" y="24" width="154" height="2" uuid="5997203f-8f9b-43e1-a5a1-85fb688c63fc"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
			</frame>
			<staticText>
				<reportElement x="1" y="0" width="154" height="12" uuid="2bb00f51-4f91-4f86-bb96-1664078bbaba">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[RESUMO POR USUÁRIO]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField>
				<reportElement x="1" y="1" width="100" height="12" uuid="9542d139-5fc8-4fe3-9f17-093802aac470">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="dddcad10-1362-40fc-9942-a348d40af5a3"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuario}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="101" y="1" width="54" height="12" uuid="8be7377a-34ea-4b85-8244-bfa016782790">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="a104b836-557a-48f6-a948-9b6b1c521957"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{personOuPatient}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
