<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="application-report-order-attendance-pending" pageWidth="841" pageHeight="595" orientation="Landscape" whenNoDataType="NoDataSection" columnWidth="801" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4ff84453-1320-4ac5-a9ad-242cce5098f5">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="0"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="1000"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_spazio_gestar"/>
	<subDataset name="Dataset1" uuid="7fe0c814-75e0-454d-bbae-8410466db122">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MySql "/>
		<queryString language="SQL">
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="filters" class="java.util.ArrayList"/>
	<parameter name="nameReport" class="java.lang.String"/>
	<parameter name="costCenter" class="java.util.ArrayList"/>
	<parameter name="performer" class="java.util.ArrayList"/>
	<parameter name="dataInicio" class="java.lang.String"/>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="user" class="java.util.ArrayList"/>
	<parameter name="costCenterStart" class="java.lang.String"/>
	<parameter name="costCenterEnd" class="java.lang.String"/>
	<parameter name="performerStart" class="java.lang.String"/>
	<parameter name="performerEnd" class="java.lang.String"/>
	<parameter name="performerRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "$X{IN,performer.performer_id, performer} " :
"person_performer.`name`  >= " +"$P" +"{performerStart}"+" AND person_performer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="costCenterRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{costCenterStart} == null || $P{costCenterEnd} == null ? "and $X{IN,cost_center.cost_center_id, costCenter} " :
"and cost_center.`name`  >= " +"$P" +"{costCenterStart}"+" AND cost_center.`name` <= " + "$P" + "{costCenterEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{userStart} == null || $P{userEnd} == null ? "and $X{IN,user_order.user_id, user} " :
"and person_user.`name`  >= " +"$P" +"{userStart}"+" AND person_user.`name` <= " + "$P" + "{userEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userEnd" class="java.lang.String"/>
	<parameter name="userStart" class="java.lang.String"/>
	<parameter name="dateRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{dataInicio} == null || $P{dataFim} == null ? " " :
"AND billing.dateat BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="firm" class="java.util.ArrayList"/>
	<parameter name="firmStart" class="java.lang.String"/>
	<parameter name="firmEnd" class="java.lang.String"/>
	<parameter name="firmRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{firmStart} == null || $P{firmEnd} == null ? "and  $X{IN,company.company_id, firm} " :
"and company.`name`  >= " +"$P" +"{firmStart}"+" AND company.`name` <= " + "$P" + "{firmEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="insurance" class="java.util.ArrayList"/>
	<parameter name="insuranceStart" class="java.lang.String"/>
	<parameter name="insuranceEnd" class="java.lang.String"/>
	<parameter name="insuranceRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{insuranceStart} == null || $P{insuranceEnd} == null ? "and  $X{IN,`insurance`.`insurance_id`, insurance} " :
"and `insurance`.`name`  >= " +"$P" +"{insuranceStart}"+" AND `insurance`.`name` <= " + "$P" + "{insuranceEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="onlyGerarateCash" class="java.util.ArrayList"/>
	<parameter name="notGerarateCash" class="java.util.ArrayList"/>
	<parameter name="onlyGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{onlyGerarateCash} ==  null ?  " " : "and  $X{IN,insurance.payment_model, onlyGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="notGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{notGerarateCash} ==  null ? " " : "and  $X{IN,insurance.payment_model, notGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="groupProcedure" class="java.util.ArrayList"/>
	<parameter name="groupProcedureStart" class="java.lang.String"/>
	<parameter name="groupProcedureEnd" class="java.lang.String"/>
	<parameter name="groupProcedureRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{groupProcedureStart} == null || $P{groupProcedureEnd} == null ? "and  $X{IN, `group`.group_id, groupProcedure} " :
"and `group`.`name`  >= " +"$P" +"{groupProcedureStart}"+" AND `group`.`name` <= " + "$P" + "{groupProcedureEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="patient" class="java.util.ArrayList"/>
	<parameter name="patientStart" class="java.lang.String"/>
	<parameter name="patientEnd" class="java.lang.String"/>
	<parameter name="patientRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{patientStart} == null || $P{patientEnd} == null ? "and $X{IN,patient.patient_id, patient} " :
"and person_patient.`name`  >= " +"$P" +"{patientStart}"+" AND person_patient.`name` <= " + "$P" + "{patientEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="procedureEnd" class="java.lang.String"/>
	<parameter name="procedureStart" class="java.lang.String"/>
	<parameter name="procedure" class="java.util.ArrayList"/>
	<parameter name="procedureRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{procedureStart} == null || $P{procedureEnd} == null ? "and $X{IN,`procedure`.procedure_id, procedure} " :
"and `procedure`.`name`  >= " +"$P" +"{procedureStart}"+" AND `procedure`.`name` <= " + "$P" + "{procedureEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="blocked" class="java.util.ArrayList"/>
	<parameter name="pending" class="java.util.ArrayList"/>
	<parameter name="blockedRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{blocked} == null ? " " : "and  $X{IN,`order`.state, blocked} "]]></defaultValueExpression>
	</parameter>
	<parameter name="pendingRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{pending} == null ?  " " : "and  $X{IN,`order`.state, pending} "]]></defaultValueExpression>
	</parameter>
	<parameter name="normal" class="java.util.ArrayList"/>
	<parameter name="normalRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{normal} == null ?  " " : "and  $X{IN,`order`.state, normal} "]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[select 
	gloss.gloss_id as 'GlosS_id',
	company.name as 'company',
	order_item.value as 'Vl.Atend',
	gloss.value as 'Vl.GLosa', 
	case when gloss.amount_paid then gloss.amount_paid else 0. end +
	case when gloss.amount_paid_fee  then gloss.amount_paid_fee else 0. end +
	case when gloss.amount_paid_material then gloss.amount_paid_material else 0. end +
	case when gloss.amount_paid_medicament then gloss.amount_paid_medicament else 0. end + 
	case when gloss.amount_paid_auxiliary = null then gloss.amount_paid_auxiliary else 0. end  AS 'Vl.pago',
	insurance.name as 'Convênio',
	person_performer.name as 'Profissional',
	person_patient.name as 'Paciente',
	order_item.name as 'Serviço',
	CONCAT(gloss_type.code , ' - ', gloss_type.name ) AS 'Motivo Glosa',
	`order`.number as 'Guia',
	DATE_FORMAT(order_item.`date`, '%d/%m/%Y') as 'Dt. Atend',
	DATE_FORMAT(gloss.payday, '%d/%m/%Y') as 'Dt.Pgto.Glosa',
	DATE_FORMAT(billing.dateat, '%d/%m/%Y') as 'Dt.Fatur'
	-- image.content AS logo
from gloss
left join billing_pay_order_item on billing_pay_order_item.billing_pay_order_item_id = gloss.billing_pay_order_item_id
LEFT JOIN billing_pay ON  billing_pay.billing_pay_id = billing_pay_order_item.billing_pay_id
LEFT JOIN billing on billing.billing_id = billing_pay.billing_id
left join order_item on order_item.order_item_id = billing_pay_order_item.order_item_id
LEFT JOIN gloss_type_item ON gloss_type_item.gloss_id = gloss.gloss_id 
LEFT JOIN gloss_type ON gloss_type.gloss_type_id = gloss_type_item.gloss_type_id 
left join `order` on `order`.order_id = order_item.order_id
left join insurance on insurance.insurance_id = order.insurance_id
LEFT JOIN performer ON performer.performer_id = order_item.performer_id
LEFT JOIN professional ON professional.professional_id = performer.professional_id 
LEFT JOIN person person_performer ON person_performer.person_id = professional.person_id
LEFT JOIN patient ON patient.patient_id = `order`.patient_id
LEFT JOIN person person_patient ON person_patient.person_id = patient.person_id
LEFT JOIN company_cost_center ON company_cost_center.company_cost_center_id = `order`.company_cost_center_id  
LEFT JOIN company ON company.company_id = company_cost_center.company_id
LEFT JOIN image ON image.image_id = company.image_id
-- 
where 
    
$P!{performerRange}
				$P!{costCenterRange}
				$P!{insuranceRange}
				$P!{firmRange}
				$P!{groupProcedureRange}
				$P!{procedureRange}
				$P!{userRange}
				$P!{onlyGerarateCashRange}
				$P!{notGerarateCashRange}
				$P!{dateRange}     AND
				
(COALESCE(gloss.amount_paid, 0) +
       COALESCE(gloss.amount_paid_fee, 0) +
       COALESCE(gloss.amount_paid_material, 0) +
       COALESCE(gloss.amount_paid_medicament, 0) +
       COALESCE(gloss.amount_paid_auxiliary, 0)) <> 0 
       


-- 
order by order_item.date ,person_performer.name
;]]>
	</queryString>
	<field name="GlosS_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="gloss_id"/>
		<property name="com.jaspersoft.studio.field.label" value="GlosS_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="gloss"/>
		<fieldDescription><![CDATA[ID da Glosa]]></fieldDescription>
	</field>
	<field name="company" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="company"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="company"/>
	</field>
	<field name="Vl.Atend" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="value"/>
		<property name="com.jaspersoft.studio.field.label" value="Vl.Atend"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="Vl.GLosa" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="value"/>
		<property name="com.jaspersoft.studio.field.label" value="Vl.GLosa"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="gloss"/>
	</field>
	<field name="Vl.pago" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="Vl.pago"/>
		<property name="com.jaspersoft.studio.field.label" value="Vl.pago"/>
	</field>
	<field name="Convênio" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="Convênio"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="insurance"/>
	</field>
	<field name="Profissional" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="Profissional"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="Paciente" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="Paciente"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="Serviço" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="Serviço"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="Motivo Glosa" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="Motivo Glosa"/>
		<property name="com.jaspersoft.studio.field.label" value="Motivo Glosa"/>
	</field>
	<field name="Guia" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="number"/>
		<property name="com.jaspersoft.studio.field.label" value="Guia"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
	</field>
	<field name="Dt. Atend" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="Dt. Atend"/>
		<property name="com.jaspersoft.studio.field.label" value="Dt. Atend"/>
	</field>
	<field name="Dt.Pgto.Glosa" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="Dt.Pgto.Glosa"/>
		<property name="com.jaspersoft.studio.field.label" value="Dt.Pgto.Glosa"/>
	</field>
	<field name="Dt.Fatur" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="Dt.Fatur"/>
		<property name="com.jaspersoft.studio.field.label" value="Dt.Fatur"/>
	</field>
	<variable name="Vl.GLosa1" class="java.lang.Double" resetType="Group" resetGroup="Group_Professional" calculation="Sum">
		<variableExpression><![CDATA[$F{Vl.GLosa}]]></variableExpression>
	</variable>
	<variable name="Vl.pago1" class="java.lang.Double" resetType="Group" resetGroup="Group_Professional" calculation="Sum">
		<variableExpression><![CDATA[$F{Vl.pago}]]></variableExpression>
	</variable>
	<variable name="Vl.GLosa2" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{Vl.GLosa}]]></variableExpression>
	</variable>
	<variable name="Vl.pago2" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{Vl.pago}]]></variableExpression>
	</variable>
	<group name="Group_Professional">
		<groupExpression><![CDATA[$F{Profissional}]]></groupExpression>
		<groupHeader>
			<band height="24">
				<textField>
					<reportElement x="0" y="1" width="56" height="12" uuid="d5fc643e-5372-439e-baef-31355d6be12d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Profissional: "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="56" y="1" width="230" height="12" uuid="e52d2511-8816-46fb-a431-0a7f2e00ead7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Profissional}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="12" width="140" height="12" uuid="89234e3c-50dc-44fa-8884-7aea88af7a72">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="3897c172-65b1-4097-87c3-5044775eaa85"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Convênio]]></text>
				</staticText>
				<staticText>
					<reportElement x="140" y="12" width="120" height="12" uuid="09aa1687-1eab-495e-901f-0c6611064651">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="85d87e08-1746-4171-8bd3-ce257354e649"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Paciente]]></text>
				</staticText>
				<staticText>
					<reportElement x="261" y="12" width="108" height="12" uuid="06ad9535-5898-4dc5-a9de-a4f416d6f37d">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="e17250c3-5cae-48ae-9403-2a3efb23540d"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Serviço]]></text>
				</staticText>
				<staticText>
					<reportElement x="370" y="12" width="40" height="12" uuid="2d7b506d-e5fd-42b6-80a0-73aded28f5ab">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="e9f58853-8d12-420f-aaf7-99046b0ea3a2"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Guia]]></text>
				</staticText>
				<staticText>
					<reportElement x="410" y="12" width="50" height="12" uuid="de4b8d85-adc9-43e1-8ed7-7c2471d1539a">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="20644051-6ffa-4d73-adf5-e53bdfcbdeeb"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Dt. Atend]]></text>
				</staticText>
				<staticText>
					<reportElement x="460" y="12" width="55" height="12" uuid="df63eaf6-a8cc-45dd-a840-65c7f828e8b1">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="d43e1d32-ac93-49cc-ae2a-26041926cf50"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Dt.Pgto.Glosa]]></text>
				</staticText>
				<staticText>
					<reportElement x="518" y="12" width="50" height="12" uuid="502be280-17b9-4137-9bad-258783c7eff4">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="3b655582-cc33-4144-b6fb-************"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Dt.Fatur]]></text>
				</staticText>
				<staticText>
					<reportElement x="569" y="12" width="50" height="12" uuid="26aea756-9354-42db-8993-2fcb198d6fd6">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="336af692-25de-452f-b031-7e14b2ed6810"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Vl.GLosa]]></text>
				</staticText>
				<staticText>
					<reportElement x="675" y="12" width="125" height="12" uuid="16643daa-3979-4e02-afa9-1dc7634fe717">
						<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="5e792e63-bcff-4154-8dc4-c11b402cd3dd"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Motivo Glosa]]></text>
				</staticText>
				<frame>
					<reportElement x="0" y="23" width="800" height="1" uuid="17a2d756-**************-106fd08a3c4a">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</frame>
				<frame>
					<reportElement x="1" y="0" width="800" height="1" uuid="79425917-23c6-4339-b2f5-666f8f395f06">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
				</frame>
				<textField>
					<reportElement x="620" y="12" width="50" height="12" uuid="cd7c5261-2571-488e-be2a-79e5af35e9a9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Gl.Receb."]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="16">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<textField>
					<reportElement x="474" y="1" width="94" height="12" uuid="2d3627a0-cefd-4b44-a788-824f8ded0bc8">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Total do profissional: "]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Page" pattern="#,##0.00">
					<reportElement x="569" y="1" width="50" height="12" uuid="0905e84c-4bcc-4b21-ac27-cef81980c0a1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{Vl.GLosa1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="620" y="1" width="50" height="12" uuid="941c6e63-bb06-492c-bb26-61445c8322ec">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{Vl.pago1}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<pageHeader>
		<band height="60">
			<textField isBlankWhenNull="true">
				<reportElement x="230" y="31" width="344" height="25" uuid="321f1f63-b46c-439c-a430-8165e08bf9f1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Relação de Glosas Pagas"]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy">
				<reportElement x="730" y="17" width="60" height="15" uuid="e11903a3-920d-48d0-b89c-80f2ee64d61c"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="760" y="45" width="30" height="15" uuid="cb3aa02c-7575-4b86-87c8-9000228b885f"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="665" y="17" width="65" height="14" uuid="ee7dd153-6548-48cc-b06c-cefec483ceb3"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Data:]]></text>
			</staticText>
			<staticText>
				<reportElement x="665" y="45" width="65" height="14" uuid="ed407f09-8cb1-4d5a-9121-132a6a2aad1b"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Página:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="120" y="2" width="524" height="30" uuid="10c97d4d-01dd-4674-bd38-c91df8d39112"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="20" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{company}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="665" y="31" width="65" height="14" uuid="5caec54e-2731-4c4a-a32d-a4fac0113823"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Hora:]]></text>
			</staticText>
			<textField pattern="HH:mm">
				<reportElement x="730" y="30" width="60" height="15" uuid="f0d82f9d-3c9a-45b4-9db8-4ea0b4bd592e"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="12">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField>
				<reportElement x="0" y="0" width="140" height="12" uuid="51f35581-f01e-488b-9ff2-676b3473f020">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="3897c172-65b1-4097-87c3-5044775eaa85"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Convênio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="140" y="0" width="120" height="12" uuid="3ebd5c7c-5acb-46cc-98b5-0b3f66d3c36d">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="85d87e08-1746-4171-8bd3-ce257354e649"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Paciente}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="261" y="0" width="108" height="12" uuid="05520fe8-1e1d-4072-8e14-57f67cc1bb19">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="e17250c3-5cae-48ae-9403-2a3efb23540d"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Serviço}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="370" y="0" width="40" height="12" uuid="003c0072-0d4a-49bf-a393-9a37802724d9">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="e9f58853-8d12-420f-aaf7-99046b0ea3a2"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Guia}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="410" y="0" width="50" height="12" uuid="c72e848b-86dd-4998-8f60-ef2505a5d81a">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="20644051-6ffa-4d73-adf5-e53bdfcbdeeb"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Dt. Atend}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="460" y="0" width="55" height="12" uuid="c95ff38f-1db2-4594-9a81-a6663c062ba6">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="d43e1d32-ac93-49cc-ae2a-26041926cf50"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Dt.Pgto.Glosa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="518" y="0" width="50" height="12" uuid="b6ee2af6-e2f1-4d6f-b05e-fe5918950b21">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="3b655582-cc33-4144-b6fb-************"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Dt.Fatur}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="569" y="0" width="50" height="12" uuid="5af44f26-**************-48dce3147f7d">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="336af692-25de-452f-b031-7e14b2ed6810"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Vl.GLosa}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="620" y="0" width="50" height="12" uuid="3825fce8-e450-432f-bed4-0d851181e09f">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7388fe7f-4906-4b5f-9793-63043dcb827e"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Vl.pago}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="675" y="0" width="125" height="12" uuid="242165ee-156c-4101-8b16-7446a1f7da2d">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="5e792e63-bcff-4154-8dc4-c11b402cd3dd"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Motivo Glosa}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="15">
			<staticText>
				<reportElement x="518" y="3" width="49" height="12" uuid="325b6183-dcac-46c5-9051-c7f8c1e2a82e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Total Geral:]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="1" width="800" height="1" uuid="49f63265-1473-42c6-adae-e98b528e7b02">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<textField pattern="#,##0.00">
				<reportElement x="569" y="3" width="50" height="12" uuid="d914f2b7-4de9-46c0-8902-ab589bd13cc9">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{Vl.GLosa2}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="620" y="3" width="50" height="12" uuid="6847a315-ef32-4bc6-8ad4-0c1c5fa4afdd">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{Vl.pago2}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
	<noData>
		<band height="79">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="801" height="79" uuid="f54daa93-7c74-49ca-b93b-86d091ab637a"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="26" isBold="true"/>
				</textElement>
				<text><![CDATA[Não há dados para os filtros selecionados.]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
