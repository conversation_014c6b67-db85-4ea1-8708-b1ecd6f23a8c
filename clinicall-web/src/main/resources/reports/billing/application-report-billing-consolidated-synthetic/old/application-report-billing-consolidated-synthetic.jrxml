<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.0.final using JasperReports Library version 6.20.0-2bc7ab61c56f459e8176eb05c7705e145cd400ad  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="application-report-billing-consolidated-synthetic" pageWidth="595" pageHeight="841" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4ff84453-1320-4ac5-a9ad-242cce5098f5">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Sql-Local"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="0"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="1000"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<subDataset name="Dataset1" uuid="7fe0c814-75e0-454d-bbae-8410466db122">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MySql "/>
		<queryString language="SQL">
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="costCenter" class="java.util.ArrayList"/>
	<parameter name="performer" class="java.util.ArrayList"/>
	<parameter name="insurance" class="java.util.ArrayList"/>
	<parameter name="dataInicio" class="java.lang.String"/>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="filters" class="java.util.ArrayList"/>
	<parameter name="nameReport" class="java.lang.String"/>
	<parameter name="user" class="java.util.ArrayList"/>
	<parameter name="costCenterStart" class="java.lang.String"/>
	<parameter name="costCenterEnd" class="java.lang.String"/>
	<parameter name="performerStart" class="java.lang.String"/>
	<parameter name="performerEnd" class="java.lang.String"/>
	<parameter name="performerRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "AND $X{IN,performer.performer_id, performer} " :
"AND person_performer.`name`  >= " +"$P" +"{performerStart}"+" AND person_performer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="costCenterRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{costCenterStart} == null || $P{costCenterEnd} == null ? "and $X{IN,cost_center.cost_center_id, costCenter} " :
"and cost_center.`name`  >= " +"$P" +"{costCenterStart}"+" AND cost_center.`name` <= " + "$P" + "{costCenterEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{userStart} == null || $P{userEnd} == null ? "and $X{IN,user.user_id, user} " :
"and personUser.`name`  >= " +"$P" +"{userStart}"+" AND personUser.`name` <= " + "$P" + "{userEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userEnd" class="java.lang.String"/>
	<parameter name="userStart" class="java.lang.String"/>
	<parameter name="firm" class="java.util.ArrayList"/>
	<parameter name="firmStart" class="java.lang.String"/>
	<parameter name="firmEnd" class="java.lang.String"/>
	<parameter name="firmRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{firmStart} == null || $P{firmEnd} == null ? "and  $X{IN,company.company_id, firm} " :
"and company.`name`  >= " +"$P" +"{firmStart}"+" AND company.`name` <= " + "$P" + "{firmEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="onlyGerarateCash" class="java.util.ArrayList"/>
	<parameter name="notGerarateCash" class="java.util.ArrayList"/>
	<parameter name="onlyGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{onlyGerarateCash} ==  null ?  " " : "and  $X{IN,insurance.payment_model, onlyGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="notGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{notGerarateCash} ==  null ? " " : "and  $X{IN,insurance.payment_model, notGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="printValue" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="printValueClinical" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="printCalculationBasis" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="insuranceStart" class="java.lang.String"/>
	<parameter name="insuranceEnd" class="java.lang.String"/>
	<parameter name="insuranceRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{insuranceStart} == null || $P{insuranceEnd} == null ? "and  $X{IN,`insurance`.`insurance_id`, insurance} " :
"and `insurance`.`name`  >= " +"$P" +"{insuranceStart}"+" AND `insurance`.`name` <= " + "$P" + "{insuranceEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="dateRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{dataInicio} == null || $P{dataFim} == null ?  " $X{IN,billing.dateat , dataInicio}" :
"billing.dateat BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="cover" class="java.util.ArrayList"/>
	<parameter name="coverRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{cover} == null  ? " " : "and $X{IN,`billing`.`cover` , cover} "]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT 
`order`.`order_id`,
`billing`.`insurance_id` ,
`patient`.`patient_id`,
`order`.`date` AS procedure_date, -- data do atendimento
`order`.`number` AS guide_number, -- numero da guia
`insurance`.`name` AS insurance_name, -- nome do convênio
`person_performer`.`name` AS performer_name, -- nome do profissional
`person_patient`.`name` AS patient_name, -- nome do paciente
`patient`.`enrollment` AS patient_enrollment, -- matricula
`currency`.`name` AS currency_name,
`company`.`name` AS company_name,
`image`.`content` AS logo,
`order`.`value` AS order_value,
count(`order_item`.`order_item_id`) AS quantity -- quantidade

FROM `billing` 

 JOIN `insurance` ON `insurance`.`insurance_id` = `billing`.`insurance_id`
LEFT JOIN `billing_order_item` ON `billing_order_item`.`billing_id` = `billing`.`billing_id`
LEFT JOIN `order_item` ON `order_item`.`order_item_id` = `billing_order_item`.`order_item_id`
LEFT JOIN `order` ON `order`.`order_id` = `order_item`.`order_id`
LEFT JOIN `patient` ON `patient`.`patient_id` = `order`.`patient_id`
LEFT JOIN `person` AS `person_patient` ON `person_patient`.`person_id` = `patient`.`person_id`
LEFT JOIN `performer` ON `performer`.`performer_id` = `order_item`.`performer_id`
LEFT JOIN `professional` ON `professional`.`professional_id` = `performer`.`professional_id`
LEFT JOIN `person` AS `person_performer` ON `person_performer`.`person_id` = `professional`.`person_id`
LEFT JOIN `currency` ON `currency`.`currency_id` = `order_item`.`currency_id`
LEFT JOIN `company_cost_center` on `company_cost_center`.`company_cost_center_id` = `billing`.`company_cost_center_id`
LEFT JOIN `company` on `company`.`company_id` = `company_cost_center`.`company_id`
LEFT JOIN `image` on `image`.`image_id` = `company`.`image_id`

WHERE 

billing.release = 0 and
 $P!{dateRange}
 $P!{performerRange}
 $P!{costCenterRange}
  $P!{firmRange}
 $P!{userRange} 
  $P!{coverRange} 
  $P!{insuranceRange} 
  
GROUP BY 1,2,3,4,5,6,7,8,9,10,11,12,13
ORDER BY 2, 3 , 4, 5]]>
	</queryString>
	<field name="order_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="order_id"/>
		<property name="com.jaspersoft.studio.field.label" value="order_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
		<fieldDescription><![CDATA[Id da Ordem de Serviço (Conta Hospitalar)]]></fieldDescription>
	</field>
	<field name="insurance_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="insurance_id"/>
		<property name="com.jaspersoft.studio.field.label" value="insurance_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="billing"/>
		<fieldDescription><![CDATA[Id do Convenio]]></fieldDescription>
	</field>
	<field name="patient_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="patient_id"/>
		<property name="com.jaspersoft.studio.field.label" value="patient_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="patient"/>
		<fieldDescription><![CDATA[Id do Paciente]]></fieldDescription>
	</field>
	<field name="procedure_date" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.name" value="date"/>
		<property name="com.jaspersoft.studio.field.label" value="procedure_date"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
	</field>
	<field name="guide_number" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="number"/>
		<property name="com.jaspersoft.studio.field.label" value="guide_number"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
	</field>
	<field name="insurance_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="insurance_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="insurance"/>
	</field>
	<field name="performer_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="performer_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="patient_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="patient_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="patient_enrollment" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="enrollment"/>
		<property name="com.jaspersoft.studio.field.label" value="patient_enrollment"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="patient"/>
	</field>
	<field name="currency_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="currency_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="currency"/>
	</field>
	<field name="company_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="company_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="company"/>
	</field>
	<field name="logo" class="byte[]">
		<property name="com.jaspersoft.studio.field.name" value="content"/>
		<property name="com.jaspersoft.studio.field.label" value="logo"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="image"/>
	</field>
	<field name="order_value" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="value"/>
		<property name="com.jaspersoft.studio.field.label" value="order_value"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
	</field>
	<field name="quantity" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.name" value="quantity"/>
		<property name="com.jaspersoft.studio.field.label" value="quantity"/>
	</field>
	<variable name="order_value1" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{order_value}]]></variableExpression>
	</variable>
	<variable name="order_value2" class="java.lang.Double" resetType="Group" resetGroup="insurance" calculation="Sum">
		<variableExpression><![CDATA[$F{order_value}]]></variableExpression>
	</variable>
	<group name="insurance" isStartNewPage="true" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{insurance_id}]]></groupExpression>
		<groupHeader>
			<band height="52">
				<staticText>
					<reportElement x="0" y="3" width="40" height="14" uuid="6283b5ba-4c25-44c7-b245-f5800751730c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Convênio:]]></text>
				</staticText>
				<line>
					<reportElement x="0" y="1" width="555" height="1" uuid="a7d6ff40-2776-4e04-8762-edcca652c680">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<staticText>
					<reportElement x="0" y="36" width="60" height="14" uuid="93d4ba93-a3a7-4391-a962-0f93832a594a">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Matricula]]></text>
				</staticText>
				<staticText>
					<reportElement x="66" y="36" width="107" height="14" uuid="b382b005-5136-4b8d-879f-e7e96891e59c">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Nome]]></text>
				</staticText>
				<staticText>
					<reportElement x="178" y="36" width="106" height="14" uuid="56820a70-969a-4cd9-91cb-0bf0664bc741">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Profissional]]></text>
				</staticText>
				<staticText>
					<reportElement x="290" y="36" width="46" height="14" uuid="3cc3c8f9-8a9f-4ea6-a836-8ba1a0103a04">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[OS]]></text>
				</staticText>
				<staticText>
					<reportElement x="402" y="36" width="52" height="14" uuid="22efcdc1-a38c-4796-8497-9d1a32385416">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Guia]]></text>
				</staticText>
				<staticText>
					<reportElement x="341" y="36" width="56" height="14" uuid="47fee26d-015a-4541-8180-4536f8bea924">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Data]]></text>
				</staticText>
				<staticText>
					<reportElement x="460" y="36" width="42" height="14" uuid="2ef2dba7-b796-48b8-aadf-e471ea8eae3c">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Qtd]]></text>
				</staticText>
				<staticText>
					<reportElement x="506" y="36" width="40" height="14" uuid="a752781d-c58f-4681-a447-8219c9cda164">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Valor]]></text>
				</staticText>
				<line>
					<reportElement x="1" y="51" width="555" height="1" uuid="32b904be-0923-4cb4-a7a4-480de8f8f275">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<line>
					<reportElement x="1" y="33" width="555" height="1" uuid="0c2a7eff-ff49-4681-83a8-df6a2ece8b4d">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<staticText>
					<reportElement x="0" y="17" width="40" height="14" uuid="0682a778-1530-4f58-ad3f-4c89decc92eb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[C.G.C.:]]></text>
				</staticText>
				<textField>
					<reportElement x="45" y="3" width="265" height="14" uuid="b9548e63-56b0-42ea-995d-77c5d6b6f45d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement>
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{insurance_name}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="18">
				<staticText>
					<reportElement x="0" y="4" width="110" height="14" uuid="2d28a142-a7d6-493a-a22f-ad5dd41c3ac9">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Left">
						<font size="7" isBold="true"/>
					</textElement>
					<text><![CDATA[Total do Convênio:]]></text>
				</staticText>
				<line>
					<reportElement x="0" y="1" width="555" height="1" uuid="109534db-8cc4-4db3-bfa3-62166569f1a8">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
				</line>
				<textField pattern="#,##0.00">
					<reportElement x="506" y="4" width="40" height="14" uuid="4c672979-2e38-4042-8f16-d86b8bfe446d">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{order_value2}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<pageHeader>
		<band height="94">
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="31" width="555" height="25" uuid="321f1f63-b46c-439c-a430-8165e08bf9f1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{nameReport}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy">
				<reportElement x="491" y="11" width="60" height="15" uuid="e11903a3-920d-48d0-b89c-80f2ee64d61c"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="521" y="39" width="30" height="15" uuid="cb3aa02c-7575-4b86-87c8-9000228b885f"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="428" y="11" width="58" height="14" uuid="ee7dd153-6548-48cc-b06c-cefec483ceb3"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Data:]]></text>
			</staticText>
			<staticText>
				<reportElement x="428" y="39" width="58" height="14" uuid="ed407f09-8cb1-4d5a-9121-132a6a2aad1b"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Página:]]></text>
			</staticText>
			<image hAlign="Center" vAlign="Middle">
				<reportElement x="-9" y="4" width="85" height="40" uuid="59c84930-55aa-4a7a-af69-2e386701470d"/>
				<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64(new String($F{logo}).getBytes("UTF-8")))]]></imageExpression>
			</image>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="6" y="75" width="542" height="16" uuid="6402bda8-77f9-4af2-9180-b51b332e81fc"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{filters}.isEmpty() ? null : $P{filters}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="62" width="555" height="21" uuid="7e73f7dc-2685-4299-b5ab-d4d53f2963a1"/>
				<textElement textAlignment="Center">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Filtros Selecionados]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="2" width="555" height="30" uuid="10c97d4d-01dd-4674-bd38-c91df8d39112">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{company_name}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="428" y="25" width="58" height="14" uuid="5caec54e-2731-4c4a-a32d-a4fac0113823"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Hora:]]></text>
			</staticText>
			<textField pattern="HH:mm">
				<reportElement x="491" y="24" width="60" height="15" uuid="f0d82f9d-3c9a-45b4-9db8-4ea0b4bd592e"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="16">
			<textField isBlankWhenNull="true">
				<reportElement x="290" y="2" width="46" height="14" uuid="a2abc1d9-276c-43ad-9101-54533687b578">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{order_id}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="341" y="2" width="56" height="14" uuid="fefe5396-e6bf-4099-a2e3-e03f1b4f50e8">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{procedure_date}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="402" y="2" width="52" height="14" uuid="969c0e9c-49af-474d-970f-a43aa5d15132">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{guide_number}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="178" y="2" width="106" height="14" uuid="f3a660b1-37c3-481f-a822-46348e991cb0">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{performer_name}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="66" y="2" width="107" height="14" uuid="64896f20-75cc-467d-a591-2a27bb8777f7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{patient_name}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="2" width="60" height="14" uuid="954c6f58-5336-4cc7-8fb4-4af72664a053">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{patient_enrollment}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="506" y="2" width="40" height="14" uuid="62cdd705-5958-456e-9230-32b188b98820">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{order_value}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="460" y="2" width="42" height="14" uuid="e4eafcf1-744e-4334-802c-d2c8eded9883">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantity}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="18">
			<line>
				<reportElement x="0" y="1" width="555" height="1" uuid="68ff0bc2-8257-4c6d-ad2b-f362d28a0d2b">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
		</band>
	</summary>
	<noData>
		<band height="20">
			<staticText>
				<reportElement x="0" y="0" width="555" height="20" uuid="a3b4df7e-b79b-4f79-9c57-aa045a148094"/>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[NÃO HÁ DADOS PARA OS FILTROS SELECIONADOS.]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
