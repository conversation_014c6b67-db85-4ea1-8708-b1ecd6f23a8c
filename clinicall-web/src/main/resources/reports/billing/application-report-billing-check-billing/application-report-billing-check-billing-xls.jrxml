<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="application-report-billing-check-billing" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="e977fe01-7054-4e10-8e8b-ac70bfb4acc8">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="642"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="357"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="filters" class="java.util.ArrayList"/>
	<parameter name="nameReport" class="java.lang.String"/>
	<parameter name="companyName" class="java.lang.String"/>
	<parameter name="total" class="java.lang.Double"/>
	<parameter name="quantity" class="java.lang.Integer"/>
	<parameter name="prodQuantity" class="java.lang.Integer"/>
	<parameter name="prodCountDistinct" class="java.lang.Long"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="orderList" class="java.util.List">
		<fieldDescription><![CDATA[orderList]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="57" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="551" height="57" uuid="a51d35dc-8905-46f2-9ef3-d87695d0d706"/>
				<textField pattern="dd/MM/yyyy">
					<reportElement x="491" y="15" width="60" height="13" uuid="a243fbc6-f191-4787-b477-597cb3bc9001"/>
					<textElement textAlignment="Right">
						<font size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="491" y="41" width="60" height="14" uuid="cfe1b822-2de1-4ace-96f3-ccc1023fcb16"/>
					<textElement textAlignment="Right">
						<font size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="450" y="15" width="41" height="13" uuid="bade666d-55e9-4ab2-abba-102960f9a338"/>
					<textElement textAlignment="Right">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Data:]]></text>
				</staticText>
				<staticText>
					<reportElement x="450" y="41" width="41" height="14" uuid="d62c0831-0965-47ef-941d-1181f99873b7"/>
					<textElement textAlignment="Right">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Página:]]></text>
				</staticText>
				<staticText>
					<reportElement x="450" y="28" width="41" height="13" uuid="b13973d8-35af-41d0-b81b-c313b16b0ff2"/>
					<textElement textAlignment="Right">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[Hora:]]></text>
				</staticText>
				<textField pattern="HH:mm">
					<reportElement x="491" y="30" width="60" height="13" uuid="47d404f4-16bd-452d-8755-67ae8251b88b"/>
					<textElement textAlignment="Right">
						<font size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="154" y="3" width="243" height="25" uuid="6881f091-581b-40e4-830d-32883810d564">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Center">
						<font size="14" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{companyName}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="125" y="30" width="300" height="25" uuid="6827223a-183b-4197-844e-d43e1d87fda1"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="14" isBold="true"/>
					</textElement>
					<text><![CDATA[Atendimentos a Faturar]]></text>
				</staticText>
			</frame>
		</band>
	</pageHeader>
	<detail>
		<band height="36" splitType="Stretch">
			<frame>
				<reportElement key="" x="0" y="0" width="555" height="36" uuid="2cc63ce9-5429-495e-a152-c22c145503a8">
					<property name="com.jaspersoft.studio.element.name" value="sub_Report_Procedure"/>
				</reportElement>
				<subreport>
					<reportElement x="0" y="0" width="555" height="36" uuid="11964afa-f4c2-450e-8351-7e70e34c83ce"/>
					<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource
        ($F{orderList})]]></dataSourceExpression>
					<subreportExpression><![CDATA["reports/billing/application-report-billing-check-billing/application-report-billing-check-billing-sub-group.jasper"]]></subreportExpression>
				</subreport>
			</frame>
		</band>
	</detail>
	<lastPageFooter>
		<band height="40">
			<frame>
				<reportElement x="0" y="0" width="555" height="40" uuid="3a36c974-a9f5-4346-97d6-95eb1489b1e3"/>
				<box>
					<topPen lineWidth="1.0"/>
				</box>
				<staticText>
					<reportElement x="2" y="14" width="65" height="13" uuid="4c96cdcb-55bf-4d4a-8034-5ccb9e1baff1">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<text><![CDATA[Quantidade:]]></text>
				</staticText>
				<staticText>
					<reportElement x="106" y="14" width="130" height="13" uuid="df8fdf53-bf6a-4d05-9241-96f10db767d3">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<text><![CDATA[Procedimentos Lançados:]]></text>
				</staticText>
				<staticText>
					<reportElement x="448" y="14" width="60" height="13" uuid="d869a416-1c7c-4b46-8ed6-d399eb395dd4"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<text><![CDATA[Total Geral:]]></text>
				</staticText>
				<textField pattern="#,##0.00##">
					<reportElement x="508" y="14" width="46" height="13" uuid="26a84f9f-75af-4a95-83b9-86debc5307ea"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{total}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="67" y="14" width="33" height="13" uuid="ab8f974f-0ecb-42b6-b6ed-6328a19ad73f"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{quantity}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="236" y="14" width="30" height="13" uuid="a0a40010-951b-4962-a0e0-04dcd3e87de6"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{prodQuantity}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="282" y="14" width="130" height="13" uuid="999639ae-287f-41d1-81cb-ed31454ee447">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<text><![CDATA[Procedimentos Distintos:]]></text>
				</staticText>
				<textField>
					<reportElement x="412" y="14" width="30" height="13" uuid="d570bce0-9690-4bfd-8615-84f185c48636"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{prodCountDistinct}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</lastPageFooter>
</jasperReport>
