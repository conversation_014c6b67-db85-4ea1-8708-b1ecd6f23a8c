<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="sub-agreement-customization" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="be3c8451-fc8d-46b1-b3ba-7172b619013c">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_imeb"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="accreditation_id" class="java.lang.Integer"/>
	<parameter name="procedure_id" class="java.lang.Integer"/>
	<parameter name="agreement_id" class="java.lang.Integer"/>
	<parameter name="procedure_value" class="java.lang.Double"/>
	<queryString>
		<![CDATA[SELECT 
accreditation.code,
COALESCE (product.name, fee.name) AS name,
currency.name AS currency_name,
accreditation.value 
FROM kit
LEFT JOIN product ON product.product_id = kit.product_id
LEFT JOIN fee ON fee.fee_id = kit.fee_id
LEFT JOIN accreditation ON accreditation.product_id  = product.product_id 
LEFT JOIN currency ON currency.currency_id = accreditation.currency_id 
WHERE kit.procedure_id = $P{procedure_id} 
AND kit.accreditation_id = $P{accreditation_id} 
AND accreditation.agreement_id = $P{agreement_id}]]>
	</queryString>
	<field name="code" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="code"/>
		<property name="com.jaspersoft.studio.field.label" value="code"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="accreditation"/>
		<fieldDescription><![CDATA[Codigo para Credenciamento]]></fieldDescription>
	</field>
	<field name="name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="name"/>
	</field>
	<field name="currency_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="currency_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="currency"/>
	</field>
	<field name="value" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="value"/>
		<property name="com.jaspersoft.studio.field.label" value="value"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="accreditation"/>
		<fieldDescription><![CDATA[Valor do Indice]]></fieldDescription>
	</field>
	<variable name="value1" class="java.lang.Double" resetType="Column" calculation="Sum">
		<variableExpression><![CDATA[$F{value}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="10" splitType="Stretch">
			<textField>
				<reportElement x="71" y="1" width="130" height="9" uuid="ca664607-0baf-40bc-856d-3eea81f82d55">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{name}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="493" y="1" width="60" height="9" uuid="df6ded2b-c9d8-4def-9b3e-7f96637c1224">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{value}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="202" y="1" width="48" height="9" uuid="93138d2f-44f8-4150-8c8e-7b76390e008d">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{value}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="251" y="1" width="48" height="9" uuid="b4d9376f-d6fc-40c6-9df6-8ac5fb6de151">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{currency_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="1" width="70" height="9" uuid="0855afca-6b24-4d71-96e3-b2bc6bad425c">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{code}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="22">
			<staticText>
				<reportElement x="270" y="1" width="150" height="10" uuid="52b1ec24-85cd-46ce-85f9-7dfbcedf470c">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor Total do Kit:]]></text>
			</staticText>
			<textField pattern="#,##0.00">
				<reportElement x="493" y="2" width="60" height="9" uuid="044d838b-f951-4295-84e7-1d7e45e56ac6">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{value1}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="493" y="13" width="60" height="9" uuid="4267ad0e-f54b-4681-9de4-2477225a0648">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{value1}+$P{procedure_value}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="270" y="12" width="150" height="10" uuid="6cd5d043-74ff-401c-b897-7ed3370ebdb8">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Valor Total do Procedimento:]]></text>
			</staticText>
			<line>
				<reportElement x="259" y="11" width="295" height="1" uuid="043d16ea-ac12-4d5d-a2f8-21a85829c15a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</line>
		</band>
	</columnFooter>
</jasperReport>
