<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="application-report-operational-clinical-staff" pageWidth="595" pageHeight="841" whenNoDataType="NoDataSection" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4ff84453-1320-4ac5-a9ad-242cce5098f5">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="0"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="1000"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_spazio_psi"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<subDataset name="Dataset1" uuid="7fe0c814-75e0-454d-bbae-8410466db122">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MySql "/>
		<queryString language="SQL">
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="filters" class="java.util.ArrayList"/>
	<parameter name="nameReport" class="java.lang.String"/>
	<parameter name="costCenter" class="java.util.ArrayList"/>
	<parameter name="performer" class="java.util.ArrayList"/>
	<parameter name="dataInicio" class="java.lang.String"/>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="user" class="java.util.ArrayList"/>
	<parameter name="costCenterStart" class="java.lang.String"/>
	<parameter name="costCenterEnd" class="java.lang.String"/>
	<parameter name="performerStart" class="java.lang.String"/>
	<parameter name="performerEnd" class="java.lang.String"/>
	<parameter name="performerRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "$X{IN,performer.performer_id, performer} " :
"person_performer.`name`  >= " +"$P" +"{performerStart}"+" AND person_performer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="costCenterRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{costCenterStart} == null || $P{costCenterEnd} == null ? "and $X{IN,cost_center.cost_center_id, costCenter} " :
"and cost_center.`name`  >= " +"$P" +"{costCenterStart}"+" AND cost_center.`name` <= " + "$P" + "{costCenterEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{userStart} == null || $P{userEnd} == null ? "and $X{IN,user_order.user_id, user} " :
"and person_user.`name`  >= " +"$P" +"{userStart}"+" AND person_user.`name` <= " + "$P" + "{userEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userEnd" class="java.lang.String"/>
	<parameter name="userStart" class="java.lang.String"/>
	<parameter name="dateRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{dataInicio} == null || $P{dataFim} == null ? " " :
"AND order_item.`date` BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="firm" class="java.util.ArrayList"/>
	<parameter name="firmStart" class="java.lang.String"/>
	<parameter name="firmEnd" class="java.lang.String"/>
	<parameter name="firmRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{firmStart} == null || $P{firmEnd} == null ? "and  $X{IN,company.company_id, firm} " :
"and company.`name`  >= " +"$P" +"{firmStart}"+" AND company.`name` <= " + "$P" + "{firmEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="insurance" class="java.util.ArrayList"/>
	<parameter name="insuranceStart" class="java.lang.String"/>
	<parameter name="insuranceEnd" class="java.lang.String"/>
	<parameter name="insuranceRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{insuranceStart} == null || $P{insuranceEnd} == null ? "and  $X{IN,`insurance`.`insurance_id`, insurance} " :
"and `insurance`.`name`  >= " +"$P" +"{insuranceStart}"+" AND `insurance`.`name` <= " + "$P" + "{insuranceEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="onlyGerarateCash" class="java.util.ArrayList"/>
	<parameter name="notGerarateCash" class="java.util.ArrayList"/>
	<parameter name="onlyGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{onlyGerarateCash} ==  null ?  " " : "and  $X{IN,insurance.payment_model, onlyGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="notGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{notGerarateCash} ==  null ? " " : "and  $X{IN,insurance.payment_model, notGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="groupProcedure" class="java.util.ArrayList"/>
	<parameter name="groupProcedureStart" class="java.lang.String"/>
	<parameter name="groupProcedureEnd" class="java.lang.String"/>
	<parameter name="groupProcedureRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{groupProcedureStart} == null || $P{groupProcedureEnd} == null ? "and  $X{IN, `group`.group_id, groupProcedure} " :
"and `group`.`name`  >= " +"$P" +"{groupProcedureStart}"+" AND `group`.`name` <= " + "$P" + "{groupProcedureEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="patient" class="java.util.ArrayList"/>
	<parameter name="patientStart" class="java.lang.String"/>
	<parameter name="patientEnd" class="java.lang.String"/>
	<parameter name="patientRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{patientStart} == null || $P{patientEnd} == null ? "and $X{IN,patient.patient_id, patient} " :
"and person_patient.`name`  >= " +"$P" +"{patientStart}"+" AND person_patient.`name` <= " + "$P" + "{patientEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="procedureEnd" class="java.lang.String"/>
	<parameter name="procedureStart" class="java.lang.String"/>
	<parameter name="procedure" class="java.util.ArrayList"/>
	<parameter name="procedureRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{procedureStart} == null || $P{procedureEnd} == null ? "and $X{IN,`procedure`.procedure_id, procedure} " :
"and `procedure`.`name`  >= " +"$P" +"{procedureStart}"+" AND `procedure`.`name` <= " + "$P" + "{procedureEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="blocked" class="java.util.ArrayList"/>
	<parameter name="pending" class="java.util.ArrayList"/>
	<parameter name="blockedRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{blocked} == null ? " " : "and  $X{IN,`order`.state, blocked} "]]></defaultValueExpression>
	</parameter>
	<parameter name="pendingRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{pending} == null ?  " " : "and  $X{IN,`order`.state, pending} "]]></defaultValueExpression>
	</parameter>
	<parameter name="normal" class="java.util.ArrayList"/>
	<parameter name="normalRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{normal} == null ?  " " : "and  $X{IN,`order`.state, normal} "]]></defaultValueExpression>
	</parameter>
	<parameter name="speciality" class="java.util.ArrayList"/>
	<parameter name="specialityStart" class="java.lang.String"/>
	<parameter name="specialityEnd" class="java.lang.String"/>
	<parameter name="specialityRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{specialityStart} == null || $P{specialityEnd} == null ? "and  $X{IN,speciality.speciality_id, speciality} " :
"and speciality.`name`  >= " +"$P" +"{specialityStart}"+" AND speciality.`name` <= " + "$P" + "{specialityEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT

person_performer.name AS professional_name,
professional.council_number AS council_number,
person_performer.cpf AS professional_cpf,
speciality.name AS scpeciality_name,
person_performer.birthday AS professional_birthday,
contact_list_email.email AS professional_email

FROM

professional

 JOIN performer ON performer.professional_id = professional.professional_id
LEFT JOIN person person_performer ON person_performer.person_id = professional.person_id 
LEFT JOIN speciality ON speciality.speciality_id = professional.speciality_id 
LEFT JOIN council ON council.council_id = professional.council_id 
LEFT JOIN contact_list ON contact_list.contact_list_id = person_performer.contact_list_id AND contact_list.`type` = 'G'
LEFT JOIN (
		SELECT  contact_list_email.parent_id AS parent_id,
                MAX(contact_list_email.contact_list_id) AS contact_list_id
        FROM contact_list contact_list_email
        WHERE contact_list_email.`type` = 'M'  -- EMail
        GROUP BY 1        
        ) AS contact_list_email_max ON contact_list_email_max.parent_id = contact_list.contact_list_id
LEFT JOIN (
		SELECT	contact_list_email.contact_list_id,
				contact_list_email.email
		FROM contact_list contact_list_email
		WHERE contact_list_email.`type` = 'M'  -- EMail
    ) AS contact_list_email ON contact_list_email.contact_list_id = contact_list_email_max.contact_list_id
    
    WHERE
    performer.active = true AND
     $P!{performerRange}
	 $P!{specialityRange}
	 
	ORDER BY 1]]>
	</queryString>
	<field name="professional_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="professional_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="council_number" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="council_number"/>
		<property name="com.jaspersoft.studio.field.label" value="council_number"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="professional"/>
		<fieldDescription><![CDATA[Numero do Conselho]]></fieldDescription>
	</field>
	<field name="professional_cpf" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="cpf"/>
		<property name="com.jaspersoft.studio.field.label" value="professional_cpf"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="scpeciality_name" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="scpeciality_name"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="speciality"/>
	</field>
	<field name="professional_birthday" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.name" value="birthday"/>
		<property name="com.jaspersoft.studio.field.label" value="professional_birthday"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="professional_email" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="professional_email"/>
		<property name="com.jaspersoft.studio.field.label" value="professional_email"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="contact_list"/>
	</field>
	<pageHeader>
		<band height="91">
			<textField pattern="dd/MM/yyyy">
				<reportElement x="490" y="10" width="60" height="15" uuid="e11903a3-920d-48d0-b89c-80f2ee64d61c"/>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="520" y="38" width="30" height="15" uuid="cb3aa02c-7575-4b86-87c8-9000228b885f"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="404" y="10" width="81" height="14" uuid="ee7dd153-6548-48cc-b06c-cefec483ceb3"/>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" isBold="true"/>
				</textElement>
				<text><![CDATA[Data:]]></text>
			</staticText>
			<staticText>
				<reportElement x="404" y="38" width="81" height="14" uuid="ed407f09-8cb1-4d5a-9121-132a6a2aad1b"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Página:]]></text>
			</staticText>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="6" y="70" width="544" height="20" uuid="6402bda8-77f9-4af2-9180-b51b332e81fc"/>
				<textElement>
					<font fontName="SansSerif" size="8" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{filters}.isEmpty() ? null : $P{filters}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="55" width="555" height="17" uuid="7e73f7dc-2685-4299-b5ab-d4d53f2963a1"/>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="8" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Filtros Selecionados]]></text>
			</staticText>
			<staticText>
				<reportElement x="404" y="24" width="81" height="14" uuid="5caec54e-2731-4c4a-a32d-a4fac0113823"/>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" isBold="true"/>
				</textElement>
				<text><![CDATA[Hora:]]></text>
			</staticText>
			<textField pattern="HH:mm">
				<reportElement x="490" y="23" width="60" height="15" uuid="f0d82f9d-3c9a-45b4-9db8-4ea0b4bd592e"/>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="90" y="12" width="310" height="38" uuid="d4c569dd-e133-42be-b4e0-0c3e297fb8c4"/>
				<textElement textAlignment="Center">
					<font size="22" isBold="true"/>
				</textElement>
				<text><![CDATA[Corpo Clinico]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="23">
			<line>
				<reportElement x="1" y="6" width="550" height="1" uuid="e16b8545-e76d-4347-874c-855e7d702e48">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="7" y="8" width="142" height="12" uuid="f3accf36-8a65-4461-9b7a-3fb60c6ef8df">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="SansSerif" size="6" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Profissional(a)]]></text>
			</staticText>
			<staticText>
				<reportElement x="265" y="8" width="112" height="12" uuid="fa46624e-0ce3-490a-acc3-b86568136445">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="SansSerif" size="6" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Especialidade]]></text>
			</staticText>
			<staticText>
				<reportElement x="193" y="8" width="70" height="12" uuid="5719479c-9ab6-409e-a277-ba83f6c4a8f2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="SansSerif" size="6" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[CPF]]></text>
			</staticText>
			<staticText>
				<reportElement x="379" y="8" width="60" height="12" uuid="cba97353-bbae-47e5-9cc4-5a308a3b2bf4">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="SansSerif" size="6" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Data Nascimento]]></text>
			</staticText>
			<line>
				<reportElement x="1" y="22" width="550" height="1" uuid="d0a1bd55-ebfc-4287-b45c-ff8bc633d963">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="150" y="8" width="40" height="12" uuid="4cd21659-8563-4fd2-8f2b-e914cbe22725">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="SansSerif" size="6" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[CRM]]></text>
			</staticText>
			<staticText>
				<reportElement x="440" y="8" width="110" height="12" uuid="2ce86efd-a54a-43ea-99a3-93e05e3ab7cf">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="6" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Email]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="15">
			<textField isBlankWhenNull="true">
				<reportElement x="7" y="3" width="142" height="12" uuid="a3b6f38f-f646-41b3-8040-5774c6fb964a"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{professional_name}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="265" y="3" width="112" height="12" uuid="548d0089-2a54-4e2c-876c-e3948090051e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{scpeciality_name}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="379" y="3" width="60" height="12" uuid="38eb36fe-d648-408e-bc64-79d9b4a8cf04">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{professional_birthday}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="193" y="3" width="70" height="12" uuid="1effd4a3-4724-4db1-b23d-61d414782239">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{professional_cpf}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="151" y="3" width="40" height="12" uuid="9fc73267-fb74-4113-a9ad-c6bdfb24cb40">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{council_number}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="440" y="3" width="110" height="12" uuid="1cdb2385-07aa-4825-8127-108fb1043ff1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{professional_email}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<noData>
		<band height="79">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="554" height="79" uuid="f54daa93-7c74-49ca-b93b-86d091ab637a"/>
				<textElement textAlignment="Center">
					<font size="26" isBold="true"/>
				</textElement>
				<text><![CDATA[Não há dados para os filtros selecionados.]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
