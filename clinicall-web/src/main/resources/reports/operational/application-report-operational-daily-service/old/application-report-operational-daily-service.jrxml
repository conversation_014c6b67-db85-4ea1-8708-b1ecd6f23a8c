<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="application-report-operational-daily-service" pageWidth="595" pageHeight="841" whenNoDataType="NoDataSection" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4ff84453-1320-4ac5-a9ad-242cce5098f5">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="0"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="1000"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_grupo_ame"/>
	<subDataset name="Dataset1" uuid="7fe0c814-75e0-454d-bbae-8410466db122">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MySql "/>
		<queryString language="SQL">
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="costCenter" class="java.util.ArrayList"/>
	<parameter name="performer" class="java.util.ArrayList"/>
	<parameter name="insurance" class="java.util.ArrayList"/>
	<parameter name="dataInicio" class="java.lang.String"/>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="filters" class="java.util.ArrayList"/>
	<parameter name="nameReport" class="java.lang.String"/>
	<parameter name="user" class="java.util.ArrayList"/>
	<parameter name="costCenterStart" class="java.lang.String"/>
	<parameter name="costCenterEnd" class="java.lang.String"/>
	<parameter name="performerStart" class="java.lang.String"/>
	<parameter name="performerEnd" class="java.lang.String"/>
	<parameter name="performerRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "$X{IN,performer.performer_id, performer} " :
"personPerformer.`name`  >= " +"$P" +"{performerStart}"+" AND personPerformer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="costCenterRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{costCenterStart} == null || $P{costCenterEnd} == null ? "and $X{IN,cost_center.cost_center_id, costCenter} " :
"and cost_center.`name`  >= " +"$P" +"{costCenterStart}"+" AND cost_center.`name` <= " + "$P" + "{costCenterEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{userStart} == null || $P{userEnd} == null ? "and $X{IN,user.user_id, user} " :
"and personUser.`name`  >= " +"$P" +"{userStart}"+" AND personUser.`name` <= " + "$P" + "{userEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userEnd" class="java.lang.String"/>
	<parameter name="userStart" class="java.lang.String"/>
	<parameter name="dateRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{dataInicio} == null || $P{dataFim} == null ? " " :
"AND order_item.`date` BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="firm" class="java.util.ArrayList"/>
	<parameter name="firmStart" class="java.lang.String"/>
	<parameter name="firmEnd" class="java.lang.String"/>
	<parameter name="firmRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{firmStart} == null || $P{firmEnd} == null ? "and  $X{IN,company.company_id, firm} " :
"and company.`name`  >= " +"$P" +"{firmStart}"+" AND company.`name` <= " + "$P" + "{firmEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT 
	image.content as logo,
	`order`.`number` as numeroOS,
	cost_center.`name` as centroResultado, 
	company.`name` as companyName,
	order_item.`date` as dataAtendimento,
	order_item.`code` as codigoAtendimento,
	order_item.`name` as nomeServiço,
	order_item.`hour` as horaLancamento,
	personPatient.`name` as nomePaciente,
	personPatient.person_id,
	cid.`code` as cid,
	insurance.`name` as nomeConvenio,
	performer.performer_id as idMedico,
	personPerformer.`name` as nomeMedico,
	order_item.quantity as qtd,
	COALESCE (order_pay.value, order_item.`total`) as valorServico,  -- valor com mat-med-taxa
	pay_type.tax as impostoFormaPagamento,
	pay_type.fee as taxaFormaPagamento,
	insurance_tax.medic as impostoConvênio,
	vw_tax_insurance.value AS taxaConvenio,
	
	CASE WHEN vw_tax_insurance.value > 0 AND  vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id  -- IMPOSTO CONVENIO / IMPOSTO PAGAMENTO / TAXA
	     THEN COALESCE (order_pay.value, order_item.`total`) - (COALESCE (order_pay.value, order_item.`total`) * (insurance_tax.medic + pay_type.tax + pay_type.fee)) / 100 
	   
	     WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id  --  IMPOSTO PAGAMENTO / TAXA
	     THEN COALESCE (order_pay.value, order_item.`total`) - (COALESCE (order_pay.value, order_item.`total`) * (pay_type.tax + pay_type.fee)) / 100 
	     
	     WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_tax_insurance.value > 0 AND pay_type.pay_type_id  -- IMPOSTO PAGAMENTO / IMPOSTO CONVENIO
	     THEN COALESCE (order_pay.value, order_item.`total`) - (COALESCE (order_pay.value, order_item.`total`) * (pay_type.tax + insurance_tax.medic )) / 100 
	     
	     WHEN vw_fee_discount_on_pay_type.value > 0 AND vw_tax_insurance.value > 0 AND pay_type.pay_type_id  -- IMPOSTO CONVENIO / TAXA
	     THEN COALESCE (order_pay.value, order_item.`total`) - (COALESCE (order_pay.value, order_item.`total`) * (pay_type.fee + insurance_tax.medic )) / 100 
	     
	     WHEN vw_tax_insurance.value > 0 
	     THEN COALESCE (order_pay.value, order_item.`total`) - (COALESCE (order_pay.value, order_item.`total`) * insurance_tax.medic ) / 100 -- IMPOSTO CONVENIO
	    
	     WHEN vw_tax_discount_on_pay_type.value > 0 AND pay_type.pay_type_id 
		 THEN COALESCE (order_pay.value, order_item.`total`) - (COALESCE (order_pay.value, order_item.`total`) * pay_type.tax ) / 100 -- IMPOSTO PAGAMENTO
		 
		 WHEN vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id 
		 THEN COALESCE (order_pay.value, order_item.`total`) - (COALESCE (order_pay.value, order_item.`total`) * pay_type.fee ) / 100  -- TAXA
	     
		 ELSE COALESCE (order_pay.value, order_item.`total`) END AS baseCalculo,

	CASE WHEN vw_tax_insurance.value > 0 AND  vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id -- IMPOSTO CONVENIO / IMPOSTO PAGAMENTO / TAXA
	     THEN COALESCE (order_pay.value, order_item.`total`) - (COALESCE (order_pay.value, order_item.`total`) * (insurance_tax.medic + pay_type.tax + pay_type.fee)) / 100 
	   
	     WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id --  IMPOSTO PAGAMENTO / TAXA
	     THEN COALESCE (order_pay.value, order_item.`total`) - (COALESCE (order_pay.value, order_item.`total`) * (pay_type.tax + pay_type.fee)) / 100 
	     
	     WHEN vw_tax_discount_on_pay_type.value > 0 AND vw_tax_insurance.value > 0 AND pay_type.pay_type_id -- IMPOSTO PAGAMENTO / IMPOSTO CONVENIO
	     THEN COALESCE (order_pay.value, order_item.`total`) - (COALESCE (order_pay.value, order_item.`total`) * (pay_type.tax + insurance_tax.medic )) / 100 
	     
	     WHEN vw_fee_discount_on_pay_type.value > 0 AND vw_tax_insurance.value > 0 AND pay_type.pay_type_id -- IMPOSTO CONVENIO / TAXA
	     THEN COALESCE (order_pay.value, order_item.`total`) - (COALESCE (order_pay.value, order_item.`total`) * (pay_type.fee + insurance_tax.medic )) / 100 
	     
	     WHEN vw_tax_insurance.value > 0
	     THEN COALESCE (order_pay.value, order_item.`total`) - (COALESCE (order_pay.value, order_item.`total`) * insurance_tax.medic ) / 100 -- IMPOSTO CONVENIO
	    
	     WHEN vw_tax_discount_on_pay_type.value > 0 AND pay_type.pay_type_id
		 THEN COALESCE (order_pay.value, order_item.`total`) - (COALESCE (order_pay.value, order_item.`total`) * pay_type.tax ) / 100 -- IMPOSTO PAGAMENTO
		 
		 WHEN vw_fee_discount_on_pay_type.value > 0 AND pay_type.pay_type_id
		 THEN COALESCE (order_pay.value, order_item.`total`) - (COALESCE (order_pay.value, order_item.`total`) * pay_type.fee ) / 100  -- TAXA
	     
		 ELSE COALESCE (order_pay.value, order_item.`total`) END * (order_item.participation_value / 100) AS valorMedico
    
FROM `order` 

LEFT JOIN order_item  ON order_item.order_id = `order`.order_id
LEFT JOIN company_cost_center ON company_cost_center.company_cost_center_id = `order`.company_cost_center_id
LEFT JOIN company  ON company.company_id = company_cost_center.company_id
LEFT JOIN image ON image.image_id = company.image_id
LEFT JOIN cost_center ON cost_center.cost_center_id = company_cost_center.cost_center_id
LEFT JOIN patient ON patient.patient_id = `order`.patient_id
LEFT JOIN person as personPatient ON personPatient.person_id = patient.person_id
LEFT JOIN order_diagnosis ON order_diagnosis.order_id = `order`.order_id and order_diagnosis.main = 1
LEFT JOIN cid ON cid.cid_id = order_diagnosis.cid_id
LEFT JOIN insurance  ON insurance.insurance_id = `order`.insurance_id
LEFT JOIN insurance_tax  ON insurance_tax.insurance_id = insurance.insurance_id
LEFT JOIN performer ON performer.performer_id = order_item.performer_id
LEFT JOIN professional ON professional.professional_id = performer.professional_id
LEFT JOIN person as personPerformer ON personPerformer.person_id = professional.person_id
LEFT JOIN user ON user.user_id = `order`.created_by
LEFT JOIN person as personUser ON user.person_id = personUser.person_id
LEFT JOIN `order_pay` ON `order_pay`.`order_id` = `order`.`order_id` 
LEFT JOIN `pay_type`  ON `pay_type`.`pay_type_id` = `order_pay`.`pay_type_id` 
LEFT JOIN billing_pay_order_item ON billing_pay_order_item.order_item_id = order_item.order_item_id 
LEFT JOIN gloss ON gloss.billing_pay_order_item_id = billing_pay_order_item.billing_pay_order_item_id 
LEFT JOIN `procedure` ON `procedure`.procedure_id = order_item.procedure_id 
LEFT JOIN `group` ON `group`.group_id = `procedure`.group_id
LEFT JOIN speciality ON speciality.speciality_id = professional.speciality_id
LEFT JOIN order_tiss ON order_tiss.order_id = `order`.order_id 
LEFT JOIN type_attendance ON type_attendance.type_attendance_id = order_tiss.type_attendance_id 

LEFT JOIN (SELECT COALESCE(parameter_value.value, parameter.value) AS value,
            parameter_value.key 
            FROM parameter 
            LEFT JOIN parameter_value ON parameter_value.parameter_id = parameter.parameter_id 
            WHERE parameter.name = "DISCOUNT_TAX_ON_INSURANCE"
            ) AS vw_tax_insurance ON vw_tax_insurance.key = professional.professional_id 

LEFT JOIN (SELECT COALESCE(parameter_value.value, parameter.value) AS value,
            parameter_value.key 
            FROM parameter 
            LEFT JOIN parameter_value ON parameter_value.parameter_id = parameter.parameter_id 
            WHERE parameter.name = "DISCOUNT_TAX_ON_PAY_TYPE"
            ) AS vw_tax_discount_on_pay_type ON vw_tax_discount_on_pay_type.key = professional.professional_id
            
LEFT JOIN (SELECT COALESCE(parameter_value.value, parameter.value) AS value,
            parameter_value.key 
            FROM parameter 
            LEFT JOIN parameter_value ON parameter_value.parameter_id = parameter.parameter_id 
            WHERE parameter.name = "DISCOUNT_FEE_ON_PAY_TYPE"
            ) AS vw_fee_discount_on_pay_type ON vw_tax_discount_on_pay_type.key = professional.professional_id               

            WHERE 
            
             $P!{performerRange}
 			$P!{costCenterRange}
  			$P!{firmRange}
 			$P!{userRange}
  			$P!{dateRange}          

ORDER BY
	personPerformer.`name`,
	performer.performer_id,
	order_item.`date`,
	personPatient.`name`;]]>
	</queryString>
	<field name="logo" class="byte[]">
		<property name="com.jaspersoft.studio.field.name" value="content"/>
		<property name="com.jaspersoft.studio.field.label" value="logo"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="image"/>
	</field>
	<field name="numeroOS" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="number"/>
		<property name="com.jaspersoft.studio.field.label" value="numeroOS"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
	</field>
	<field name="centroResultado" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="centroResultado"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="cost_center"/>
	</field>
	<field name="companyName" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="companyName"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="company"/>
	</field>
	<field name="dataAtendimento" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.name" value="date"/>
		<property name="com.jaspersoft.studio.field.label" value="dataAtendimento"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="codigoAtendimento" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="code"/>
		<property name="com.jaspersoft.studio.field.label" value="codigoAtendimento"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="nomeServiço" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="nomeServiço"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="horaLancamento" class="java.sql.Time">
		<property name="com.jaspersoft.studio.field.name" value="hour"/>
		<property name="com.jaspersoft.studio.field.label" value="horaLancamento"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="nomePaciente" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="nomePaciente"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="person_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="person_id"/>
		<property name="com.jaspersoft.studio.field.label" value="person_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
		<fieldDescription><![CDATA[Id da Pessoa]]></fieldDescription>
	</field>
	<field name="cid" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="code"/>
		<property name="com.jaspersoft.studio.field.label" value="cid"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="cid"/>
	</field>
	<field name="nomeConvenio" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="nomeConvenio"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="insurance"/>
	</field>
	<field name="idMedico" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="performer_id"/>
		<property name="com.jaspersoft.studio.field.label" value="idMedico"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="performer"/>
	</field>
	<field name="nomeMedico" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="nomeMedico"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="qtd" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="quantity"/>
		<property name="com.jaspersoft.studio.field.label" value="qtd"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="valorServico" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="valorServico"/>
		<property name="com.jaspersoft.studio.field.label" value="valorServico"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_item"/>
	</field>
	<field name="impostoFormaPagamento" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="tax"/>
		<property name="com.jaspersoft.studio.field.label" value="impostoFormaPagamento"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="pay_type"/>
	</field>
	<field name="taxaFormaPagamento" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="fee"/>
		<property name="com.jaspersoft.studio.field.label" value="taxaFormaPagamento"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="pay_type"/>
	</field>
	<field name="impostoConvênio" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="medic"/>
		<property name="com.jaspersoft.studio.field.label" value="impostoConvênio"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="insurance_tax"/>
	</field>
	<field name="taxaConvenio" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="taxaConvenio"/>
		<property name="com.jaspersoft.studio.field.label" value="taxaConvenio"/>
	</field>
	<field name="baseCalculo" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="baseCalculo"/>
		<property name="com.jaspersoft.studio.field.label" value="baseCalculo"/>
	</field>
	<field name="valorMedico" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="valorMedico"/>
		<property name="com.jaspersoft.studio.field.label" value="valorMedico"/>
	</field>
	<variable name="nomePaciente1" class="java.lang.Integer" resetType="Group" resetGroup="professionalGroup" calculation="Count">
		<variableExpression><![CDATA[$F{nomePaciente}]]></variableExpression>
	</variable>
	<variable name="valorMedico1" class="java.lang.Double" resetType="Group" resetGroup="professionalGroup" calculation="Sum">
		<variableExpression><![CDATA[$F{valorMedico}]]></variableExpression>
	</variable>
	<variable name="valorMedico2" class="java.lang.Double" resetType="Group" resetGroup="Group1" calculation="Sum">
		<variableExpression><![CDATA[$F{valorMedico}]]></variableExpression>
	</variable>
	<variable name="qtd1" class="java.lang.Double" resetType="Group" resetGroup="Group1" calculation="Sum">
		<variableExpression><![CDATA[$F{qtd}]]></variableExpression>
	</variable>
	<variable name="valorServico1" class="java.lang.Double" resetType="Group" resetGroup="Group1" calculation="Sum">
		<variableExpression><![CDATA[$F{valorServico}]]></variableExpression>
	</variable>
	<variable name="qtd2" class="java.lang.Double" resetType="Group" resetGroup="professionalGroup" calculation="Sum">
		<variableExpression><![CDATA[$F{qtd}]]></variableExpression>
	</variable>
	<variable name="valorServico2" class="java.lang.Double" resetType="Group" resetGroup="professionalGroup" calculation="Sum">
		<variableExpression><![CDATA[$F{valorServico}]]></variableExpression>
	</variable>
	<variable name="nomePaciente2" class="java.lang.Integer" resetType="Group" resetGroup="Group1" calculation="DistinctCount">
		<variableExpression><![CDATA[$F{person_id}]]></variableExpression>
	</variable>
	<variable name="baseCalculo1" class="java.lang.Double" resetType="Group" resetGroup="Group1" calculation="Sum">
		<variableExpression><![CDATA[$F{baseCalculo}]]></variableExpression>
	</variable>
	<variable name="baseCalculo2" class="java.lang.Double" resetType="Group" resetGroup="professionalGroup" calculation="Sum">
		<variableExpression><![CDATA[$F{baseCalculo}]]></variableExpression>
	</variable>
	<variable name="qtd3" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{qtd}]]></variableExpression>
	</variable>
	<variable name="valorServico3" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorServico}]]></variableExpression>
	</variable>
	<variable name="baseCalculo3" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{baseCalculo}]]></variableExpression>
	</variable>
	<variable name="valorMedico3" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valorMedico}]]></variableExpression>
	</variable>
	<group name="professionalGroup" isStartNewPage="true" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{idMedico}]]></groupExpression>
		<groupHeader>
			<band height="51">
				<rectangle>
					<reportElement x="-18" y="2" width="589" height="49" backcolor="rgba(255, 255, 255, 0.0)" uuid="bc760a72-8241-49d3-bf5b-4f84ed1ae473"/>
				</rectangle>
				<textField isBlankWhenNull="true">
					<reportElement x="39" y="8" width="231" height="14" uuid="d446760a-8268-446b-aeb7-1534d2f09c9a">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{nomeMedico}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="7" y="35" width="40" height="14" uuid="5b372dd9-1a84-4508-84af-3d6081aa556b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Data]]></text>
				</staticText>
				<staticText>
					<reportElement x="49" y="35" width="23" height="14" uuid="65d50393-e5cc-4637-aacd-2f9ff545b65f">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Hora]]></text>
				</staticText>
				<staticText>
					<reportElement x="73" y="35" width="112" height="14" uuid="56425de1-ec16-4222-b6c1-4878e66a2df2">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Paciente]]></text>
				</staticText>
				<staticText>
					<reportElement x="187" y="35" width="32" height="14" uuid="549f6a63-eb7a-4434-b4c6-59efd58e64d1">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[O.S]]></text>
				</staticText>
				<staticText>
					<reportElement x="220" y="35" width="112" height="14" uuid="c362f59d-40ed-48aa-b459-087459330f30">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Serviço]]></text>
				</staticText>
				<staticText>
					<reportElement x="333" y="35" width="21" height="14" uuid="d3e2d439-fd7d-4b86-a4f4-4903fbfe6c8b">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[CID]]></text>
				</staticText>
				<staticText>
					<reportElement x="355" y="35" width="92" height="14" uuid="d1e4aed0-9adb-4905-9bed-027b817c30e5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Convênio]]></text>
				</staticText>
				<staticText>
					<reportElement x="452" y="35" width="19" height="14" uuid="658fab67-3085-4b7c-9efd-6bea3d1c35d9">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Qtd.]]></text>
				</staticText>
				<staticText>
					<reportElement x="475" y="35" width="21" height="14" uuid="8d03fbd8-9aaf-477b-8c3b-d6d266d687f6">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Valor]]></text>
				</staticText>
				<staticText>
					<reportElement x="501" y="35" width="24" height="14" uuid="9bb21f3f-a674-42a0-b8e8-0093a62dd472">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[B.Calc]]></text>
				</staticText>
				<staticText>
					<reportElement x="530" y="35" width="30" height="14" uuid="78a0352c-7964-4cbd-9071-d37e833f4a17">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Med(R$)]]></text>
				</staticText>
				<staticText>
					<reportElement x="8" y="8" width="30" height="14" uuid="88d093ff-ab0f-43cb-9402-f34b8969d898">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Dr(a).:]]></text>
				</staticText>
				<staticText>
					<reportElement x="277" y="8" width="49" height="14" uuid="cd732e60-3400-43c4-9f0b-e809656566c3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Empresa:]]></text>
				</staticText>
				<staticText>
					<reportElement x="376" y="8" width="49" height="14" uuid="662384a5-be49-432c-9b62-98e5fb2f4699">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Unidade:]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12">
				<staticText>
					<reportElement x="333" y="1" width="90" height="11" uuid="5439b285-2273-4103-a941-05187d5ea193"/>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Total do médico:]]></text>
				</staticText>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="530" y="2" width="30" height="10" uuid="6bd6ad4f-d714-4476-b3ee-25473a344d72">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorMedico1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="429" y="2" width="30" height="10" uuid="75928dc7-c090-420a-8cc4-2d55856f2d88">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{qtd2}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="461" y="2" width="30" height="10" uuid="d1b402e6-9553-4cb1-b685-489f5eb5c14d">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorServico2}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="496" y="2" width="30" height="10" uuid="3b37ec5e-42a6-4c09-adb4-96cbaa3dabdd">
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{baseCalculo2}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="Group1">
		<groupExpression><![CDATA[$F{dataAtendimento}]]></groupExpression>
		<groupFooter>
			<band height="35">
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="530" y="1" width="30" height="10" uuid="4d8a201b-e82a-4b57-8966-9fe135de14cf">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorMedico2}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="377" y="1" width="70" height="17" uuid="d7e35b7f-7966-4bce-b092-f6ffcdfad32b"/>
					<textElement>
						<font size="6" isBold="true"/>
					</textElement>
					<text><![CDATA[Total do dia:]]></text>
				</staticText>
				<staticText>
					<reportElement x="5" y="18" width="129" height="17" uuid="be73139c-9dd5-4e67-b718-f53cd2095cf6">
						<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Total de pacientes do dia:]]></text>
				</staticText>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="452" y="1" width="19" height="10" uuid="cd40ae1a-28df-4bf7-8a01-9ca35e0b60d5">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{qtd1}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="475" y="1" width="21" height="10" uuid="fc6f1995-54c5-4e1f-a82b-cdcc89af7d55">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorServico1}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="136" y="18" width="100" height="17" uuid="9de44332-e6d7-4be4-963e-25a4bdbba6f5">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{nomePaciente2}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00">
					<reportElement x="501" y="1" width="24" height="10" uuid="29076b84-57ad-44df-8928-d0edb2fa3a29">
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement textAlignment="Right">
						<font size="6"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{baseCalculo1}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<pageHeader>
		<band height="94">
			<textField isBlankWhenNull="true">
				<reportElement x="86" y="31" width="339" height="25" uuid="321f1f63-b46c-439c-a430-8165e08bf9f1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{nameReport}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy">
				<reportElement x="491" y="11" width="60" height="15" uuid="e11903a3-920d-48d0-b89c-80f2ee64d61c"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="521" y="39" width="30" height="15" uuid="cb3aa02c-7575-4b86-87c8-9000228b885f"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="428" y="11" width="58" height="14" uuid="ee7dd153-6548-48cc-b06c-cefec483ceb3"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Data:]]></text>
			</staticText>
			<staticText>
				<reportElement x="428" y="39" width="58" height="14" uuid="ed407f09-8cb1-4d5a-9121-132a6a2aad1b"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Página:]]></text>
			</staticText>
			<image hAlign="Center" vAlign="Middle">
				<reportElement x="-9" y="4" width="85" height="40" uuid="59c84930-55aa-4a7a-af69-2e386701470d"/>
				<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64(new String($F{logo}).getBytes("UTF-8")))]]></imageExpression>
			</image>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="6" y="75" width="542" height="16" uuid="6402bda8-77f9-4af2-9180-b51b332e81fc"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{filters}.isEmpty() ? null : $P{filters}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="86" y="62" width="339" height="21" uuid="7e73f7dc-2685-4299-b5ab-d4d53f2963a1">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Filtros Selecionados]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="86" y="2" width="339" height="30" uuid="10c97d4d-01dd-4674-bd38-c91df8d39112">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{companyName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="428" y="25" width="58" height="14" uuid="5caec54e-2731-4c4a-a32d-a4fac0113823"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Hora:]]></text>
			</staticText>
			<textField pattern="HH:mm">
				<reportElement x="491" y="24" width="60" height="15" uuid="f0d82f9d-3c9a-45b4-9db8-4ea0b4bd592e"/>
				<textElement textAlignment="Right">
					<font size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="12">
			<textField isBlankWhenNull="true">
				<reportElement x="187" y="2" width="32" height="10" uuid="496fa08f-**************-736abb20f72e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{numeroOS}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="7" y="2" width="40" height="10" uuid="fbf9e184-4c35-4355-a7d7-bc76e3d42999">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataAtendimento}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="220" y="2" width="112" height="10" uuid="f202a5a3-8e3f-4721-9593-ff883c238754">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomeServiço}]]></textFieldExpression>
			</textField>
			<textField pattern="HH:mm" isBlankWhenNull="true">
				<reportElement x="49" y="2" width="23" height="10" uuid="51e6a44e-bb38-4fc8-b68d-209caa76438b">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{horaLancamento}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="73" y="2" width="112" height="10" uuid="2a4c0409-3ea7-4739-99b0-61838c765254">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomePaciente}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="333" y="2" width="21" height="10" uuid="e152a78c-efcb-4fcd-82d5-dbbdeefd0cd5">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cid}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="355" y="2" width="92" height="10" uuid="3e0e1168-0e76-45c8-b16f-c11e7db61e3c">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomeConvenio}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="452" y="2" width="19" height="10" uuid="9a1cea73-8f81-4242-b92f-509be7a8d80d">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{qtd}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="475" y="2" width="21" height="10" uuid="70840917-38ff-4ac6-a992-cde0e711e6dc">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valorServico}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="530" y="2" width="30" height="10" uuid="e3e2098a-129b-4f5d-8afb-8b0c42f1787c">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valorMedico}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="501" y="2" width="24" height="10" uuid="fe23156d-abdf-4fec-a37c-7baec54937f4">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{baseCalculo}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="11">
			<textField pattern="#,##0.00">
				<reportElement x="429" y="1" width="30" height="10" uuid="93446add-524e-4c09-b970-082e5451b536">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{qtd3}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="461" y="1" width="30" height="10" uuid="3985bdbd-dc67-450e-b92f-cd4aae4ed595">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{valorServico3}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="496" y="1" width="30" height="10" uuid="b8e26083-b3ab-41f8-8733-7def93b08e53">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{baseCalculo3}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="530" y="1" width="30" height="10" uuid="e54de1cd-9275-4e36-8e98-a46a113dbf1b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font size="6"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{valorMedico3}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="333" y="0" width="90" height="11" uuid="e04e945b-88d4-46c2-be32-e9facd09adf9"/>
				<textElement>
					<font size="6" isBold="true"/>
				</textElement>
				<text><![CDATA[Total do geral:]]></text>
			</staticText>
		</band>
	</summary>
	<noData>
		<band height="79">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="555" height="79" uuid="1173d87a-4bb0-4b34-9c27-1f52cbd6e6e8"/>
				<textElement textAlignment="Center">
					<font size="26" isBold="true"/>
				</textElement>
				<text><![CDATA[Não há dados para os filtros selecionados.]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
