<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="application-report-order-attendance-pending" pageWidth="841" pageHeight="595" orientation="Landscape" whenNoDataType="NoDataSection" columnWidth="801" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4ff84453-1320-4ac5-a9ad-242cce5098f5">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="0"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="1000"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_demo"/>
	<subDataset name="Dataset1" uuid="7fe0c814-75e0-454d-bbae-8410466db122">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MySql "/>
		<queryString language="SQL">
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="filters" class="java.util.ArrayList"/>
	<parameter name="nameReport" class="java.lang.String"/>
	<parameter name="costCenter" class="java.util.ArrayList"/>
	<parameter name="performer" class="java.util.ArrayList"/>
	<parameter name="dataInicio" class="java.lang.String"/>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="user" class="java.util.ArrayList"/>
	<parameter name="costCenterStart" class="java.lang.String"/>
	<parameter name="costCenterEnd" class="java.lang.String"/>
	<parameter name="performerStart" class="java.lang.String"/>
	<parameter name="performerEnd" class="java.lang.String"/>
	<parameter name="performerRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "$X{IN,performer.performer_id, performer} " :
"person_performer.`name`  >= " +"$P" +"{performerStart}"+" AND person_performer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="costCenterRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{costCenterStart} == null || $P{costCenterEnd} == null ? "and $X{IN,cost_center.cost_center_id, costCenter} " :
"and cost_center.`name`  >= " +"$P" +"{costCenterStart}"+" AND cost_center.`name` <= " + "$P" + "{costCenterEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{userStart} == null || $P{userEnd} == null ? "and $X{IN,user_order.user_id, user} " :
"and person_user.`name`  >= " +"$P" +"{userStart}"+" AND person_user.`name` <= " + "$P" + "{userEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="userEnd" class="java.lang.String"/>
	<parameter name="userStart" class="java.lang.String"/>
	<parameter name="dateRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{dataInicio} == null || $P{dataFim} == null ? " " :
"AND `order`.`date` BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="firm" class="java.util.ArrayList"/>
	<parameter name="firmStart" class="java.lang.String"/>
	<parameter name="firmEnd" class="java.lang.String"/>
	<parameter name="firmRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{firmStart} == null || $P{firmEnd} == null ? "and  $X{IN,company.company_id, firm} " :
"and company.`name`  >= " +"$P" +"{firmStart}"+" AND company.`name` <= " + "$P" + "{firmEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="insurance" class="java.util.ArrayList"/>
	<parameter name="insuranceStart" class="java.lang.String"/>
	<parameter name="insuranceEnd" class="java.lang.String"/>
	<parameter name="insuranceRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{insuranceStart} == null || $P{insuranceEnd} == null ? "and  $X{IN,`insurance`.`insurance_id`, insurance} " :
"and `insurance`.`name`  >= " +"$P" +"{insuranceStart}"+" AND `insurance`.`name` <= " + "$P" + "{insuranceEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="onlyGerarateCash" class="java.util.ArrayList"/>
	<parameter name="notGerarateCash" class="java.util.ArrayList"/>
	<parameter name="onlyGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{onlyGerarateCash} ==  null ?  " " : "and  $X{IN,insurance.payment_model, onlyGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="notGerarateCashRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{notGerarateCash} ==  null ? " " : "and  $X{IN,insurance.payment_model, notGerarateCash} "]]></defaultValueExpression>
	</parameter>
	<parameter name="groupProcedure" class="java.util.ArrayList"/>
	<parameter name="groupProcedureStart" class="java.lang.String"/>
	<parameter name="groupProcedureEnd" class="java.lang.String"/>
	<parameter name="groupProcedureRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{groupProcedureStart} == null || $P{groupProcedureEnd} == null ? "and  $X{IN, `group`.group_id, groupProcedure} " :
"and `group`.`name`  >= " +"$P" +"{groupProcedureStart}"+" AND `group`.`name` <= " + "$P" + "{groupProcedureEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="patient" class="java.util.ArrayList"/>
	<parameter name="patientStart" class="java.lang.String"/>
	<parameter name="patientEnd" class="java.lang.String"/>
	<parameter name="patientRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{patientStart} == null || $P{patientEnd} == null ? "and $X{IN,patient.patient_id, patient} " :
"and person_patient.`name`  >= " +"$P" +"{patientStart}"+" AND person_patient.`name` <= " + "$P" + "{patientEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="procedureEnd" class="java.lang.String"/>
	<parameter name="procedureStart" class="java.lang.String"/>
	<parameter name="procedure" class="java.util.ArrayList"/>
	<parameter name="procedureRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{procedureStart} == null || $P{procedureEnd} == null ? "and $X{IN,`procedure`.procedure_id, procedure} " :
"and `procedure`.`name`  >= " +"$P" +"{procedureStart}"+" AND `procedure`.`name` <= " + "$P" + "{procedureEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="blocked" class="java.util.ArrayList"/>
	<parameter name="pending" class="java.util.ArrayList"/>
	<parameter name="blockedRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{blocked} == null ? " " : "and  $X{IN,`order`.state, blocked} "]]></defaultValueExpression>
	</parameter>
	<parameter name="pendingRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{pending} == null ?  " " : "and  $X{IN,`order`.state, pending} "]]></defaultValueExpression>
	</parameter>
	<parameter name="normal" class="java.util.ArrayList"/>
	<parameter name="normalRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{normal} == null ?  " " : "and  $X{IN,`order`.state, normal} "]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[select 
 person_patient.`name` 			as 'Paciente'    -- paciente 
,DATE_FORMAT(person_patient.birthday, '%d/%m/%Y') 	
								as 'Nascimento'  -- nascimento
,case when person_patient.cpf = '00000000515' then '*' else person_patient.cpf end 			as 'CPF Paciente'         -- cpf
,person_payer.`name` 			as 'Pago Por'    -- pago por
,person_payer.cpf				as 'CPF Pagador'
,pay_type.`name` 				as 'Tipo Pag'    -- tipo pag
,person_professional.`name` 	as 'Médico Atend'-- medico atendeu
,`order`.`number` 				as 'OS'          -- os
,order_payer.invoice 			as 'NF'          -- Nota fiscal
,DATE_FORMAT(`order`.date, '%d/%m/%Y') 		
								as 'Data'        -- data
,insurance.name                 as 'Convênio'
,ELT(MONTH(`order`.date), 'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro') 
								as 'Mês'
,MONTH(`order`.date) as 'numData' 								
,`order_pay`.value 					as 'Valor Total' -- valor total
,company.alias 					as 'Unidade'   	 -- unidade
from `order`
left join `order_payer` on `order`.order_id = order_payer.order_id
join `order_item` on order_item.order_id = `order`.order_id
left join `order_pay` on order_pay.order_id = `order`.order_id
join person as person_payer on person_payer.person_id = order_payer.person_id
join patient on patient.patient_id = `order`.patient_id
join person as person_patient on person_patient.person_id = patient.person_id
join performer on performer.performer_id = order_item.performer_id
join professional on professional.professional_id = performer.professional_id
join person as person_professional on person_professional.person_id = professional.person_id
left join `pay_type` on pay_type.pay_type_id = order_pay.pay_type_id
join insurance on insurance.insurance_id = `order`.insurance_id and insurance.payment_model = 'P' or insurance.payment_model = 'R'
join company_cost_center on company_cost_center.company_cost_center_id = `order`.company_cost_center_id
join company on company.company_id = company_cost_center.company_id
join cost_center on cost_center.cost_center_id = company_cost_center.cost_center_id
where 
$P!{performerRange}
 $P!{costCenterRange}
  $P!{firmRange}
 $P!{userRange}
 $P!{dateRange}
and exists(select 1 from `order` where order_payer.order_id = `order`.order_id)
and order_payer.invoice is not null
-- and `order`.`number` = 315068
order by 13
,11
,1
;]]>
	</queryString>
	<field name="Paciente" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="Paciente"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="Nascimento" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="Nascimento"/>
		<property name="com.jaspersoft.studio.field.label" value="Nascimento"/>
	</field>
	<field name="CPF Paciente" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="CPF Paciente"/>
		<property name="com.jaspersoft.studio.field.label" value="CPF Paciente"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="Pago Por" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="Pago Por"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="CPF Pagador" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="cpf"/>
		<property name="com.jaspersoft.studio.field.label" value="CPF Pagador"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="Tipo Pag" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="Tipo Pag"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="pay_type"/>
	</field>
	<field name="Médico Atend" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="Médico Atend"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="OS" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="number"/>
		<property name="com.jaspersoft.studio.field.label" value="OS"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order"/>
	</field>
	<field name="NF" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="invoice"/>
		<property name="com.jaspersoft.studio.field.label" value="NF"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_payer"/>
	</field>
	<field name="Data" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="Data"/>
		<property name="com.jaspersoft.studio.field.label" value="Data"/>
	</field>
	<field name="Convênio" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="Convênio"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="insurance"/>
	</field>
	<field name="Mês" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="Mês"/>
		<property name="com.jaspersoft.studio.field.label" value="Mês"/>
	</field>
	<field name="numData" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="numData"/>
		<property name="com.jaspersoft.studio.field.label" value="numData"/>
	</field>
	<field name="Valor Total" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="value"/>
		<property name="com.jaspersoft.studio.field.label" value="Valor Total"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="order_pay"/>
	</field>
	<field name="Unidade" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="alias"/>
		<property name="com.jaspersoft.studio.field.label" value="Unidade"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="company"/>
	</field>
	<variable name="Valor Total1" class="java.lang.Double" resetType="Group" resetGroup="insurance_group" calculation="Sum">
		<variableExpression><![CDATA[$F{Valor Total}]]></variableExpression>
	</variable>
	<variable name="Valor Total2" class="java.lang.Double" resetType="Group" resetGroup="month_group" calculation="Sum">
		<variableExpression><![CDATA[$F{Valor Total}]]></variableExpression>
	</variable>
	<variable name="Valor Total3" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{Valor Total}]]></variableExpression>
	</variable>
	<group name="month_group">
		<groupExpression><![CDATA[$F{numData}]]></groupExpression>
		<groupHeader>
			<band height="13">
				<staticText>
					<reportElement x="1" y="0" width="30" height="13" uuid="8988931c-c90f-438b-aca8-45b38112fa27">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Mês:]]></text>
				</staticText>
				<textField>
					<reportElement x="31" y="0" width="98" height="13" uuid="d8468367-2781-4d8c-87bb-d6d75a648733"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Mês}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<staticText>
					<reportElement x="672" y="0" width="75" height="12" uuid="00f636c7-3f63-4a7d-ab69-73a17f830cb7">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Total do mês:]]></text>
				</staticText>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="747" y="0" width="54" height="12" uuid="038ce5bb-735f-400e-81a4-167f098d26c0"/>
					<textElement textAlignment="Right">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{Valor Total2}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="insurance_group">
		<groupExpression><![CDATA[$F{Convênio}]]></groupExpression>
		<groupHeader>
			<band height="13">
				<staticText>
					<reportElement x="1" y="0" width="48" height="13" uuid="bdeaf1b2-5f2c-465b-bde6-7650c776ca42"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Convênio:]]></text>
				</staticText>
				<textField>
					<reportElement x="49" y="0" width="230" height="13" uuid="ec3b1733-e471-4f4d-863f-deb51e488465"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{Convênio}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="14">
				<staticText>
					<reportElement x="672" y="2" width="75" height="12" uuid="d5b19a97-04ab-478d-be48-f2c38300fabd">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<textElement>
						<font size="8" isBold="true"/>
					</textElement>
					<text><![CDATA[Total do convênio:]]></text>
				</staticText>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="747" y="2" width="54" height="12" uuid="86c4abed-4c2c-461f-93fd-bfe12f6f4d6f"/>
					<textElement textAlignment="Right">
						<font size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{Valor Total1}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<pageHeader>
		<band height="73">
			<textField isBlankWhenNull="true">
				<reportElement x="111" y="31" width="541" height="25" uuid="321f1f63-b46c-439c-a430-8165e08bf9f1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Relação Dmed"]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy">
				<reportElement x="751" y="0" width="50" height="12" uuid="e11903a3-920d-48d0-b89c-80f2ee64d61c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="771" y="24" width="30" height="12" uuid="cb3aa02c-7575-4b86-87c8-9000228b885f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="695" y="0" width="56" height="12" uuid="ee7dd153-6548-48cc-b06c-cefec483ceb3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Data:]]></text>
			</staticText>
			<staticText>
				<reportElement x="695" y="24" width="56" height="12" uuid="ed407f09-8cb1-4d5a-9121-132a6a2aad1b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Página:]]></text>
			</staticText>
			<staticText>
				<reportElement x="293" y="56" width="181" height="17" uuid="7e73f7dc-2685-4299-b5ab-d4d53f2963a1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Filtros Selecionados]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="111" y="0" width="541" height="30" uuid="10c97d4d-01dd-4674-bd38-c91df8d39112"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="18" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Unidade}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="695" y="12" width="56" height="12" uuid="5caec54e-2731-4c4a-a32d-a4fac0113823">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Hora:]]></text>
			</staticText>
			<textField pattern="HH:mm">
				<reportElement x="751" y="12" width="50" height="12" uuid="f0d82f9d-3c9a-45b4-9db8-4ea0b4bd592e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="28">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<line>
				<reportElement x="1" y="0" width="800" height="1" uuid="e16b8545-e76d-4347-874c-855e7d702e48">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="1" y="27" width="800" height="1" uuid="d0a1bd55-ebfc-4287-b45c-ff8bc633d963">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="1" y="1" width="38" height="13" uuid="b759cf51-508c-446d-81c8-f0bda43a3141">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Unidade:]]></text>
			</staticText>
			<textField>
				<reportElement x="39" y="1" width="240" height="13" uuid="25a9a7c0-9872-41d4-8302-9bf4810bfdb4">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Unidade}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="15" width="110" height="12" uuid="4a4de97d-07c1-40bf-a3af-e80e5fae7a2d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Paciente]]></text>
			</staticText>
			<staticText>
				<reportElement x="114" y="15" width="52" height="12" uuid="96bcd8c8-3d6d-4861-ab5a-73708a61e9a4">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Nascimento]]></text>
			</staticText>
			<staticText>
				<reportElement x="167" y="15" width="53" height="12" uuid="dd19f6fe-5876-478e-95e7-020e51b4ed58">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[CPF]]></text>
			</staticText>
			<staticText>
				<reportElement x="227" y="15" width="100" height="12" uuid="92a14d1d-07a9-4174-a624-aa8ab54c4cdb">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Pago por]]></text>
			</staticText>
			<staticText>
				<reportElement x="333" y="15" width="76" height="12" uuid="c62bc621-e74d-4ac0-9c28-1fda775b4543">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Tipo pag.]]></text>
			</staticText>
			<staticText>
				<reportElement x="418" y="15" width="53" height="12" uuid="3914135b-a6fc-400a-b950-884bd39f3a67">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[CPF pagador]]></text>
			</staticText>
			<staticText>
				<reportElement x="478" y="15" width="112" height="12" uuid="5e9cf537-132f-4ce4-adbd-c979bc250e50">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Profissional]]></text>
			</staticText>
			<staticText>
				<reportElement x="594" y="15" width="48" height="12" uuid="7c6ed335-ac4f-4aa1-aed8-7e9d494b0af6">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[OS]]></text>
			</staticText>
			<staticText>
				<reportElement x="645" y="15" width="62" height="12" uuid="2a6afb17-3f13-45fd-b9ac-94025745f8fe">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[NF]]></text>
			</staticText>
			<staticText>
				<reportElement x="707" y="15" width="50" height="12" uuid="ed93befc-7b61-4ee8-813d-2b660497f8f5">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Data]]></text>
			</staticText>
			<staticText>
				<reportElement x="760" y="15" width="41" height="12" uuid="d757c002-5063-475b-a31d-574d21e4d18b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Vl. Total]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="12">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField isBlankWhenNull="true">
				<reportElement x="1" y="0" width="110" height="12" uuid="2fb8744d-fb13-4085-a456-815a14cd6122">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="ab01b13a-2c6f-4967-8fc5-1a5967df81ab"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Paciente}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="114" y="0" width="52" height="12" uuid="f0a6759a-d93a-401b-837a-f811a314ec91">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="78c1933f-5225-4d8d-b522-6e7c8def12e1"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Nascimento}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="167" y="0" width="53" height="12" uuid="85544d85-ec56-473a-afc2-40852e0b1ea7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="403f39e7-b93a-4436-aff5-b65699929b99"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CPF Paciente}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="227" y="0" width="100" height="12" uuid="bcada0f4-5e32-454c-8bb0-392afc5de575">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="104d98c8-c9e3-4943-bef1-58a5e5d9d9dc"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Pago Por}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="333" y="0" width="76" height="12" uuid="c1113315-f422-437c-b24e-70836151899a">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="ee3540d3-1409-403d-ae03-04ec3f202c5c"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Tipo Pag}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="418" y="0" width="53" height="12" uuid="f39ca2c7-61e0-4073-bd09-958fcad98a21">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="8d289b6b-4ced-460b-ad84-a82e907d381b"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CPF Pagador}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="478" y="0" width="112" height="12" uuid="065c0530-c9dd-435e-8273-93b130c2d6e0">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="df5c6861-a10f-44f8-89ff-baf404b23f55"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Médico Atend}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="594" y="0" width="48" height="12" uuid="8bd06619-87be-47a1-b389-d58984e80682">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="cfbf48be-22a8-4818-8bc0-da9ef2afd371"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{OS}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="645" y="0" width="62" height="12" uuid="f4b97b67-c128-4854-ad06-ced8d93cb998">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="76f9612c-6f28-4b17-96a5-506ab72be12b"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NF}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="707" y="0" width="50" height="12" uuid="3ebd2276-3620-4e77-ace4-2521762cb4d4">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9d05f59c-6a5e-4381-b2fe-915c868ca0c2"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Data}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="760" y="0" width="41" height="12" uuid="56c77c8d-7e87-46db-9c2d-b051008b3604">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="79763fb2-2583-402a-bcbe-99e904585754"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Valor Total}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="120">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="664" y="3" width="83" height="12" uuid="325b6183-dcac-46c5-9051-c7f8c1e2a82e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Total Geral:]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="1" width="800" height="1" uuid="49f63265-1473-42c6-adae-e98b528e7b02">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="747" y="3" width="52" height="12" uuid="6a017df1-0671-4ec2-872c-cb08e4ea8058"/>
				<textElement textAlignment="Right">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{Valor Total3}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement x="0" y="0" width="200" height="120" uuid="db1cf4eb-6728-4cf1-adac-425c75fc76e6"/>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["reports/operational/application-report-operational-relationship-dmed/totalizadorDMED.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</summary>
	<noData>
		<band height="79">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="801" height="79" uuid="f54daa93-7c74-49ca-b93b-86d091ab637a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="26" isBold="true"/>
				</textElement>
				<text><![CDATA[Não há dados para os filtros selecionados.]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
