<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="application-report-order-attendance-pending" pageWidth="595" pageHeight="841" whenNoDataType="NoDataSection" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4ff84453-1320-4ac5-a9ad-242cce5098f5">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="176"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="817"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="cw_life"/>
	<subDataset name="Dataset1" uuid="7fe0c814-75e0-454d-bbae-8410466db122">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MySql "/>
		<queryString language="SQL">
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="filters" class="java.util.ArrayList"/>
	<parameter name="nameReport" class="java.lang.String"/>
	<parameter name="patient" class="java.util.ArrayList"/>
	<parameter name="patientStart" class="java.lang.String"/>
	<parameter name="patientEnd" class="java.lang.String"/>
	<parameter name="insuranceStart" class="java.lang.String"/>
	<parameter name="insuranceEnd" class="java.lang.String"/>
	<parameter name="insuranceRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{insuranceStart} == null || $P{insuranceEnd} == null ? "and  $X{IN,`insurance`.`insurance_id`, insurance} " :
"and `insurance`.`name`  >= " +"$P" +"{insuranceStart}"+" AND `insurance`.`name` <= " + "$P" + "{insuranceEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="patientRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{patientStart} == null || $P{patientEnd} == null ? " $X{IN,patient.patient_id, patient} " :
" p1.`name`  >= " +"$P" +"{patientStart}"+" AND p1.`name` <= " + "$P" + "{patientEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="dataInicio" class="java.lang.String"/>
	<parameter name="dataFim" class="java.lang.String"/>
	<parameter name="dateRange" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{dataInicio} == null || $P{dataFim} == null ? " " :
" AND patient.`dated` BETWEEN " +"$P" +"{dataInicio}"+" AND $P" + "{dataFim}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="insurance" class="java.util.ArrayList"/>
	<parameter name="performer" class="java.util.ArrayList"/>
	<parameter name="performerStart" class="java.lang.String"/>
	<parameter name="performerEnd" class="java.lang.String"/>
	<parameter name="performerRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{performerStart} == null || $P{performerEnd} == null ? "$X{IN,performer.performer_id, performer} " :
"person_performer.`name`  >= " +"$P" +"{performerStart}"+" AND person_performer.`name` <= " + "$P" + "{performerEnd}" + " "]]></defaultValueExpression>
	</parameter>
	<parameter name="patientActiveCondition" class="java.lang.String">
		<defaultValueExpression><![CDATA[$P{patientActive} == true ? "  patient.active = true" :  "1=1"]]></defaultValueExpression>
	</parameter>
	<parameter name="patientActive" class="java.lang.Boolean"/>
	<queryString>
		<![CDATA[select 
(select company.name from company where company_id = 1) as 'company',
patient.patient_id,
 p1.`name` as Paciente,
 p1.`birthday` as `Data Aniversario`,
 patient.medical_record as Matrícula,
 CONCAT(
        FLOOR(DATEDIFF(CURDATE(), p1.birthday) / 365.25),
        ' anos e ',
        FLOOR((DATEDIFF(CURDATE(), p1.birthday) % 365.25) / 30.44),
        ' meses'
    ) AS `paciente.idade`
,coalesce(contact_list.email, 'Não cadastrado')  as 'email',
coalesce(phone.phone, 'Não cadastrado')  as 'phone'
from
 patient 
LEFT join person p1 on p1.person_id = `patient`.person_id
LEFT join insurance on `patient`.insurance_id = `insurance`.insurance_id
LEFT join contact_list on contact_list.parent_id = p1.contact_list_id and contact_list.type = "M"
LEFT join contact_list phone on phone.parent_id = p1.contact_list_id and phone.type = "S"
left join `parameter` on `parameter`.`name` = 'PERFORMER_PATIENT'
left join `parameter_value` on `parameter_value`.`parameter_id` = `parameter`.`parameter_id` and parameter_value.`key` = patient_id
left join `professional` on `parameter_value`.`value` = `professional`.`professional_id`
left join performer on performer.professional_id = professional.professional_id
left join person p2 on p2.person_id = `professional`.person_id

where 



$P!{patientRange}
$P!{insuranceRange}
$P!{dateRange}
AND $P!{patientActiveCondition}


ORDER BY
    p1.`name`;]]>
	</queryString>
	<field name="company" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="company"/>
		<property name="com.jaspersoft.studio.field.label" value="company"/>
	</field>
	<field name="patient_id" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.name" value="patient_id"/>
		<property name="com.jaspersoft.studio.field.label" value="patient_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="patient"/>
		<fieldDescription><![CDATA[Id do Paciente]]></fieldDescription>
	</field>
	<field name="Paciente" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="Paciente"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="Data Aniversario" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.name" value="birthday"/>
		<property name="com.jaspersoft.studio.field.label" value="Data Aniversario"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="person"/>
	</field>
	<field name="Matrícula" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="medical_record"/>
		<property name="com.jaspersoft.studio.field.label" value="Matrícula"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="patient"/>
	</field>
	<field name="paciente.idade" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="paciente.idade"/>
		<property name="com.jaspersoft.studio.field.label" value="paciente.idade"/>
	</field>
	<field name="email" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="email"/>
		<property name="com.jaspersoft.studio.field.label" value="email"/>
	</field>
	<field name="phone" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="phone"/>
		<property name="com.jaspersoft.studio.field.label" value="phone"/>
	</field>
	<variable name="patient_id1" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{patient_id}]]></variableExpression>
	</variable>
	<pageHeader>
		<band height="91">
			<textField isBlankWhenNull="true">
				<reportElement x="180" y="30" width="200" height="26" uuid="321f1f63-b46c-439c-a430-8165e08bf9f1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="15" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{nameReport}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy">
				<reportElement x="491" y="30" width="60" height="15" uuid="e11903a3-920d-48d0-b89c-80f2ee64d61c"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="521" y="58" width="30" height="15" uuid="cb3aa02c-7575-4b86-87c8-9000228b885f"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="410" y="30" width="81" height="14" uuid="ee7dd153-6548-48cc-b06c-cefec483ceb3"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Data:]]></text>
			</staticText>
			<staticText>
				<reportElement x="410" y="58" width="81" height="14" uuid="ed407f09-8cb1-4d5a-9121-132a6a2aad1b"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Página:]]></text>
			</staticText>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="6" y="71" width="553" height="20" uuid="6402bda8-77f9-4af2-9180-b51b332e81fc"/>
				<textElement>
					<font fontName="Arial" size="8" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{filters}.isEmpty() ? null : $P{filters}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="200" y="56" width="150" height="15" uuid="7e73f7dc-2685-4299-b5ab-d4d53f2963a1"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Filtros Selecionados]]></text>
			</staticText>
			<staticText>
				<reportElement x="410" y="44" width="81" height="14" uuid="5caec54e-2731-4c4a-a32d-a4fac0113823"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Hora:]]></text>
			</staticText>
			<textField pattern="HH:mm">
				<reportElement x="491" y="43" width="60" height="15" uuid="f0d82f9d-3c9a-45b4-9db8-4ea0b4bd592e"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="80" y="0" width="400" height="30" uuid="98929365-4f78-4a45-bbb4-8d4861b01c09"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="20" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{company}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="27">
			<line>
				<reportElement x="-3" y="8" width="555" height="1" uuid="e16b8545-e76d-4347-874c-855e7d702e48">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="3" y="10" width="110" height="12" uuid="f3accf36-8a65-4461-9b7a-3fb60c6ef8df">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Nome ]]></text>
			</staticText>
			<staticText>
				<reportElement x="260" y="11" width="82" height="11" uuid="c2872f2f-1aa4-4e05-943d-475a5ed2ebcd">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Idade]]></text>
			</staticText>
			<staticText>
				<reportElement x="116" y="10" width="57" height="12" uuid="28aa540f-863a-4a57-8932-052d7ac07090">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Nascimento]]></text>
			</staticText>
			<staticText>
				<reportElement x="175" y="10" width="83" height="12" uuid="3b7e5e53-d269-4b24-ae3c-85e3f47d90c5">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Matrícula]]></text>
			</staticText>
			<line>
				<reportElement x="-3" y="24" width="555" height="1" uuid="196463f0-8985-4a39-a278-15e699785281">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="344" y="11" width="121" height="11" uuid="d70237ee-38c3-4616-9a93-7f7fad1fde94">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="e9b0a0bf-87e7-4691-80f0-980196ce3596"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Email]]></text>
			</staticText>
			<staticText>
				<reportElement x="467" y="11" width="81" height="11" uuid="30461c85-13f0-4912-b191-1887f0047fb7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="e9b0a0bf-87e7-4691-80f0-980196ce3596"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Celular/Tel]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="13">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField isBlankWhenNull="true">
				<reportElement x="3" y="2" width="110" height="11" uuid="a3b6f38f-f646-41b3-8040-5774c6fb964a">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="false"/>
					<paragraph lineSpacingSize="1.0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Paciente}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="260" y="2" width="82" height="11" uuid="94edb9e2-15c5-4ecd-a66d-7719200970c6"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{paciente.idade}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="116" y="1" width="57" height="12" uuid="03393d29-c202-4503-8a46-542a067e9a11"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Data Aniversario}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="175" y="1" width="83" height="12" uuid="f958d71e-08dc-4ce0-a7ce-0bd35108eddd"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Matrícula}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="344" y="2" width="121" height="11" uuid="b1f30c8d-ba85-4a78-91c7-c1d47ec1ae2c">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4dc32438-f2da-4a43-b53f-14a4f1aec087"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{email}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="467" y="2" width="81" height="11" uuid="c83fe69a-b77e-4d98-87a4-0125dc9ca707">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4dc32438-f2da-4a43-b53f-14a4f1aec087"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{phone}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="15">
			<staticText>
				<reportElement x="20" y="3" width="30" height="12" uuid="325b6183-dcac-46c5-9051-c7f8c1e2a82e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="7" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Total:]]></text>
			</staticText>
			<line>
				<reportElement x="-3" y="0" width="555" height="1" uuid="49f63265-1473-42c6-adae-e98b528e7b02">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<textField>
				<reportElement x="52" y="3" width="28" height="12" uuid="eee0c2d0-ab66-4d9c-b613-80f291a2983b">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{patient_id1}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
	<noData>
		<band height="79">
			<staticText>
				<reportElement mode="Opaque" x="7" y="0" width="542" height="79" uuid="f54daa93-7c74-49ca-b93b-86d091ab637a"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="26" isBold="true"/>
				</textElement>
				<text><![CDATA[Não há dados para os filtros selecionados.]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
