<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.byjg</groupId>
	<artifactId>service-smscep</artifactId>
	<version>1.0-SNAPSHOT</version>
	<name>Service SMSCE</name>
	<packaging>jar</packaging>
	
	
	<parent>
		<groupId>br.com.focusts</groupId>
		<artifactId>clinicall</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<licenses>
		<license>
			<name>Apache License, Version 2.0</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
			<distribution>repo</distribution>
		</license>
	</licenses>

	<scm>
		<url>https://github.com/byjg/sms-cep-service-java</url>
	</scm>

	<developers>
		<developer>
			<name>Joao Gilberto Magalhaes</name>
			<organization>ByJG</organization>
			<organizationUrl>https://github.com/byjg</organizationUrl>
		</developer>
	</developers>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<archive>
						<manifest>
							<addClasspath>true</addClasspath>
							<classpathPrefix>lib/</classpathPrefix>
							<mainClass>com.byjg.services.Run</mainClass>
						</manifest>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<file>${project.basedir}/target/${project.artifactId}-${project.version}.jar</file>
					<repositoryId>remote-repository</repositoryId>
					<url>https://oss.sonatype.org/content/repositories/snapshots//</url>
					<groupId>${project.groupId}</groupId>
					<artifactId>${project.artifactId}</artifactId>
					<version>${project.version}</version>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>