<?xml version="1.0" encoding="ISO-8859-1"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema" xmlns:ans="http://www.ans.gov.br/padroes/tiss/schemas" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" targetNamespace="http://www.ans.gov.br/padroes/tiss/schemas" elementFormDefault="qualified">
	<!--VERSÃ?O TISS 4.00.00 - Mensagens do PadrÃ£o TISS-->
	<import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
	<include schemaLocation="tissAssinaturaDigital_v1.01.xsd"/>
	<!-- Estrutura da mensagem do TISS -->
	<element name="mensagemTISS">
		<complexType>
			<sequence>
				<element name="cabecalho" type="ans:cabecalhoTransacao"/>
				<choice>
					<element name="operadoraParaPrestador" type="ans:operadoraPrestador" minOccurs="0"/>
					<element name="prestadorParaOperadora" type="ans:prestadorOperadora" minOccurs="0"/>
				</choice>
				<element name="epilogo" type="ans:epilogo"/>
				<element ref="ds:Signature" minOccurs="0"/>
			</sequence>
		</complexType>
	</element>
	<!-- Definicao do cabecalho das mensagens -->
	<complexType name="cabecalhoTransacao">
		<sequence>
			<element name="identificacaoTransacao">
				<complexType>
					<sequence>
						<element name="tipoTransacao" type="ans:dm_tipoTransacao"/>
						<element name="sequencialTransacao" type="ans:st_texto12"/>
						<element name="dataRegistroTransacao" type="ans:st_data"/>
						<element name="horaRegistroTransacao" type="ans:st_hora"/>
					</sequence>
				</complexType>
			</element>
			<element name="falhaNegocio" type="ans:dm_tipoGlosa" minOccurs="0"/>
			<element name="origem">
				<complexType>
					<choice>
						<!-- origem de mensagens de prestadores para as operadoras -->
						<element name="identificacaoPrestador">
							<complexType>
								<complexContent>
									<extension base="ans:ct_prestadorIdentificacao"/>
								</complexContent>
							</complexType>
						</element>
						<!-- origem de mensagens de operadoras para os prestadores -->
						<element name="registroANS" type="ans:st_registroANS"/>
					</choice>
				</complexType>
			</element>
			<element name="destino">
				<complexType>
					<choice>
						<!--  destino de mensagens de operadoras para prestadores: informar cÃ³digo prestador na operadora -->
						<element name="identificacaoPrestador" type="ans:ct_prestadorIdentificacao"/>
						<!-- destino de mensagens de prestadores para as operadoras: informar registro ANS da operadora -->
						<element name="registroANS" type="ans:st_registroANS"/>
					</choice>
				</complexType>
			</element>
			<element name="Padrao" type="ans:dm_versao"/>
			<element name="loginSenhaPrestador" type="ans:ct_loginSenha" minOccurs="0"/>
		</sequence>
	</complexType>
	<!-- ************************************************ Mensagens da OPERADORA para o PRESTADOR ************** -->
	<complexType name="operadoraPrestador">
		<choice>
			<element name="recebimentoLote" type="ans:ct_recebimentoLote" minOccurs="0"/>
			<element name="recebimentoAnexo" minOccurs="0">
				<complexType>
					<choice>
						<element name="mensagemErro" type="ans:ct_motivoGlosa"/>
						<element name="protocoloRecebimentoAnexo" type="ans:ct_anexoRecebimento"/>
					</choice>
				</complexType>
			</element>
			<element name="recebimentoRecursoGlosa" type="ans:ct_recebimentoRecurso" minOccurs="0"/>
			<element name="demonstrativosRetorno" type="ans:ct_demonstrativoRetorno" minOccurs="0"/>
			<element name="situacaoProtocolo" type="ans:ct_situacaoProtocolo" minOccurs="0"/>
			<!-- Estrutura para responder a solicitaÃ§Ã£o de procedimento (ctm_solicitcaoLote) -->
			<element name="autorizacaoServicos" type="ans:ct_situacaoAutorizacao" minOccurs="0"/>
			<element name="situacaoAutorizacao" type="ans:ct_situacaoAutorizacao" minOccurs="0"/>
			<element name="respostaElegibilidade" minOccurs="0">
				<complexType>
					<choice>
						<element name="codigoGlosa" type="ans:dm_tipoGlosa"/>
						<element name="reciboElegibilidade" type="ans:ct_elegibilidadeRecibo"/>
					</choice>
				</complexType>
			</element>
			<element name="reciboCancelaGuia" type="ans:ct_reciboCancelaGuia" minOccurs="0"/>
			<element name="reciboComunicacao" type="ans:ct_reciboComunicacao" minOccurs="0"/>
			<element name="respostaRecursoGlosa" type="ans:ct_respostaGlosa" minOccurs="0"/>
			<!-- incluido na versÃ£o 4.00.00 -->
			<element name="recebimentoDocumentos" minOccurs="0">
				<complexType>
					<choice>
						<element name="mensagemErro" type="ans:ct_motivoGlosa"/>
						<element name="reciboDocumento" type="ans:ct_reciboDocumentos"/>
					</choice>
				</complexType>
			</element>
		</choice>
	</complexType>
	<!-- ************************************************ Mensagens da PRESTADOR para a OPERADORA ************** -->
	<complexType name="prestadorOperadora">
		<choice>
			<element name="loteGuias" type="ans:ctm_guiaLote" minOccurs="0"/>
			<element name="loteAnexos" type="ans:ct_anexoLote" minOccurs="0"/>
			<element name="solicitacaoDemonstrativoRetorno" type="ans:ct_demonstrativoSolicitacao" minOccurs="0"/>
			<element name="solicitacaoStatusProtocolo" type="ans:ct_protocoloSolicitacaoStatus" minOccurs="0"/>
			<element name="solicitacaoProcedimento" type="ans:ct_solicitacaoProcedimento" minOccurs="0"/>
			<element name="solicitaStatusAutorizacao" type="ans:ct_autorizacaoSolicitaStatus" minOccurs="0"/>
			<element name="verificaElegibilidade" type="ans:ct_elegibilidadeVerifica" minOccurs="0"/>
			<element name="cancelaGuia" type="ans:ct_guiaCancelamento" minOccurs="0"/>
			<element name="comunicacaoInternacao" type="ans:ctm_beneficiarioComunicacao" minOccurs="0"/>
			<element name="recursoGlosa" type="ans:ct_guiaRecursoLote" minOccurs="0"/>
			<element name="solicitacaoStatusRecursoGlosa" type="ans:ct_protocoloSolicitacaoStatus" minOccurs="0"/>
			<!-- incluido na versÃ£o 4.00.00 -->
			<element name="envioDocumentos" type="ans:ct_envioDocumentos" minOccurs="0"/>
		</choice>
	</complexType>
	<complexType name="epilogo">
		<sequence>
			<element name="hash" type="string"/>
		</sequence>
	</complexType>
	<!--VERSÃ?O TISS 4.00.00 - TissSimpleTypesV4_00_00-->
	<!-- Schema com os tipos simples de dados utilizados na construÃ§Ã£o dos tipos complexos -->
	<simpleType name="dm_caraterAtendimento">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_caraterMonitoramento">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="E"/>
			<enumeration value="U"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_CBOS">
		<restriction base="string">
			<enumeration value="201115"/>
			<enumeration value="203015"/>
			<enumeration value="213150"/>
			<enumeration value="225105"/>
			<enumeration value="225110"/>
			<enumeration value="225148"/>
			<enumeration value="225185"/>
			<enumeration value="225115"/>
			<enumeration value="225120"/>
			<enumeration value="225210"/>
			<enumeration value="225215"/>
			<enumeration value="225220"/>
			<enumeration value="225225"/>
			<enumeration value="225230"/>
			<enumeration value="225235"/>
			<enumeration value="225240"/>
			<enumeration value="225305"/>
			<enumeration value="225350"/>
			<enumeration value="225125"/>
			<enumeration value="225130"/>
			<enumeration value="225135"/>
			<enumeration value="225140"/>
			<enumeration value="225310"/>
			<enumeration value="225145"/>
			<enumeration value="225150"/>
			<enumeration value="225315"/>
			<enumeration value="225320"/>
			<enumeration value="225155"/>
			<enumeration value="225160"/>
			<enumeration value="225245"/>
			<enumeration value="225165"/>
			<enumeration value="225170"/>
			<enumeration value="225175"/>
			<enumeration value="225180"/>
			<enumeration value="225250"/>
			<enumeration value="225190"/>
			<enumeration value="225195"/>
			<enumeration value="225103"/>
			<enumeration value="225106"/>
			<enumeration value="225255"/>
			<enumeration value="225109"/>
			<enumeration value="225260"/>
			<enumeration value="225112"/>
			<enumeration value="225118"/>
			<enumeration value="225265"/>
			<enumeration value="225121"/>
			<enumeration value="225270"/>
			<enumeration value="225275"/>
			<enumeration value="225325"/>
			<enumeration value="225124"/>
			<enumeration value="225127"/>
			<enumeration value="225280"/>
			<enumeration value="225133"/>
			<enumeration value="225330"/>
			<enumeration value="225136"/>
			<enumeration value="225139"/>
			<enumeration value="225285"/>
			<enumeration value="223204"/>
			<enumeration value="223208"/>
			<enumeration value="223212"/>
			<enumeration value="223216"/>
			<enumeration value="223220"/>
			<enumeration value="223224"/>
			<enumeration value="223228"/>
			<enumeration value="223232"/>
			<enumeration value="223236"/>
			<enumeration value="223240"/>
			<enumeration value="223244"/>
			<enumeration value="223248"/>
			<enumeration value="223252"/>
			<enumeration value="223256"/>
			<enumeration value="223260"/>
			<enumeration value="223264"/>
			<enumeration value="223268"/>
			<enumeration value="223272"/>
			<enumeration value="223505"/>
			<enumeration value="223605"/>
			<enumeration value="223910"/>
			<enumeration value="223905"/>
			<enumeration value="223710"/>
			<enumeration value="223810"/>
			<enumeration value="239425"/>
			<enumeration value="251510"/>
			<enumeration value="251545"/>
			<enumeration value="251550"/>
			<enumeration value="251605"/>
			<enumeration value="322205"/>
			<enumeration value="322220"/>
			<enumeration value="322225"/>
			<enumeration value="322230"/>
			<enumeration value="516210"/>
			<enumeration value="225121"/>
			<enumeration value="225325"/>
			<enumeration value="223276"/>
			<enumeration value="223280"/>
			<enumeration value="223284"/>
			<enumeration value="223288"/>
			<enumeration value="223293"/>
			<enumeration value="225122"/>
			<enumeration value="225142"/>
			<enumeration value="225151"/>
			<enumeration value="225203"/>
			<enumeration value="225290"/>
			<enumeration value="225295"/>
			<enumeration value="225335"/>
			<enumeration value="225340"/>
			<enumeration value="225345"/>
			<enumeration value="223625"/>
			<enumeration value="223630"/>
			<enumeration value="223635"/>
			<enumeration value="223640"/>
			<enumeration value="223645"/>
			<enumeration value="223650"/>
			<enumeration value="223655"/>
			<enumeration value="223660"/>
			<enumeration value="223435"/>
			<enumeration value="223430"/>
			<enumeration value="223415"/>
			<enumeration value="223405"/>
			<enumeration value="223420"/>
			<enumeration value="223440"/>
			<enumeration value="223445"/>
			<enumeration value="223425"/>
			<enumeration value="223565"/>
			<enumeration value="223560"/>
			<enumeration value="223555"/>
			<enumeration value="223550"/>
			<enumeration value="223545"/>
			<enumeration value="223540"/>
			<enumeration value="223535"/>
			<enumeration value="223530"/>
			<enumeration value="223525"/>
			<enumeration value="223520"/>
			<enumeration value="223515"/>
			<enumeration value="223510"/>
			<enumeration value="223570"/>
			<enumeration value="223705"/>
			<enumeration value="223845"/>
			<enumeration value="223830"/>
			<enumeration value="223825"/>
			<enumeration value="223835"/>
			<enumeration value="223840"/>
			<enumeration value="223820"/>
			<enumeration value="223815"/>
			<enumeration value="225154"/>
			<enumeration value="221105"/>
			<enumeration value="221205"/>
			<enumeration value="224105"/>
			<enumeration value="224110"/>
			<enumeration value="224115"/>
			<enumeration value="224120"/>
			<enumeration value="224125"/>
			<enumeration value="224130"/>
			<enumeration value="224135"/>
			<enumeration value="226305"/>
			<enumeration value="999999"/>
		</restriction>
		<!--		
201115	Geneticista
203015	Pesquisador em biologia de microorganismos e parasitas 
213150	FÃ­sico mÃ©dico 
221105	BiÃ³logo 
225105	MÃ©dico acupunturista 
225110	MÃ©dico alergista e imunologista
225148	MÃ©dico anatomopatologista
225115	MÃ©dico angiologista 
225120	MÃ©dico cardiologista 
225210	MÃ©dico cirurgiÃ£o cardiovascular 
225215	MÃ©dico cirurgiÃ£o de cabeÃ§a e pescoÃ§o 
225220	MÃ©dico cirurgiÃ£o do aparelho digestivo 
225225	MÃ©dico cirurgiÃ£o geral 
225230	MÃ©dico cirurgiÃ£o pediÃ¡trico 
225235	MÃ©dico cirurgiÃ£o plÃ¡stico 
225240	MÃ©dico cirurgiÃ£o torÃ¡cico 
225305	MÃ©dico citopatologista 
225125	MÃ©dico clÃ­nico
225130	MÃ©dico de famÃ­lia e comunidade
225135	MÃ©dico dermatologista 
225140	MÃ©dico do trabalho 
225310	MÃ©dico em endoscopia 
225145	MÃ©dico em medicina de trÃ¡fego 
225150	MÃ©dico em medicina intensiva 
225315	MÃ©dico em medicina nuclear 
225320	MÃ©dico em radiologia e diagnÃ³stico por imagem 
225155	MÃ©dico endocrinologista e metabologista 
225160	MÃ©dico fisiatra 
225245	MÃ©dico foniatra 
225165	MÃ©dico gastroenterologista 
225170	MÃ©dico generalista 
225175	MÃ©dico geneticista 
225180	MÃ©dico geriatra 
225250	MÃ©dico ginecologista e obstetra 
225185	MÃ©dico Hematologista
225190	MÃ©dico Hemoterapeuta 
225195	MÃ©dico Homeopata
225103	MÃ©dico infectologista 
225106	MÃ©dico legista
225255	MÃ©dico Mastologista
225109	MÃ©dico Nefrologista
225260	MÃ©dico  neurocirurgiÃ£o
225350	MÃ©dico neurofisiologista 
225112	MÃ©dico neurologista 
225118	MÃ©dico nutrologista 
225265	MÃ©dico oftalmologista
225121	MÃ©dico oncologista
225270	MÃ©dico ortopedista e traumatologista 
225275	MÃ©dico otorrinolaringologista 
225325	MÃ©dico patologista clÃ­nico
225124	MÃ©dico pediatra
225127	MÃ©dico pneumologista 
225280	MÃ©dico proctologista 
225133	MÃ©dico psiquiatra 
225330	MÃ©dico radioterapeuta 
225136	MÃ©dico reumatologista 
225139	MÃ©dico sanitarista
225285	MÃ©dico urologista 
223204	CirurgiÃ£o dentista - auditor 
223208	CirurgiÃ£o dentista - clÃ­nico geral 
223212	CirurgiÃ£o dentista - endodontista 
223216	CirurgiÃ£o dentista - epidemiologista 
223220	CirurgiÃ£o dentista - estomatologista 
223224	CirurgiÃ£o dentista - implantodontista 
223228	CirurgiÃ£o dentista - odontogeriatra 
223232	CirurgiÃ£o dentista - odontologista legal 
223236	CirurgiÃ£o dentista - odontopediatra 
223240	CirurgiÃ£o dentista - ortopedista e ortodontista 
223244	CirurgiÃ£o dentista - patologista bucal 
223248	CirurgiÃ£o dentista - periodontista 
223252	CirurgiÃ£o dentista - protesiÃ³logo bucomaxilofacial 
223256	CirurgiÃ£o dentista - protesista 
223260	CirurgiÃ£o dentista - radiologista 
223264	CirurgiÃ£o dentista - reabilitador oral 
223268	CirurgiÃ£o dentista - traumatologista bucomaxilofacial 
223272	CirurgiÃ£o dentista de saÃºde coletiva 
223505	Enfermeiro 
223605	Fisioterapeuta geral
223910	Ortoptista 
223620	Peripatologista 
223905	Terapeuta ocupacional 
223710	Nutricionista 
223810	FonoaudiÃ³logo 
239425	Psicopedagogo 
251510	PsicÃ³logo clÃ­nico 
251545	NeuropsicÃ³logo 
251550	Psicanalista 
251605	Assistente social 

322205	TÃ©cnico de enfermagem 
322220	TÃ©cnico de enfermagem psiquiÃ¡trica 
322225	Instrumentador cirÃºrgico
322230	Auxiliar de enfermagem 
516210	Cuidador de idosos

*************************** Incluidos em Dezembro/2013 - versÃ£o 3.01.00 ***************************
225121	MÃ©dico oncologista clÃ­nico
225325	MÃ©dico patologista
223276	CirurgiÃ£o dentista - odontologia do trabalho
223280	CirurgiÃ£o dentista - dentÃ­stica
223284	CirurgiÃ£o dentista - disfunÃ§Ã£o temporomandibular e dor orofacial
223288	CirurgiÃ£o dentista - odontologia para pacientes com necessidades especiais
223293	CirurgiÃ£o-dentista da estratÃ©gia de saÃºde da famÃ­lia
225122	MÃ©dico cancerologista pediÃ¡trico
225142	MÃ©dico da estratÃ©gia de saÃºde da famÃ­lia
225151	MÃ©dico anestesiologista
225203	MÃ©dico em cirurgia vascular
225290	MÃ©dico cancerologista cirÃºrgico
225295	MÃ©dico cirurgiÃ£o da mÃ£o
225335	MÃ©dico patologista clÃ­nico / medicina laboratorial
225340	MÃ©dico hemoterapeuta
225345	MÃ©dico hiperbarista
**************************************************************************

*************************** Incluidos em Abril/2016 - versÃ£o 3.02.02 ***************************
223625    Fisioterapeuta respiratÃ³ria
223630    Fisioterapeuta neurofuncional
223635    Fisioterapeuta traumato-ortopÃ©dica funcional
223640    Fisioterapeuta osteopata
223645    Fisioterapeuta quiropraxista
223650    Fisioterapeuta acupunturista
223655    Fisioterapeuta esportivo
223660    Fisioterapeuta  do trabalho
***************************************************************************************

******************************** Incluidos em Maio/2016 0- versÃ£o 3.03.00 *********************
223435	FarmacÃªutico industrial
223430	FarmacÃªutico em saÃºde pÃºblica
223415	FarmacÃªutico analista clÃ­nico
223405	FarmacÃªutico
223420	FarmacÃªutico de alimentos
223440	FarmacÃªutico toxicologista
223445	FarmacÃªutico hospitalar e clÃ­nico
223425	FarmacÃªutico prÃ¡ticas integrativas e complementares
223565	Enfermeiro da estratÃ©gia de saÃºde da famÃ­lia
223560	Enfermeiro sanitarista
223555	Enfermeiro puericultor e pediÃ¡trico
223550	Enfermeiro psiquiÃ¡trico
223545	Enfermeiro obstÃ©trico
223540	Enfermeiro neonatologista
223535	Enfermeiro nefrologista
223530	Enfermeiro do trabalho
223525	Enfermeiro de terapia intensiva
223520	Enfermeiro de centro cirÃºrgico
223515	Enfermeiro de bordo
223510	Enfermeiro auditor
223570	Perfusionista
223705	Dietista
223845	FonoaudiÃ³logo em voz
223830	FonoaudiÃ³logo em linguagem
223825	FonoaudiÃ³logo em disfagia
223835	FonoaudiÃ³logo em motricidade orofacial
223840	FonoaudiÃ³logo em saÃºde coletiva
223820	FonoaudiÃ³logo em audiologia
223815	FonoaudiÃ³logo educacional
225154	MÃ©dico antroposÃ³fico

******************************** Incluido em MarÃ§o/2017 - versÃ£o 3.03.02 *********************
221105	BiÃ³logo 
******************************** Incluido em Maio/2021 - versÃ£o 4.00.00 *********************
221205 - BiomÃ©dico		
224105 - Avaliador fÃ­sico
224110 - Ludomotricista		
224115 - Preparador de atleta
224120 - Preparador fÃ­sico
224125 - TÃ©cnico de desporto individual e coletivo (exceto futebol)
224130 - TÃ©cnico de laboratÃ³rio e fiscalizaÃ§Ã£o desportiva
224135 - Treinador profissional de futebol
226305 - Musicoterapeuta

999999	CBO-S desconhecido ou nÃ£o informado pelo solicitante
-->
	</simpleType>
	<simpleType name="dm_CBOSmonitor">
		<restriction base="string">
			<enumeration value="201115"/>
			<enumeration value="203015"/>
			<enumeration value="213150"/>
			<enumeration value="225105"/>
			<enumeration value="225110"/>
			<enumeration value="225148"/>
			<enumeration value="225185"/>
			<enumeration value="225115"/>
			<enumeration value="225120"/>
			<enumeration value="225210"/>
			<enumeration value="225215"/>
			<enumeration value="225220"/>
			<enumeration value="225225"/>
			<enumeration value="225230"/>
			<enumeration value="225235"/>
			<enumeration value="225240"/>
			<enumeration value="225305"/>
			<enumeration value="225350"/>
			<enumeration value="225125"/>
			<enumeration value="225130"/>
			<enumeration value="225135"/>
			<enumeration value="225140"/>
			<enumeration value="225310"/>
			<enumeration value="225145"/>
			<enumeration value="225150"/>
			<enumeration value="225315"/>
			<enumeration value="225320"/>
			<enumeration value="225155"/>
			<enumeration value="225160"/>
			<enumeration value="225245"/>
			<enumeration value="225165"/>
			<enumeration value="225170"/>
			<enumeration value="225175"/>
			<enumeration value="225180"/>
			<enumeration value="225250"/>
			<enumeration value="225190"/>
			<enumeration value="225195"/>
			<enumeration value="225103"/>
			<enumeration value="225106"/>
			<enumeration value="225255"/>
			<enumeration value="225109"/>
			<enumeration value="225260"/>
			<enumeration value="225112"/>
			<enumeration value="225118"/>
			<enumeration value="225265"/>
			<enumeration value="225121"/>
			<enumeration value="225270"/>
			<enumeration value="225275"/>
			<enumeration value="225325"/>
			<enumeration value="225124"/>
			<enumeration value="225127"/>
			<enumeration value="225280"/>
			<enumeration value="225133"/>
			<enumeration value="225330"/>
			<enumeration value="225136"/>
			<enumeration value="225139"/>
			<enumeration value="225285"/>
			<enumeration value="223204"/>
			<enumeration value="223208"/>
			<enumeration value="223212"/>
			<enumeration value="223216"/>
			<enumeration value="223220"/>
			<enumeration value="223224"/>
			<enumeration value="223228"/>
			<enumeration value="223232"/>
			<enumeration value="223236"/>
			<enumeration value="223240"/>
			<enumeration value="223244"/>
			<enumeration value="223248"/>
			<enumeration value="223252"/>
			<enumeration value="223256"/>
			<enumeration value="223260"/>
			<enumeration value="223264"/>
			<enumeration value="223268"/>
			<enumeration value="223272"/>
			<enumeration value="223505"/>
			<enumeration value="223605"/>
			<enumeration value="223910"/>
			<enumeration value="223905"/>
			<enumeration value="223710"/>
			<enumeration value="223810"/>
			<enumeration value="239425"/>
			<enumeration value="251510"/>
			<enumeration value="251545"/>
			<enumeration value="251550"/>
			<enumeration value="251605"/>
			<enumeration value="322205"/>
			<enumeration value="322220"/>
			<enumeration value="322225"/>
			<enumeration value="322230"/>
			<enumeration value="516210"/>
			<enumeration value="225121"/>
			<enumeration value="225325"/>
			<enumeration value="223276"/>
			<enumeration value="223280"/>
			<enumeration value="223284"/>
			<enumeration value="223288"/>
			<enumeration value="223293"/>
			<enumeration value="225122"/>
			<enumeration value="225142"/>
			<enumeration value="225151"/>
			<enumeration value="225203"/>
			<enumeration value="225290"/>
			<enumeration value="225295"/>
			<enumeration value="225335"/>
			<enumeration value="225340"/>
			<enumeration value="225345"/>
			<enumeration value="999999"/>
			<enumeration value="131205"/>
			<enumeration value="131210"/>
			<enumeration value="131120"/>
			<enumeration value="2011"/>
			<enumeration value="201115"/>
			<enumeration value="203305"/>
			<enumeration value="203010"/>
			<enumeration value="203015"/>
			<enumeration value="203020"/>
			<enumeration value="203025"/>
			<enumeration value="213150"/>
			<enumeration value="221105"/>
			<enumeration value="2231"/>
			<enumeration value="2232"/>
			<enumeration value="223101"/>
			<enumeration value="223102"/>
			<enumeration value="223103"/>
			<enumeration value="223104"/>
			<enumeration value="223105"/>
			<enumeration value="223106"/>
			<enumeration value="223107"/>
			<enumeration value="223108"/>
			<enumeration value="223109"/>
			<enumeration value="223110"/>
			<enumeration value="223111"/>
			<enumeration value="223112"/>
			<enumeration value="223113"/>
			<enumeration value="223114"/>
			<enumeration value="223115"/>
			<enumeration value="223116"/>
			<enumeration value="223117"/>
			<enumeration value="223118"/>
			<enumeration value="223119"/>
			<enumeration value="223120"/>
			<enumeration value="223121"/>
			<enumeration value="223122"/>
			<enumeration value="223123"/>
			<enumeration value="223124"/>
			<enumeration value="223125"/>
			<enumeration value="223126"/>
			<enumeration value="223127"/>
			<enumeration value="223128"/>
			<enumeration value="223129"/>
			<enumeration value="223130"/>
			<enumeration value="223131"/>
			<enumeration value="223132"/>
			<enumeration value="223133"/>
			<enumeration value="223134"/>
			<enumeration value="223135"/>
			<enumeration value="223136"/>
			<enumeration value="223137"/>
			<enumeration value="223138"/>
			<enumeration value="223139"/>
			<enumeration value="2235"/>
			<enumeration value="223204"/>
			<enumeration value="223208"/>
			<enumeration value="223212"/>
			<enumeration value="223216"/>
			<enumeration value="223220"/>
			<enumeration value="223224"/>
			<enumeration value="223228"/>
			<enumeration value="223232"/>
			<enumeration value="223236"/>
			<enumeration value="223305"/>
			<enumeration value="223405"/>
			<enumeration value="2236"/>
			<enumeration value="223605"/>
			<enumeration value="223615"/>
			<enumeration value="223620"/>
			<enumeration value="2237"/>
			<enumeration value="223705"/>
			<enumeration value="223710"/>
			<enumeration value="2238"/>
			<enumeration value="223810"/>
			<enumeration value="223140"/>
			<enumeration value="223141"/>
			<enumeration value="223142"/>
			<enumeration value="223143"/>
			<enumeration value="223144"/>
			<enumeration value="223145"/>
			<enumeration value="223146"/>
			<enumeration value="223147"/>
			<enumeration value="223148"/>
			<enumeration value="223540"/>
			<enumeration value="223550"/>
			<enumeration value="223244"/>
			<enumeration value="223240"/>
			<enumeration value="223248"/>
			<enumeration value="223545"/>
			<enumeration value="223149"/>
			<enumeration value="223150"/>
			<enumeration value="223151"/>
			<enumeration value="223152"/>
			<enumeration value="223252"/>
			<enumeration value="223153"/>
			<enumeration value="223154"/>
			<enumeration value="223155"/>
			<enumeration value="223156"/>
			<enumeration value="223157"/>
			<enumeration value="223256"/>
			<enumeration value="223510"/>
			<enumeration value="223515"/>
			<enumeration value="223520"/>
			<enumeration value="223525"/>
			<enumeration value="223530"/>
			<enumeration value="223535"/>
			<enumeration value="223555"/>
			<enumeration value="223260"/>
			<enumeration value="223560"/>
			<enumeration value="223264"/>
			<enumeration value="223268"/>
			<enumeration value="223272"/>
			<enumeration value="22415"/>
			<enumeration value="239425"/>
			<enumeration value="2515"/>
			<enumeration value="251505"/>
			<enumeration value="251605"/>
			<enumeration value="251510"/>
			<enumeration value="252105"/>
			<enumeration value="251515"/>
			<enumeration value="251520"/>
			<enumeration value="251525"/>
			<enumeration value="251530"/>
			<enumeration value="251535"/>
			<enumeration value="251540"/>
			<enumeration value="251545"/>
			<enumeration value="251550"/>
			<enumeration value="301105"/>
			<enumeration value="313505"/>
			<enumeration value="313410"/>
			<enumeration value="3225"/>
			<enumeration value="322105"/>
			<enumeration value="322205"/>
			<enumeration value="322305"/>
			<enumeration value="322405"/>
			<enumeration value="322505"/>
			<enumeration value="322605"/>
			<enumeration value="322210"/>
			<enumeration value="322410"/>
			<enumeration value="322115"/>
			<enumeration value="322215"/>
			<enumeration value="322415"/>
			<enumeration value="322220"/>
			<enumeration value="322420"/>
			<enumeration value="324105"/>
			<enumeration value="322225"/>
			<enumeration value="324205"/>
			<enumeration value="324110"/>
			<enumeration value="3251"/>
			<enumeration value="322230"/>
			<enumeration value="324210"/>
			<enumeration value="325105"/>
			<enumeration value="324115"/>
			<enumeration value="322235"/>
			<enumeration value="325110"/>
			<enumeration value="322240"/>
			<enumeration value="325310"/>
			<enumeration value="325115"/>
			<enumeration value="3522"/>
			<enumeration value="352210"/>
			<enumeration value="411010"/>
			<enumeration value="415120"/>
			<enumeration value="422105"/>
			<enumeration value="422110"/>
			<enumeration value="422115"/>
			<enumeration value="5151"/>
			<enumeration value="5152"/>
			<enumeration value="513220"/>
			<enumeration value="515105"/>
			<enumeration value="515110"/>
			<enumeration value="515210"/>
			<enumeration value="513430"/>
			<enumeration value="515115"/>
			<enumeration value="515120"/>
			<enumeration value="516210"/>
			<enumeration value="516805"/>
			<enumeration value="516115"/>
			<enumeration value="516135"/>
			<enumeration value="519305"/>
			<enumeration value="521130"/>
			<enumeration value="623315"/>
			<enumeration value="741105"/>
			<enumeration value="766420"/>
			<enumeration value="782310"/>
			<enumeration value="915105"/>
			<enumeration value="2231F3"/>
			<enumeration value="3222B3"/>
			<enumeration value="2231F4"/>
			<enumeration value="2231F5"/>
			<enumeration value="2231F6"/>
			<enumeration value="2231F7"/>
			<enumeration value="2231F8"/>
			<enumeration value="223625"/>
			<enumeration value="223630"/>
			<enumeration value="223635"/>
			<enumeration value="223640"/>
			<enumeration value="223645"/>
			<enumeration value="223650"/>
			<enumeration value="223655"/>
			<enumeration value="223660"/>
			<enumeration value="223435"/>
			<enumeration value="223430"/>
			<enumeration value="223415"/>
			<enumeration value="223405"/>
			<enumeration value="223420"/>
			<enumeration value="223440"/>
			<enumeration value="223445"/>
			<enumeration value="223425"/>
			<enumeration value="223565"/>
			<enumeration value="223560"/>
			<enumeration value="223555"/>
			<enumeration value="223550"/>
			<enumeration value="223545"/>
			<enumeration value="223540"/>
			<enumeration value="223535"/>
			<enumeration value="223530"/>
			<enumeration value="223525"/>
			<enumeration value="223520"/>
			<enumeration value="223515"/>
			<enumeration value="223510"/>
			<enumeration value="223570"/>
			<enumeration value="223705"/>
			<enumeration value="223845"/>
			<enumeration value="223830"/>
			<enumeration value="223825"/>
			<enumeration value="223835"/>
			<enumeration value="223840"/>
			<enumeration value="223820"/>
			<enumeration value="223815"/>
			<enumeration value="225154"/>
			<enumeration value="221205"/>
			<enumeration value="224105"/>
			<enumeration value="224110"/>
			<enumeration value="224115"/>
			<enumeration value="224120"/>
			<enumeration value="224125"/>
			<enumeration value="224130"/>
			<enumeration value="224135"/>
			<enumeration value="226305"/>
		</restriction>
		<!--		
201115	Geneticista
203015	Pesquisador em biologia de microorganismos e parasitas 
213150	FÃ­sico mÃ©dico 
221105	BiÃ³logo 
225105	MÃ©dico acupunturista 
225110	MÃ©dico alergista e imunologista
225148	MÃ©dico anatomopatologista
225115	MÃ©dico angiologista 
225120	MÃ©dico cardiologista 
225210	MÃ©dico cirurgiÃ£o cardiovascular 
225215	MÃ©dico cirurgiÃ£o de cabeÃ§a e pescoÃ§o 
225220	MÃ©dico cirurgiÃ£o do aparelho digestivo 
225225	MÃ©dico cirurgiÃ£o geral 
225230	MÃ©dico cirurgiÃ£o pediÃ¡trico 
225235	MÃ©dico cirurgiÃ£o plÃ¡stico 
225240	MÃ©dico cirurgiÃ£o torÃ¡cico 
225305	MÃ©dico citopatologista 
225125	MÃ©dico clÃ­nico
225130	MÃ©dico de famÃ­lia e comunidade
225135	MÃ©dico dermatologista 
225140	MÃ©dico do trabalho 
225310	MÃ©dico em endoscopia 
225145	MÃ©dico em medicina de trÃ¡fego 
225150	MÃ©dico em medicina intensiva 
225315	MÃ©dico em medicina nuclear 
225320	MÃ©dico em radiologia e diagnÃ³stico por imagem 
225155	MÃ©dico endocrinologista e metabologista 
225160	MÃ©dico fisiatra 
225245	MÃ©dico foniatra 
225165	MÃ©dico gastroenterologista 
225170	MÃ©dico generalista 
225175	MÃ©dico geneticista 
225180	MÃ©dico geriatra 
225250	MÃ©dico ginecologista e obstetra 
225185	MÃ©dico Hematologista
225190	MÃ©dico Hemoterapeuta 
225195	MÃ©dico Homeopata
225103	MÃ©dico infectologista 
225106	MÃ©dico legista
225255	MÃ©dico Mastologista
225109	MÃ©dico Nefrologista
225260	MÃ©dico  neurocirurgiÃ£o
225350	MÃ©dico neurofisiologista 
225112	MÃ©dico neurologista 
225118	MÃ©dico nutrologista 
225265	MÃ©dico oftalmologista
225121	MÃ©dico oncologista
225270	MÃ©dico ortopedista e traumatologista 
225275	MÃ©dico otorrinolaringologista 
225325	MÃ©dico patologista clÃ­nico
225124	MÃ©dico pediatra
225127	MÃ©dico pneumologista 
225280	MÃ©dico proctologista 
225133	MÃ©dico psiquiatra 
225330	MÃ©dico radioterapeuta 
225136	MÃ©dico reumatologista 
225139	MÃ©dico sanitarista
225285	MÃ©dico urologista 
223204	CirurgiÃ£o dentista - auditor 
223208	CirurgiÃ£o dentista - clÃ­nico geral 
223212	CirurgiÃ£o dentista - endodontista 
223216	CirurgiÃ£o dentista - epidemiologista 
223220	CirurgiÃ£o dentista - estomatologista 
223224	CirurgiÃ£o dentista - implantodontista 
223228	CirurgiÃ£o dentista - odontogeriatra 
223232	CirurgiÃ£o dentista - odontologista legal 
223236	CirurgiÃ£o dentista - odontopediatra 
223240	CirurgiÃ£o dentista - ortopedista e ortodontista 
223244	CirurgiÃ£o dentista - patologista bucal 
223248	CirurgiÃ£o dentista - periodontista 
223252	CirurgiÃ£o dentista - protesiÃ³logo bucomaxilofacial 
223256	CirurgiÃ£o dentista - protesista 
223260	CirurgiÃ£o dentista - radiologista 
223264	CirurgiÃ£o dentista - reabilitador oral 
223268	CirurgiÃ£o dentista - traumatologista bucomaxilofacial 
223272	CirurgiÃ£o dentista de saÃºde coletiva 
223505	Enfermeiro 
223605	Fisioterapeuta geral
223910	Ortoptista 
223620	Peripatologista 
223905	Terapeuta ocupacional 
223710	Nutricionista 
223810	FonoaudiÃ³logo 
239425	Psicopedagogo 
251510	PsicÃ³logo clÃ­nico 
251545	NeuropsicÃ³logo 
251550	Psicanalista 
251605	Assistente social 

322205	TÃ©cnico de enfermagem 
322220	TÃ©cnico de enfermagem psiquiÃ¡trica 
322225	Instrumentador cirÃºrgico
322230	Auxiliar de enfermagem 
516210	Cuidador de idosos

*************************** Incluidos em Dezembro/2013 - versÃ£o 3.01.00 ***************************
225121	MÃ©dico oncologista clÃ­nico
225325	MÃ©dico patologista
223276	CirurgiÃ£o dentista - odontologia do trabalho
223280	CirurgiÃ£o dentista - dentÃ­stica
223284	CirurgiÃ£o dentista - disfunÃ§Ã£o temporomandibular e dor orofacial
223288	CirurgiÃ£o dentista - odontologia para pacientes com necessidades especiais
223293	CirurgiÃ£o-dentista da estratÃ©gia de saÃºde da famÃ­lia
225122	MÃ©dico cancerologista pediÃ¡trico
225142	MÃ©dico da estratÃ©gia de saÃºde da famÃ­lia
225151	MÃ©dico anestesiologista
225203	MÃ©dico em cirurgia vascular
225290	MÃ©dico cancerologista cirÃºrgico
225295	MÃ©dico cirurgiÃ£o da mÃ£o
225335	MÃ©dico patologista clÃ­nico / medicina laboratorial
225340	MÃ©dico hemoterapeuta
225345	MÃ©dico hiperbarista
**************************************************************************

*************************** Incluidos em Abril/2016 - versÃ£o 3.02.02 ***************************
223625    Fisioterapeuta respiratÃ³ria
223630    Fisioterapeuta neurofuncional
223635    Fisioterapeuta traumato-ortopÃ©dica funcional
223640    Fisioterapeuta osteopata
223645    Fisioterapeuta quiropraxista
223650    Fisioterapeuta acupunturista
223655    Fisioterapeuta esportivo
223660    Fisioterapeuta  do trabalho
***************************************************************************************

******************************** Incluidos em Maio/2016 0- versÃ£o 3.03.00 *********************
223435	FarmacÃªutico industrial
223430	FarmacÃªutico em saÃºde pÃºblica
223415	FarmacÃªutico analista clÃ­nico
223405	FarmacÃªutico
223420	FarmacÃªutico de alimentos
223440	FarmacÃªutico toxicologista
223445	FarmacÃªutico hospitalar e clÃ­nico
223425	FarmacÃªutico prÃ¡ticas integrativas e complementares
223565	Enfermeiro da estratÃ©gia de saÃºde da famÃ­lia
223560	Enfermeiro sanitarista
223555	Enfermeiro puericultor e pediÃ¡trico
223550	Enfermeiro psiquiÃ¡trico
223545	Enfermeiro obstÃ©trico
223540	Enfermeiro neonatologista
223535	Enfermeiro nefrologista
223530	Enfermeiro do trabalho
223525	Enfermeiro de terapia intensiva
223520	Enfermeiro de centro cirÃºrgico
223515	Enfermeiro de bordo
223510	Enfermeiro auditor
223570	Perfusionista
223705	Dietista
223845	FonoaudiÃ³logo em voz
223830	FonoaudiÃ³logo em linguagem
223825	FonoaudiÃ³logo em disfagia
223835	FonoaudiÃ³logo em motricidade orofacial
223840	FonoaudiÃ³logo em saÃºde coletiva
223820	FonoaudiÃ³logo em audiologia
223815	FonoaudiÃ³logo educacional
225154	MÃ©dico antroposÃ³fico
******************************** Incluido em Maio/2021 - versÃ£o 4.00.00 *********************
221205 - BiomÃ©dico		
224105 - Avaliador fÃ­sico
224110 - Ludomotricista		
224115 - Preparador de atleta
224120 - Preparador fÃ­sico
224125 - TÃ©cnico de desporto individual e coletivo (exceto futebol)
224130 - TÃ©cnico de laboratÃ³rio e fiscalizaÃ§Ã£o desportiva
224135 - Treinador profissional de futebol
226305 - Musicoterapeuta
******************************************************************************

999999	CBO-S desconhecido ou nÃ£o informado pelo solicitante
-->
	</simpleType>
	<simpleType name="dm_condicaoClinica">
		<restriction base="string">
			<maxLength value="1"/>
			<enumeration value="A"/>
			<enumeration value="E"/>
			<enumeration value="H"/>
			<enumeration value="C"/>
			<enumeration value="R"/>
		</restriction>
	</simpleType>
	<!--versÃ£o 3.03 - acrescentado zero a esquerda para compatibilizar com a estrutura da tabela 26 do padrÃ£o - GT e COPISS de 04/08/2015-->
	<simpleType name="dm_conselhoProfissional">
		<restriction base="string">
			<maxLength value="2"/>
			<enumeration value="01"/>
			<enumeration value="02"/>
			<enumeration value="03"/>
			<enumeration value="04"/>
			<enumeration value="05"/>
			<enumeration value="06"/>
			<enumeration value="07"/>
			<enumeration value="08"/>
			<enumeration value="09"/>
			<enumeration value="10"/>
			<enumeration value="11"/>
			<enumeration value="12"/>
			<enumeration value="13"/>
			<enumeration value="14"/>
			<enumeration value="15"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_dente">
		<restriction base="string">
			<maxLength value="2"/>
			<enumeration value="11"/>
			<enumeration value="12"/>
			<enumeration value="13"/>
			<enumeration value="14"/>
			<enumeration value="15"/>
			<enumeration value="16"/>
			<enumeration value="17"/>
			<enumeration value="18"/>
			<enumeration value="19"/>
			<enumeration value="21"/>
			<enumeration value="22"/>
			<enumeration value="23"/>
			<enumeration value="24"/>
			<enumeration value="25"/>
			<enumeration value="26"/>
			<enumeration value="27"/>
			<enumeration value="28"/>
			<enumeration value="29"/>
			<enumeration value="31"/>
			<enumeration value="32"/>
			<enumeration value="33"/>
			<enumeration value="34"/>
			<enumeration value="35"/>
			<enumeration value="36"/>
			<enumeration value="37"/>
			<enumeration value="38"/>
			<enumeration value="39"/>
			<enumeration value="41"/>
			<enumeration value="42"/>
			<enumeration value="43"/>
			<enumeration value="44"/>
			<enumeration value="45"/>
			<enumeration value="46"/>
			<enumeration value="47"/>
			<enumeration value="48"/>
			<enumeration value="49"/>
			<enumeration value="51"/>
			<enumeration value="52"/>
			<enumeration value="53"/>
			<enumeration value="54"/>
			<enumeration value="55"/>
			<enumeration value="59"/>
			<enumeration value="61"/>
			<enumeration value="62"/>
			<enumeration value="63"/>
			<enumeration value="64"/>
			<enumeration value="65"/>
			<enumeration value="69"/>
			<enumeration value="71"/>
			<enumeration value="72"/>
			<enumeration value="73"/>
			<enumeration value="74"/>
			<enumeration value="75"/>
			<enumeration value="79"/>
			<enumeration value="81"/>
			<enumeration value="82"/>
			<enumeration value="83"/>
			<enumeration value="84"/>
			<enumeration value="85"/>
			<enumeration value="89"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_debitoCreditoIndicador">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
		</restriction>
		<!--1-Debito-->
		<!--2-CrÃ©dito-->
	</simpleType>
	<simpleType name="dm_debitoCreditoTipo">
		<restriction base="string">
			<maxLength value="2"/>
			<enumeration value="01"/>
			<enumeration value="02"/>
			<enumeration value="03"/>
			<enumeration value="04"/>
			<enumeration value="05"/>
			<enumeration value="06"/>
			<enumeration value="07"/>
			<enumeration value="08"/>
			<enumeration value="09"/>
		</restriction>
		<!--01-IRRF-->
		<!--02-ISS-->
		<!--03-INSS-->
		<!--04-PIS-->
		<!--05-COFINS-->
		<!--06-CSLL-->
		<!--07-Descontos financeiros-->
		<!--08-Ajuste de pagamento anterior-->
		<!--09-DeterminaÃ§Ã£o judicial-->
	</simpleType>
	<simpleType name="dm_diagnosticoImagem">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
			<enumeration value="5"/>
			<enumeration value="6"/>
		</restriction>
		<!--1 - Tomografia-->
		<!--2 - RessonÃ¢ncia MagnÃ©tica-->
		<!--3 - Raio-X-->
		<!--4 - Outras-->
		<!--5 - Ultrassonografia-->
		<!--6 - PET-->
	</simpleType>
	<simpleType name="dm_ecog">
		<restriction base="string">
			<enumeration value="0"/>
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_estadiamento">
		<restriction base="string">
			<enumeration value="0"/>
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
			<enumeration value="5"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_face">
		<restriction base="string">
			<pattern value="[OLMVDIP]{1,5}"/>
		</restriction>
	</simpleType>
	<!--		
Este campo Ã© uma combinaÃ§Ã£o livre das faces abaixo realizada pelo profissional
O	Oclusal
L	Lingual
M	Mesial
V	Vestibular
D	Distal
I	Incisal
P	Palatina
-->
	<simpleType name="dm_finalidadeTratamento">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
			<enumeration value="5"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_formaEnvio">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_formaPagamento">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_grauPart">
		<restriction base="string">
			<maxLength value="2"/>
			<enumeration value="00"/>
			<enumeration value="01"/>
			<enumeration value="02"/>
			<enumeration value="03"/>
			<enumeration value="04"/>
			<enumeration value="05"/>
			<enumeration value="06"/>
			<enumeration value="07"/>
			<enumeration value="08"/>
			<enumeration value="09"/>
			<enumeration value="10"/>
			<enumeration value="11"/>
			<enumeration value="12"/>
			<enumeration value="13"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_indicadorAcidente">
		<restriction base="string">
			<enumeration value="0"/>
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="9"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_indicadorIdentificacao">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_metastase">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="8"/>
			<enumeration value="9"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_motivoSaida">
		<restriction base="string">
			<enumeration value="11"/>
			<enumeration value="12"/>
			<enumeration value="14"/>
			<enumeration value="15"/>
			<enumeration value="16"/>
			<enumeration value="18"/>
			<enumeration value="19"/>
			<enumeration value="21"/>
			<enumeration value="22"/>
			<enumeration value="23"/>
			<enumeration value="24"/>
			<enumeration value="25"/>
			<enumeration value="26"/>
			<enumeration value="27"/>
			<enumeration value="28"/>
			<enumeration value="31"/>
			<enumeration value="32"/>
			<enumeration value="41"/>
			<enumeration value="42"/>
			<enumeration value="43"/>
			<enumeration value="51"/>
			<enumeration value="61"/>
			<enumeration value="62"/>
			<enumeration value="63"/>
			<enumeration value="64"/>
			<enumeration value="65"/>
			<enumeration value="66"/>
			<enumeration value="67"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_motivoSaidaObito">
		<restriction base="string">
			<enumeration value="41"/>
			<enumeration value="42"/>
			<enumeration value="43"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_nodulo">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
			<enumeration value="5"/>
			<enumeration value="8"/>
			<enumeration value="9"/>
		</restriction>
	</simpleType>
	<!--
		1	N1
		2	N2
		3	N3
		4	N0
		5	Nx
		8	NÃ£o se aplica
		9	Sem informaÃ§Ã£o
	-->
	<!--
	</simpleType>-->
	<!--	<simpleType name="dm_obitoMulher">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			-->
	<!-- 1 GrÃ¡vida -->
	<!--
			-->
	<!-- 2 AtÃ© 42 dias apÃ³s tÃ©rmino gestaÃ§Ã£o -->
	<!--
			-->
	<!-- 3 De 43 dias Ã  12 meses apÃ³s tÃ©rmino gestaÃ§Ã£o -->
	<!--
		</restriction>
	</simpleType>-->
	<simpleType name="dm_opcaoFabricante">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_objetoRecurso">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_outrasDespesas">
		<restriction base="string">
			<enumeration value="01"/>
			<enumeration value="02"/>
			<enumeration value="03"/>
			<enumeration value="05"/>
			<enumeration value="07"/>
			<enumeration value="08"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_posicaoProfissao">
		<restriction base="string">
			<enumeration value="00"/>
			<enumeration value="01"/>
			<enumeration value="02"/>
			<enumeration value="03"/>
			<enumeration value="04"/>
			<enumeration value="05"/>
			<enumeration value="06"/>
			<enumeration value="07"/>
			<enumeration value="08"/>
			<enumeration value="09"/>
			<enumeration value="10"/>
			<enumeration value="11"/>
			<enumeration value="12"/>
			<enumeration value="13"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_regiao">
		<restriction base="string">
			<maxLength value="4"/>
			<enumeration value="AI"/>
			<enumeration value="AS"/>
			<enumeration value="HASD"/>
			<enumeration value="HASE"/>
			<enumeration value="HAID"/>
			<enumeration value="HAIE"/>
			<enumeration value="ASAI"/>
			<enumeration value="S1"/>
			<enumeration value="S2"/>
			<enumeration value="S3"/>
			<enumeration value="S4"/>
			<enumeration value="S5"/>
			<enumeration value="S6"/>
			<enumeration value="LG"/>
			<enumeration value="CL"/>
			<enumeration value="AB"/>
			<enumeration value="PA"/>
			<enumeration value="MJ"/>
			<enumeration value="PD"/>
			<enumeration value="PM"/>
			<enumeration value="RM"/>
			<enumeration value="MA"/>
			<enumeration value="GI"/>
			<enumeration value="PT"/>
			<enumeration value="TP"/>
			<enumeration value="RIS"/>
			<enumeration value="RCSD"/>
			<enumeration value="RPSD"/>
			<enumeration value="RCID"/>
			<enumeration value="RMSD"/>
			<enumeration value="RCSE"/>
			<enumeration value="RPSE"/>
			<enumeration value="RMSE"/>
			<enumeration value="RII"/>
			<enumeration value="RPID"/>
			<enumeration value="RMID"/>
			<enumeration value="RCIE"/>
			<enumeration value="RPIE"/>
			<enumeration value="RMIE"/>
			<enumeration value="RMD"/>
			<enumeration value="RME"/>
			<enumeration value="RPD"/>
			<enumeration value="RPE"/>
			<enumeration value="RMPE"/>
			<enumeration value="RMPD"/>
			<enumeration value="SM"/>
			<enumeration value="TU"/>
			<enumeration value="SI"/>
			<enumeration value="FLI"/>
			<enumeration value="FLA"/>
			<enumeration value="UV"/>
			<enumeration value="PP"/>
			<enumeration value="PI"/>
			<enumeration value="LS"/>
			<enumeration value="LI"/>
			<enumeration value="RL"/>
			<enumeration value="RP"/>
			<enumeration value="RV"/>
			<enumeration value="RSMD"/>
			<enumeration value="RSME"/>
			<enumeration value="RSL"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_regimeInternacao">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_sexo">
		<restriction base="string">
			<maxLength value="1"/>
			<enumeration value="1"/>
			<enumeration value="3"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_semMovimentoInclusao">
		<restriction base="string">
			<maxLength value="4"/>
			<enumeration value="5016"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_simNao">
		<restriction base="string">
			<enumeration value="S"/>
			<enumeration value="N"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_statusCancelamento">
		<restriction base="string">
			<maxLength value="1"/>
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
			<enumeration value="5"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_statusComunicacaoBeneficiario">
		<restriction base="string">
			<enumeration value="P"/>
			<enumeration value="B"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_statusProtocolo">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
			<enumeration value="5"/>
			<enumeration value="6"/>
			<enumeration value="7"/>
			<enumeration value="8"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_statusSolicitacao">
		<restriction base="string">
			<length value="1"/>
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
			<enumeration value="5"/>
			<enumeration value="6"/>
			<enumeration value="7"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_statusTransacaoMonitor">
		<restriction base="string">
			<length value="1"/>
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
		</restriction>
	</simpleType>
	<!-- a estrutura abaixo filtra  de dm_tabelaGeral, somente os cÃ³digos de tabela que serÃ£o utilizados como campos das guias -->
	<simpleType name="dm_tabelaMonitor">
		<restriction base="string">
			<enumeration value="18"/>
			<enumeration value="19"/>
			<enumeration value="20"/>
			<enumeration value="22"/>
			<enumeration value="63"/>
			<enumeration value="90"/>
			<enumeration value="98"/>
			<enumeration value="00"/>
		</restriction>
		<!-- 18 TUSS _ Taxas hospitalares, diÃ¡rias e gases medicinais -->
		<!-- 19 TUSS _ Materiais -->
		<!-- 20 TUSS - Medicamentos -->
		<!-- 22 TUSS _ Procedimentos e eventos em saÃºde (medicina, odonto e demais Ã¡reas de saÃºde) -->
		<!-- 63 TUSS_ Grupo de procedimentos e itens assistenciais para envio de dados para ANS -->
		<!-- 00 Tabela PrÃ³pria das Operadoras -->
	</simpleType>
	<simpleType name="dm_tabela">
		<restriction base="string">
			<enumeration value="18"/>
			<enumeration value="19"/>
			<enumeration value="20"/>
			<enumeration value="22"/>
			<enumeration value="90"/>
			<enumeration value="98"/>
			<enumeration value="00"/>
		</restriction>
		<!-- 18 TUSS _ Taxas hospitalares, diÃ¡rias e gases medicinais -->
		<!-- 19 TUSS _ Materiais -->
		<!-- 20 TUSS - Medicamentos -->
		<!-- 22 TUSS _ Procedimentos e eventos em saÃºde (medicina, odonto e demais Ã¡reas de saÃºde) -->
		<!-- 00 Tabela PrÃ³pria das Operadoras -->
	</simpleType>
	<simpleType name="dm_tabelaPacote">
		<restriction base="string">
			<enumeration value="18"/>
			<enumeration value="19"/>
			<enumeration value="20"/>
			<enumeration value="22"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tabelaGeral">
		<restriction base="string">
			<enumeration value="05"/>
			<enumeration value="12"/>
			<enumeration value="18"/>
			<enumeration value="19"/>
			<enumeration value="20"/>
			<enumeration value="22"/>
			<enumeration value="23"/>
			<enumeration value="24"/>
			<enumeration value="25"/>
			<enumeration value="26"/>
			<enumeration value="27"/>
			<enumeration value="28"/>
			<enumeration value="29"/>
			<enumeration value="30"/>
			<enumeration value="31"/>
			<enumeration value="32"/>
			<enumeration value="33"/>
			<enumeration value="34"/>
			<enumeration value="35"/>
			<enumeration value="36"/>
			<enumeration value="37"/>
			<enumeration value="38"/>
			<enumeration value="39"/>
			<enumeration value="40"/>
			<enumeration value="41"/>
			<enumeration value="42"/>
			<enumeration value="43"/>
			<enumeration value="44"/>
			<enumeration value="45"/>
			<enumeration value="46"/>
			<enumeration value="47"/>
			<enumeration value="48"/>
			<enumeration value="49"/>
			<enumeration value="50"/>
			<enumeration value="51"/>
			<enumeration value="52"/>
			<enumeration value="53"/>
			<enumeration value="54"/>
			<enumeration value="55"/>
			<enumeration value="56"/>
			<enumeration value="57"/>
			<enumeration value="58"/>
			<enumeration value="59"/>
			<enumeration value="60"/>
			<enumeration value="61"/>
			<enumeration value="90"/>
			<enumeration value="98"/>
			<enumeration value="00"/>
		</restriction>
		<!-- 01 Lista de Procedimentos MÃ©dicos AMB 90 (Inativo)** -->
		<!-- 02 Lista de Procedimentos MÃ©dicos AMB 92 (Inativo)** -->
		<!-- 03 Lista de Procedimentos MÃ©dicos AMB 96 (Inativo)** -->
		<!-- 04 Lista de Procedimentos MÃ©dicos AMB 99 (Inativo)** -->
		<!-- 05 Tabela BrasÃ­ndice -->
		<!-- 06 ClassificaÃ§Ã£o Brasileira Hierarquizada de Procedimentos MÃ©dicos (Inativo)** -->
		<!-- 07 Tabela CIEFAS-93 (Inativo)** -->
		<!-- 08 Tabela CIEFAS-2000 (Inativo)** -->
		<!-- 09 Rol de Procedimentos ANS (Inativo)** -->
		<!-- 10 Tabela de Procedimentos Ambulatoriais SUS (Inativo)** -->
		<!-- 11 Tabela de Procedimentos Hospitalares SUS (Inativo)** -->
		<!-- 12 Tabela SIMPRO -->
		<!-- 13 Tabela TUNEP -->
		<!-- 14 Tabela VRPO (Inativo)**-->
		<!-- 15 Tabela de IntercÃ¢mbio Sistema Uniodonto (Inativo)** -->
		<!-- 16 TUSS _ Procedimentos MÃ©dicos(Inativo) -->
		<!-- 17 TUSS _ Procedimentos OdontolÃ³gicos (Inativo) -->
		<!-- 18 TUSS _ Taxas hospitalares, diÃ¡rias e gases medicinais -->
		<!-- 19 TUSS _ Materiais -->
		<!-- 20 TUSS - Medicamentos -->
		<!-- 21 TUSS _ Outras Ã¡reas da saÃºde (Inativo) -->
		<!-- 22 TUSS _ Procedimentos e eventos em saÃºde (medicina, odonto e demais Ã¡reas de saÃºde) -->
		<!--23	Conselho profissional
			24	Tipo de internaÃ§Ã£o
			25	Sexo
			26	Regime de InternaÃ§Ã£o
			27	Tipo obstÃ©trica
			28	Tipo de consulta
			29	Tipo de doenÃ§a
			30	Unidade de tempo de doenÃ§a referida pelo paciente 
			31	Indicador de Acidente
			32	Tipo de atendimento
			33	Tipo de acomodaÃ§Ã£o
			34	Motivo de saÃ­da da internaÃ§Ã£o
			35	Ã?bito em mulher
			36	Tipo de Faturamento 
			37	Via de acesso
			38	TÃ©cnica utilizada
			39	Grau de participaÃ§Ã£o
			40	Faces do dente
			41	SituaÃ§Ã£o Inicial do Dente
			42	RegiÃµes da Boca
			43	Dentes
			44	Status do protocolo
			45	CBO-S (especialidade)
			46	Glosas, negativas e demais mensagens
			47	Unidade de Medida
			48	Forma de Pagamento
			49	Status do Cancelamento
			50	Status da SolicitaÃ§Ã£o
			51	Tipo de Demonstrativo
			52	Escala de Capacidade Funcional (ECOG - Escala de Zubrod)
			53	CarÃ¡ter do Atendimento
			54	CÃ³digo da Despesa
			55	Finalidade do Tratamento
			56	MetÃ¡stases
			57	NÃ³dulo
			58	DiagnÃ³stico por imagem
			59	Tipo de Quimioterapia
			60	Tumor
			61	Via de administraÃ§Ã£o-->
		<!-- 90 Tabela PrÃ³pria Pacote OdontolÃ³gico -->
		<!-- 98 Tabela PrÃ³pria de Pacotes -->
		<!-- 00 Tabela PrÃ³pria das Operadoras -->
	</simpleType>
	<simpleType name="dm_tabelasDiagnostico">
		<restriction base="string">
			<enumeration value="CID-10"/>
		</restriction>
	</simpleType>
	<!--<simpleType name="dm_tecnicaRadiografica">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
			-->
	<!--1 - Tomografia-->
	<!--
			-->
	<!--2 - RessonÃ¢ncia MagnÃ©tica-->
	<!--
			-->
	<!--3 - Raio-X-->
	<!--
			-->
	<!--4 - Outras-->
	<!--
		</restriction>
	</simpleType>-->
	<simpleType name="dm_tecnicaUtilizada">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoAcomodacao">
		<restriction base="string">
			<enumeration value="02"/>
			<enumeration value="09"/>
			<enumeration value="10"/>
			<enumeration value="11"/>
			<enumeration value="12"/>
			<enumeration value="13"/>
			<enumeration value="14"/>
			<enumeration value="15"/>
			<enumeration value="16"/>
			<enumeration value="17"/>
			<enumeration value="18"/>
			<enumeration value="19"/>
			<enumeration value="20"/>
			<enumeration value="21"/>
			<enumeration value="22"/>
			<enumeration value="25"/>
			<enumeration value="26"/>
			<enumeration value="27"/>
			<enumeration value="28"/>
			<enumeration value="29"/>
			<enumeration value="30"/>
			<enumeration value="31"/>
			<enumeration value="32"/>
			<enumeration value="33"/>
			<enumeration value="36"/>
			<enumeration value="37"/>
			<enumeration value="38"/>
			<enumeration value="39"/>
			<enumeration value="40"/>
			<enumeration value="41"/>
			<enumeration value="43"/>
			<enumeration value="44"/>
			<enumeration value="45"/>
			<enumeration value="46"/>
			<enumeration value="47"/>
			<enumeration value="48"/>
			<enumeration value="49"/>
			<enumeration value="50"/>
			<enumeration value="51"/>
			<enumeration value="52"/>
			<enumeration value="53"/>
			<enumeration value="56"/>
			<enumeration value="57"/>
			<enumeration value="58"/>
			<enumeration value="59"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoAtendimento">
		<restriction base="string">
			<enumeration value="01"/>
			<enumeration value="02"/>
			<enumeration value="03"/>
			<enumeration value="04"/>
			<enumeration value="05"/>
			<enumeration value="08"/>
			<enumeration value="09"/>
			<enumeration value="10"/>
			<enumeration value="13"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoAtendimentoIntermediario">
		<restriction base="string">
			<maxLength value="1"/>
			<enumeration value="1"/>
			<enumeration value="2"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoAtendimentoOdonto">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
			<enumeration value="5"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoConsulta">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoDemonstrativo">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoDemonstrativoPagamento">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="3"/>
		</restriction>
	</simpleType>
	<!--<simpleType name="dm_tipoDoenca">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			-->
	<!--1- A - Aguda-->
	<!--
			-->
	<!--2- C - CrÃ´nica-->
	<!--
		</restriction>
	</simpleType>-->
	<simpleType name="dm_tipoEvento">
		<restriction base="string">
			<enumeration value="I"/>
			<enumeration value="A"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoEventoMonitoramento">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
			<enumeration value="5"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_origemEventoAtencaoSaude">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
			<enumeration value="5"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoFaturamento">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoFaturamentoMonitoramento">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
			<enumeration value="F"/>
			<enumeration value="T"/>
			<enumeration value="P"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoFaturamentoOdonto">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="4"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoGuia">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoGlosa">
		<restriction base="string">
			<maxLength value="4"/>
			<enumeration value="1001"/>
			<enumeration value="1002"/>
			<enumeration value="1003"/>
			<enumeration value="1004"/>
			<enumeration value="1005"/>
			<enumeration value="1006"/>
			<enumeration value="1007"/>
			<enumeration value="1008"/>
			<enumeration value="1009"/>
			<enumeration value="1010"/>
			<enumeration value="1011"/>
			<enumeration value="1012"/>
			<enumeration value="1013"/>
			<enumeration value="1014"/>
			<enumeration value="1015"/>
			<enumeration value="1016"/>
			<enumeration value="1017"/>
			<enumeration value="1018"/>
			<enumeration value="1019"/>
			<enumeration value="1020"/>
			<enumeration value="1021"/>
			<enumeration value="1022"/>
			<enumeration value="1023"/>
			<enumeration value="1024"/>
			<enumeration value="1025"/>
			<enumeration value="1101"/>
			<enumeration value="1102"/>
			<enumeration value="1103"/>
			<enumeration value="1104"/>
			<enumeration value="1201"/>
			<enumeration value="1202"/>
			<enumeration value="1203"/>
			<enumeration value="1204"/>
			<enumeration value="1205"/>
			<enumeration value="1206"/>
			<enumeration value="1207"/>
			<enumeration value="1208"/>
			<enumeration value="1209"/>
			<enumeration value="1210"/>
			<enumeration value="1211"/>
			<enumeration value="1212"/>
			<enumeration value="1213"/>
			<enumeration value="1214"/>
			<enumeration value="1215"/>
			<enumeration value="1216"/>
			<enumeration value="1217"/>
			<enumeration value="1218"/>
			<enumeration value="1301"/>
			<enumeration value="1302"/>
			<enumeration value="1303"/>
			<enumeration value="1304"/>
			<enumeration value="1305"/>
			<enumeration value="1306"/>
			<enumeration value="1307"/>
			<enumeration value="1308"/>
			<enumeration value="1309"/>
			<enumeration value="1310"/>
			<enumeration value="1311"/>
			<enumeration value="1312"/>
			<enumeration value="1313"/>
			<enumeration value="1314"/>
			<enumeration value="1315"/>
			<enumeration value="1316"/>
			<enumeration value="1317"/>
			<enumeration value="1318"/>
			<enumeration value="1319"/>
			<enumeration value="1320"/>
			<enumeration value="1321"/>
			<enumeration value="1322"/>
			<enumeration value="1323"/>
			<enumeration value="1401"/>
			<enumeration value="1402"/>
			<enumeration value="1403"/>
			<enumeration value="1404"/>
			<enumeration value="1405"/>
			<enumeration value="1406"/>
			<enumeration value="1407"/>
			<enumeration value="1408"/>
			<enumeration value="1409"/>
			<enumeration value="1410"/>
			<enumeration value="1411"/>
			<enumeration value="1412"/>
			<enumeration value="1413"/>
			<enumeration value="1414"/>
			<enumeration value="1415"/>
			<enumeration value="1416"/>
			<enumeration value="1417"/>
			<enumeration value="1418"/>
			<enumeration value="1419"/>
			<enumeration value="1420"/>
			<enumeration value="1421"/>
			<enumeration value="1422"/>
			<enumeration value="1423"/>
			<enumeration value="1424"/>
			<enumeration value="1425"/>
			<enumeration value="1426"/>
			<enumeration value="1427"/>
			<enumeration value="1428"/>
			<enumeration value="1429"/>
			<enumeration value="1430"/>
			<enumeration value="1431"/>
			<enumeration value="1432"/>
			<enumeration value="1433"/>
			<enumeration value="1434"/>
			<enumeration value="1435"/>
			<enumeration value="1436"/>
			<enumeration value="1437"/>
			<enumeration value="1438"/>
			<enumeration value="1501"/>
			<enumeration value="1502"/>
			<enumeration value="1503"/>
			<enumeration value="1504"/>
			<enumeration value="1505"/>
			<enumeration value="1506"/>
			<enumeration value="1507"/>
			<enumeration value="1508"/>
			<enumeration value="1509"/>
			<enumeration value="1601"/>
			<enumeration value="1602"/>
			<enumeration value="1603"/>
			<enumeration value="1604"/>
			<enumeration value="1605"/>
			<enumeration value="1606"/>
			<enumeration value="1607"/>
			<enumeration value="1608"/>
			<enumeration value="1609"/>
			<enumeration value="1610"/>
			<enumeration value="1611"/>
			<enumeration value="1612"/>
			<enumeration value="1613"/>
			<enumeration value="1614"/>
			<enumeration value="1615"/>
			<enumeration value="1701"/>
			<enumeration value="1702"/>
			<enumeration value="1703"/>
			<enumeration value="1704"/>
			<enumeration value="1705"/>
			<enumeration value="1706"/>
			<enumeration value="1707"/>
			<enumeration value="1708"/>
			<enumeration value="1709"/>
			<enumeration value="1710"/>
			<enumeration value="1711"/>
			<enumeration value="1712"/>
			<enumeration value="1713"/>
			<enumeration value="1714"/>
			<enumeration value="1715"/>
			<enumeration value="1716"/>
			<enumeration value="1717"/>
			<enumeration value="1718"/>
			<enumeration value="1719"/>
			<enumeration value="1720"/>
			<enumeration value="1721"/>
			<enumeration value="1722"/>
			<enumeration value="1723"/>
			<enumeration value="1724"/>
			<enumeration value="1725"/>
			<enumeration value="1726"/>
			<enumeration value="1727"/>
			<enumeration value="1728"/>
			<enumeration value="1729"/>
			<enumeration value="1730"/>
			<enumeration value="1731"/>
			<enumeration value="1732"/>
			<enumeration value="1733"/>
			<enumeration value="1734"/>
			<enumeration value="1735"/>
			<enumeration value="1736"/>
			<enumeration value="1737"/>
			<enumeration value="1738"/>
			<enumeration value="1739"/>
			<enumeration value="1740"/>
			<enumeration value="1741"/>
			<enumeration value="1742"/>
			<enumeration value="1743"/>
			<enumeration value="1744"/>
			<enumeration value="1745"/>
			<enumeration value="1746"/>
			<enumeration value="1747"/>
			<enumeration value="1748"/>
			<enumeration value="1749"/>
			<enumeration value="1801"/>
			<enumeration value="1802"/>
			<enumeration value="1803"/>
			<enumeration value="1804"/>
			<enumeration value="1805"/>
			<enumeration value="1806"/>
			<enumeration value="1807"/>
			<enumeration value="1808"/>
			<enumeration value="1809"/>
			<enumeration value="1810"/>
			<enumeration value="1811"/>
			<enumeration value="1812"/>
			<enumeration value="1813"/>
			<enumeration value="1814"/>
			<enumeration value="1815"/>
			<enumeration value="1816"/>
			<enumeration value="1817"/>
			<enumeration value="1818"/>
			<enumeration value="1819"/>
			<enumeration value="1820"/>
			<enumeration value="1821"/>
			<enumeration value="1822"/>
			<enumeration value="1823"/>
			<enumeration value="1824"/>
			<enumeration value="1825"/>
			<enumeration value="1826"/>
			<enumeration value="1827"/>
			<enumeration value="1828"/>
			<enumeration value="1829"/>
			<enumeration value="1830"/>
			<enumeration value="1831"/>
			<enumeration value="1832"/>
			<enumeration value="1833"/>
			<enumeration value="1834"/>
			<enumeration value="1835"/>
			<enumeration value="1836"/>
			<enumeration value="1837"/>
			<enumeration value="1838"/>
			<enumeration value="1839"/>
			<enumeration value="1840"/>
			<enumeration value="1901"/>
			<enumeration value="1902"/>
			<enumeration value="1903"/>
			<enumeration value="1904"/>
			<enumeration value="1905"/>
			<enumeration value="1906"/>
			<enumeration value="1907"/>
			<enumeration value="1908"/>
			<enumeration value="1909"/>
			<enumeration value="1910"/>
			<enumeration value="1911"/>
			<enumeration value="1912"/>
			<enumeration value="1913"/>
			<enumeration value="1914"/>
			<enumeration value="1915"/>
			<enumeration value="1916"/>
			<enumeration value="1917"/>
			<enumeration value="1918"/>
			<enumeration value="2001"/>
			<enumeration value="2002"/>
			<enumeration value="2003"/>
			<enumeration value="2004"/>
			<enumeration value="2005"/>
			<enumeration value="2006"/>
			<enumeration value="2007"/>
			<enumeration value="2008"/>
			<enumeration value="2009"/>
			<enumeration value="2010"/>
			<enumeration value="2011"/>
			<enumeration value="2012"/>
			<enumeration value="2013"/>
			<enumeration value="2014"/>
			<enumeration value="2015"/>
			<enumeration value="2101"/>
			<enumeration value="2102"/>
			<enumeration value="2103"/>
			<enumeration value="2104"/>
			<enumeration value="2105"/>
			<enumeration value="2106"/>
			<enumeration value="2107"/>
			<enumeration value="2108"/>
			<enumeration value="2109"/>
			<enumeration value="2110"/>
			<enumeration value="2111"/>
			<enumeration value="2112"/>
			<enumeration value="2113"/>
			<enumeration value="2114"/>
			<enumeration value="2115"/>
			<enumeration value="2201"/>
			<enumeration value="2202"/>
			<enumeration value="2203"/>
			<enumeration value="2204"/>
			<enumeration value="2205"/>
			<enumeration value="2206"/>
			<enumeration value="2207"/>
			<enumeration value="2208"/>
			<enumeration value="2209"/>
			<enumeration value="2210"/>
			<enumeration value="2211"/>
			<enumeration value="2212"/>
			<enumeration value="2213"/>
			<enumeration value="2301"/>
			<enumeration value="2302"/>
			<enumeration value="2303"/>
			<enumeration value="2304"/>
			<enumeration value="2305"/>
			<enumeration value="2306"/>
			<enumeration value="2307"/>
			<enumeration value="2308"/>
			<enumeration value="2309"/>
			<enumeration value="2310"/>
			<enumeration value="2401"/>
			<enumeration value="2402"/>
			<enumeration value="2403"/>
			<enumeration value="2404"/>
			<enumeration value="2405"/>
			<enumeration value="2406"/>
			<enumeration value="2407"/>
			<enumeration value="2408"/>
			<enumeration value="2409"/>
			<enumeration value="2410"/>
			<enumeration value="2411"/>
			<enumeration value="2412"/>
			<enumeration value="2413"/>
			<enumeration value="2414"/>
			<enumeration value="2415"/>
			<enumeration value="2416"/>
			<enumeration value="2417"/>
			<enumeration value="2418"/>
			<enumeration value="2419"/>
			<enumeration value="2420"/>
			<enumeration value="2421"/>
			<enumeration value="2422"/>
			<enumeration value="2423"/>
			<enumeration value="2424"/>
			<enumeration value="2501"/>
			<enumeration value="2502"/>
			<enumeration value="2503"/>
			<enumeration value="2504"/>
			<enumeration value="2505"/>
			<enumeration value="2506"/>
			<enumeration value="2507"/>
			<enumeration value="2508"/>
			<enumeration value="2509"/>
			<enumeration value="2510"/>
			<enumeration value="2511"/>
			<enumeration value="2512"/>
			<enumeration value="2513"/>
			<enumeration value="2514"/>
			<enumeration value="2515"/>
			<enumeration value="2516"/>
			<enumeration value="2601"/>
			<enumeration value="2602"/>
			<enumeration value="2603"/>
			<enumeration value="2604"/>
			<enumeration value="2605"/>
			<enumeration value="2606"/>
			<enumeration value="2607"/>
			<enumeration value="2608"/>
			<enumeration value="2609"/>
			<enumeration value="2610"/>
			<enumeration value="2611"/>
			<enumeration value="2612"/>
			<enumeration value="2613"/>
			<enumeration value="2614"/>
			<enumeration value="2701"/>
			<enumeration value="2702"/>
			<enumeration value="2703"/>
			<enumeration value="2704"/>
			<enumeration value="2705"/>
			<enumeration value="2706"/>
			<enumeration value="2707"/>
			<enumeration value="2708"/>
			<enumeration value="2709"/>
			<enumeration value="2710"/>
			<enumeration value="2711"/>
			<enumeration value="2712"/>
			<enumeration value="2713"/>
			<enumeration value="2714"/>
			<enumeration value="2715"/>
			<enumeration value="2716"/>
			<enumeration value="2717"/>
			<enumeration value="2718"/>
			<enumeration value="2801"/>
			<enumeration value="2802"/>
			<enumeration value="2803"/>
			<enumeration value="2804"/>
			<enumeration value="2805"/>
			<enumeration value="2806"/>
			<enumeration value="2807"/>
			<enumeration value="2808"/>
			<enumeration value="2809"/>
			<enumeration value="2810"/>
			<enumeration value="2811"/>
			<enumeration value="2812"/>
			<enumeration value="2813"/>
			<enumeration value="2814"/>
			<enumeration value="2815"/>
			<enumeration value="2816"/>
			<enumeration value="2817"/>
			<enumeration value="2818"/>
			<enumeration value="2819"/>
			<enumeration value="2820"/>
			<enumeration value="2821"/>
			<enumeration value="2822"/>
			<enumeration value="2901"/>
			<enumeration value="2902"/>
			<enumeration value="2903"/>
			<enumeration value="2904"/>
			<enumeration value="2905"/>
			<enumeration value="2906"/>
			<enumeration value="2907"/>
			<enumeration value="2908"/>
			<enumeration value="2909"/>
			<enumeration value="3001"/>
			<enumeration value="3002"/>
			<enumeration value="3003"/>
			<enumeration value="3004"/>
			<enumeration value="3005"/>
			<enumeration value="3006"/>
			<enumeration value="3007"/>
			<enumeration value="3008"/>
			<enumeration value="3009"/>
			<enumeration value="3010"/>
			<enumeration value="3011"/>
			<enumeration value="3012"/>
			<enumeration value="3013"/>
			<enumeration value="3014"/>
			<enumeration value="3015"/>
			<enumeration value="3016"/>
			<enumeration value="3017"/>
			<enumeration value="3018"/>
			<enumeration value="3019"/>
			<enumeration value="3020"/>
			<enumeration value="3021"/>
			<enumeration value="3022"/>
			<enumeration value="3023"/>
			<enumeration value="3024"/>
			<enumeration value="3025"/>
			<enumeration value="3026"/>
			<enumeration value="3027"/>
			<enumeration value="3028"/>
			<enumeration value="3029"/>
			<enumeration value="3030"/>
			<enumeration value="3031"/>
			<enumeration value="3032"/>
			<enumeration value="3033"/>
			<enumeration value="3034"/>
			<enumeration value="3035"/>
			<enumeration value="3036"/>
			<enumeration value="3037"/>
			<enumeration value="3038"/>
			<enumeration value="3039"/>
			<enumeration value="3040"/>
			<enumeration value="3041"/>
			<enumeration value="3042"/>
			<enumeration value="3043"/>
			<enumeration value="3044"/>
			<enumeration value="3045"/>
			<enumeration value="3046"/>
			<enumeration value="3047"/>
			<enumeration value="3048"/>
			<enumeration value="3049"/>
			<enumeration value="3050"/>
			<enumeration value="3051"/>
			<enumeration value="3052"/>
			<enumeration value="3053"/>
			<enumeration value="3054"/>
			<enumeration value="3055"/>
			<enumeration value="3056"/>
			<enumeration value="3057"/>
			<enumeration value="3058"/>
			<enumeration value="3059"/>
			<enumeration value="3060"/>
			<enumeration value="3061"/>
			<enumeration value="3062"/>
			<enumeration value="3063"/>
			<enumeration value="3064"/>
			<enumeration value="3065"/>
			<enumeration value="3066"/>
			<enumeration value="3067"/>
			<enumeration value="3068"/>
			<enumeration value="3069"/>
			<enumeration value="3070"/>
			<enumeration value="3071"/>
			<enumeration value="3072"/>
			<enumeration value="3073"/>
			<enumeration value="3074"/>
			<enumeration value="3075"/>
			<enumeration value="3076"/>
			<enumeration value="3077"/>
			<enumeration value="3078"/>
			<enumeration value="3079"/>
			<enumeration value="3080"/>
			<enumeration value="3081"/>
			<enumeration value="3082"/>
			<enumeration value="3083"/>
			<enumeration value="3084"/>
			<enumeration value="3085"/>
			<enumeration value="3086"/>
			<enumeration value="3087"/>
			<enumeration value="3088"/>
			<enumeration value="3089"/>
			<enumeration value="3090"/>
			<enumeration value="3091"/>
			<enumeration value="3092"/>
			<enumeration value="3093"/>
			<enumeration value="3094"/>
			<enumeration value="3095"/>
			<enumeration value="3096"/>
			<enumeration value="3097"/>
			<enumeration value="3098"/>
			<enumeration value="3100"/>
			<enumeration value="3101"/>
			<enumeration value="3102"/>
			<enumeration value="3103"/>
			<enumeration value="3104"/>
			<enumeration value="3105"/>
			<enumeration value="3106"/>
			<enumeration value="3107"/>
			<enumeration value="3108"/>
			<enumeration value="3109"/>
			<enumeration value="3110"/>
			<enumeration value="3111"/>
			<enumeration value="3112"/>
			<enumeration value="3113"/>
			<enumeration value="3114"/>
			<enumeration value="3115"/>
			<enumeration value="3116"/>
			<enumeration value="3117"/>
			<enumeration value="3118"/>
			<enumeration value="3119"/>
			<enumeration value="3120"/>
			<enumeration value="3121"/>
			<enumeration value="3122"/>
			<enumeration value="3123"/>
			<enumeration value="3124"/>
			<enumeration value="3125"/>
			<enumeration value="3126"/>
			<enumeration value="3127"/>
			<enumeration value="3128"/>
			<enumeration value="3129"/>
			<enumeration value="3130"/>
			<enumeration value="3131"/>
			<enumeration value="3132"/>
			<enumeration value="3133"/>
			<enumeration value="3134"/>
			<enumeration value="3135"/>
			<enumeration value="3136"/>
			<enumeration value="3137"/>
			<enumeration value="3138"/>
			<enumeration value="3139"/>
			<enumeration value="3140"/>
			<enumeration value="3141"/>
			<enumeration value="3142"/>
			<enumeration value="3143"/>
			<enumeration value="3144"/>
			<enumeration value="3145"/>
			<enumeration value="3146"/>
			<enumeration value="3147"/>
			<enumeration value="3148"/>
			<enumeration value="3149"/>
			<enumeration value="3150"/>
			<enumeration value="3151"/>
			<enumeration value="3152"/>
			<enumeration value="3153"/>
			<enumeration value="3154"/>
			<enumeration value="3155"/>
			<enumeration value="3156"/>
			<enumeration value="3157"/>
			<enumeration value="3158"/>
			<enumeration value="3159"/>
			<enumeration value="3160"/>
			<enumeration value="3161"/>
			<enumeration value="3162"/>
			<enumeration value="3163"/>
			<enumeration value="3164"/>
			<enumeration value="3165"/>
			<enumeration value="3166"/>
			<enumeration value="5001"/>
			<enumeration value="5002"/>
			<enumeration value="5003"/>
			<enumeration value="5004"/>
			<enumeration value="5005"/>
			<enumeration value="5006"/>
			<enumeration value="5007"/>
			<enumeration value="5008"/>
			<enumeration value="5009"/>
			<enumeration value="5010"/>
			<enumeration value="5011"/>
			<enumeration value="5012"/>
			<enumeration value="5013"/>
			<enumeration value="5014"/>
			<enumeration value="5015"/>
			<enumeration value="5016"/>
			<enumeration value="5017"/>
			<enumeration value="5018"/>
			<enumeration value="5019"/>
			<enumeration value="5020"/>
			<enumeration value="5021"/>
			<enumeration value="5022"/>
			<enumeration value="5023"/>
			<enumeration value="5024"/>
			<enumeration value="5025"/>
			<enumeration value="5026"/>
			<enumeration value="5027"/>
			<enumeration value="5028"/>
			<enumeration value="5029"/>
			<enumeration value="5030"/>
			<enumeration value="5031"/>
			<enumeration value="5032"/>
			<enumeration value="5033"/>
			<enumeration value="5034"/>
			<enumeration value="5035"/>
			<enumeration value="5036"/>
			<enumeration value="5037"/>
			<enumeration value="5038"/>
			<enumeration value="5039"/>
			<enumeration value="5040"/>
			<enumeration value="5041"/>
			<enumeration value="5042"/>
			<enumeration value="5043"/>
			<enumeration value="5044"/>
			<enumeration value="5045"/>
			<enumeration value="5046"/>
			<enumeration value="5047"/>
			<enumeration value="5048"/>
			<enumeration value="5049"/>
			<enumeration value="5050"/>
			<enumeration value="5051"/>
			<enumeration value="5052"/>
			<enumeration value="5053"/>
			<enumeration value="5054"/>
			<enumeration value="5055"/>
			<enumeration value="5056"/>
			<enumeration value="5057"/>
			<enumeration value="5058"/>
			<enumeration value="5059"/>
			<enumeration value="5060"/>
			<enumeration value="5061"/>
			<enumeration value="5062"/>
			<enumeration value=""/>
		</restriction>
	</simpleType>
	<simpleType name="dm_etapasAutorizacao">
		<restriction base="ans:st_texto1">
			<enumeration value="1"/>
			<enumeration value="2"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoIdent">
		<restriction base="ans:st_texto2">
			<enumeration value="01"/>
			<enumeration value="02"/>
			<enumeration value="03"/>
			<enumeration value="04"/>
			<enumeration value="05"/>
			<enumeration value="06"/>
			<enumeration value="07"/>
			<enumeration value="08"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoInternacao">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
			<enumeration value="5"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoInternacaoMonitoramento">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
			<enumeration value="5"/>
			<enumeration value="6"/>
			<enumeration value="7"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoLancamento">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoQuimioterapia">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoTransacaoANS">
		<restriction base="string">
			<enumeration value="MONITORAMENTO"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoTransacaoQualidade">
		<restriction base="string">
			<enumeration value="QUALIDADE"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tipoTransacao">
		<restriction base="string">
			<enumeration value="ENVIO_LOTE_GUIAS"/>
			<enumeration value="ENVIO_ANEXO"/>
			<enumeration value="SOLIC_DEMONSTRATIVO_RETORNO"/>
			<enumeration value="SOLIC_STATUS_PROTOCOLO"/>
			<enumeration value="SOLICITACAO_PROCEDIMENTOS"/>
			<enumeration value="SOLICITA_STATUS_AUTORIZACAO"/>
			<enumeration value="VERIFICA_ELEGIBILIDADE"/>
			<enumeration value="CANCELA_GUIA"/>
			<enumeration value="COMUNICACAO_BENEFICIARIO"/>
			<enumeration value="RECURSO_GLOSA"/>
			<enumeration value="SOLIC_STATUS_RECURSO_GLOSA"/>
			<enumeration value="PROTOCOLO_RECEBIMENTO"/>
			<enumeration value="PROTOCOLO_RECEBIMENTO_ANEXO"/>
			<enumeration value="RECEBIMENTO_RECURSO_GLOSA"/>
			<enumeration value="DEMONSTRATIVO_ANALISE_CONTA"/>
			<enumeration value="DEMONSTRATIVO_PAGAMENTO"/>
			<enumeration value="DEMONSTRATIVO_ODONTOLOGIA"/>
			<enumeration value="SITUACAO_PROTOCOLO"/>
			<enumeration value="RESPOSTA_SOLICITACAO"/>
			<enumeration value="AUTORIZACAO_ODONTOLOGIA"/>
			<enumeration value="STATUS_AUTORIZACAO"/>
			<enumeration value="SITUACAO_ELEGIBILIDADE"/>
			<enumeration value="CANCELAMENTO_GUIA_RECIBO"/>
			<enumeration value="RECIBO_COMUNICACAO"/>
			<enumeration value="RESPOSTA_RECURSO_GLOSA"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_tumor">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
			<enumeration value="5"/>
			<enumeration value="6"/>
			<enumeration value="7"/>
			<enumeration value="8"/>
			<enumeration value="9"/>
		</restriction>
	</simpleType>
	<!--	

Tabela 67 - Terminologia de tumor	
	
CÃ³digo do Termo	Termo
1	T1
2	T2
3	T3
4	T4
5	T0
6	Tis
7	Tx
8	NÃ£o se aplica
9	Sem informaÃ§Ã£o
-->
	<simpleType name="dm_UF">
		<restriction base="string">
			<length value="2"/>
			<enumeration value="11"/>
			<enumeration value="12"/>
			<enumeration value="13"/>
			<enumeration value="14"/>
			<enumeration value="15"/>
			<enumeration value="16"/>
			<enumeration value="17"/>
			<enumeration value="21"/>
			<enumeration value="22"/>
			<enumeration value="23"/>
			<enumeration value="24"/>
			<enumeration value="25"/>
			<enumeration value="26"/>
			<enumeration value="27"/>
			<enumeration value="28"/>
			<enumeration value="29"/>
			<enumeration value="31"/>
			<enumeration value="32"/>
			<enumeration value="33"/>
			<enumeration value="35"/>
			<enumeration value="41"/>
			<enumeration value="42"/>
			<enumeration value="43"/>
			<enumeration value="50"/>
			<enumeration value="51"/>
			<enumeration value="52"/>
			<enumeration value="53"/>
			<enumeration value="98"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_unidadeMedida">
		<restriction base="string">
			<length value="3"/>
			<enumeration value="001"/>
			<enumeration value="002"/>
			<enumeration value="003"/>
			<enumeration value="004"/>
			<enumeration value="005"/>
			<enumeration value="006"/>
			<enumeration value="007"/>
			<enumeration value="008"/>
			<enumeration value="009"/>
			<enumeration value="010"/>
			<enumeration value="011"/>
			<enumeration value="012"/>
			<enumeration value="013"/>
			<enumeration value="014"/>
			<enumeration value="015"/>
			<enumeration value="016"/>
			<enumeration value="017"/>
			<enumeration value="018"/>
			<enumeration value="019"/>
			<enumeration value="020"/>
			<enumeration value="021"/>
			<enumeration value="022"/>
			<enumeration value="023"/>
			<enumeration value="024"/>
			<enumeration value="025"/>
			<enumeration value="026"/>
			<enumeration value="027"/>
			<enumeration value="028"/>
			<enumeration value="029"/>
			<enumeration value="030"/>
			<enumeration value="031"/>
			<enumeration value="032"/>
			<enumeration value="033"/>
			<enumeration value="034"/>
			<enumeration value="035"/>
			<enumeration value="036"/>
			<enumeration value="037"/>
			<enumeration value="038"/>
			<enumeration value="039"/>
			<enumeration value="040"/>
			<enumeration value="041"/>
			<enumeration value="042"/>
			<enumeration value="043"/>
			<enumeration value="044"/>
			<enumeration value="045"/>
			<enumeration value="046"/>
			<enumeration value="047"/>
			<enumeration value="048"/>
			<enumeration value="049"/>
			<enumeration value="050"/>
			<enumeration value="051"/>
			<enumeration value="052"/>
			<enumeration value="053"/>
			<enumeration value="054"/>
			<enumeration value="055"/>
			<enumeration value="056"/>
			<enumeration value="057"/>
			<enumeration value="058"/>
			<enumeration value="059"/>
			<enumeration value="060"/>
			<enumeration value="061"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_unidadeTempoCiclo">
		<restriction base="string">
			<length value="1"/>
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_versao">
		<restriction base="string">
			<enumeration value="4.00.00"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_versao_monitor">
		<restriction base="string">
			<enumeration value="3.05.00"/>
		</restriction>
	</simpleType>
	<!--versÃ£o 3.03 criando tabela de dominio da versao encaminhada para a operadora-->
	<simpleType name="dm_versaoPrestador">
		<restriction base="string">
			<enumeration value="001"/>
			<enumeration value="002"/>
			<enumeration value="003"/>
			<enumeration value="004"/>
			<enumeration value="005"/>
			<enumeration value="006"/>
			<enumeration value="007"/>
			<enumeration value="008"/>
			<enumeration value="009"/>
			<enumeration value="010"/>
			<enumeration value="011"/>
			<enumeration value="012"/>
			<enumeration value="013"/>
			<enumeration value="014"/>
			<enumeration value="015"/>
			<enumeration value="016"/>
			<enumeration value="017"/>
			<enumeration value="018"/>
			<enumeration value="019"/>
			<enumeration value="020"/>
			<enumeration value="021"/>
			<enumeration value="022"/>
			<enumeration value="023"/>
		</restriction>
		<!--Cod VersÃ£o   
			001	01.00.00
			002	01.01.00
			003	02.00.00
			004	02.01.01
			005	02.01.02
			006	02.01.03
			007	02.02.01
			008	02.02.02
			009	02.02.03
			010	03.00.00
			011	03.00.01
			012	03.01.00
			013	03.02.00
			014	03.02.01
			015	03.02.02
			016	03.03.00
            017 03.03.01
			018	03.03.02
			019	03.03.03
            020	03.04.00
			021	03.04.01	
			022 03.05.00
			023 04.00.00
-->
	</simpleType>
	<simpleType name="dm_viaDeAcesso">
		<restriction base="string">
			<maxLength value="1"/>
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_viaAdministracao">
		<restriction base="string">
			<maxLength value="2"/>
			<enumeration value="01"/>
			<enumeration value="02"/>
			<enumeration value="03"/>
			<enumeration value="04"/>
			<enumeration value="05"/>
			<enumeration value="06"/>
			<enumeration value="07"/>
			<enumeration value="08"/>
			<enumeration value="09"/>
			<enumeration value="10"/>
			<enumeration value="11"/>
			<enumeration value="12"/>
			<enumeration value="13"/>
			<enumeration value="14"/>
			<enumeration value="15"/>
			<enumeration value="16"/>
			<enumeration value="17"/>
			<enumeration value="18"/>
			<enumeration value="19"/>
			<enumeration value="20"/>
			<enumeration value="21"/>
			<enumeration value="22"/>
			<enumeration value="23"/>
			<enumeration value="24"/>
			<enumeration value="25"/>
			<enumeration value="26"/>
			<enumeration value="27"/>
			<enumeration value="28"/>
			<enumeration value="29"/>
			<enumeration value="30"/>
			<enumeration value="31"/>
			<enumeration value="32"/>
			<enumeration value="33"/>
			<enumeration value="34"/>
			<enumeration value="35"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_ausenciaCodValidacao">
		<restriction base="string">
			<enumeration value="01"/>
			<enumeration value="02"/>
			<enumeration value="03"/>
			<enumeration value="04"/>
			<enumeration value="05"/>
			<enumeration value="06"/>
			<enumeration value="07"/>
		</restriction>
	</simpleType>
	<!--tag incluida na versao 4.00.00-->
	<simpleType name="dm_cobEsp">
		<restriction base="string">
			<maxLength value="2"/>
			<enumeration value="01"/>
			<enumeration value="02"/>
			<enumeration value="03"/>
		</restriction>
	</simpleType>
	<!--tag incluida na versao 4.00.00-->
	<simpleType name="dm_formatoDocumento">
		<restriction base="string">
			<enumeration value="01"/>
			<enumeration value="02"/>
			<enumeration value="03"/>
			<enumeration value="04"/>
		</restriction>
	</simpleType>
	<!--tag incluida na versao 4.00.00-->
	<simpleType name="dm_regimeAtendimento">
		<restriction base="string">
			<enumeration value="01"/>
			<enumeration value="02"/>
			<enumeration value="03"/>
			<enumeration value="04"/>
			<enumeration value="05"/>
		</restriction>
	</simpleType>
	<!--tag incluida na versao 4.00.00-->
	<simpleType name="dm_saudeOcupacional">
		<restriction base="string">
			<enumeration value="01"/>
			<enumeration value="02"/>
			<enumeration value="03"/>
			<enumeration value="04"/>
			<enumeration value="05"/>
			<enumeration value="06"/>
		</restriction>
	</simpleType>
	<!--tag incluida na versao 4.00.00-->
	<simpleType name="dm_tipoPagamento">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
		</restriction>
	</simpleType>
	<!--tag incluida na versao 4.00.00-->
	<simpleType name="dm_tipoDocumento">
		<restriction base="string">
			<enumeration value="01"/>
			<enumeration value="02"/>
			<enumeration value="03"/>
			<enumeration value="04"/>
			<enumeration value="05"/>
			<enumeration value="06"/>
			<enumeration value="07"/>
			<enumeration value="08"/>
			<enumeration value="09"/>
			<enumeration value="10"/>
			<enumeration value="11"/>
			<enumeration value="12"/>
			<enumeration value="13"/>
			<enumeration value="14"/>
			<enumeration value="15"/>
			<enumeration value="16"/>
			<enumeration value="17"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_declaracaoNascidoObito">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="11"/>
			<pattern value="[a-zA-Z0-9]+"/>
		</restriction>
	</simpleType>
	<simpleType name="dm_diagnosticoCID10">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="4"/>
			<pattern value="[a-zA-Z0-9]+"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto7Padrao">
		<restriction base="string">
			<pattern value="[0-9]{1,7}"/>
		</restriction>
		<!-- NOVO -->
	</simpleType>
	<simpleType name="st_texto15Padrao">
		<restriction base="string">
			<pattern value="[0-9]{1,15}"/>
		</restriction>
		<!-- NOVO -->
	</simpleType>
	<simpleType name="st_texto20Padrao">
		<restriction base="string">
			<pattern value="[a-zA-Z0-9]{1,20}"/>
		</restriction>
		<!-- NOVO -->
	</simpleType>
	<simpleType name="st_CNPJCPFPadrao">
		<restriction base="string">
			<pattern value="[0-9]{11,14}"/>
		</restriction>
		<!-- NOVO -->
	</simpleType>
	<simpleType name="st_grupoProcedimento">
		<restriction base="string">
			<pattern value="[0-9]{3}"/>
		</restriction>
		<!-- NOVO -->
	</simpleType>
	<simpleType name="st_codigoProcedimento">
		<restriction base="string">
			<pattern value="[a-zA-Z0-9]{1,10}"/>
		</restriction>
		<!-- NOVO -->
	</simpleType>
	<simpleType name="st_dataPadrao">
		<restriction base="date">
			<minInclusive value="1850-01-01"/>
		</restriction>
		<!-- NOVO -->
	</simpleType>
	<simpleType name="st_CNPJ">
		<restriction base="string">
			<pattern value="[0-9]{14}"/>
		</restriction>
	</simpleType>
	<simpleType name="st_CPF">
		<restriction base="string">
			<pattern value="[0-9]{11}"/>
		</restriction>
	</simpleType>
	<simpleType name="st_competencia">
		<restriction base="string">
			<pattern value="[0-9]{4}[0-9]{2}"/>
		</restriction>
		<!--formato ano/mes -->
	</simpleType>
	<simpleType name="st_data">
		<restriction base="date"/>
	</simpleType>
	<simpleType name="st_dataHora">
		<restriction base="dateTime"/>
	</simpleType>
	<simpleType name="st_decimal3-2">
		<restriction base="decimal">
			<totalDigits value="3"/>
			<fractionDigits value="2"/>
		</restriction>
	</simpleType>
	<simpleType name="st_decimal4-2">
		<restriction base="decimal">
			<totalDigits value="4"/>
			<fractionDigits value="2"/>
		</restriction>
	</simpleType>
	<simpleType name="st_decimal5-2">
		<restriction base="decimal">
			<totalDigits value="5"/>
			<fractionDigits value="2"/>
		</restriction>
	</simpleType>
	<simpleType name="st_decimal7-2">
		<restriction base="decimal">
			<totalDigits value="7"/>
			<fractionDigits value="2"/>
		</restriction>
	</simpleType>
	<simpleType name="st_decimal8-2">
		<restriction base="decimal">
			<totalDigits value="8"/>
			<fractionDigits value="2"/>
		</restriction>
	</simpleType>
	<simpleType name="st_decimal9-2">
		<restriction base="decimal">
			<totalDigits value="9"/>
			<fractionDigits value="2"/>
		</restriction>
	</simpleType>
	<simpleType name="st_decimal7-4">
		<restriction base="decimal">
			<totalDigits value="7"/>
			<fractionDigits value="4"/>
		</restriction>
	</simpleType>
	<simpleType name="st_decimal8-4">
		<restriction base="decimal">
			<totalDigits value="8"/>
			<fractionDigits value="4"/>
		</restriction>
	</simpleType>
	<simpleType name="st_decimal10-2">
		<restriction base="decimal">
			<fractionDigits value="2"/>
			<totalDigits value="10"/>
		</restriction>
	</simpleType>
	<simpleType name="st_decimal12-2">
		<restriction base="decimal">
			<totalDigits value="12"/>
			<fractionDigits value="2"/>
		</restriction>
	</simpleType>
	<simpleType name="st_decimal9-4">
		<restriction base="decimal">
			<totalDigits value="9"/>
			<fractionDigits value="4"/>
		</restriction>
	</simpleType>
	<simpleType name="st_decimal12-4">
		<restriction base="decimal">
			<totalDigits value="12"/>
			<fractionDigits value="4"/>
		</restriction>
	</simpleType>
	<simpleType name="st_hora">
		<restriction base="time"/>
	</simpleType>
	<simpleType name="st_logico">
		<restriction base="boolean"/>
	</simpleType>
	<simpleType name="st_numerico2">
		<restriction base="integer">
			<totalDigits value="2"/>
		</restriction>
	</simpleType>
	<simpleType name="st_numerico3">
		<restriction base="integer">
			<totalDigits value="3"/>
		</restriction>
	</simpleType>
	<simpleType name="st_numerico4">
		<restriction base="integer">
			<totalDigits value="4"/>
		</restriction>
	</simpleType>
	<simpleType name="st_numerico5">
		<restriction base="integer">
			<totalDigits value="5"/>
		</restriction>
	</simpleType>
	<simpleType name="st_numerico8">
		<restriction base="integer">
			<totalDigits value="8"/>
		</restriction>
	</simpleType>
	<simpleType name="st_numerico12">
		<restriction base="integer">
			<totalDigits value="12"/>
		</restriction>
	</simpleType>
	<simpleType name="st_registroANS">
		<restriction base="string">
			<maxLength value="6"/>
			<pattern value="[0-9]{6}"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto1">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="1"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto2">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="2"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto3">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="3"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto4">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="4"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto5">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="5"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto6">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="6"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto7">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="7"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto8">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="8"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto10">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="10"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto11">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="11"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto12">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="12"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto14">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="14"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto15">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="15"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto20">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="20"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto30">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="30"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto32">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="32"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto40">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="40"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto60">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="60"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto70">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="70"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto100">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="100"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto150">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="150"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto500">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="500"/>
		</restriction>
	</simpleType>
	<simpleType name="st_texto1000">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="1000"/>
		</restriction>
	</simpleType>
	<simpleType name="st_tissFault">
		<restriction base="string">
			<enumeration value="DestinatarioInvalido"/>
			<enumeration value="RemetenteInvalido"/>
			<enumeration value="LoginInvalido"/>
			<enumeration value="VersaoInvalida"/>
			<enumeration value="HashInvalido"/>
			<enumeration value="SchemaInvalido"/>
			<enumeration value="ErroInesperadoServidor"/>
		</restriction>
	</simpleType>
	<!--VERSÃ?O TISS 4.00.00 - TissComplexTypesV4_00_00-->
	<!--<include schemaLocation="http://www.ans.gov.br/padroes/tiss/schemas/tissSimpleTypesV3_04_01.xsd"/>-->
	<complexType name="ct_anexoCabecalho">
		<sequence>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="numeroGuiaAnexo" type="ans:st_texto20"/>
			<element name="numeroGuiaReferenciada" type="ans:st_texto20"/>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="dataSolicitacao" type="ans:st_data"/>
			<element name="senha" type="ans:st_texto20" minOccurs="0"/>
			<element name="dataAutorizacao" type="ans:st_data" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ct_anexoRecebimento">
		<annotation>
			<documentation> estrutura de recibo do recebimento de um lote de anexos dos prestadores</documentation>
		</annotation>
		<sequence>
			<element name="nrProtocoloRecebimento" type="ans:st_texto12"/>
			<element name="dataEnvioAnexo" type="ans:st_data"/>
			<element name="numeroLote" type="ans:st_texto12"/>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="dadosPrestador" type="ans:ct_contratadoDados"/>
			<element name="qtAnexosClinicos" type="ans:st_numerico3"/>
			<element name="anexosClinicos">
				<complexType>
					<sequence>
						<choice>
							<element name="anexoOPME" type="ans:ctm_autorizacaoOPME"/>
							<element name="anexoQuimio" type="ans:ctm_autorizacaoQuimio"/>
							<element name="anexoRadio" type="ans:ctm_autorizacaoRadio"/>
							<element name="anexoSituacaoInicial" maxOccurs="100">
								<complexType>
									<complexContent>
										<extension base="ans:cto_anexoSituacaoInicial">
											<sequence>
												<element name="nomeBeneficiario" type="ans:st_texto70"/>
												<element name="nomeSocialBeneficiario" type="ans:st_texto70" minOccurs="0"/>
											</sequence>
										</extension>
									</complexContent>
								</complexType>
							</element>
						</choice>
					</sequence>
				</complexType>
			</element>
			<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ct_autorizacaoDados">
		<sequence>
			<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="ausenciaCodValidacao" type="ans:dm_ausenciaCodValidacao" minOccurs="0"/>
			<element name="codValidacao" type="ans:st_texto10" minOccurs="0"/>
			<element name="dataAutorizacao" type="ans:st_data" minOccurs="0"/>
			<element name="senha" type="ans:st_texto20" minOccurs="0"/>
			<element name="dataValidadeSenha" type="ans:st_data" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ct_autorizacaoSADT">
		<sequence>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="dataAutorizacao" type="ans:st_data"/>
			<element name="senha" type="ans:st_texto20" minOccurs="0"/>
			<element name="dataValidadeSenha" type="ans:st_data" minOccurs="0"/>
			<element name="ausenciaCodValidacao" type="ans:dm_ausenciaCodValidacao" minOccurs="0"/>
			<element name="codValidacao" type="ans:st_texto10" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ct_autorizacaoInternacao">
		<sequence>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="dataAutorizacao" type="ans:st_data"/>
			<element name="senha" type="ans:st_texto20"/>
			<element name="dataValidadeSenha" type="ans:st_data" minOccurs="0"/>
			<element name="ausenciaCodValidacao" type="ans:dm_ausenciaCodValidacao" minOccurs="0"/>
			<element name="codValidacao" type="ans:st_texto10" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ct_autorizacaoSolicitaStatus">
		<sequence>
			<element name="identificacaoSolicitacao" type="ans:ct_guiaCabecalho"/>
			<element name="dadosBeneficiario" type="ans:ct_beneficiarioDados"/>
			<element name="dadosContratado" type="ans:ct_contratadoDados"/>
		</sequence>
	</complexType>
	<complexType name="ct_beneficiarioDados">
		<sequence>
			<element name="numeroCarteira" type="ans:st_texto20"/>
			<element name="atendimentoRN" type="ans:dm_simNao"/>
			<element name="tipoIdent" type="ans:dm_tipoIdent" minOccurs="0"/>
			<element name="identificadorBeneficiario" type="base64Binary" minOccurs="0"/>
			<!-- retirados na versÃ£o 4.00.00
			<element name="nomeBeneficiario" type="ans:st_texto70"/>
			<element name="numeroCNS" type="ans:st_texto15" minOccurs="0"/>
			<element name="templateBiometrico" type="base64Binary" minOccurs="0"/>
			-->
		</sequence>
	</complexType>
	<complexType name="ct_contaMedicaResumo">
		<annotation>
			<documentation>utilizado no demonstrativo de anÃ¡lise de conta</documentation>
		</annotation>
		<sequence>
			<element name="numeroLotePrestador" type="ans:st_texto12"/>
			<element name="numeroProtocolo" type="ans:st_texto12"/>
			<element name="dataProtocolo" type="ans:st_data"/>
			<element name="GlosaProtocolo" type="ans:ct_motivoGlosa" minOccurs="0"/>
			<element name="situacaoProtocolo" type="ans:dm_statusProtocolo"/>
			<element name="relacaoGuias" minOccurs="0" maxOccurs="unbounded">
				<complexType>
					<sequence>
						<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
						<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
						<element name="senha" type="ans:st_texto20" minOccurs="0"/>
						<!-- retirado na versÃ£o 4.00.00
						<element name="nomeBeneficiario" type="ans:st_texto70"/>
						-->
						<element name="numeroCarteira" type="ans:st_texto20"/>
						<element name="dataInicioFat" type="ans:st_data"/>
						<element name="horaInicioFat" type="ans:st_hora" minOccurs="0"/>
						<element name="dataFimFat" type="ans:st_data" minOccurs="0"/>
						<element name="horaFimFat" type="ans:st_hora" minOccurs="0"/>
						<element name="motivoGlosaGuia" type="ans:ct_motivoGlosa" minOccurs="0" maxOccurs="unbounded"/>
						<element name="situacaoGuia" type="ans:dm_statusProtocolo"/>
						<element name="detalhesGuia" minOccurs="0" maxOccurs="unbounded">
							<complexType>
								<sequence>
									<element name="sequencialItem" type="ans:st_numerico4"/>
									<element name="dataRealizacao" type="ans:st_data"/>
									<element name="procedimento" type="ans:ct_procedimentoDados"/>
									<element name="grauParticipacao" type="ans:dm_grauPart" minOccurs="0"/>
									<element name="valorInformado" type="ans:st_decimal8-2"/>
									<element name="qtdExecutada" type="ans:st_decimal9-4"/>
									<element name="valorProcessado" type="ans:st_decimal8-2"/>
									<element name="valorLiberado" type="ans:st_decimal8-2"/>
									<element name="relacaoGlosa" minOccurs="0" maxOccurs="unbounded">
										<complexType>
											<sequence>
												<element name="valorGlosa" type="ans:st_decimal8-2"/>
												<element name="tipoGlosa" type="ans:dm_tipoGlosa"/>
											</sequence>
										</complexType>
									</element>
								</sequence>
							</complexType>
						</element>
						<!--                                                                                   TOTAIS DA GUIA -->
						<element name="valorInformadoGuia" type="ans:st_decimal10-2"/>
						<element name="valorProcessadoGuia" type="ans:st_decimal10-2"/>
						<element name="valorLiberadoGuia" type="ans:st_decimal10-2"/>
						<element name="valorGlosaGuia" type="ans:st_decimal10-2" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
			<!--                                       TOTAIS DO PROTOCOLO -->
			<element name="valorInformadoProtocolo" type="ans:st_decimal10-2"/>
			<element name="valorProcessadoProtocolo" type="ans:st_decimal10-2"/>
			<element name="valorLiberadoProtocolo" type="ans:st_decimal10-2"/>
			<element name="valorGlosaProtocolo" type="ans:st_decimal10-2" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ct_contratadoDados">
		<choice>
			<element name="codigoPrestadorNaOperadora" type="ans:st_texto14"/>
			<element name="cpfContratado" type="ans:st_CPF"/>
			<element name="cnpjContratado" type="ans:st_CNPJ"/>
		</choice>
		<!--<sequence>-->
		<!-- retirado na versÃ£o 4.00.00
			<element name="nomeContratado" type="ans:st_texto70"/>
			-->
		<!--</sequence>-->
	</complexType>
	<complexType name="ct_contratadoDadosNome">
		<sequence>
			<choice>
				<element name="codigoPrestadorNaOperadora" type="ans:st_texto14"/>
				<element name="cpfContratado" type="ans:st_CPF"/>
				<element name="cnpjContratado" type="ans:st_CNPJ"/>
			</choice>
			<element name="nomeContratado" type="ans:st_texto70"/>
		</sequence>
	</complexType>
	<complexType name="ct_contratadoProfissionalDados">
		<sequence>
			<element name="nomeProfissional" type="ans:st_texto70" minOccurs="0"/>
			<element name="conselhoProfissional" type="ans:dm_conselhoProfissional"/>
			<element name="numeroConselhoProfissional" type="ans:st_texto15"/>
			<element name="UF" type="ans:dm_UF"/>
			<element name="CBOS" type="ans:dm_CBOS"/>
		</sequence>
	</complexType>
	<complexType name="ct_creditoOdonto">
		<sequence>
			<element name="valorCredito" type="ans:st_decimal8-2"/>
			<element name="descricao" type="ans:st_texto40"/>
		</sequence>
	</complexType>
	<complexType name="ct_dadosResumoDemonstrativo">
		<sequence>
			<element name="dataProtocolo" type="ans:st_data"/>
			<element name="numeroProtocolo" type="ans:st_texto12"/>
			<element name="numeroLote" type="ans:st_texto12"/>
			<element name="valorInformado" type="ans:st_decimal10-2"/>
			<element name="valorProcessado" type="ans:st_decimal10-2"/>
			<element name="valorLiberado" type="ans:st_decimal10-2"/>
			<element name="valorGlosa" type="ans:st_decimal10-2" minOccurs="0"/>
			<element name="guiasDoLote" maxOccurs="100">
				<complexType>
					<sequence>
						<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
						<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
						<element name="senha" type="ans:st_texto20" minOccurs="0"/>
						<element name="tipoPagamento" type="ans:dm_tipoPagamento"/>
						<element name="valorProcessadoGuia" type="ans:st_decimal10-2"/>
						<element name="valorLiberadoGuia" type="ans:st_decimal10-2"/>
						<element name="valorGlosaGuia" type="ans:st_decimal10-2" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_dadosComplementaresBeneficiario">
		<sequence>
			<element name="peso" type="ans:st_decimal5-2"/>
			<element name="altura" type="ans:st_decimal5-2"/>
			<element name="superficieCorporal" type="ans:st_decimal4-2"/>
			<element name="idade" type="ans:st_numerico3"/>
			<element name="sexo" type="ans:dm_sexo"/>
		</sequence>
	</complexType>
	<complexType name="ct_dadosComplementaresBeneficiarioRadio">
		<sequence>
			<element name="idade" type="ans:st_numerico3"/>
			<element name="sexo" type="ans:dm_sexo"/>
		</sequence>
	</complexType>
	<complexType name="ct_demonstrativoCabecalho">
		<sequence>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="numeroDemonstrativo" type="ans:st_texto20"/>
			<element name="nomeOperadora" type="ans:st_texto70"/>
			<element name="numeroCNPJ" type="ans:st_CNPJ"/>
			<element name="dataEmissao" type="ans:st_data"/>
		</sequence>
	</complexType>
	<complexType name="ct_demonstrativoRetorno">
		<choice>
			<element name="mensagemErro" type="ans:ct_motivoGlosa"/>
			<element name="demonstrativoAnaliseConta" type="ans:ctm_demonstrativoAnaliseConta" maxOccurs="30"/>
			<element name="demonstrativoPagamento" type="ans:ctm_demonstrativoPagamento"/>
			<element name="demonstrativoPagamentoOdonto" type="ans:cto_demonstrativoOdontologia"/>
			<element name="situacaoDemonstrativoRetorno">
				<complexType>
					<sequence>
						<element name="identificacaoOperadora" type="ans:st_registroANS"/>
						<element name="dadosPrestador" type="ans:ct_contratadoDados"/>
						<element name="numeroProtocolo" type="ans:st_texto12"/>
						<element name="protocoloSolicitacaoDemonstrativo" type="ans:st_texto12"/>
						<element name="tipoDemonstrativo" type="ans:dm_tipoDemonstrativo"/>
						<element name="dataSituacaoDemonstrativo" type="ans:st_data"/>
						<element name="situacaoDemonstrativo" type="ans:dm_statusProtocolo"/>
					</sequence>
				</complexType>
			</element>
		</choice>
	</complexType>
	<complexType name="ct_demonstrativoSolicitacao">
		<annotation>
			<documentation>estrutura para solicitaÃ§Ã£o de demonstrativo de pagamento</documentation>
		</annotation>
		<choice>
			<element name="demonstrativoPagamento">
				<complexType>
					<sequence>
						<element name="dadosPrestador" type="ans:ct_contratadoDados"/>
						<element name="dataSolicitacao" type="ans:st_data"/>
						<element name="tipoDemonstrativo" type="ans:dm_tipoDemonstrativoPagamento"/>
						<element name="periodo">
							<complexType>
								<choice>
									<element name="dataPagamento" type="ans:st_data"/>
									<element name="competencia" type="ans:st_competencia"/>
								</choice>
							</complexType>
						</element>
					</sequence>
				</complexType>
			</element>
			<element name="demonstrativoAnalise">
				<complexType>
					<sequence>
						<element name="dadosPrestador" type="ans:ct_contratadoDados"/>
						<element name="dataSolicitacao" type="ans:st_data"/>
						<element name="protocolos">
							<complexType>
								<sequence>
									<element name="numeroProtocolo" type="ans:st_texto12" maxOccurs="30"/>
								</sequence>
							</complexType>
						</element>
					</sequence>
				</complexType>
			</element>
		</choice>
	</complexType>
	<complexType name="ct_diagnostico">
		<sequence>
			<element name="tabelaDiagnostico" type="ans:dm_tabelasDiagnostico"/>
			<element name="codigoDiagnostico" type="ans:st_texto4"/>
			<element name="descricaoDiagnostico" type="ans:st_texto150"/>
		</sequence>
	</complexType>
	<complexType name="ct_diagnosticoOncologico">
		<sequence>
			<element name="dataDiagnostico" type="ans:st_data" minOccurs="0"/>
			<element name="diagnosticoCID" type="ans:dm_diagnosticoCID10" minOccurs="0" maxOccurs="4"/>
			<element name="estadiamento" type="ans:dm_estadiamento"/>
			<element name="finalidade" type="ans:dm_finalidadeTratamento"/>
			<element name="ecog" type="ans:dm_ecog"/>
			<element name="diagnosticoHispatologico" type="ans:st_texto1000" minOccurs="0"/>
			<element name="infoRelevantes" type="ans:st_texto1000" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ct_descontos">
		<sequence>
			<element name="indicador" type="ans:dm_debitoCreditoIndicador"/>
			<element name="tipoDebitoCredito" type="ans:dm_debitoCreditoTipo"/>
			<element name="descricaoDbCr" type="ans:st_texto40"/>
			<element name="valorDbCr" type="ans:st_decimal8-2"/>
		</sequence>
	</complexType>
	<complexType name="ct_drogasSolicitadas">
		<sequence>
			<element name="dataProvavel" type="ans:st_data"/>
			<element name="identificacao" type="ans:ct_procedimentoDados"/>
			<element name="qtDoses" type="ans:st_decimal7-2"/>
			<element name="unidadeMedida" type="ans:dm_unidadeMedida"/>
			<element name="viaAdministracao" type="ans:dm_viaAdministracao"/>
			<element name="frequencia" type="ans:st_numerico2"/>
		</sequence>
	</complexType>
	<complexType name="ct_elegibilidadeRecibo">
		<sequence>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="numeroCarteira" type="ans:st_texto20"/>
			<element name="ausenciaCodValidacao" type="ans:dm_ausenciaCodValidacao" minOccurs="0"/>
			<element name="codValidacao" type="ans:st_texto10" minOccurs="0"/>
			<element name="validadeCarteira" type="ans:st_data" minOccurs="0"/>
			<element name="nomeBeneficiario" type="ans:st_texto70"/>
			<!-- retirado na versÃ£o 4.00.00
			<element name="numeroCNS" type="ans:st_texto15" minOccurs="0"/>
			-->
			<element name="nomeSocialBeneficiario" type="ans:st_texto70" minOccurs="0"/>
			<element name="tipoIdent" type="ans:dm_tipoIdent" minOccurs="0"/>
			<element name="identificadorBeneficiario" type="base64Binary" minOccurs="0"/>
			<!-- retirado na versao 4.00.00
			<element name="templateBiometrico" type="base64Binary" minOccurs="0"/>
			-->
			<element name="respostaSolicitacao" type="ans:dm_simNao"/>
			<element name="motivosNegativa" minOccurs="0">
				<complexType>
					<sequence>
						<element name="motivoNegativa" type="ans:ct_motivoGlosa" maxOccurs="unbounded"/>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_glosaRecibo">
		<annotation>
			<documentation>recibo de recurso de glosa</documentation>
		</annotation>
		<sequence>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="numeroGuiaRecGlosaPrestador" type="ans:st_texto20"/>
			<element name="numeroGuiaRecGlosaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="nomeOperadora" type="ans:st_texto70"/>
			<element name="objetoRecurso" type="ans:dm_objetoRecurso"/>
			<element name="codigoPrestador" type="ans:st_texto14"/>
			<element name="numeroLote" type="ans:st_numerico12"/>
			<element name="numeroProtocolo" type="ans:st_numerico12"/>
			<element name="opcaoRecurso">
				<complexType>
					<choice>
						<element name="recursoProtocolo">
							<complexType>
								<sequence>
									<element name="codigoGlosaProtocolo" type="ans:dm_tipoGlosa"/>
									<element name="justificativaProtocolo" type="ans:st_texto500"/>
									<element name="recursoAcatado" type="ans:dm_simNao"/>
									<element name="justificativaOPSnaoAcatadoProt" type="ans:st_texto500" minOccurs="0"/>
								</sequence>
							</complexType>
						</element>
						<element name="recursoGuia" maxOccurs="unbounded">
							<complexType>
								<choice>
									<element name="respostaGuia" type="ans:ct_respostaGlosaGuiaMedica"/>
									<element name="respostaGuiaItens" type="ans:ct_respostaGlosaItemMedico"/>
								</choice>
							</complexType>
						</element>
					</choice>
				</complexType>
			</element>
			<element name="dataRecurso" type="ans:st_data"/>
			<element name="valorTotalRecursado" type="ans:st_decimal10-2"/>
			<element name="valorTotalAcatado" type="ans:st_decimal10-2"/>
		</sequence>
	</complexType>
	<complexType name="ct_glosaReciboOdonto">
		<annotation>
			<documentation>retorno do recurso de glosa de odonto</documentation>
		</annotation>
		<sequence>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="numeroGuiaRecGlosaPrestador" type="ans:st_texto20"/>
			<element name="nomeOperadora" type="ans:st_texto70"/>
			<element name="numeroGuiaRecGlosaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="objetoRecurso" type="ans:dm_objetoRecurso"/>
			<element name="codigoPrestador" type="ans:st_texto14"/>
			<element name="numeroLote" type="ans:st_numerico12"/>
			<element name="numeroProtocolo" type="ans:st_numerico12"/>
			<element name="opcaoRecurso">
				<complexType>
					<choice>
						<element name="recursoProtocolo">
							<complexType>
								<sequence>
									<element name="codigoGlosaProtocolo" type="ans:dm_tipoGlosa"/>
									<element name="justificativaProtocolo" type="ans:st_texto500"/>
									<element name="recursoAcatado" type="ans:dm_simNao"/>
								</sequence>
							</complexType>
						</element>
						<element name="recursoGuia" maxOccurs="unbounded">
							<complexType>
								<choice>
									<element name="respostaRecursoGuiaOdonto" type="ans:ct_respostaRecursoGuiaOdonto"/>
									<element name="respostaRecursoItemOdonto" type="ans:ct_respostaRecursoItemOdonto"/>
								</choice>
							</complexType>
						</element>
					</choice>
				</complexType>
			</element>
			<element name="dataRecurso" type="ans:st_data"/>
			<element name="valorTotalRecursado" type="ans:st_decimal10-2"/>
			<element name="valorTotalAcatado" type="ans:st_decimal10-2"/>
		</sequence>
	</complexType>
	<complexType name="ct_elegibilidadeVerifica">
		<sequence>
			<element name="dadosPrestador" type="ans:ct_contratadoDados"/>
			<element name="numeroCarteira" type="ans:st_texto20"/>
			<!-- retirado na versÃ£o 4.00.00
			<element name="nomeBeneficiario" type="ans:st_texto70"/>
			-->
			<!-- retirado na versÃ£o 4.00.00
			<element name="numeroCNS" type="ans:st_texto15" minOccurs="0"/>
			-->
			<element name="tipoIdent" type="ans:dm_tipoIdent" minOccurs="0"/>
			<element name="identificadorBeneficiario" type="base64Binary" minOccurs="0"/>
			<!-- retirado na versao 4.00.00
			<element name="templateBiometrico" type="base64Binary" minOccurs="0"/>
			-->
			<element name="validadeCarteira" type="ans:st_data" minOccurs="0"/>
			<element name="ausenciaCodValidacao" type="ans:dm_ausenciaCodValidacao" minOccurs="0"/>
			<element name="codValidacao" type="ans:st_texto10" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ct_fontePagadora">
		<choice>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="identificacaoUnidadePagadora" type="ans:st_CNPJ"/>
		</choice>
	</complexType>
	<complexType name="ct_guiaCabecalho">
		<sequence>
			<element name="registroANS" type="ans:st_registroANS"/>
			<!--<element name="fontePagadora" type="ans:ct_fontePagadora"/>-->
			<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
			<!--<element minOccurs="0" name="numeroGuiaOperadora" type="ans:st_texto20"/>-->
		</sequence>
	</complexType>
	<complexType name="ct_guiaCancelamento">
		<sequence>
			<element name="dadosPrestador" type="ans:ct_contratadoDados"/>
			<element name="tipoCancelamento">
				<complexType>
					<choice>
						<element name="tipoCancelamentoLote">
							<complexType>
								<sequence>
									<element name="numeroLote" type="ans:st_texto12"/>
									<element name="numeroProtocolo" type="ans:st_texto12" minOccurs="0"/>
								</sequence>
							</complexType>
						</element>
						<element name="tipoCancelamentoGuia" maxOccurs="unbounded">
							<complexType>
								<choice>
									<element name="tipoGuia" type="ans:dm_tipoGuia"/>
									<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
									<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
									<element name="numeroProtocolo" type="ans:st_texto12" minOccurs="0"/>
								</choice>
							</complexType>
						</element>
					</choice>
				</complexType>
			</element>
			<!--<element name="dadosGuia" type="ans:ct_guiaCabecalho" />-->
		</sequence>
	</complexType>
	<complexType name="ct_guiaCancelamentoRecibo">
		<sequence>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="dadosPrestador" type="ans:ct_contratadoDados"/>
			<element name="retornoStatus">
				<complexType>
					<choice>
						<element name="loteCancelado">
							<complexType>
								<sequence>
									<element name="numeroLote" type="ans:st_texto12"/>
									<element name="numeroprotocolo" type="ans:st_texto12" minOccurs="0"/>
									<element name="statusCancelamento" type="ans:dm_statusCancelamento"/>
								</sequence>
							</complexType>
						</element>
						<element name="guiasCanceladas">
							<complexType>
								<sequence>
									<element name="dadosGuia" maxOccurs="unbounded">
										<complexType>
											<sequence>
												<element name="tipoGuia" type="ans:dm_tipoGuia"/>
												<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
												<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
												<element name="numeroProtocolo" type="ans:st_texto12" minOccurs="0"/>
												<element name="statusCancelamento" type="ans:dm_statusCancelamento"/>
											</sequence>
										</complexType>
									</element>
								</sequence>
							</complexType>
						</element>
					</choice>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_guiaDados">
		<sequence>
			<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="ausenciaCodValidacao" type="ans:dm_ausenciaCodValidacao" minOccurs="0"/>
			<element name="codValidacao" type="ans:st_texto10" minOccurs="0"/>
			<element name="dadosBeneficiario" type="ans:ct_beneficiarioDados"/>
			<element name="dataRealizacao" type="ans:st_data" minOccurs="0"/>
			<element name="vlInformadoGuia" type="ans:ct_valorTotal" minOccurs="0"/>
			<element name="glosaGuia" minOccurs="0">
				<complexType>
					<sequence>
						<element name="motivoGlosa" type="ans:ct_motivoGlosa" maxOccurs="unbounded"/>
					</sequence>
				</complexType>
			</element>
			<element name="procedimentosRealizados" minOccurs="0">
				<complexType>
					<sequence>
						<element name="procedimentoRealizado" maxOccurs="unbounded">
							<complexType>
								<complexContent>
									<extension base="ans:ct_procedimentoExecutado">
										<sequence>
											<element name="glosasProcedimento" minOccurs="0">
												<complexType>
													<sequence>
														<element name="motivoGlosa" type="ans:ct_motivoGlosa" maxOccurs="unbounded"/>
														<element name="valorGlosaProcedimento" type="ans:st_decimal10-2"/>
													</sequence>
												</complexType>
											</element>
										</sequence>
									</extension>
								</complexContent>
							</complexType>
						</element>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_guiaDadosAnexo">
		<sequence>
			<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="dadosBeneficiario" type="ans:ct_beneficiarioDados"/>
			<element name="dataEmissao_SolicitacaoAnexo" type="ans:st_data"/>
			<element name="vlInformadoGuia" type="ans:ct_valorTotal"/>
			<element name="glosaAnexo" minOccurs="0">
				<complexType>
					<sequence>
						<element name="motivoGlosa" type="ans:ct_motivoGlosa" maxOccurs="unbounded"/>
						<element name="vlGlosaAnexo" type="ans:st_decimal10-2"/>
					</sequence>
				</complexType>
			</element>
			<element name="procedimentosSolicitados" minOccurs="0">
				<complexType>
					<sequence>
						<element name="procedimentoSolicitado" maxOccurs="unbounded">
							<complexType>
								<sequence>
									<element name="procedimento" type="ans:ct_procedimentoDados"/>
									<element name="opcaoFabricante" type="ans:dm_opcaoFabricante" minOccurs="0"/>
									<element name="qtdSolicitada" type="ans:st_decimal5-2"/>
									<element name="valorSolicitado" type="ans:st_decimal8-2" minOccurs="0"/>
									<element name="qtdAutorizada" type="ans:st_decimal5-2"/>
									<element name="valorAutorizado" type="ans:st_decimal8-2"/>
									<element name="glosasProcedimento">
										<complexType>
											<sequence>
												<element name="motivoGlosa" type="ans:ct_motivoGlosa" maxOccurs="unbounded"/>
												<element name="valorGlosaProcedimento" type="ans:st_decimal10-2"/>
											</sequence>
										</complexType>
									</element>
								</sequence>
							</complexType>
						</element>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_guiaDadosOdonto">
		<sequence>
			<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="ausenciaCodValidacao" type="ans:dm_ausenciaCodValidacao" minOccurs="0"/>
			<element name="codValidacao" type="ans:st_texto10" minOccurs="0"/>
			<element name="numeroCarteira" type="ans:st_texto20"/>
			<element name="atendimentoRN" type="ans:dm_simNao"/>
			<!-- retirado na versÃ£o 4.00.00
			<element name="nomeBeneficiario" type="ans:st_texto70"/>
			-->
			<!-- retirado na versÃ£o 4.00.00
			<element name="numeroCNS" type="ans:st_texto15" minOccurs="0"/>
			-->
			<element name="tipoIdent" type="ans:dm_tipoIdent" minOccurs="0"/>
			<element name="identificadorBeneficiario" type="base64Binary" minOccurs="0"/>
			<!-- retirado na versÃ£o 4.00.00
			<element name="templateBiometrico" type="base64Binary" minOccurs="0"/>
			-->
			<element name="vlInformadoGuia" type="ans:ct_valorTotal" minOccurs="0"/>
			<element name="glosaGuia" minOccurs="0">
				<complexType>
					<sequence>
						<element name="motivoGlosa" type="ans:ct_motivoGlosa" maxOccurs="unbounded"/>
					</sequence>
				</complexType>
			</element>
			<element name="procedimentosRealizados" minOccurs="0">
				<complexType>
					<sequence>
						<element name="procedimentoRealizado" maxOccurs="unbounded">
							<complexType>
								<complexContent>
									<extension base="ans:ct_procedimentoExecutadoOdonto">
										<sequence>
											<element name="glosasProcedimento" minOccurs="0">
												<complexType>
													<sequence>
														<element name="motivoGlosa" type="ans:ct_motivoGlosa" maxOccurs="unbounded"/>
														<element name="valorGlosaProcedimento" type="ans:st_decimal10-2"/>
													</sequence>
												</complexType>
											</element>
										</sequence>
									</extension>
								</complexContent>
							</complexType>
						</element>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_guiaRecurso">
		<annotation>
			<documentation>estrutura utilizada no retorno do recurso de glosa</documentation>
		</annotation>
		<sequence>
			<element name="numeroProtocolo" type="ans:st_texto12"/>
			<element name="numDemoAnalisePagto" type="ans:st_texto12" minOccurs="0"/>
			<element name="numeroGuiaRecurso" type="ans:st_texto20"/>
			<element name="numeroGuiaOrigem" type="ans:st_texto20"/>
			<element name="motivosGlosa" type="ans:ct_motivoGlosa" minOccurs="0" maxOccurs="unbounded"/>
		</sequence>
	</complexType>
	<complexType name="ct_guiaRecursoLote">
		<annotation>
			<documentation>lote de recurso de glosa</documentation>
		</annotation>
		<choice>
			<element name="guiaRecursoGlosaOdonto" type="ans:cto_recursoGlosaOdonto"/>
			<element name="guiaRecursoGlosa" type="ans:ctm_recursoGlosa"/>
		</choice>
	</complexType>
	<complexType name="ct_guiaValorTotal">
		<sequence>
			<element name="valorProcedimentos" type="ans:st_decimal10-2" minOccurs="0"/>
			<element name="valorDiarias" type="ans:st_decimal10-2" minOccurs="0"/>
			<element name="valorTaxasAlugueis" type="ans:st_decimal10-2" minOccurs="0"/>
			<element name="valorMateriais" type="ans:st_decimal10-2" minOccurs="0"/>
			<element name="valorMedicamentos" type="ans:st_decimal10-2" minOccurs="0"/>
			<element name="valorOPME" type="ans:st_decimal10-2" minOccurs="0"/>
			<element name="valorGasesMedicinais" type="ans:st_decimal10-2" minOccurs="0"/>
			<element name="valorTotalGeral" type="ans:st_decimal10-2"/>
		</sequence>
	</complexType>
	<complexType name="ct_guiaValorTotalSADT">
		<sequence>
			<element name="valorProcedimentos" type="ans:st_decimal10-2" minOccurs="0"/>
			<element name="valorTaxasAlugueis" type="ans:st_decimal10-2" minOccurs="0"/>
			<element name="valorMateriais" type="ans:st_decimal10-2" minOccurs="0"/>
			<element name="valorMedicamentos" type="ans:st_decimal10-2" minOccurs="0"/>
			<element name="valorOPME" type="ans:st_decimal10-2" minOccurs="0"/>
			<element name="valorGasesMedicinais" type="ans:st_decimal10-2" minOccurs="0"/>
			<element name="valorTotalGeral" type="ans:st_decimal10-2"/>
		</sequence>
	</complexType>
	<complexType name="ct_hipoteseDiagnostica">
		<sequence>
			<element name="diagnostico" type="ans:ct_diagnostico"/>
			<element name="indicacaoAcidente" type="ans:dm_indicadorAcidente"/>
		</sequence>
	</complexType>
	<complexType name="ct_identEquipe">
		<sequence>
			<element name="grauPart" type="ans:dm_grauPart"/>
			<element name="codProfissional">
				<complexType>
					<choice>
						<element name="codigoPrestadorNaOperadora" type="ans:st_texto14"/>
						<element name="cpfContratado" type="ans:st_CPF"/>
					</choice>
				</complexType>
			</element>
			<element name="nomeProf" type="ans:st_texto70"/>
			<element name="conselho" type="ans:dm_conselhoProfissional"/>
			<element name="numeroConselhoProfissional" type="ans:st_texto15"/>
			<element name="UF" type="ans:dm_UF"/>
			<element name="CBOS" type="ans:dm_CBOS"/>
		</sequence>
	</complexType>
	<complexType name="ct_identEquipeSADT">
		<sequence>
			<element name="grauPart" type="ans:dm_grauPart" minOccurs="0"/>
			<element name="codProfissional">
				<complexType>
					<choice>
						<element name="codigoPrestadorNaOperadora" type="ans:st_texto14"/>
						<element name="cpfContratado" type="ans:st_CPF"/>
					</choice>
				</complexType>
			</element>
			<element name="nomeProf" type="ans:st_texto70"/>
			<element name="conselho" type="ans:dm_conselhoProfissional"/>
			<element name="numeroConselhoProfissional" type="ans:st_texto15"/>
			<element name="UF" type="ans:dm_UF"/>
			<element name="CBOS" type="ans:dm_CBOS"/>
		</sequence>
	</complexType>
	<complexType name="ct_intervaloCiclos">
		<sequence>
			<element name="tempo" type="ans:st_numerico2"/>
			<element name="unidade" type="ans:dm_unidadeTempoCiclo"/>
		</sequence>
	</complexType>
	<complexType name="ct_loteStatus">
		<annotation>
			<documentation>resposta a uma solicitaÃ§Ã£o de situaÃ§Ã£o de protocolo</documentation>
		</annotation>
		<sequence>
			<element name="statusProtocolo" type="ans:dm_statusProtocolo"/>
			<element name="numeroProtocolo" type="ans:st_texto12"/>
			<element name="numeroLote" type="ans:st_texto12"/>
			<element name="dataEnvioLote" type="ans:st_data"/>
			<element name="valorTotalLote" type="ans:ct_valorTotal"/>
			<element name="guiasTISS">
				<complexType>
					<choice>
						<element name="guiasMedicas" maxOccurs="unbounded">
							<complexType>
								<sequence>
									<element name="guias" type="ans:ct_guiaDados"/>
								</sequence>
							</complexType>
						</element>
						<element name="guiasOdonto" maxOccurs="unbounded">
							<complexType>
								<sequence>
									<element name="guias" type="ans:ct_guiaDadosOdonto"/>
								</sequence>
							</complexType>
						</element>
					</choice>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_loteAnexoStatus">
		<annotation>
			<documentation>resposta a uma solicitaÃ§Ã£o de situaÃ§Ã£o de protocolo</documentation>
		</annotation>
		<sequence>
			<element name="statusProtocolo" type="ans:dm_statusProtocolo"/>
			<element name="numeroProtocolo" type="ans:st_texto12"/>
			<element name="numeroLote" type="ans:st_texto12"/>
			<element name="dataEnvioLote" type="ans:st_data"/>
			<element name="anexosClinicos">
				<complexType>
					<choice>
						<element name="anexoOPME" type="ans:ctm_autorizacaoOPME"/>
						<element name="anexoQuimio" type="ans:ctm_autorizacaoQuimio"/>
						<element name="anexoRadio" type="ans:ctm_autorizacaoRadio"/>
					</choice>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_motivoGlosa">
		<sequence>
			<element name="codigoGlosa" type="ans:dm_tipoGlosa"/>
			<element name="descricaoGlosa" type="ans:st_texto500" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ct_opmeDados">
		<sequence>
			<element name="identificacaoOPME" type="ans:ct_procedimentoDados"/>
			<element name="nomeFabricante" type="ans:st_texto70"/>
		</sequence>
	</complexType>
	<complexType name="ct_opmUtilizada">
		<sequence>
			<element name="OPM">
				<complexType>
					<sequence>
						<element name="identificacaoOPM" maxOccurs="unbounded">
							<complexType>
								<sequence>
									<element name="identificacaoOPME" type="ans:ct_procedimentoDados"/>
									<element name="quantidade" type="ans:st_numerico2"/>
									<element name="codigoBarra" type="ans:st_texto20" minOccurs="0"/>
									<element name="valorUnitario" type="ans:st_decimal8-2" minOccurs="0"/>
									<element name="valorTotal" type="ans:st_decimal8-2" minOccurs="0"/>
								</sequence>
							</complexType>
						</element>
					</sequence>
				</complexType>
			</element>
			<element name="valorTotalOPM" type="ans:st_decimal8-2" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ct_outrasDespesas">
		<sequence>
			<element name="despesa" maxOccurs="unbounded">
				<complexType>
					<sequence>
						<element name="sequencialItem" type="ans:st_numerico4"/>
						<element name="codigoDespesa" type="ans:dm_outrasDespesas"/>
						<element name="servicosExecutados" type="ans:ct_procedimentoExecutadoOutras"/>
						<element name="itemVinculado" type="ans:st_numerico4" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_pagamentoDados">
		<sequence>
			<element name="dataPagamento" type="ans:st_data"/>
			<element name="formaPagamento" type="ans:dm_formaPagamento"/>
			<element name="banco" type="ans:st_texto4" minOccurs="0"/>
			<element name="agencia" type="ans:st_texto7" minOccurs="0"/>
			<element name="nrContaCheque" type="ans:st_texto20" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ct_pagamentoResumo">
		<sequence>
			<element name="numeroFatura" type="ans:st_texto12" minOccurs="0"/>
			<element name="numeroLote" type="ans:st_texto12"/>
			<element name="numeroProtocolo" type="ans:st_texto12"/>
			<element name="valorTotalLote" type="ans:ct_valorTotal"/>
		</sequence>
	</complexType>
	<complexType name="ct_prestadorIdentificacao">
		<choice>
			<element name="CNPJ" type="ans:st_CNPJ"/>
			<element name="CPF" type="ans:st_CPF"/>
			<element name="codigoPrestadorNaOperadora" type="ans:st_texto14"/>
		</choice>
	</complexType>
	<complexType name="ct_loginSenha">
		<sequence>
			<element name="loginPrestador" type="ans:st_texto20"/>
			<element name="senhaPrestador" type="ans:st_texto32"/>
		</sequence>
	</complexType>
	<complexType name="ct_procedimentoAutorizado">
		<sequence>
			<element name="sequencialItem" type="ans:st_numerico4"/>
			<element name="procedimento" type="ans:ct_procedimentoDados"/>
			<element name="quantidadeSolicitada" type="ans:st_numerico3"/>
			<element name="quantidadeAutorizada" type="ans:st_numerico3"/>
			<element name="valorSolicitado" type="ans:st_decimal8-2" minOccurs="0"/>
			<element name="valorAutorizado" type="ans:st_decimal8-2" minOccurs="0"/>
			<element name="opcaoFabricante" type="ans:dm_opcaoFabricante" minOccurs="0"/>
			<element name="registroANVISA" type="ans:st_texto15" minOccurs="0"/>
			<element name="codigoRefFabricante" type="ans:st_texto60" minOccurs="0"/>
			<element name="autorizacaoFuncionamento" type="ans:st_texto30" minOccurs="0"/>
			<element name="motivosNegativa" minOccurs="0">
				<complexType>
					<sequence>
						<element name="motivoNegativa" type="ans:ct_motivoGlosa" maxOccurs="unbounded"/>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_procedimentosComplementares">
		<sequence>
			<element name="dataProvavel" type="ans:st_data"/>
			<element name="identificacao" type="ans:ct_procedimentoDados"/>
			<element name="quantidade" type="ans:st_decimal5-2"/>
		</sequence>
	</complexType>
	<complexType name="ct_procedimentoDados">
		<sequence>
			<element name="codigoTabela" type="ans:dm_tabela"/>
			<element name="codigoProcedimento" type="ans:st_texto10"/>
			<element name="descricaoProcedimento" type="ans:st_texto150"/>
		</sequence>
	</complexType>
	<complexType name="ct_procedimentoExecutado">
		<sequence>
			<element name="sequencialItem" type="ans:st_numerico4"/>
			<element name="dataExecucao" type="ans:st_data"/>
			<element name="horaInicial" type="ans:st_hora" minOccurs="0"/>
			<element name="horaFinal" type="ans:st_hora" minOccurs="0"/>
			<element name="procedimento" type="ans:ct_procedimentoDados"/>
			<element name="unidadeMedida" type="ans:dm_unidadeMedida" minOccurs="0"/>
			<element name="quantidadeExecutada" type="ans:st_decimal9-4"/>
			<element name="viaAcesso" type="ans:dm_viaDeAcesso" minOccurs="0"/>
			<element name="tecnicaUtilizada" type="ans:dm_tecnicaUtilizada" minOccurs="0"/>
			<element name="valorUnitario" type="ans:st_decimal8-2"/>
			<element name="valorTotal" type="ans:st_decimal8-2"/>
			<element name="codigoDespesa" type="ans:dm_outrasDespesas" minOccurs="0"/>
			<element name="fatorReducaoAcrescimo" type="ans:st_decimal3-2" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ct_procedimentoExecutadoOdonto">
		<sequence>
			<element name="sequencialItem" type="ans:st_numerico4"/>
			<element name="procedimento" type="ans:ct_procedimentoDados"/>
			<element name="denteRegiao" minOccurs="0">
				<complexType>
					<choice>
						<element name="codDente" type="ans:dm_dente"/>
						<element name="codRegiao" type="ans:dm_regiao"/>
					</choice>
				</complexType>
			</element>
			<element name="denteFace" type="ans:dm_face" minOccurs="0"/>
			<element name="qtdProc" type="ans:st_numerico2" minOccurs="0"/>
			<element name="qtdUS" type="ans:st_decimal8-2" minOccurs="0"/>
			<element name="valorProc" type="ans:st_decimal8-2" minOccurs="0"/>
			<element name="valorFranquia" type="ans:st_decimal8-2" minOccurs="0"/>
			<element name="autorizado" type="ans:dm_simNao" minOccurs="0"/>
			<element name="dataRealizacao" type="ans:st_data"/>
		</sequence>
	</complexType>
	<complexType name="ct_procedimentoExecutadoOutras">
		<sequence>
			<element name="dataExecucao" type="ans:st_data"/>
			<element name="horaInicial" type="ans:st_hora" minOccurs="0"/>
			<element name="horaFinal" type="ans:st_hora" minOccurs="0"/>
			<element name="codigoTabela" type="ans:dm_tabela"/>
			<element name="codigoProcedimento" type="ans:st_texto10"/>
			<element name="quantidadeExecutada" type="ans:st_decimal9-4"/>
			<element name="unidadeMedida" type="ans:dm_unidadeMedida"/>
			<element name="reducaoAcrescimo" type="ans:st_decimal3-2"/>
			<element name="valorUnitario" type="ans:st_decimal8-2"/>
			<element name="valorTotal" type="ans:st_decimal8-2"/>
			<element name="descricaoProcedimento" type="ans:st_texto150"/>
			<element name="registroANVISA" type="ans:st_texto15" minOccurs="0"/>
			<element name="codigoRefFabricante" type="ans:st_texto60" minOccurs="0"/>
			<element name="autorizacaoFuncionamento" type="ans:st_texto30" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ct_procedimentoExecutadoInt">
		<sequence>
			<element name="sequencialItem" type="ans:st_numerico4"/>
			<element name="dataExecucao" type="ans:st_data"/>
			<element name="horaInicial" type="ans:st_hora" minOccurs="0"/>
			<element name="horaFinal" type="ans:st_hora" minOccurs="0"/>
			<element name="procedimento" type="ans:ct_procedimentoDados"/>
			<element name="quantidadeExecutada" type="ans:st_numerico3"/>
			<element name="viaAcesso" type="ans:dm_viaDeAcesso" minOccurs="0"/>
			<element name="tecnicaUtilizada" type="ans:dm_tecnicaUtilizada" minOccurs="0"/>
			<element name="reducaoAcrescimo" type="ans:st_decimal3-2"/>
			<element name="valorUnitario" type="ans:st_decimal8-2"/>
			<element name="valorTotal" type="ans:st_decimal8-2"/>
			<element name="identEquipe" minOccurs="0" maxOccurs="unbounded">
				<complexType>
					<sequence>
						<element name="identificacaoEquipe" type="ans:ct_identEquipe"/>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_procedimentoExecutadoHonorIndiv">
		<sequence>
			<element name="sequencialItem" type="ans:st_numerico4"/>
			<element name="dataExecucao" type="ans:st_data"/>
			<element name="horaInicial" type="ans:st_hora" minOccurs="0"/>
			<element name="horaFinal" type="ans:st_hora" minOccurs="0"/>
			<element name="procedimento" type="ans:ct_procedimentoDados"/>
			<element name="quantidadeExecutada" type="ans:st_numerico3"/>
			<element name="viaAcesso" type="ans:dm_viaDeAcesso" minOccurs="0"/>
			<element name="tecnicaUtilizada" type="ans:dm_tecnicaUtilizada" minOccurs="0"/>
			<element name="reducaoAcrescimo" type="ans:st_decimal3-2"/>
			<element name="valorUnitario" type="ans:st_decimal8-2"/>
			<element name="valorTotal" type="ans:st_decimal8-2"/>
			<element name="profissionais" maxOccurs="unbounded">
				<complexType>
					<sequence>
						<element name="grauParticipacao" type="ans:dm_grauPart"/>
						<element name="codProfissional">
							<complexType>
								<choice>
									<element name="codigoPrestadorNaOperadora" type="ans:st_texto14"/>
									<element name="cpfContratado" type="ans:st_CPF"/>
								</choice>
							</complexType>
						</element>
						<element name="nomeProfissional" type="ans:st_texto70"/>
						<element name="conselhoProfissional" type="ans:dm_conselhoProfissional"/>
						<element name="numeroConselhoProfissional" type="ans:st_texto15"/>
						<element name="UF" type="ans:dm_UF"/>
						<element name="CBO" type="ans:dm_CBOS"/>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_procedimentoExecutadoSadt">
		<sequence>
			<element name="sequencialItem" type="ans:st_numerico4"/>
			<element name="dataExecucao" type="ans:st_data"/>
			<element name="horaInicial" type="ans:st_hora" minOccurs="0"/>
			<element name="horaFinal" type="ans:st_hora" minOccurs="0"/>
			<element name="procedimento" type="ans:ct_procedimentoDados"/>
			<element name="quantidadeExecutada" type="ans:st_numerico3"/>
			<element name="viaAcesso" type="ans:dm_viaDeAcesso" minOccurs="0"/>
			<element name="tecnicaUtilizada" type="ans:dm_tecnicaUtilizada" minOccurs="0"/>
			<element name="reducaoAcrescimo" type="ans:st_decimal3-2"/>
			<element name="valorUnitario" type="ans:st_decimal8-2"/>
			<element name="valorTotal" type="ans:st_decimal8-2"/>
			<element name="equipeSadt" type="ans:ct_identEquipeSADT" minOccurs="0" maxOccurs="unbounded"/>
		</sequence>
	</complexType>
	<complexType name="ct_procedimentoSolicitado">
		<sequence>
			<element name="procedimento" type="ans:ct_procedimentoDados"/>
			<element name="unidadeMedida" type="ans:dm_unidadeMedida"/>
			<element name="quantidadeSolicitada" type="ans:st_numerico3"/>
		</sequence>
	</complexType>
	<complexType name="ct_protocoloDetalhe">
		<sequence>
			<element name="numeroProtocolo" type="ans:st_texto12"/>
			<element name="valorTotalProtocolo" type="ans:st_decimal10-2"/>
			<element name="glosaProtocolo" minOccurs="0">
				<complexType>
					<sequence>
						<element name="motivosGlosa">
							<complexType>
								<sequence>
									<element name="motivoGlosa" type="ans:ct_motivoGlosa" maxOccurs="unbounded"/>
								</sequence>
							</complexType>
						</element>
						<element name="vlGlosaProtocolo" type="ans:st_decimal10-2"/>
					</sequence>
				</complexType>
			</element>
			<element name="dadosGuiasProtocolo">
				<complexType>
					<choice>
						<element name="dadosGuias" type="ans:ct_guiaDados" maxOccurs="unbounded"/>
						<element name="dadosGuiasOdonto" type="ans:ct_guiaDadosOdonto" maxOccurs="unbounded"/>
					</choice>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_anexoLote">
		<annotation>
			<documentation> estrutura da resposta da operadora a um lote de anexos</documentation>
		</annotation>
		<sequence>
			<element name="numeroLote" type="ans:st_texto12"/>
			<element name="AnexosGuiasTISS">
				<complexType>
					<choice>
						<element name="anexoSituacaoInicial" type="ans:cto_anexoSituacaoInicial" maxOccurs="100"/>
						<element name="anexoSolicitacaoRadio" type="ans:ctm_anexoSolicitacaoRadio"/>
						<element name="anexoSolicitacaoQuimio" type="ans:ctm_anexoSolicitacaoQuimio"/>
						<element name="anexoSolicitacaoOPME" type="ans:ctm_anexoSolicitacaoOPME"/>
					</choice>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_protocoloDetalheAnexo">
		<sequence>
			<element name="numeroProtocolo" type="ans:st_texto12"/>
			<element name="valorTotalProtocolo" type="ans:st_decimal10-2"/>
			<element name="glosasProtocolo" type="ans:ct_motivoGlosa" minOccurs="0" maxOccurs="unbounded"/>
			<element name="vlGlosaProtocolo" type="ans:st_decimal10-2" minOccurs="0"/>
			<element name="dadosGuias" type="ans:ct_guiaDadosAnexo" maxOccurs="unbounded"/>
		</sequence>
	</complexType>
	<complexType name="ct_protocoloRecurso">
		<annotation>
			<documentation> estrutura da resposta da operadora a um lote de guias de recurso de glosa de medicina e de odonto</documentation>
		</annotation>
		<sequence>
			<element name="numeroProtocolo" type="ans:st_texto12"/>
			<element name="glosaProtocolo" type="ans:ct_motivoGlosa" minOccurs="0" maxOccurs="unbounded"/>
			<element name="dadosGuias" type="ans:ct_guiaRecurso" minOccurs="0" maxOccurs="unbounded"/>
		</sequence>
	</complexType>
	<complexType name="ct_protocoloRecebimentoAnexo">
		<sequence>
			<element name="numeroProtocolo" type="ans:st_texto12"/>
			<element name="identificacaoOperadora" type="ans:ct_fontePagadora"/>
			<element name="dadosPrestador" type="ans:ct_contratadoDados"/>
			<element name="numeroLote" type="ans:st_texto12"/>
			<element name="dataEnvioLote" type="ans:st_texto12"/>
			<element name="detalheProtocolo" type="ans:ct_protocoloDetalheAnexo"/>
		</sequence>
	</complexType>
	<complexType name="ct_protocoloRecebimento">
		<sequence>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="dadosPrestador" type="ans:ct_contratadoDados"/>
			<element name="numeroLote" type="ans:st_texto12"/>
			<element name="dataEnvioLote" type="ans:st_data"/>
			<element name="detalheProtocolo" type="ans:ct_protocoloDetalhe"/>
		</sequence>
	</complexType>
	<complexType name="ct_protocoloRecebimentoRecurso">
		<sequence>
			<element name="numeroProtocolo" type="ans:st_texto12"/>
			<element name="identificacaoOperadora" type="ans:ct_fontePagadora"/>
			<element name="dadosPrestador" type="ans:ct_contratadoDados"/>
			<element name="numeroLote" type="ans:st_texto12"/>
			<element name="dataEnvioLote" type="ans:st_data"/>
			<element name="detalheProtocolo" type="ans:ct_protocoloRecurso"/>
		</sequence>
	</complexType>
	<complexType name="ct_protocoloSolicitacaoStatus">
		<sequence>
			<element name="dadosPrestador" type="ans:ct_contratadoDados"/>
			<element name="numeroProtocolo" type="ans:st_texto12"/>
		</sequence>
	</complexType>
	<complexType name="ct_protocoloStatus">
		<annotation>
			<documentation> estrutura utilizada na resposta da operadora sobre a situaÃ§Ã£o do protocolo</documentation>
		</annotation>
		<sequence>
			<element name="identificacaoOperadora" type="ans:st_registroANS"/>
			<!--<element name="numeroProtocolo" type="ans:st_texto12"/>
			<element name="statusProtocolo" type="ans:dm_statusProtocolo"/>-->
			<element name="dadosPrestador" type="ans:ct_contratadoDados"/>
			<element name="lote">
				<complexType>
					<choice>
						<element name="detalheLote" type="ans:ct_loteStatus"/>
						<element name="mensagemErro" type="ans:ct_motivoGlosa"/>
					</choice>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_protocoloAnexoStatus">
		<annotation>
			<documentation> estrutura utilizada na resposta da operadora sobre a situaÃ§Ã£o do protocolo</documentation>
		</annotation>
		<sequence>
			<element name="identificacaoOperadora" type="ans:st_registroANS"/>
			<!--<element name="numeroProtocolo" type="ans:st_texto12"/>
			<element name="statusProtocolo" type="ans:dm_statusProtocolo"/>-->
			<element name="dadosPrestador" type="ans:ct_contratadoDados"/>
			<element name="loteAnexo">
				<complexType>
					<choice>
						<element name="detalheLoteAnexo" type="ans:ct_loteAnexoStatus"/>
						<element name="mensagemErro" type="ans:ct_motivoGlosa"/>
					</choice>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_recursoGlosaRecebimento">
		<sequence>
			<element name="nrProtocoloRecursoGlosa" type="ans:st_texto12"/>
			<element name="dataEnvioRecurso" type="ans:st_data"/>
			<element name="dataRecebimentoRecurso" type="ans:st_data"/>
			<element name="numeroLote" type="ans:st_texto12"/>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="dadosPrestador" type="ans:ct_contratadoDados"/>
			<element name="nrProtocoloRecursado" type="ans:st_texto12"/>
			<element name="recursoProtocolo" minOccurs="0">
				<complexType>
					<sequence>
						<element name="codigoGlosaProtocolo" type="ans:dm_tipoGlosa"/>
						<element name="justificativaProtocolo" type="ans:st_texto500"/>
					</sequence>
				</complexType>
			</element>
			<element name="qtGuiasRecurso" type="ans:st_numerico3" minOccurs="0"/>
			<element name="guiasRecurso" minOccurs="0" maxOccurs="100">
				<complexType>
					<sequence>
						<element name="numeroGuiaOrigem" type="ans:st_texto20"/>
						<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
						<element name="senha" type="ans:st_texto20" minOccurs="0"/>
						<element name="opcaoRecursoGuia">
							<complexType>
								<choice>
									<element name="recursoGuia" maxOccurs="unbounded">
										<complexType>
											<sequence>
												<element name="codGlosaGuia" type="ans:dm_tipoGlosa"/>
												<element name="justificativaGuia" type="ans:st_texto150"/>
											</sequence>
										</complexType>
									</element>
									<element name="itensGuia" maxOccurs="unbounded">
										<complexType>
											<sequence>
												<element name="sequencialItem" type="ans:st_numerico4"/>
												<element name="dataInicio" type="ans:st_data"/>
												<element name="dataFim" type="ans:st_data" minOccurs="0"/>
												<element name="procRecurso" type="ans:ct_procedimentoDados"/>
												<element name="denteRegiao" minOccurs="0">
													<complexType>
														<choice>
															<element name="codDente" type="ans:dm_dente"/>
															<element name="codRegiao" type="ans:dm_regiao"/>
														</choice>
													</complexType>
												</element>
												<element name="denteFace" type="ans:dm_face" minOccurs="0"/>
												<element name="codGlosaItem" type="ans:dm_tipoGlosa"/>
												<element name="valorRecursado" type="ans:st_decimal8-2"/>
												<element name="justificativaItem" type="ans:st_texto500"/>
											</sequence>
										</complexType>
									</element>
								</choice>
							</complexType>
						</element>
					</sequence>
				</complexType>
			</element>
			<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
			<element name="valorTotalRecursado" type="ans:st_decimal10-2"/>
		</sequence>
	</complexType>
	<complexType name="ct_recebimentoLote">
		<choice>
			<element name="mensagemErro" type="ans:ct_motivoGlosa"/>
			<element name="protocoloRecebimento" type="ans:ct_protocoloRecebimento"/>
		</choice>
	</complexType>
	<complexType name="ct_recebimentoRecurso">
		<choice>
			<element name="mensagemErro" type="ans:ct_motivoGlosa"/>
			<element name="protocoloRecebimento" type="ans:ct_recursoGlosaRecebimento"/>
		</choice>
	</complexType>
	<complexType name="ct_reciboCancelaGuia">
		<choice>
			<element name="mensagemErro" type="ans:ct_motivoGlosa"/>
			<element name="reciboCancelaGuia" type="ans:ct_guiaCancelamentoRecibo"/>
		</choice>
	</complexType>
	<complexType name="ct_reciboComunicacao">
		<choice>
			<element name="codigoGlosa" type="ans:dm_tipoGlosa"/>
			<!-- retirado na versÃ£o 4.00.00
			<element name="mensagemErro" type="ans:ct_motivoGlosa"/>
			-->
			<element name="reciboComunicacao" type="ans:ctm_beneficiarioComunicacaoRecibo"/>
		</choice>
	</complexType>
	<complexType name="ct_respostaElegibilidade">
		<choice>
			<element name="codigoGlosa" type="ans:ct_motivoGlosa"/>
			<!-- retirado na versÃ£o 4.00.00
			<element name="mensagemErro" type="ans:ct_motivoGlosa"/>
			-->
			<element name="reciboElegibilidade" type="ans:ct_elegibilidadeRecibo"/>
		</choice>
	</complexType>
	<complexType name="ct_respostaGlosa">
		<choice>
			<element name="reciboGlosa" type="ans:ct_glosaRecibo"/>
			<element name="reciboGlosaOdonto" type="ans:ct_glosaReciboOdonto"/>
			<element name="reciboGlosaStatus">
				<complexType>
					<sequence>
						<element name="nrProtocoloRecursoGlosa" type="ans:st_texto12"/>
						<element name="dataEnvioRecurso" type="ans:st_data"/>
						<element name="dataRecebimentoRecurso" type="ans:st_data"/>
						<element name="numeroLote" type="ans:st_texto12"/>
						<element name="registroANS" type="ans:st_registroANS"/>
						<element name="dadosPrestador" type="ans:ct_contratadoDados"/>
						<element name="nrProtocoloSituacaoRecursoGlosa" type="ans:st_texto12"/>
						<element name="dataSituacao" type="ans:st_data"/>
						<element name="situacaoProtocolo" type="ans:dm_statusProtocolo"/>
					</sequence>
				</complexType>
			</element>
			<element name="mensagemErro" type="ans:ct_motivoGlosa"/>
		</choice>
	</complexType>
	<complexType name="ct_respostaGlosaGuiaMedica">
		<sequence>
			<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="senha" type="ans:st_texto20" minOccurs="0"/>
			<element name="codGlosa" type="ans:dm_tipoGlosa"/>
			<element name="justificativaPrestador" type="ans:st_texto500"/>
			<element name="recursoGuiaAcatado" type="ans:dm_simNao"/>
			<element name="justificativaOPSnaoAcatadoGuia" type="ans:st_texto500" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ct_respostaGlosaItemMedico">
		<sequence>
			<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="senha" type="ans:st_texto20" minOccurs="0"/>
			<element name="dataRealizacao" type="ans:st_data"/>
			<element name="dataFim" type="ans:st_data" minOccurs="0"/>
			<element name="sequencialItem" type="ans:st_numerico4"/>
			<element name="procRecurso" type="ans:ct_procedimentoDados"/>
			<element name="codGlosa" type="ans:dm_tipoGlosa"/>
			<element name="valorRecursado" type="ans:st_decimal8-2"/>
			<element name="justificativaPrestador" type="ans:st_texto500"/>
			<element name="valorAcatadado" type="ans:st_decimal8-2"/>
			<element name="justificativaOperadora" type="ans:st_texto500" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ct_respostaRecursoGuiaOdonto">
		<sequence>
			<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="senha" type="ans:st_texto20" minOccurs="0"/>
			<element name="codGlosaGuia" type="ans:dm_tipoGlosa"/>
			<element name="justificativaGuia" type="ans:st_texto500"/>
			<element name="recursoAcatadoGuia" type="ans:dm_simNao"/>
		</sequence>
	</complexType>
	<complexType name="ct_respostaRecursoItemOdonto">
		<sequence>
			<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="senha" type="ans:st_texto20" minOccurs="0"/>
			<element name="recursoProcedimento" maxOccurs="unbounded">
				<complexType>
					<sequence>
						<element name="sequencialItem" type="ans:st_numerico4"/>
						<element name="dataRealizacao" type="ans:st_data"/>
						<element name="denteRegiao" minOccurs="0">
							<complexType>
								<choice>
									<element name="codDente" type="ans:dm_dente"/>
									<element name="codRegiao" type="ans:dm_regiao"/>
								</choice>
							</complexType>
						</element>
						<element name="denteFace" type="ans:dm_face" minOccurs="0"/>
						<element name="quantidade" type="ans:st_numerico2"/>
						<element name="procRecurso" type="ans:ct_procedimentoDados"/>
						<element name="codGlosaProc" type="ans:dm_tipoGlosa"/>
						<element name="valorRecursado" type="ans:st_decimal8-2"/>
						<element name="justificativaPrestador" type="ans:st_texto500"/>
						<element name="valorAcatado" type="ans:st_decimal8-2"/>
						<element name="justificativaOperadora" type="ans:st_texto500" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_situacaoAutorizacao">
		<choice>
			<element name="mensagemErro" type="ans:ct_motivoGlosa"/>
			<element name="autorizacaoInternacao" type="ans:ctm_autorizacaoInternacao"/>
			<element name="autorizacaoServico" type="ans:ctm_autorizacaoServico"/>
			<element name="autorizacaoProrrogacao" type="ans:ctm_autorizacaoProrrogacao"/>
			<element name="autorizacaoServicoOdonto" type="ans:cto_autorizacaoServico"/>
		</choice>
	</complexType>
	<complexType name="ct_situacaoProtocolo">
		<choice>
			<element name="mensagemErro" type="ans:ct_motivoGlosa"/>
			<element name="situacaoDoProtocolo" type="ans:ct_protocoloStatus"/>
			<element name="situacaoProtocoloAnexo" type="ans:ct_protocoloAnexoStatus"/>
		</choice>
	</complexType>
	<!--<complexType name="ct_tempoAproximado">
		<sequence>
			<element name="tempo" type="ans:st_numerico3"/>
			<element name="unidade" type="ans:dm_unidadeTempo"/>
		</sequence>
	</complexType>-->
	<complexType name="ct_situacaoClinica">
		<sequence>
			<element name="dentes" maxOccurs="unbounded">
				<complexType>
					<sequence>
						<element name="elementoDentario" type="ans:dm_dente"/>
						<element name="condicaoClinica" type="ans:dm_condicaoClinica"/>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ct_solicitacaoProcedimento">
		<complexContent>
			<extension base="ans:ctm_solicitacaoLote"/>
		</complexContent>
	</complexType>
	<!--<complexType name="ct_tempoDoenca">
		<sequence>
			<element name="tempo" type="ans:st_numerico2"/>
			<element name="unidade" type="ans:dm_unidadeTempoOPME"/>
		</sequence>
	</complexType>-->
	<complexType name="ct_valorCreditoDesconto">
		<sequence>
			<element name="tipoLancamento" type="ans:dm_tipoLancamento"/>
			<element name="descricao" type="ans:st_texto100"/>
			<element name="valor" type="ans:st_decimal10-2"/>
		</sequence>
	</complexType>
	<complexType name="ct_valorTotal">
		<sequence>
			<element name="valorProcessado" type="ans:st_decimal10-2"/>
			<element name="valorGlosa" type="ans:st_decimal10-2"/>
			<element name="valorLiberado" type="ans:st_decimal10-2"/>
		</sequence>
	</complexType>
	<!-- incluido na versÃ£o 4.00.00-->
	<complexType name="ct_envioDocumentos">
		<sequence>
			<element name="numeroLote" type="ans:st_texto12" minOccurs="0"/>
			<element name="numeroProtocolo" type="ans:st_texto12" minOccurs="0"/>
			<element name="numeroGuiaPrestador" type="ans:st_texto20" minOccurs="0"/>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="numeroDocumento" type="ans:st_texto20"/>
			<element name="naturezaGuia" type="ans:dm_tipoGuia"/>
			<element name="formatoDocumento" type="ans:dm_formatoDocumento"/>
			<element name="seqReferenciaItem" type="ans:st_numerico4" minOccurs="0"/>
			<element name="documento" type="base64Binary"/>
			<element name="tipoDocumento" type="ans:dm_tipoDocumento"/>
			<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
			<element name="assinaturaDigital" type="ans:Signature" minOccurs="0"/>
		</sequence>
	</complexType>
	<!-- incluido na versÃ£o 4.00.00-->
	<complexType name="ct_reciboDocumentos">
		<sequence>
			<element name="numeroLote" type="ans:st_texto12" minOccurs="0"/>
			<element name="numeroProtocolo" type="ans:st_texto12" minOccurs="0"/>
			<element name="numeroGuiaPrestador" type="ans:st_texto20" minOccurs="0"/>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="numeroDocumento" type="ans:st_texto20"/>
			<element name="protocoloDoc" type="ans:st_texto20"/>
			<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
		</sequence>
	</complexType>
	<!--VERSÃ?O TISS 4.00.00 - TissGuiasv4_00_00-->
	<!--************************************************ OPME SOLICITAÃ?Ã?O  ************************************ -->
	<complexType name="ctm_anexoSolicitacaoOPME">
		<sequence>
			<element name="cabecalhoAnexo" type="ans:ct_anexoCabecalho"/>
			<element name="dadosBeneficiario" type="ans:ct_beneficiarioDados"/>
			<element name="profissionalSolicitante" type="ans:ctm_anexoSolicitante"/>
			<element name="justificativaTecnica" type="ans:st_texto1000"/>
			<element name="especificacaoMaterial" type="ans:st_texto500" minOccurs="0"/>
			<element name="opmeSolicitadas">
				<complexType>
					<sequence>
						<element name="opmeSolicitada" maxOccurs="unbounded">
							<complexType>
								<sequence>
									<element name="identificacaoOPME" type="ans:ct_procedimentoDados"/>
									<element name="opcaoFabricante" type="ans:dm_opcaoFabricante"/>
									<element name="quantidadeSolicitada" type="ans:st_numerico3"/>
									<element name="valorSolicitado" type="ans:st_decimal8-2" minOccurs="0"/>
									<element name="registroANVISA" type="ans:st_texto15" minOccurs="0"/>
									<element name="codigoRefFabricante" type="ans:st_texto60" minOccurs="0"/>
									<element name="autorizacaoFuncionamento" type="ans:st_texto30" minOccurs="0"/>
								</sequence>
							</complexType>
						</element>
					</sequence>
				</complexType>
			</element>
			<element name="Observacao" type="ans:st_texto500" minOccurs="0"/>
		</sequence>
	</complexType>
	<!--************************************************ QUIMIOTERAPIA  ************************************ -->
	<complexType name="ctm_anexoSolicitacaoQuimio">
		<sequence>
			<element name="cabecalhoAnexo" type="ans:ct_anexoCabecalho"/>
			<element name="dadosBeneficiario" type="ans:ct_beneficiarioDados"/>
			<element name="dadosComplementaresBeneficiario" type="ans:ct_dadosComplementaresBeneficiario"/>
			<element name="medicoSolicitante" type="ans:ctm_anexoSolicitante"/>
			<element name="diagnosticoOncologicoQuimioterapia">
				<complexType>
					<sequence>
						<element name="diagQuimio" type="ans:ct_diagnosticoOncologico"/>
						<element name="tumor" type="ans:dm_tumor"/>
						<element name="nodulo" type="ans:dm_nodulo"/>
						<element name="metastase" type="ans:dm_metastase"/>
						<element name="tipoQuimioterapia" type="ans:dm_tipoQuimioterapia"/>
						<element name="planoTerapeutico" type="ans:st_texto1000"/>
					</sequence>
				</complexType>
			</element>
			<element name="drogasSolicitadas">
				<complexType>
					<sequence>
						<element name="drogaSolicitada" type="ans:ct_drogasSolicitadas" maxOccurs="unbounded"/>
					</sequence>
				</complexType>
			</element>
			<element name="tratamentosAnteriores" minOccurs="0">
				<complexType>
					<sequence>
						<element name="cirurgia" type="ans:st_texto40" minOccurs="0"/>
						<element name="datacirurgia" type="ans:st_data" minOccurs="0"/>
						<element name="areaIrradiada" type="ans:st_texto40" minOccurs="0"/>
						<element name="dataIrradiacao" type="ans:st_data" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
			<element name="numeroCiclos" type="ans:st_numerico2"/>
			<element name="cicloAtual" type="ans:st_numerico2"/>
			<element name="diasCicloAtual" type="ans:st_numerico3"/>
			<element name="intervaloCiclos" type="ans:st_numerico3"/>
			<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
		</sequence>
	</complexType>
	<!--************************************************ RADIOTERAPIA  ************************************ -->
	<complexType name="ctm_anexoSolicitacaoRadio">
		<sequence>
			<element name="cabecalhoAnexo" type="ans:ct_anexoCabecalho"/>
			<element name="dadosBeneficiario" type="ans:ct_beneficiarioDados"/>
			<element name="dadosComplementaresBeneficiario" type="ans:ct_dadosComplementaresBeneficiarioRadio"/>
			<element name="medicoSolicitante" type="ans:ctm_anexoSolicitante"/>
			<element name="diagnosticoOncologicoRadio">
				<complexType>
					<sequence>
						<element name="diagRadio" type="ans:ct_diagnosticoOncologico"/>
						<element name="diagnosticoImagem" type="ans:dm_diagnosticoImagem" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
			<element name="tratamentosAnteriores" minOccurs="0">
				<complexType>
					<sequence>
						<element name="cirurgia" type="ans:st_texto40" minOccurs="0"/>
						<element name="datacirurgia" type="ans:st_data" minOccurs="0"/>
						<element name="quimioterapia" type="ans:st_texto40" minOccurs="0"/>
						<element name="dataQuimioterapia" type="ans:st_data" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
			<element name="numeroCampos" type="ans:st_numerico3"/>
			<element name="doseCampo" type="ans:st_numerico4"/>
			<element name="doseTotal" type="ans:st_numerico4"/>
			<element name="nrDias" type="ans:st_numerico3"/>
			<element name="dtPrevistaInicio" type="ans:st_data"/>
			<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
		</sequence>
	</complexType>
	<!--******************************************************** Fim da Radioterapia ***************************************************** -->
	<complexType name="ctm_anexoSolicitante">
		<sequence>
			<element name="nomeProfissional" type="ans:st_texto70"/>
			<element name="telefoneProfissional" type="ans:st_texto11"/>
			<element name="emailProfissional" type="ans:st_texto60" minOccurs="0"/>
		</sequence>
	</complexType>
	<!-- Utilizado na resposta da operadora na autorizaÃ§Ã£o de solic prorrogaÃ§Ã£o de internaÃ§Ã£o -->
	<complexType name="ctm_autorizacaoProrrogacao">
		<sequence>
			<element name="autorizacaoDosServicos" type="ans:ctm_autorizacaoServico"/>
			<element name="nomeContratado" type="ans:st_texto70" minOccurs="0"/>
			<element name="diariasAutorizadas" type="ans:st_numerico3" minOccurs="0"/>
			<element name="acomodacaoAutorizada" type="ans:dm_tipoAcomodacao" minOccurs="0"/>
			<element name="justificativaOperadora" type="ans:st_texto500" minOccurs="0"/>
		</sequence>
	</complexType>
	<!--  Utilizado na resposta da operadora na autorizaÃ§Ã£o de solicitacao de internaÃ§Ã£o  -->
	<complexType name="ctm_autorizacaoInternacao">
		<sequence>
			<element name="autorizacaoDosServicos" type="ans:ctm_autorizacaoServico"/>
			<element name="dataProvavelAdmissao" type="ans:st_data" minOccurs="0"/>
			<element name="qtdDiariasAutorizadas" type="ans:st_numerico3" minOccurs="0"/>
			<element name="tipoAcomodacaoAutorizada" type="ans:dm_tipoAcomodacao" minOccurs="0"/>
		</sequence>
	</complexType>
	<!-- Utilizado na resposta da operadora ao recebimento dos anexo de soliccitaÃ§Ã£o de OPME -->
	<complexType name="ctm_autorizacaoOPME">
		<sequence>
			<element name="dadosAutorizacao" type="ans:ct_autorizacaoDados"/>
			<element name="dadosBeneficiario" type="ans:ct_beneficiarioDados"/>
			<element name="nomebeneficiario" type="ans:st_texto70"/>
			<element name="nomeSocialBeneficiario" type="ans:st_texto70" minOccurs="0"/>
			<element name="statusSolicitacao" type="ans:dm_statusSolicitacao"/>
			<element name="motivosNegativa" minOccurs="0">
				<complexType>
					<sequence>
						<element name="motivoNegativa" type="ans:ct_motivoGlosa" maxOccurs="unbounded"/>
					</sequence>
				</complexType>
			</element>
			<element name="prestadorAutorizado" type="ans:ct_contratadoDados" minOccurs="0"/>
			<element name="servicosAutorizadosOPME" minOccurs="0" maxOccurs="unbounded">
				<complexType>
					<complexContent>
						<extension base="ans:ct_procedimentoAutorizado"/>
					</complexContent>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ctm_autorizacaoRadio">
		<sequence>
			<element name="dadosAutorizacao" type="ans:ct_autorizacaoDados"/>
			<element name="numeroCarteira" type="ans:st_texto20"/>
			<element name="nomeBeneficiario" type="ans:st_texto70"/>
			<!--removido da versÃ£o 4.00.00
			<element name="numeroCNS" type="ans:st_texto15" minOccurs="0"/>
			-->
			<element name="nomeSocialBeneficiario" type="ans:st_texto70" minOccurs="0"/>
			<element name="statusSolicitacao" type="ans:dm_statusSolicitacao"/>
			<element name="dadosComplementaresBeneficiario" type="ans:ct_dadosComplementaresBeneficiarioRadio"/>
			<element name="medicoSolicitante" type="ans:ctm_anexoSolicitante"/>
			<element name="diagnosticoOncologicoRadio">
				<complexType>
					<sequence>
						<element name="diagRadio" type="ans:ct_diagnosticoOncologico"/>
						<element name="diagnosticoImagem" type="ans:dm_diagnosticoImagem" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
			<element name="tratamentosAnteriores" minOccurs="0">
				<complexType>
					<sequence>
						<element name="cirurgia" type="ans:st_texto40" minOccurs="0"/>
						<element name="datacirurgia" type="ans:st_data" minOccurs="0"/>
						<element name="quimioterapia" type="ans:st_texto40" minOccurs="0"/>
						<element name="dataQuimioterapia" type="ans:st_data" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
			<element name="numeroCampos" type="ans:st_numerico3"/>
			<element name="doseCampo" type="ans:st_numerico4"/>
			<element name="doseTotal" type="ans:st_numerico4"/>
			<element name="nrDias" type="ans:st_numerico3"/>
			<element name="dtPrevistaInicio" type="ans:st_data"/>
			<element name="motivosNegativa" minOccurs="0">
				<complexType>
					<sequence>
						<element name="motivoNegativa" type="ans:ct_motivoGlosa" maxOccurs="unbounded"/>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ctm_autorizacaoQuimio">
		<sequence>
			<element name="dadosAutorizacao" type="ans:ct_autorizacaoDados"/>
			<element name="numeroCarteira" type="ans:st_texto20"/>
			<element name="statusSolicitacao" type="ans:dm_statusSolicitacao"/>
			<element name="nomeBeneficiario" type="ans:st_texto70"/>
			<!--retirado na versÃ£o 4.00.00
			<element name="numeroCNS" type="ans:st_texto15" minOccurs="0"/>
			-->
			<element name="nomeSocialBeneficiario" type="ans:st_texto70" minOccurs="0"/>
			<element name="dadosComplementaresBeneficiario" type="ans:ct_dadosComplementaresBeneficiario"/>
			<element name="medicoSolicitante" type="ans:ctm_anexoSolicitante"/>
			<element name="diagnosticoOncologicoQuimioterapia">
				<complexType>
					<sequence>
						<element name="diagQuimio" type="ans:ct_diagnosticoOncologico"/>
						<element name="tumor" type="ans:dm_tumor"/>
						<element name="nodulo" type="ans:dm_nodulo"/>
						<element name="metastase" type="ans:dm_metastase"/>
						<element name="tipoQuimioterapia" type="ans:dm_tipoQuimioterapia"/>
						<element name="planoTerapeutico" type="ans:st_texto1000"/>
					</sequence>
				</complexType>
			</element>
			<element name="drogasSolicitadas">
				<complexType>
					<sequence>
						<element name="drogaSolicitada" type="ans:ct_drogasSolicitadas" maxOccurs="unbounded"/>
					</sequence>
				</complexType>
			</element>
			<element name="tratamentosAnteriores" minOccurs="0">
				<complexType>
					<sequence>
						<element name="cirurgia" type="ans:st_texto40" minOccurs="0"/>
						<element name="datacirurgia" type="ans:st_data" minOccurs="0"/>
						<element name="areaIrradiada" type="ans:st_texto40" minOccurs="0"/>
						<element name="dataIrradiacao" type="ans:st_data" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
			<element name="numeroCiclos" type="ans:st_numerico2"/>
			<element name="cicloAtual" type="ans:st_numerico2"/>
			<element name="diasCicloAtual" type="ans:st_numerico3"/>
			<element name="intervaloCiclos" type="ans:st_numerico3"/>
			<element name="motivosNegativa" minOccurs="0">
				<complexType>
					<sequence>
						<element name="motivoNegativa" type="ans:ct_motivoGlosa" maxOccurs="unbounded"/>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<!-- Utilizado na resposta da operadora na autorizaÃ§Ã£o de solic de internaÃ§Ã£o e de prorrogaÃ§Ã£o de internaÃ§Ã£o -->
	<complexType name="ctm_autorizacaoServico">
		<sequence>
			<element name="dadosAutorizacao" type="ans:ct_autorizacaoDados"/>
			<element name="tipoEtapaAutorizacao" type="ans:dm_etapasAutorizacao"/>
			<element name="dadosBeneficiario" type="ans:ct_beneficiarioDados"/>
			<element name="nomeBeneficiario" type="ans:st_texto70"/>
			<element name="nomeSocialBeneficiario" type="ans:st_texto70" minOccurs="0"/>
			<element name="prestadorAutorizado" minOccurs="0">
				<complexType>
					<sequence>
						<element name="dadosContratado" type="ans:ct_contratadoDados"/>
						<element name="cnesContratado" type="ans:st_texto7"/>
					</sequence>
				</complexType>
			</element>
			<element name="statusSolicitacao" type="ans:dm_statusSolicitacao"/>
			<element name="motivosNegativa" minOccurs="0">
				<complexType>
					<sequence>
						<!-- alterado na versÃ£o 4.00.00
						<element name="motivoNegativa" type="ans:ct_motivoGlosa" maxOccurs="unbounded"/>
						-->
						<!-- alterado na versÃ£o 4.00.00 -->
						<element name="codigoGlosa" type="ans:dm_tipoGlosa" maxOccurs="unbounded"/>
					</sequence>
				</complexType>
			</element>
			<element name="servicosAutorizados" minOccurs="0">
				<complexType>
					<sequence>
						<element name="servicoAutorizado" type="ans:ct_procedimentoAutorizado" maxOccurs="unbounded"/>
					</sequence>
				</complexType>
			</element>
			<element name="observacao" type="ans:st_texto1000" minOccurs="0"/>
			<element name="autorizacaoQuimio" type="ans:ctm_autorizacaoQuimio" minOccurs="0"/>
			<element name="autorizacaoRadio" type="ans:ctm_autorizacaoRadio" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ctm_beneficiarioComunicacao">
		<sequence>
			<element name="dadosBeneficiario" type="ans:ct_beneficiarioDados"/>
			<element name="dataEvento" type="ans:st_data"/>
			<element name="tipoEvento" type="ans:dm_tipoEvento"/>
			<element name="dadosInternacao">
				<complexType>
					<choice>
						<element name="motivoEncerramento" type="ans:dm_motivoSaida"/>
						<element name="tipoInternacao" type="ans:dm_tipoInternacao"/>
					</choice>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<!-- incluido na versao 4.00.00 -->
	<complexType name="ctm_beneficiarioComunicacaoRet">
		<sequence>
			<element name="dadosBeneficiario" type="ans:ct_beneficiarioDados"/>
			<element name="nomeBeneficiario" type="ans:st_texto70"/>
			<element name="nomeSocialBeneficiario" type="ans:st_texto70" minOccurs="0"/>
			<element name="tipoEvento" type="ans:dm_tipoEvento"/>
			<element name="dadosInternacao">
				<complexType>
					<choice>
						<element name="motivoEncerramento" type="ans:dm_motivoSaida"/>
						<element name="tipoInternacao" type="ans:dm_tipoInternacao"/>
					</choice>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ctm_beneficiarioComunicacaoRecibo">
		<sequence>
			<element name="statusComunicacao" type="ans:dm_simNao"/>
			<!-- alterado na versÃ£o 4.00.00
			<element name="beneficiarioComunicacao" type="ans:ctm_beneficiarioComunicacao"/>
			-->
			<element name="beneficiarioComunicacaoRet" type="ans:ctm_beneficiarioComunicacaoRet"/>
			<element name="codigoGlosa" type="ans:dm_tipoGlosa" minOccurs="0"/>
		</sequence>
	</complexType>
	<complexType name="ctm_consultaAtendimento">
		<sequence>
			<element name="coberturaEspecial" type="ans:dm_cobEsp" minOccurs="0"/>
			<element name="regimeAtendimento" type="ans:dm_regimeAtendimento"/>
			<element name="saudeOcupacional" type="ans:dm_saudeOcupacional" minOccurs="0"/>
			<element name="dataAtendimento" type="ans:st_data"/>
			<element name="tipoConsulta" type="ans:dm_tipoConsulta"/>
			<element name="procedimento">
				<complexType>
					<sequence>
						<element name="codigoTabela" type="ans:dm_tabela"/>
						<element name="codigoProcedimento" type="ans:st_texto10"/>
						<element name="valorProcedimento" type="ans:st_decimal8-2"/>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<!--============================= GUIA DE CONSULTA ===========================-->
	<complexType name="ctm_consultaGuia">
		<sequence>
			<element name="cabecalhoConsulta" type="ans:ct_guiaCabecalho"/>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="ausenciaCodValidacao" type="ans:dm_ausenciaCodValidacao" minOccurs="0"/>
			<element name="codValidacao" type="ans:st_texto10" minOccurs="0"/>
			<element name="dadosBeneficiario" type="ans:ct_beneficiarioDados"/>
			<element name="contratadoExecutante">
				<complexType>
					<complexContent>
						<extension base="ans:ct_contratadoDados">
							<sequence>
								<element name="CNES" type="ans:st_texto7"/>
							</sequence>
						</extension>
					</complexContent>
				</complexType>
			</element>
			<element name="profissionalExecutante" type="ans:ct_contratadoProfissionalDados"/>
			<element name="indicacaoAcidente" type="ans:dm_indicadorAcidente"/>
			<element name="dadosAtendimento" type="ans:ctm_consultaAtendimento"/>
			<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
			<element name="assinaturaDigitalGuia" type="ans:Signature" minOccurs="0"/>
			<!--<element name="assinaturaDigitalGuia" type="ans:assinaturaDigital" minOccurs="0"/>-->
		</sequence>
	</complexType>
	<!--=============================  DEMONSTRATIVO DE ANALISE DE CONTA ===========================-->
	<complexType name="ctm_demonstrativoAnaliseConta">
		<sequence>
			<element name="cabecalhoDemonstrativo" type="ans:ct_demonstrativoCabecalho"/>
			<element name="dadosPrestador">
				<complexType>
					<sequence>
						<element name="dadosContratado" type="ans:ct_contratadoDados"/>
						<element name="CNES" type="ans:st_texto7"/>
					</sequence>
				</complexType>
			</element>
			<element name="dadosConta">
				<complexType>
					<sequence>
						<element name="dadosProtocolo" maxOccurs="unbounded">
							<complexType>
								<complexContent>
									<extension base="ans:ct_contaMedicaResumo"/>
								</complexContent>
							</complexType>
						</element>
					</sequence>
				</complexType>
			</element>
			<element name="valorInformadoGeral" type="ans:st_decimal10-2"/>
			<element name="valorProcessadoGeral" type="ans:st_decimal10-2"/>
			<element name="valorLiberadoGeral" type="ans:st_decimal10-2"/>
			<element name="valorGlosaGeral" type="ans:st_decimal10-2" minOccurs="0"/>
		</sequence>
	</complexType>
	<!--============================= DEMONSTRATIVO DE PAGAMENTO ===========================-->
	<complexType name="ctm_demonstrativoPagamento">
		<sequence>
			<element name="cabecalhoDemonstrativo" type="ans:ct_demonstrativoCabecalho"/>
			<element name="dadosContratado">
				<complexType>
					<sequence>
						<element name="dadosPrestador" type="ans:ct_contratadoDados"/>
						<element name="CNES" type="ans:st_texto7"/>
					</sequence>
				</complexType>
			</element>
			<element name="pagamentos">
				<complexType>
					<sequence>
						<element name="pagamentosPorData" maxOccurs="unbounded">
							<complexType>
								<sequence>
									<element name="dadosPagamento" type="ans:ct_pagamentoDados"/>
									<element name="dadosResumo">
										<complexType>
											<sequence>
												<element name="relacaoProtocolos" type="ans:ct_dadosResumoDemonstrativo" maxOccurs="unbounded"/>
											</sequence>
										</complexType>
									</element>
									<element name="totaisBrutosPorData">
										<complexType>
											<sequence>
												<element name="totalInformadoPorData" type="ans:st_decimal10-2"/>
												<element name="totalProcessadoPorData" type="ans:st_decimal10-2"/>
												<element name="totaLiberadoPorData" type="ans:st_decimal10-2"/>
												<element name="totalGlosaPorData" type="ans:st_decimal10-2"/>
											</sequence>
										</complexType>
									</element>
									<element name="debitosCreditosPorData" minOccurs="0">
										<complexType>
											<sequence>
												<element name="debitosCreditos" type="ans:ct_descontos" maxOccurs="unbounded"/>
											</sequence>
										</complexType>
									</element>
									<element name="totaisLiquidosPorData">
										<complexType>
											<sequence>
												<element name="totalDebitosPorData" type="ans:st_decimal10-2"/>
												<element name="totalCreditosPorData" type="ans:st_decimal10-2"/>
												<element name="liquidoPorData" type="ans:st_decimal10-2"/>
											</sequence>
										</complexType>
									</element>
								</sequence>
							</complexType>
						</element>
					</sequence>
				</complexType>
			</element>
			<element name="totaisDemonstrativo">
				<complexType>
					<sequence>
						<element name="totaisBrutosDemonstrativo">
							<complexType>
								<sequence>
									<element name="valorInformadoBruto" type="ans:st_decimal10-2"/>
									<element name="valorProcessadoBruto" type="ans:st_decimal10-2"/>
									<element name="valorLiberadoBruto" type="ans:st_decimal10-2"/>
									<element name="valorGlosaBruto" type="ans:st_decimal10-2"/>
								</sequence>
							</complexType>
						</element>
						<element name="debitosCreditosDemonstrativo" type="ans:ct_descontos" minOccurs="0" maxOccurs="unbounded"/>
						<element name="totaisLiquidosDemonstrativo">
							<complexType>
								<sequence>
									<element name="totalDebitosDemonstrativo" type="ans:st_decimal10-2"/>
									<element name="totalCreditosdemonstrativo" type="ans:st_decimal10-2"/>
									<element name="valorLiberadoDemonstrativo" type="ans:st_decimal10-2"/>
								</sequence>
							</complexType>
						</element>
					</sequence>
				</complexType>
			</element>
			<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
		</sequence>
	</complexType>
	<!-- LOTE DE GUIAS A SER ENVIADO A OPERADORA PELO PRESTADOR -->
	<complexType name="ctm_guiaLote">
		<sequence>
			<element name="numeroLote" type="ans:st_texto12"/>
			<element name="guiasTISS">
				<complexType>
					<choice>
						<element name="guiaSP-SADT" type="ans:ctm_sp-sadtGuia" maxOccurs="100"/>
						<element name="guiaResumoInternacao" type="ans:ctm_internacaoResumoGuia" maxOccurs="100"/>
						<element name="guiaHonorarios" type="ans:ctm_honorarioIndividualGuia" maxOccurs="100"/>
						<element name="guiaConsulta" type="ans:ctm_consultaGuia" maxOccurs="100"/>
						<element name="guiaOdonto" type="ans:cto_guiaOdontologia" maxOccurs="100"/>
					</choice>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<!--============================= GUIA DE HONORARIO INDIVIDUAL ===========================-->
	<complexType name="ctm_honorarioIndividualGuia">
		<sequence>
			<element name="cabecalhoGuia" type="ans:ct_guiaCabecalho"/>
			<element name="guiaSolicInternacao" type="ans:st_texto20"/>
			<element name="senha" type="ans:st_texto20" minOccurs="0"/>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<!-- dados beneficiario -->
			<element name="beneficiario">
				<complexType>
					<sequence>
						<element name="numeroCarteira" type="ans:st_texto20"/>
						<!-- retirado na versÃ£o 4.00.00
						<element name="nomeBeneficiario" type="ans:st_texto70"/>
						-->
						<element name="atendimentoRN" type="ans:dm_simNao"/>
					</sequence>
				</complexType>
			</element>
			<!-- dados do contratado - onde foi executado o procedimento -->
			<element name="localContratado">
				<complexType>
					<sequence>
						<element name="codigoContratado">
							<complexType>
								<choice>
									<element name="codigoNaOperadora" type="ans:st_texto14"/>
									<element name="cnpjLocalExecutante" type="ans:st_CNPJ"/>
								</choice>
							</complexType>
						</element>
						<element name="nomeContratado" type="ans:st_texto70"/>
						<element name="cnes" type="ans:st_texto7"/>
					</sequence>
				</complexType>
			</element>
			<element name="dadosContratadoExecutante">
				<complexType>
					<sequence>
						<element name="codigonaOperadora" type="ans:st_texto14"/>
						<!-- retirado na versÃ£o 4.00.00
						<element name="nomeContratadoExecutante" type="ans:st_texto70"/>
						-->
						<element name="cnesContratadoExecutante" type="ans:st_texto7"/>
					</sequence>
				</complexType>
			</element>
			<element name="dadosInternacao">
				<complexType>
					<sequence>
						<element name="dataInicioFaturamento" type="ans:st_data"/>
						<element name="dataFimFaturamento" type="ans:st_data"/>
					</sequence>
				</complexType>
			</element>
			<element name="procedimentosRealizados">
				<complexType>
					<sequence>
						<element name="procedimentoRealizado" type="ans:ct_procedimentoExecutadoHonorIndiv" maxOccurs="unbounded"/>
					</sequence>
				</complexType>
			</element>
			<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
			<element name="valorTotalHonorarios" type="ans:st_decimal10-2"/>
			<element name="dataEmissaoGuia" type="ans:st_data"/>
			<element name="assinaturaDigitalGuia" type="ans:Signature" minOccurs="0"/>
			<!--<element name="assinaturaDigitalGuia" type="ans:assinaturaDigital" minOccurs="0"/>-->
		</sequence>
	</complexType>
	<!-- Fim de honorario individual -->
	<complexType name="ctm_internacaoDados">
		<sequence>
			<element name="caraterAtendimento" type="ans:dm_caraterAtendimento"/>
			<element name="tipoFaturamento" type="ans:dm_tipoFaturamento"/>
			<element name="dataInicioFaturamento" type="ans:st_data"/>
			<element name="horaInicioFaturamento" type="ans:st_hora"/>
			<element name="dataFinalFaturamento" type="ans:st_data"/>
			<element name="horaFinalFaturamento" type="ans:st_hora"/>
			<element name="tipoInternacao" type="ans:dm_tipoInternacao"/>
			<element name="regimeInternacao" type="ans:dm_regimeInternacao"/>
			<!--<element name="qtRNutiNeonatal" type="ans:st_numerico2" minOccurs="0"/>
			<element name="obitoMulher" type="ans:dm_obitoMulher" minOccurs="0"/>
			<element name="qtObitoNeonatalPrecoce" type="ans:st_numerico2" minOccurs="0"/>
			<element name="qtObitoNeonatalTardio" type="ans:st_numerico2" minOccurs="0"/>
			<element name="qtNascidoVivoTermo" type="ans:st_numerico2" minOccurs="0"/>
			<element name="qtNascidoMorto" type="ans:st_numerico2" minOccurs="0"/>
			<element name="qtNascidoPrematuro" type="ans:st_numerico2" minOccurs="0"/>-->
			<element name="declaracoes" minOccurs="0" maxOccurs="8">
				<complexType>
					<sequence>
						<element name="declaracaoNascido" type="ans:dm_declaracaoNascidoObito" minOccurs="0"/>
						<element name="diagnosticoObito" type="ans:st_texto4" minOccurs="0"/>
						<element name="declaracaoObito" type="ans:dm_declaracaoNascidoObito" minOccurs="0"/>
						<element name="indicadorDORN" type="ans:dm_simNao" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="ctm_internacaoDadosSaida">
		<sequence>
			<element name="diagnostico" type="ans:st_texto4" minOccurs="0" maxOccurs="4"/>
			<element name="indicadorAcidente" type="ans:dm_indicadorAcidente"/>
			<element name="motivoEncerramento" type="ans:dm_motivoSaida"/>
		</sequence>
	</complexType>
	<!--============================= GUIA DE RESUMO DE INTERNACAO ===========================-->
	<complexType name="ctm_internacaoResumoGuia">
		<sequence>
			<element name="cabecalhoGuia" type="ans:ct_guiaCabecalho"/>
			<element name="numeroGuiaSolicitacaoInternacao" type="ans:st_texto20"/>
			<element name="dadosAutorizacao" type="ans:ct_autorizacaoInternacao"/>
			<element name="dadosBeneficiario" type="ans:ct_beneficiarioDados"/>
			<element name="dadosExecutante">
				<complexType>
					<sequence>
						<element name="contratadoExecutante" type="ans:ct_contratadoDados"/>
						<element name="CNES" type="ans:st_texto7"/>
					</sequence>
				</complexType>
			</element>
			<element name="dadosInternacao" type="ans:ctm_internacaoDados"/>
			<element name="dadosSaidaInternacao" type="ans:ctm_internacaoDadosSaida"/>
			<element name="procedimentosExecutados" minOccurs="0">
				<complexType>
					<sequence>
						<element name="procedimentoExecutado" type="ans:ct_procedimentoExecutadoInt" maxOccurs="unbounded"/>
					</sequence>
				</complexType>
			</element>
			<element name="valorTotal" type="ans:ct_guiaValorTotal"/>
			<element name="outrasDespesas" type="ans:ct_outrasDespesas" minOccurs="0"/>
			<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
			<!--	<element name="relatorioTecnico" type="base64Binary" minOccurs="0"/>-->
			<element name="assinaturaDigitalGuia" type="ans:Signature" minOccurs="0"/>
			<!--<element name="assinaturaDigitalGuia" type="ans:assinaturaDigital" minOccurs="0"/>-->
		</sequence>
	</complexType>
	<!--============================= GUIA DE SOLICITACAO DE INTERNACAO ===========================-->
	<complexType name="ctm_internacaoSolicitacaoGuia">
		<sequence>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
			<element name="ausenciaCodValidacao" type="ans:dm_ausenciaCodValidacao" minOccurs="0"/>
			<element name="codValidacao" type="ans:st_texto10" minOccurs="0"/>
			<element name="tipoEtapaAutorizacao" type="ans:dm_etapasAutorizacao"/>
			<element name="dadosBeneficiario" type="ans:ct_beneficiarioDados"/>
			<element name="identificacaoSolicitante">
				<complexType>
					<sequence>
						<element name="dadosDoContratado" type="ans:ct_contratadoDados"/>
						<element name="dadosProfissionalContratado" type="ans:ct_contratadoProfissionalDados"/>
					</sequence>
				</complexType>
			</element>
			<element name="dadosHospitalSolicitado">
				<complexType>
					<sequence>
						<element name="codigoIndicadonaOperadora" type="ans:st_texto14"/>
						<element name="nomeContratadoIndicado" type="ans:st_texto70"/>
						<element name="dataSugeridaInternacao" type="ans:st_data"/>
					</sequence>
				</complexType>
			</element>
			<element name="dadosInternacao">
				<complexType>
					<sequence>
						<element name="caraterAtendimento" type="ans:dm_caraterAtendimento"/>
						<element name="tipoInternacao" type="ans:dm_tipoInternacao"/>
						<element name="regimeInternacao" type="ans:dm_regimeInternacao"/>
						<element name="qtDiariasSolicitadas" type="ans:st_numerico2"/>
						<element name="indicadorOPME" type="ans:dm_simNao"/>
						<element name="indicadorQuimio" type="ans:dm_simNao"/>
						<element name="indicacaoClinica" type="ans:st_texto500"/>
					</sequence>
				</complexType>
			</element>
			<element name="hipotesesDiagnosticas">
				<complexType>
					<sequence>
						<element name="diagnosticoCID" type="ans:st_texto4" minOccurs="0" maxOccurs="4"/>
						<element name="indicadorAcidente" type="ans:dm_indicadorAcidente"/>
					</sequence>
				</complexType>
			</element>
			<element name="procedimentosSolicitados" maxOccurs="unbounded">
				<complexType>
					<sequence>
						<element name="procedimento" type="ans:ct_procedimentoDados"/>
						<element name="quantidadeSolicitada" type="ans:st_numerico3"/>
					</sequence>
				</complexType>
			</element>
			<element name="dataSolicitacao" type="ans:st_data"/>
			<element name="observacao" type="ans:st_texto1000" minOccurs="0"/>
			<!--<element name="relatorioTecnico" type="base64Binary" minOccurs="0"/>-->
			<element name="anexoClinico" minOccurs="0">
				<complexType>
					<sequence>
						<element name="solicitacaoQuimioterapia" type="ans:ctm_anexoSolicitacaoQuimio" minOccurs="0"/>
						<element name="solicitacaoRadioterapia" type="ans:ctm_anexoSolicitacaoRadio" minOccurs="0"/>
						<element name="solicitacaoOPME" type="ans:ctm_anexoSolicitacaoOPME" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<!-- Fim da solicitaÃ§Ã£o de internaÃ§Ã£o -->
	<!--============================= GUIA DE SOLICITACAO DE PRORROGAÃ?Ã?O DE INTERNACAO ===========================-->
	<complexType name="ctm_prorrogacaoSolicitacaoGuia">
		<sequence>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
			<element name="nrGuiaReferenciada" type="ans:st_texto20"/>
			<element name="dadosBeneficiario">
				<complexType>
					<sequence>
						<element name="numeroCarteira" type="ans:st_texto20"/>
						<!-- retirado na versÃ£o 4.00.00
						<element name="nomeBeneficiario" type="ans:st_texto70"/>
						-->
						<element name="tipoIdent" type="ans:dm_tipoIdent" minOccurs="0"/>
						<element name="identificadorBeneficiario" type="base64Binary" minOccurs="0"/>
						<!-- retirado na versÃ£o 4.00.00
						<element name="templateBiometrico" type="base64Binary" minOccurs="0"/>
						-->
					</sequence>
				</complexType>
			</element>
			<element name="dadosContratadoSolicitante" type="ans:ct_contratadoDados"/>
			<element name="nomeContratadoSolicitante" type="ans:st_texto70"/>
			<element name="dadosProfissionalSolicitante" type="ans:ct_contratadoProfissionalDados"/>
			<element name="dadosInternacao">
				<complexType>
					<sequence>
						<element name="qtDiariasAdicionais" type="ans:st_numerico3" minOccurs="0"/>
						<element name="tipoAcomodacaoSolicitada" type="ans:dm_tipoAcomodacao" minOccurs="0"/>
						<element name="indicacaoClinica" type="ans:st_texto500"/>
					</sequence>
				</complexType>
			</element>
			<element name="procedimentosAdicionais" minOccurs="0" maxOccurs="unbounded">
				<complexType>
					<sequence>
						<element name="procedimento" type="ans:ct_procedimentoDados"/>
						<element name="quantidadeSolicitada" type="ans:st_numerico3"/>
					</sequence>
				</complexType>
			</element>
			<element name="anexoClinicoProrrogacao" minOccurs="0">
				<complexType>
					<sequence>
						<element name="solicitacaoQuimioterapia" type="ans:ctm_anexoSolicitacaoQuimio" minOccurs="0"/>
						<element name="solicitacaoRadioterapia" type="ans:ctm_anexoSolicitacaoRadio" minOccurs="0"/>
						<element name="solicitacaoOPME" type="ans:ctm_anexoSolicitacaoOPME" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
			<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
			<!--<element name="relatorioTecnico" type="base64Binary" minOccurs="0"/>-->
			<element name="dataSolicitacao" type="ans:st_data"/>
		</sequence>
	</complexType>
	<!-- Fim da solicitaÃ§Ã£o de prorrogaÃ§Ã£o de internaÃ§Ã£o -->
	<complexType name="ctm_sp-sadtAtendimento">
		<sequence>
			<element name="tipoAtendimento" type="ans:dm_tipoAtendimento"/>
			<element name="indicacaoAcidente" type="ans:dm_indicadorAcidente"/>
			<element name="tipoConsulta" type="ans:dm_tipoConsulta" minOccurs="0"/>
			<element name="motivoEncerramento" type="ans:dm_motivoSaidaObito" minOccurs="0"/>
			<element name="regimeAtendimento" type="ans:dm_regimeAtendimento"/>
			<element name="saudeOcupacional" type="ans:dm_saudeOcupacional" minOccurs="0"/>
		</sequence>
	</complexType>
	<!-- estrutura das solicitaÃ§Ãµes de autorizaÃ§Ã£o do prestador para a operadora  de sp_sadt, internaÃ§Ã£o e prorrogaÃ§Ã£o -->
	<complexType name="ctm_solicitacaoLote">
		<choice>
			<element name="solicitacaoSP-SADT" type="ans:ctm_sp-sadtSolicitacaoGuia"/>
			<element name="solicitacaoInternacao" type="ans:ctm_internacaoSolicitacaoGuia"/>
			<element name="solicitacaoProrrogacao" type="ans:ctm_prorrogacaoSolicitacaoGuia"/>
			<element name="solicitacaoOdontologia" type="ans:cto_odontoSolicitacaoGuia"/>
		</choice>
	</complexType>
	<!--============================= GUIA DE SOLICITACAO DE SP E SADT (execuÃ§Ã£o) ==============-->
	<complexType name="ctm_sp-sadtGuia">
		<sequence>
			<element name="cabecalhoGuia">
				<complexType>
					<complexContent>
						<extension base="ans:ct_guiaCabecalho">
							<sequence>
								<element name="guiaPrincipal" type="ans:st_texto20" minOccurs="0"/>
							</sequence>
						</extension>
					</complexContent>
				</complexType>
			</element>
			<element name="dadosAutorizacao" type="ans:ct_autorizacaoSADT" minOccurs="0"/>
			<element name="dadosBeneficiario" type="ans:ct_beneficiarioDados"/>
			<element name="dadosSolicitante">
				<complexType>
					<sequence>
						<element name="contratadoSolicitante" type="ans:ct_contratadoDados"/>
						<element name="nomeContratadoSolicitante" type="ans:st_texto70"/>
						<element name="profissionalSolicitante" type="ans:ct_contratadoProfissionalDados"/>
					</sequence>
				</complexType>
			</element>
			<element name="dadosSolicitacao">
				<complexType>
					<sequence>
						<element name="dataSolicitacao" type="ans:st_data" minOccurs="0"/>
						<element name="caraterAtendimento" type="ans:dm_caraterAtendimento"/>
						<element name="indicacaoClinica" type="ans:st_texto500" minOccurs="0"/>
						<!-- incluido na versÃ£o 4.00.00 -->
						<element name="indCobEspecial" type="ans:dm_cobEsp" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
			<element name="dadosExecutante">
				<complexType>
					<sequence>
						<element name="contratadoExecutante" type="ans:ct_contratadoDados"/>
						<element name="CNES" type="ans:st_texto7"/>
					</sequence>
				</complexType>
			</element>
			<element name="dadosAtendimento" type="ans:ctm_sp-sadtAtendimento"/>
			<element name="procedimentosExecutados" minOccurs="0">
				<complexType>
					<sequence>
						<element name="procedimentoExecutado" type="ans:ct_procedimentoExecutadoSadt" maxOccurs="unbounded"/>
					</sequence>
				</complexType>
			</element>
			<element name="outrasDespesas" type="ans:ct_outrasDespesas" minOccurs="0"/>
			<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
			<element name="valorTotal" type="ans:ct_guiaValorTotal"/>
			<!--<element name="relatorioTecnico" type="base64Binary" minOccurs="0"/>-->
			<element name="assinaturaDigitalGuia" type="ans:Signature" minOccurs="0"/>
			<!--<element name="assinaturaDigitalGuia" type="ans:assinaturaDigital" minOccurs="0"/>-->
		</sequence>
	</complexType>
	<!--============================= GUIA DE SOLICITACAO DE SP E SADT ===========================-->
	<complexType name="ctm_sp-sadtSolicitacaoGuia">
		<sequence>
			<element name="cabecalhoSolicitacao" type="ans:ct_guiaCabecalho"/>
			<element name="numeroGuiaPrincipal" type="ans:st_texto20" minOccurs="0"/>
			<element name="ausenciaCodValidacao" type="ans:dm_ausenciaCodValidacao" minOccurs="0"/>
			<element name="codValidacao" type="ans:st_texto10" minOccurs="0"/>
			<element name="tipoEtapaAutorizacao" type="ans:dm_etapasAutorizacao"/>
			<element name="dadosBeneficiario" type="ans:ct_beneficiarioDados"/>
			<element name="dadosSolicitante">
				<complexType>
					<sequence>
						<element name="contratadoSolicitante" type="ans:ct_contratadoDados"/>
						<element name="nomeContratadoSolicitante" type="ans:st_texto70"/>
						<element name="profissionalSolicitante" type="ans:ct_contratadoProfissionalDados"/>
					</sequence>
				</complexType>
			</element>
			<element name="caraterAtendimento" type="ans:dm_caraterAtendimento"/>
			<element name="dataSolicitacao" type="ans:st_data"/>
			<element name="indicacaoClinica" type="ans:st_texto500" minOccurs="0"/>
			<element name="coberturaEspecial" type="ans:dm_cobEsp" minOccurs="0"/>
			<element name="procedimentosSolicitados" maxOccurs="unbounded">
				<complexType>
					<sequence>
						<element name="procedimento" type="ans:ct_procedimentoDados"/>
						<element name="quantidadeSolicitada" type="ans:st_numerico3"/>
					</sequence>
				</complexType>
			</element>
			<element name="dadosExecutante" minOccurs="0">
				<complexType>
					<sequence>
						<element name="codigonaOperadora" type="ans:st_texto14"/>
						<!-- retirado na versÃ£o  4.00.00
						<element name="nomeContratado" type="ans:st_texto70"/>
						-->
						<element name="CNES" type="ans:st_texto7"/>
					</sequence>
				</complexType>
			</element>
			<element name="anexoClinico" minOccurs="0">
				<complexType>
					<sequence>
						<element name="solicitacaoQuimioterapia" type="ans:ctm_anexoSolicitacaoQuimio" minOccurs="0"/>
						<element name="solicitacaoRadioterapia" type="ans:ctm_anexoSolicitacaoRadio" minOccurs="0"/>
						<element name="solicitacaoOPME" type="ans:ctm_anexoSolicitacaoOPME" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
			<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
			<!--	<element name="relatorioTecnico" type="base64Binary" minOccurs="0"/>-->
		</sequence>
	</complexType>
	<!-- ==========================  RECURSO DE GLOSA =========================== -->
	<complexType name="ctm_recursoGlosa">
		<sequence>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="numeroGuiaRecGlosaPrestador" type="ans:st_texto20"/>
			<element name="nomeOperadora" type="ans:st_texto70"/>
			<element name="objetoRecurso" type="ans:dm_objetoRecurso"/>
			<element name="numeroGuiaRecGlosaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="dadosContratado" type="ans:ct_contratadoDados"/>
			<element name="numeroLote" type="ans:st_texto12"/>
			<element name="numeroProtocolo" type="ans:st_numerico12"/>
			<element name="opcaoRecurso">
				<complexType>
					<choice>
						<element name="recursoProtocolo">
							<complexType>
								<sequence>
									<element name="codigoGlosaProtocolo" type="ans:dm_tipoGlosa"/>
									<!-- alterado na versÃ£o 4.00.00 -->
									<element name="justificativaProtocolo" type="ans:st_texto500"/>
								</sequence>
							</complexType>
						</element>
						<element name="recursoGuia" maxOccurs="100">
							<complexType>
								<sequence>
									<element name="numeroGuiaOrigem" type="ans:st_texto20"/>
									<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
									<element name="senha" type="ans:st_texto20" minOccurs="0"/>
									<element name="opcaoRecursoGuia">
										<complexType>
											<choice>
												<element name="recursoGuiaCompleta" maxOccurs="unbounded">
													<complexType>
														<sequence>
															<element name="codGlosaGuia" type="ans:dm_tipoGlosa"/>
															<!-- alterado na versÃ£o 4.00.00 -->
															<element name="justificativaGuia" type="ans:st_texto500"/>
														</sequence>
													</complexType>
												</element>
												<element name="itensGuia" maxOccurs="unbounded">
													<complexType>
														<sequence>
															<element name="sequencialItem" type="ans:st_numerico4"/>
															<element name="dataInicio" type="ans:st_data"/>
															<element name="dataFim" type="ans:st_data" minOccurs="0"/>
															<element name="procRecurso" type="ans:ct_procedimentoDados"/>
															<element name="grauParticipacao" type="ans:dm_grauPart" minOccurs="0"/>
															<element name="codGlosaItem" type="ans:dm_tipoGlosa"/>
															<element name="valorRecursado" type="ans:st_decimal8-2"/>
															<!--alterado na versÃ£o 4.00.00 -->
															<element name="justificativaItem" type="ans:st_texto500"/>
														</sequence>
													</complexType>
												</element>
											</choice>
										</complexType>
									</element>
								</sequence>
							</complexType>
						</element>
					</choice>
				</complexType>
			</element>
			<element name="valorTotalRecursado" type="ans:st_decimal10-2"/>
			<element name="dataRecurso" type="ans:st_data"/>
		</sequence>
	</complexType>
	<!-- Utilizado na resposta da operadora na autorizaÃ§Ã£o de serviÃ§os da odontologia -->
	<complexType name="cto_autorizacaoServico">
		<sequence>
			<element name="dadosAutorizacao" type="ans:ct_autorizacaoDados"/>
			<element name="numeroCarteira" type="ans:st_texto20"/>
			<element name="nomeBeneficiario" type="ans:st_texto70"/>
			<!-- retirado na versÃ£o 4.00.00
			<element name="numeroCNS" type="ans:st_texto15" minOccurs="0"/>
			-->
			<element name="nomeSocialBeneficiario" type="ans:st_texto70" minOccurs="0"/>
			<element name="tipoIdent" type="ans:dm_tipoIdent" minOccurs="0"/>
			<element name="identificadorBeneficiario" type="base64Binary" minOccurs="0"/>
			<!-- retirado na versÃ£o 4.00.00
			<element name="templateBiometrico" type="base64Binary" minOccurs="0"/>
			-->
			<element name="statusSolicitacao" type="ans:dm_statusSolicitacao"/>
			<element name="prestadorAutorizado" type="ans:ct_contratadoDados" minOccurs="0"/>
			<element name="procedimentosAutorizados" minOccurs="0" maxOccurs="unbounded">
				<complexType>
					<sequence>
						<element name="sequencialItem" type="ans:st_numerico4"/>
						<element name="procSolic" type="ans:ct_procedimentoDados"/>
						<element name="denteRegiao" minOccurs="0">
							<complexType>
								<choice>
									<element name="codDente" type="ans:dm_dente"/>
									<element name="codRegiao" type="ans:dm_regiao"/>
								</choice>
							</complexType>
						</element>
						<element name="denteFace" type="ans:st_texto5" minOccurs="0"/>
						<element name="qtdProc" type="ans:st_numerico2"/>
						<element name="qtdUS" type="ans:st_decimal8-2" minOccurs="0"/>
						<element name="valorProc" type="ans:st_decimal8-2"/>
						<element name="valorFranquia" type="ans:st_decimal8-2" minOccurs="0"/>
						<element name="aut" type="ans:dm_simNao"/>
						<element name="motivosNegativa" minOccurs="0">
							<complexType>
								<sequence>
									<element name="codigoGlosa" type="ans:dm_tipoGlosa" maxOccurs="unbounded"/>
								</sequence>
							</complexType>
						</element>
					</sequence>
				</complexType>
			</element>
			<element name="motivosNegativa" minOccurs="0">
				<complexType>
					<sequence>
						<!-- alterado na versÃ£o 4.00.00		
						<element name="motivoNegativa" type="ans:ct_motivoGlosa" maxOccurs="unbounded"/>
						-->
						<element name="codigoGlosa" type="ans:dm_tipoGlosa" maxOccurs="unbounded"/>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<!-- ========================= ANEXO ODONTOLOGIA SITUAÃ?Ã?O INICIAL ======================= -->
	<complexType name="cto_anexoSituacaoInicial">
		<sequence>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="numeroGuiaAnexo" type="ans:st_texto20"/>
			<element name="numeroGuiaReferenciada" type="ans:st_texto20"/>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<!-- reitrado na versÃ£o 4.00.00
			<element name="nomeBeneficiario" type="ans:st_texto70"/>
			-->
			<element name="numeroCarteira" type="ans:st_texto20"/>
			<element name="ct_situacaoInicial">
				<complexType>
					<sequence>
						<element name="situacaoClinica" type="ans:ct_situacaoClinica"/>
						<element name="doencaPeriodontal" type="ans:st_logico"/>
						<element name="alteracaoTecidoMole" type="ans:st_logico"/>
						<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<complexType name="cto_anexoSituacaoInicialnaGTO">
		<sequence>
			<element name="numeroGuiaAnexo" type="ans:st_texto20"/>
			<element name="numeroGuiaReferenciada" type="ans:st_texto20"/>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="ct_situacaoInicial">
				<complexType>
					<sequence>
						<element name="situacaoClinica" type="ans:ct_situacaoClinica"/>
						<element name="doencaPeriodontal" type="ans:st_logico" minOccurs="0"/>
						<element name="alteracaoTecidoMole" type="ans:st_logico" minOccurs="0"/>
						<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
		</sequence>
	</complexType>
	<!-- =============  DEMONSTRATIVO PAGAMENTO ODONTOLOGIA ==========================-->
	<complexType name="cto_demonstrativoOdontologia">
		<sequence>
			<element name="cabecalhoDemonstrativoOdonto">
				<complexType>
					<sequence>
						<element name="registroANS" type="ans:st_registroANS"/>
						<element name="numeroDemonstrativo" type="ans:st_texto12"/>
						<element name="nomeOperadora" type="ans:st_texto70"/>
						<element name="cnpjOper" type="ans:st_CNPJ"/>
						<element name="periodoProc">
							<complexType>
								<sequence>
									<element name="datainicio" type="ans:st_data"/>
									<element name="datafim" type="ans:st_data"/>
								</sequence>
							</complexType>
						</element>
					</sequence>
				</complexType>
			</element>
			<element name="dadosPrestador">
				<complexType>
					<sequence>
						<element name="codigoPrestador" type="ans:st_texto14"/>
						<element name="cpfCNPJContratado">
							<complexType>
								<choice>
									<element name="cnpjPrestador" type="ans:st_CNPJ"/>
									<element name="cpfContratado" type="ans:st_CPF"/>
								</choice>
							</complexType>
						</element>
					</sequence>
				</complexType>
			</element>
			<!-- PROTOCOLO ===========================================-->
			<element name="dadosPagamentoPorData" maxOccurs="unbounded">
				<complexType>
					<sequence>
						<element name="dadosPagamento">
							<complexType>
								<sequence>
									<element name="dataPagamento" type="ans:st_data"/>
									<element name="banco" type="ans:st_texto4" minOccurs="0"/>
									<element name="agencia" type="ans:st_texto7" minOccurs="0"/>
									<element name="conta" type="ans:st_texto20" minOccurs="0"/>
								</sequence>
							</complexType>
						</element>
						<element name="protocolos" maxOccurs="unbounded">
							<complexType>
								<sequence>
									<element name="numeroLote" type="ans:st_texto12"/>
									<element name="numeroProtocolo" type="ans:st_texto12"/>
									<element name="dadosPagamentoGuia" maxOccurs="unbounded">
										<complexType>
											<sequence>
												<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
												<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
												<element name="recurso" type="ans:dm_simNao"/>
												<element name="nomeExecutante" type="ans:st_texto70"/>
												<element name="carteiraBeneficiario" type="ans:st_texto20"/>
												<!-- retirado na versÃ£o 4.00.00
												<element name="nomeBeneficiario" type="ans:st_texto70"/>
												-->
												<!-- PROCEDIMENTOS DA GUIA====================================================-->
												<element name="dadosPagamento" maxOccurs="unbounded">
													<complexType>
														<sequence>
															<element name="sequencialItem" type="ans:st_numerico4"/>
															<element name="procedimento" type="ans:ct_procedimentoDados"/>
															<element name="denteRegiao" minOccurs="0">
																<complexType>
																	<choice>
																		<element name="codDente" type="ans:dm_dente"/>
																		<element name="codRegiao" type="ans:dm_regiao"/>
																	</choice>
																</complexType>
															</element>
															<element name="denteFace" type="ans:st_texto5" minOccurs="0"/>
															<element name="dataRealizacao" type="ans:st_data"/>
															<element name="qtdProc" type="ans:st_numerico2"/>
															<element name="valorInformado" type="ans:st_decimal7-2"/>
															<element name="valorProcessado" type="ans:st_decimal7-2"/>
															<element name="valorGlosaEstorno" type="ans:st_decimal7-2"/>
															<element name="valorFranquia" type="ans:st_decimal7-2"/>
															<element name="valorLiberado" type="ans:st_decimal7-2"/>
															<element name="codigosGlosa" type="ans:dm_tipoGlosa" minOccurs="0" maxOccurs="unbounded"/>
														</sequence>
													</complexType>
												</element>
												<element name="observacaoGuia" type="ans:st_texto500" minOccurs="0"/>
												<element name="valorTotalInformadoGuia" type="ans:st_decimal8-2"/>
												<element name="valorTotalProcessadoGuia" type="ans:st_decimal8-2"/>
												<element name="valorTotalGlosaGuia" type="ans:st_decimal8-2"/>
												<element name="valorTotalFranquiaGuia" type="ans:st_decimal8-2"/>
												<element name="valorTotalLiberadoGuia" type="ans:st_decimal8-2"/>
											</sequence>
										</complexType>
									</element>
									<element name="totaisPorProtocolo">
										<complexType>
											<sequence>
												<element name="valorTotalInformadoPorProtocolo" type="ans:st_decimal10-2"/>
												<element name="valorTotalProcessadoPorProtocolo" type="ans:st_decimal10-2"/>
												<element name="valorTotalGlosaPorProtocolo" type="ans:st_decimal10-2"/>
												<element name="valorTotalFranquiaPorProtocolo" type="ans:st_decimal10-2"/>
												<element name="valorTotalLiberadoPorProtocolo" type="ans:st_decimal10-2"/>
											</sequence>
										</complexType>
									</element>
								</sequence>
							</complexType>
						</element>
						<!-- GUIA =================================================================================-->
						<element name="totaisPorData">
							<complexType>
								<sequence>
									<element name="valorBrutonformadoPorData" type="ans:st_decimal10-2"/>
									<element name="valorBrutoProcessadoPorData" type="ans:st_decimal10-2"/>
									<element name="valorBrutoGlosaPorData" type="ans:st_decimal10-2"/>
									<element name="valorBrutoFranquiaPorData" type="ans:st_decimal10-2"/>
									<element name="valorBrutoLiberadoPorData" type="ans:st_decimal10-2"/>
								</sequence>
							</complexType>
						</element>
						<element name="debCredPorDataPagamento" minOccurs="0">
							<complexType>
								<sequence>
									<element name="descontos" type="ans:ct_descontos" maxOccurs="unbounded"/>
								</sequence>
							</complexType>
						</element>
						<element name="totalLiquidoPorData">
							<complexType>
								<sequence>
									<element name="valorTotalDebitosPorData" type="ans:st_decimal10-2"/>
									<element name="valorTotalCreditosPorData" type="ans:st_decimal10-2"/>
									<element name="valorFinalAReceberPorData" type="ans:st_decimal10-2"/>
								</sequence>
							</complexType>
						</element>
					</sequence>
				</complexType>
			</element>
			<element name="totaisBrutoDemonstrativo">
				<complexType>
					<sequence>
						<element name="valorInformadoPorDemonstrativoData" type="ans:st_decimal10-2"/>
						<element name="valorlProcessadoPorDemonstrativo" type="ans:st_decimal10-2"/>
						<element name="valorlGlosaPorDemonstrativo" type="ans:st_decimal10-2"/>
						<element name="valoFranquiaPorDemonstrativo" type="ans:st_decimal10-2"/>
						<element name="valorLiberadoPorDemonstrativo" type="ans:st_decimal10-2"/>
					</sequence>
				</complexType>
			</element>
			<element name="debCredDemonstrativo" minOccurs="0">
				<complexType>
					<sequence>
						<element name="descontos" type="ans:ct_descontos" maxOccurs="unbounded"/>
					</sequence>
				</complexType>
			</element>
			<element name="totalDebitosDemonstativo" type="ans:st_decimal10-2"/>
			<element name="totalCreditosDemonstrativo" type="ans:st_decimal10-2"/>
			<element name="valorRecebidoDemonstrativo" type="ans:st_decimal10-2"/>
			<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
		</sequence>
	</complexType>
	<!--============== GUIA DE TRATAMENTO ODONTOLOGICO - COBRANÃ?A ===========-->
	<complexType name="cto_guiaOdontologia">
		<sequence>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
			<element name="numeroGuiaPrincipal" type="ans:st_texto20" minOccurs="0"/>
			<element name="dataAutorizacao" type="ans:st_data" minOccurs="0"/>
			<element name="senhaAutorizacao" type="ans:st_texto20" minOccurs="0"/>
			<element name="validadeSenha" type="ans:st_data" minOccurs="0"/>
			<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="ausenciaCodValidacao" type="ans:dm_ausenciaCodValidacao" minOccurs="0"/>
			<element name="codValidacao" type="ans:st_texto10" minOccurs="0"/>
			<element name="dadosBeneficiario" type="ans:ct_beneficiarioDados"/>
			<element name="planoBeneficiario" type="ans:st_texto40"/>
			<element name="nomeEmpresa" type="ans:st_texto40" minOccurs="0"/>
			<!-- retirado na versÃ£o 4.00.00
			<element name="numeroTelefone" type="ans:st_texto11" minOccurs="0"/>
			<element name="nomeTitular" type="ans:st_texto70" minOccurs="0"/>
			-->
			<element name="dadosProfissionaisResponsaveis">
				<complexType>
					<sequence>
						<element name="nomeProfSolic" type="ans:st_texto70" minOccurs="0"/>
						<element name="croSolic" type="ans:st_texto15" minOccurs="0"/>
						<element name="ufSolic" type="ans:dm_UF" minOccurs="0"/>
						<element name="cbosSolic" type="ans:dm_CBOS" minOccurs="0"/>
						<element name="codigoProfExec" type="ans:st_texto14"/>
						<!-- retirado na versÃ£o 4.00.00
						<element name="nomeProfExec" type="ans:st_texto70"/>
						-->
						<element name="croExec" type="ans:st_texto15"/>
						<element name="ufExec" type="ans:dm_UF"/>
						<element name="cnesExec" type="ans:st_texto7"/>
						<element name="nomeProfExec2" type="ans:st_texto70" minOccurs="0"/>
						<element name="croExec2" type="ans:st_texto15" minOccurs="0"/>
						<element name="ufExec2" type="ans:dm_UF" minOccurs="0"/>
						<element name="cbosExec2" type="ans:dm_CBOS"/>
					</sequence>
				</complexType>
			</element>
			<element name="procedimentosExecutados" maxOccurs="unbounded">
				<complexType>
					<sequence>
						<element name="sequencialItem" type="ans:st_numerico4"/>
						<element name="procSolic" type="ans:ct_procedimentoDados"/>
						<element name="denteRegiao" minOccurs="0">
							<complexType>
								<choice>
									<element name="codDente" type="ans:dm_dente"/>
									<element name="codRegiao" type="ans:dm_regiao"/>
								</choice>
							</complexType>
						</element>
						<element name="denteFace" type="ans:st_texto5" minOccurs="0"/>
						<element name="qtdProc" type="ans:st_numerico2"/>
						<element name="qtdUS" type="ans:st_decimal8-2" minOccurs="0"/>
						<element name="valorProc" type="ans:st_decimal8-2"/>
						<element name="valorFranquia" type="ans:st_decimal8-2" minOccurs="0"/>
						<element name="autorizado" type="ans:st_logico"/>
						<element name="dataRealizacao" type="ans:st_data"/>
					</sequence>
				</complexType>
			</element>
			<element name="dataTerminoTrat" type="ans:st_data" minOccurs="0"/>
			<element name="tipoAtendimento" type="ans:dm_tipoAtendimentoOdonto"/>
			<element name="tipoFaturamento" type="ans:dm_tipoFaturamentoOdonto"/>
			<element name="qtdTotalUS" type="ans:st_decimal8-2" minOccurs="0"/>
			<element name="valorTotalProc" type="ans:st_decimal10-2"/>
			<element name="valorTotalFranquia" type="ans:st_decimal10-2" minOccurs="0"/>
			<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
			<element name="odontoInicial" type="ans:cto_anexoSituacaoInicialnaGTO" minOccurs="0"/>
			<element name="assinaturaDigitalGuia" type="ans:Signature" minOccurs="0"/>
			<!--<element name="assinaturaDigitalGuia" type="ans:assinaturaDigital" minOccurs="0"/>-->
		</sequence>
	</complexType>
	<!--============== GUIA DE TRATAMENTO ODONTOLOGICO - SOLICITAÃ?Ã?O===========-->
	<complexType name="cto_odontoSolicitacaoGuia">
		<sequence>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
			<!--<element name="dataEmissaoGuia" type="ans:st_data" minOccurs="0"/>-->
			<element name="numeroGuiaPrincipal" type="ans:st_texto20" minOccurs="0"/>
			<element name="ausenciaCodValidacao" type="ans:dm_ausenciaCodValidacao" minOccurs="0"/>
			<element name="codValidacao" type="ans:st_texto10" minOccurs="0"/>
			<element name="numeroCarteira" type="ans:st_texto20"/>
			<element name="atendimentoRN" type="ans:dm_simNao"/>
			<!-- retirado na versÃ£o 4.00.00
			<element name="numeroCNS" type="ans:st_texto15" minOccurs="0"/>
			-->
			<element name="tipoIdent" type="ans:dm_tipoIdent" minOccurs="0"/>
			<element name="identificadorBeneficiario" type="base64Binary" minOccurs="0"/>
			<!-- retirado na versÃ£o 4.00.00
			<element name="templateBiometrico" type="base64Binary" minOccurs="0"/>
			-->
			<element name="planoBeneficiario" type="ans:st_texto40"/>
			<element name="nomeEmpresa" type="ans:st_texto40" minOccurs="0"/>
			<element name="dadosProfissionaisResponsaveis">
				<complexType>
					<sequence>
						<element name="nomeProfSolic" type="ans:st_texto70" minOccurs="0"/>
						<element name="croSolic" type="ans:st_texto20" minOccurs="0"/>
						<element name="ufSolic" type="ans:dm_UF" minOccurs="0"/>
						<element name="cbosSolic" type="ans:dm_CBOS" minOccurs="0"/>
						<element name="codigoProfExec" type="ans:st_texto14"/>
						<element name="croExec" type="ans:st_texto20"/>
						<element name="ufExec" type="ans:dm_UF"/>
						<element name="cnesExec" type="ans:st_texto7"/>
						<element name="nomeProfExec2" type="ans:st_texto70" minOccurs="0"/>
						<element name="croExec2" type="ans:st_texto20" minOccurs="0"/>
						<element name="ufExec2" type="ans:dm_UF" minOccurs="0"/>
						<element name="cbosExec2" type="ans:dm_CBOS"/>
					</sequence>
				</complexType>
			</element>
			<element name="procedimentosSolicitados" maxOccurs="unbounded">
				<complexType>
					<sequence>
						<element name="procSolic" type="ans:ct_procedimentoDados"/>
						<element name="denteRegiao" minOccurs="0">
							<complexType>
								<choice>
									<element name="codDente" type="ans:dm_dente"/>
									<element name="codRegiao" type="ans:dm_regiao"/>
								</choice>
							</complexType>
						</element>
						<element name="denteFace" type="ans:st_texto5" minOccurs="0"/>
						<element name="qtdProc" type="ans:st_numerico2"/>
						<element name="qtdUS" type="ans:st_decimal7-2" minOccurs="0"/>
						<element name="valorProc" type="ans:st_decimal8-2"/>
						<element name="valorFranquia" type="ans:st_decimal8-2" minOccurs="0"/>
						<element name="aut" type="ans:dm_simNao"/>
						<element name="dataRealizacao" type="ans:st_data" minOccurs="0"/>
					</sequence>
				</complexType>
			</element>
			<element name="dataTerminoTrat" type="ans:st_data" minOccurs="0"/>
			<element name="tipoAtendimento" type="ans:dm_tipoAtendimentoOdonto"/>
			<element name="qtdTotalUS" type="ans:st_decimal8-2" minOccurs="0"/>
			<element name="valorTotalProc" type="ans:st_decimal8-2"/>
			<element name="valorTotalFranquia" type="ans:st_decimal8-2" minOccurs="0"/>
			<element name="observacao" type="ans:st_texto500" minOccurs="0"/>
			<element name="odontoInicial" type="ans:cto_anexoSituacaoInicial" minOccurs="0"/>
		</sequence>
	</complexType>
	<!-- ==========================  RECURSO DE GLOSA ODONTOLOGIA =========================== -->
	<complexType name="cto_recursoGlosaOdonto">
		<sequence>
			<element name="registroANS" type="ans:st_registroANS"/>
			<element name="numeroGuiaRecGlosaPrestador" type="ans:st_texto20"/>
			<element name="nomeOperadora" type="ans:st_texto70"/>
			<element name="objetoRecurso" type="ans:dm_objetoRecurso"/>
			<element name="numeroGuiaRecGlosaOperadora" type="ans:st_texto20" minOccurs="0"/>
			<element name="dadosContratado" type="ans:ct_contratadoDados"/>
			<element name="numeroLote" type="ans:st_texto12"/>
			<element name="numeroProtocolo" type="ans:st_numerico12"/>
			<element name="opcaoRecurso">
				<complexType>
					<choice>
						<element name="recursoProtocolo">
							<complexType>
								<sequence>
									<element name="codigoGlosaProtocolo" type="ans:dm_tipoGlosa"/>
									<element name="justificativaProtocolo" type="ans:st_texto500"/>
								</sequence>
							</complexType>
						</element>
						<element name="recursoGuia" maxOccurs="100">
							<complexType>
								<sequence>
									<element name="numeroGuiaPrestador" type="ans:st_texto20"/>
									<element name="numeroGuiaOperadora" type="ans:st_texto20" minOccurs="0"/>
									<element name="senha" type="ans:st_texto20" minOccurs="0"/>
									<element name="codGlosaGuia" type="ans:dm_tipoGlosa"/>
									<element name="justificativaGuia" type="ans:st_texto500"/>
									<element name="recursoProcedimento" minOccurs="0" maxOccurs="unbounded">
										<complexType>
											<sequence>
												<element name="sequencialItem" type="ans:st_numerico4"/>
												<element name="dataRealizacao" type="ans:st_data"/>
												<element name="denteRegiao" minOccurs="0">
													<complexType>
														<choice>
															<element name="codDente" type="ans:dm_dente"/>
															<element name="codRegiao" type="ans:dm_regiao"/>
														</choice>
													</complexType>
												</element>
												<element name="denteFace" type="ans:dm_face" minOccurs="0"/>
												<element name="quantidade" type="ans:st_numerico2"/>
												<element name="procRecurso" type="ans:ct_procedimentoDados"/>
												<element name="codGlosaProc" type="ans:dm_tipoGlosa"/>
												<element name="justificativaProc" type="ans:st_texto500"/>
												<element name="valorRecursado" type="ans:st_decimal8-2"/>
											</sequence>
										</complexType>
									</element>
								</sequence>
							</complexType>
						</element>
					</choice>
				</complexType>
			</element>
			<element name="valorTotalRecursado" type="ans:st_decimal10-2"/>
			<element name="dataRecurso" type="ans:st_data"/>
		</sequence>
	</complexType>
</schema>

