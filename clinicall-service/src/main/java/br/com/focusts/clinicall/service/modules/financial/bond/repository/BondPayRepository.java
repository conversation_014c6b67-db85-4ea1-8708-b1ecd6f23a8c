package br.com.focusts.clinicall.service.modules.financial.bond.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.financial.bond.po.BondPayPO;
import br.com.focusts.clinicall.service.modules.financial.bond.to.BondPayListTO;

@Repository
public interface BondPayRepository extends CrudRepository<BondPayPO, Long> {


    @Query("""
            SELECT 
            new br.com.focusts.clinicall.service.modules.financial.bond.to.BondPayListTO(
            bondPay.id,
            account.code,
            bondPay.dated,
            bondPay.value,
            payType.name as payType,
            user.login as userCreated
            )
            FROM
            BondPayPO bondPay
            LEFT JOIN PayTypePO payType ON payType.id = bondPay.payType.id
            LEFT JOIN AccountPO account ON account.id = bondPay.account.id
            LEFT JOIN UserPO user ON user.id = bondPay.createdBy
            WHERE bondPay.bond.id = :bondId
            """)
    public Page<BondPayListTO> findByBondId(Long bondId, Pageable pageable);

    @Query("""
            SELECT 
            COALESCE(SUM(bondPay.value),0)
            FROM
            BondPayPO bondPay
            WHERE bondPay.bond.id = :bondId
            """)
    public Double findByTotalPay(Long bondId);


    @Modifying
    @Query("""
            DELETE FROM BondPayPO bondPay 
            WHERE EXISTS (
                SELECT 1 FROM SendingToFinanceItemPO financeItem 
                WHERE financeItem.bond.id = bondPay.bond.id 
                AND financeItem.pay.id = :payId
            )
            """)
    public void deleteByPayId(Long payId);
}
