
package br.com.focusts.clinicall.service.modules.register.security.facade;

import br.com.focusts.clinicall.service.modules.system.po.MessagePO;
import br.com.focusts.clinicall.service.modules.system.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.security.po.ProfilePO;
import br.com.focusts.clinicall.service.modules.register.security.service.ProfileService;
import br.com.focusts.clinicall.service.modules.register.security.to.ProfileChatTO;

import java.util.List;

@Component
@Transactional
public class DefaultProfileFacade extends AbstractCrudFacade <ProfilePO, java.lang.Long> implements ProfileFacade  {

	@Autowired
	private ProfileService profileService;

@Autowired
	private MessageService messageService;

	@Override
	public CrudService<ProfilePO,java.lang.Long> getCrudService() {
            return profileService;
	}

	@Override
	public Page<ProfilePO> find(PageSearchTO pageSearchTO) {
            return profileService.find(pageSearchTO);
	}

	@Override
	public ProfilePO findByUserId(Long userId) {
		return profileService.findByUserId(userId);
	}

	@Override
	public List<MessagePO> findMessagesByProfileFromAndProfileToId(Long profileFromId, Long profileToId) {
		return messageService.findMessagesByProfileFromAndProfileToId(profileFromId,profileToId);
	}

	@Override
	public List<ProfileChatTO> findByUserChat(List<Long> userIdList) {
		return profileService.findByUserChat(userIdList);
	}

	@Override
	public ProfileChatTO findByUserChatProfile(ProfilePO profile) {
		
		return profileService.findByUserChatProfile(profile);
	}

	@Override
	public ProfileChatTO findProfileChatTOByUserId(Long userId) {
		return profileService.findProfileChatTOByUserId(userId);
	}

	
	
}
