package br.com.focusts.clinicall.service.modules.register.security.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.service.modules.register.security.po.UserRolePO;
import br.com.focusts.clinicall.service.modules.register.security.repository.UserRoleRepository;

@Service
public class DefaultUserRoleService extends AbstractCrudService<UserRolePO, Long> implements UserRoleService {

	@Autowired
	private UserRoleRepository userRoleRepository;

	@Override
	public CrudRepository<UserRolePO, Long> getCrudRepository() {
		return userRoleRepository;
	}

}
