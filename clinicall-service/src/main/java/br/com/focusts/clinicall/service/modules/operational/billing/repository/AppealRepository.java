
package br.com.focusts.clinicall.service.modules.operational.billing.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.operational.billing.po.AppealPO;

@Repository
public interface AppealRepository extends CrudRepository<AppealPO,java.lang.Long>{

    public Page<AppealPO> findByNumberStartingWith(java.lang.String number, Pageable pageable);

    @Query(nativeQuery = true, value = "SELECT COALESCE ( MAX(CAST(appeal.`number`  AS signed)) , 0) from appeal  where  `number` REGEXP '^[0-9]+$'")
    Integer findMaxAppealNumber();
}



