package br.com.focusts.clinicall.service.modules.operational.billing.to;

import java.util.List;

import br.com.focusts.clinicall.service.modules.operational.billing.po.InvoicePO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BillingSaveTO {

    private FilterTO billingFilter;
    private List<BillingOrderTO> billOrderRelease;
    private List<InvoicePO> invoice;
    private Long user;
}
