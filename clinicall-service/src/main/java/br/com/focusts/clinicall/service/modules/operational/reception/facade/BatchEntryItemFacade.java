
package br.com.focusts.clinicall.service.modules.operational.reception.facade;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.BatchEntryItemPO;

public interface BatchEntryItemFacade extends CrudFacade<BatchEntryItemPO,java.lang.Long>{

    public Page<BatchEntryItemPO> find(PageSearchTO pageSearchTO);

}

