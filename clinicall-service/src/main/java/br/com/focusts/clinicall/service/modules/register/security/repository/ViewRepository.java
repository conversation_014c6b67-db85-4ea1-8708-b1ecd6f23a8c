
package br.com.focusts.clinicall.service.modules.register.security.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.register.security.po.ViewPO;

@Repository
public interface ViewRepository extends CrudRepository<ViewPO,java.lang.Long>{

    public Page<ViewPO> findByNameContainingOrViewTypeContainingOrModule_nameContaining(java.lang.String name,java.lang.String viewType,String moduleName , Pageable pageable);

}



