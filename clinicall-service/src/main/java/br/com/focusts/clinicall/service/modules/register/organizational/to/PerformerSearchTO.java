package br.com.focusts.clinicall.service.modules.register.organizational.to;

import br.com.focusts.clinicall.fw.to.PageSearchTO;

public class PerformerSearchTO {
	
	private Long insuranceId;
	
	private Long companyId;
	
	private Long procedureId;
	
	private Long performerId;
	
	private Long specialityId;
	
	private Boolean onlyOpenSchedule;
	
	private PageSearchTO pageSearchTO;

	public Long getInsuranceId() {
		return insuranceId;
	}

	public void setInsuranceId(Long insuranceId) {
		this.insuranceId = insuranceId;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public Long getProcedureId() {
		return procedureId;
	}

	public void setProcedureId(Long procedureId) {
		this.procedureId = procedureId;
	}

	public Long getPerformerId() {
		return performerId;
	}

	public void setPerformerId(Long performerId) {
		this.performerId = performerId;
	}

	public PageSearchTO getPageSearchTO() {
		return pageSearchTO;
	}

	public void setPageSearchTO(PageSearchTO pageSearchTO) {
		this.pageSearchTO = pageSearchTO;
	}

	public Long getSpecialityId() {
		return specialityId;
	}

	public void setSpecialityId(Long specialityId) {
		this.specialityId = specialityId;
	}

	public Boolean getOnlyOpenSchedule() {
		return onlyOpenSchedule;
	}

	public void setOnlyOpenSchedule(Boolean onlyOpenSchedule) {
		this.onlyOpenSchedule = onlyOpenSchedule;
	}
	
}
