package br.com.focusts.clinicall.service.modules.tables.tiss.repository;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;

import com.querydsl.core.Tuple;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.impl.JPAQuery;

import br.com.focusts.clinicall.service.modules.operational.billing.po.QBillingPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderAuthorizationPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderClosurePO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderDiagnosisPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderItemAuxiliaryPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderItemFeePO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderItemPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderItemProductPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderItemTissPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderPatientDetailPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderTissDeclarationPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderTissPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.enums.FeeTypeEnum;
import br.com.focusts.clinicall.service.modules.register.accreditation.enums.LocationTypeEnum;
import br.com.focusts.clinicall.service.modules.register.accreditation.enums.ProductTypeEnum;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QAccreditationPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QAgreementPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QAgreementSpecPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QAuxiliaryPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QFeePO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QInsuranceDiscountRulePO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QInsurancePO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QInsuranceTissPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QMeasureTissPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QProductPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QSpecialityPO;
import br.com.focusts.clinicall.service.modules.register.operational.po.QPatientPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.QCompanyCostCenterPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.QCompanyPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.QPerformerPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.QProfessionalPO;
import br.com.focusts.clinicall.service.modules.reports.to.ReportDeclarationTO;
import br.com.focusts.clinicall.service.modules.system.constants.GlobalKeyParameterConstants;
import br.com.focusts.clinicall.service.modules.system.po.QParameterPO;
import br.com.focusts.clinicall.service.modules.system.po.QParameterValuePO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.QDegreeParticipationPO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.QPlanPO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.QTableTypePO;
import br.com.focusts.clinicall.service.modules.tables.general.po.QCidPO;
import br.com.focusts.clinicall.service.modules.tables.general.po.QCouncilPO;
import br.com.focusts.clinicall.service.modules.tables.general.po.QFirmPO;
import br.com.focusts.clinicall.service.modules.tables.general.po.QOccupationPO;
import br.com.focusts.clinicall.service.modules.tables.general.po.QPersonPO;
import br.com.focusts.clinicall.service.modules.tables.general.po.QRegistryTypePO;
import br.com.focusts.clinicall.service.modules.tables.general.po.QStatePO;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.QAccidentIndicatorPO;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.QAttendanceRegimePO;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.QClosingReasonPO;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.QHospitalizationRegimePO;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.QOccupationalHealthPO;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.QServiceCharacterPO;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.QSpecialCoveragePO;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.QTypeAttendancePO;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.QTypeHospitalizationPO;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.QTypeSurveyPO;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.QViaAccessPO;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.QViaAdministrationPO;
import br.com.focusts.clinicall.service.modules.tables.tiss.to.HeaderTransactionXmlTO;
import br.com.focusts.clinicall.service.modules.tables.tiss.to.OrderItemAuxiliaryTO;
import br.com.focusts.clinicall.service.modules.tables.tiss.to.OrderItemMatMedTaxTO;
import br.com.focusts.clinicall.service.modules.tables.tiss.to.OrderItemXmlTO;
import br.com.focusts.clinicall.service.modules.tables.tiss.to.OrderXmlTO;

@Repository
public class DefaultTissQueryDslRepository implements TissQueryDslRepository {

        @PersistenceContext
        private EntityManager entityManager;

        public HeaderTransactionXmlTO findHeaderTransaction(Long billingId) {
                QBillingPO billing = QBillingPO.billingPO;
                QInsurancePO insurance = QInsurancePO.insurancePO;
                QInsuranceTissPO insuranceTiss = QInsuranceTissPO.insuranceTissPO;
                QCompanyPO company = QCompanyPO.companyPO;
                QRegistryTypePO companyRegistryType = new QRegistryTypePO("CompanyRegisterType");
                QRegistryTypePO firmRegistryType = new QRegistryTypePO("firmRegistryType");
                QAgreementPO agreementBilling = new QAgreementPO("agreementBilling");
                QAgreementPO agreementInsurance = new QAgreementPO("agreementInsurance");
                QAgreementSpecPO agreementSpec = QAgreementSpecPO.agreementSpecPO;
                QFirmPO firm = QFirmPO.firmPO;
                QParameterPO parameterOriginProvider = new QParameterPO("parameterOriginProvider");
                QParameterValuePO parameterValueOriginProvider = new QParameterValuePO("parameterValueOriginProvider");
                QParameterPO parameterExecutingEqualProvide = new QParameterPO("parameterExecutingEqualProvide");
                QParameterValuePO parameterValueExecutingEqualProvide = new QParameterValuePO(
                                "parameterValueExecutingEqualProvide");
                QParameterPO parameterProfessionalProvideCode = new QParameterPO("parameterProfessionalProvideCode");
                QParameterValuePO parameterValueProfessionalProvideCode = new QParameterValuePO(
                                "parameterValueProfessionalProvideCode");
                QParameterPO parameterPerformerGuide = new QParameterPO("parameterPerformerGuide");
                QParameterValuePO parameterValuePerformerGuide = new QParameterValuePO("parameterValuePerformerGuide");
                QParameterPO parameterCodingXMl = new QParameterPO("parameterCodingXMl");
                QParameterValuePO parameterValueCodingXMl = new QParameterValuePO("parameterValueCodingXMl");
                QParameterPO parameterGroupAuxiliary = new QParameterPO("parameterGroupAuxiliary");
                QParameterValuePO parameterValueGroupAuxiliary = new QParameterValuePO("parameterValueGroupAuxiliary");
                QProfessionalPO professional = QProfessionalPO.professionalPO;
                QCouncilPO council = QCouncilPO.councilPO;
                QPersonPO person = QPersonPO.personPO;
                QStatePO state = QStatePO.statePO;
                QSpecialityPO speciality = QSpecialityPO.specialityPO;
                QOccupationPO occupation = QOccupationPO.occupationPO;

                JPAQuery<HeaderTransactionXmlTO> query = new JPAQuery<>(entityManager);

                query.select(Projections.bean(
                                HeaderTransactionXmlTO.class,
                                insuranceTiss.delivery.as("insuranceDelivery"),
                                insuranceTiss.contraptionConsultation.as("insuranceContraptionConsultation"),
                                insuranceTiss.contraptionSpsadt.as("insuranceContraptionSpsadt"),
                                insuranceTiss.contraptionHonorary.as("insuranceContraptionHonorary"),
                                insuranceTiss.contraptionAdmission.as("insuranceContraptionAdmission"),
                                insurance.ansRegistry.as("insuranceAns"),
                                insurance.providerCode.as("insuranceProviderCode"),
                                company.name.as("companyName"),
                                company.cnpj.as("companyRegister"),
                                company.cnes.as("companyCnes"),
                                companyRegistryType.code.as("companyRegistryType"),
                                firm.name.as("firmAgreement"),
                                firm.cnes.as("firmCnes"),
                                firm.registry.as("firmRegistry"),
                                firmRegistryType.code.as("firmResgistryType"),
                                agreementSpec.guideNumber.as("agreementContraptionConsultation"),
                                agreementSpec.sadtNumber.as("agreementContraptionSpsadt"),
                                parameterValueOriginProvider.value.as("parameterOriginProvider"),
                                parameterValueProfessionalProvideCode.value
                                                .coalesce(parameterProfessionalProvideCode.value)
                                                .as("parameterProfessionalProvideCode"),
                                parameterValueExecutingEqualProvide.value.coalesce(parameterExecutingEqualProvide.value)
                                                .as("parameterValueExecutingEqualProvide"),
                                parameterValuePerformerGuide.value.as("parameterPerformerGuide"),
                                parameterValueCodingXMl.value.as("parameterCodingXMl"),
                                parameterValueGroupAuxiliary.value.as("parameterGroupAuxiliary"),
                                person.name.as("performerGuideName"),
                                professional.councilNumber.as("performerGuideCouncilNumber"),
                                council.code.as("performerGuideCouncilType"),
                                state.code.as("performerGuideState"),
                                occupation.code.as("performerGuideCbos"),
                                person.cpf.as("performerGuideCpf"),
                                speciality.name.as("performerGuideSpeciality")))
                                .from(billing)
                                .leftJoin(insurance).on(insurance.id.eq(billing.insurance.id))
                                .leftJoin(insuranceTiss).on(insuranceTiss.insurance.id.eq(insurance.id))
                                .leftJoin(company).on(company.id.eq(billing.company.id))
                                .leftJoin(companyRegistryType).on(companyRegistryType.id.eq(company.registryType.id))
                                .leftJoin(agreementBilling).on(agreementBilling.id.eq(billing.agreement.id))
                                .leftJoin(agreementInsurance)
                                .on(agreementInsurance.insurance.id.eq(insurance.id)
                                                .and(agreementInsurance.company.id.eq(company.id)
                                                                .or(agreementInsurance.company.id.isNull())))
                                .leftJoin(agreementSpec)
                                .on(agreementSpec.agreement.id.eq(agreementBilling.id.coalesce(agreementInsurance.id)))
                                .leftJoin(firm).on(firm.id.eq(agreementSpec.firm.id))
                                .leftJoin(firmRegistryType).on(firmRegistryType.id.eq(firm.registryType.id))
                                .leftJoin(parameterOriginProvider)
                                .on(parameterOriginProvider.name
                                                .eq(GlobalKeyParameterConstants.INSURANCE_ORIGIN_PROVIDE_CODE))
                                .leftJoin(parameterValueOriginProvider)
                                .on(parameterValueOriginProvider.parameter.id.eq(parameterOriginProvider.id)
                                                .and(parameterValueOriginProvider.key.eq(insurance.id.stringValue())))
                                .leftJoin(parameterExecutingEqualProvide)
                                .on(parameterExecutingEqualProvide.name.eq(
                                                GlobalKeyParameterConstants.EXECUTING_CONTRACTOR_EQUAL_TO_THE_PROVIDER_CODE))
                                .leftJoin(parameterValueExecutingEqualProvide)
                                .on(parameterValueExecutingEqualProvide.parameter.id
                                                .eq(parameterExecutingEqualProvide.id)
                                                .and(parameterValueExecutingEqualProvide.key
                                                                .eq(insurance.id.stringValue())))
                                .leftJoin(parameterProfessionalProvideCode)
                                .on(parameterProfessionalProvideCode.name
                                                .eq(GlobalKeyParameterConstants.PROFESSIONAL_PROVIDE_CODE))
                                .leftJoin(parameterValueProfessionalProvideCode)
                                .on(parameterValueProfessionalProvideCode.parameter.id
                                                .eq(parameterProfessionalProvideCode.id)
                                                .and(parameterValueProfessionalProvideCode.key
                                                                .eq(insurance.id.stringValue())))
                                .leftJoin(parameterPerformerGuide)
                                .on(parameterPerformerGuide.name.eq(GlobalKeyParameterConstants.PERFORMER_GUIDE))
                                .leftJoin(parameterValuePerformerGuide)
                                .on(parameterValuePerformerGuide.parameter.id.eq(parameterPerformerGuide.id)
                                                .and(parameterValuePerformerGuide.key.eq(insurance.id.stringValue())))
                                .leftJoin(parameterCodingXMl)
                                .on(parameterCodingXMl.name.eq(GlobalKeyParameterConstants.CODING_FOR_THE_XML))
                                .leftJoin(parameterValueCodingXMl)
                                .on(parameterValueCodingXMl.parameter.id.eq(parameterCodingXMl.id)
                                                .and(parameterValueCodingXMl.key.eq(insurance.id.stringValue())))
                                .leftJoin(professional)
                                .on(professional.id.eq(
                                                parameterValuePerformerGuide.value.castToNum(Long.class).coalesce(0L)))
                                .leftJoin(parameterGroupAuxiliary)
                                .on(parameterGroupAuxiliary.name.eq(
                                                GlobalKeyParameterConstants.GROUP_ASSISTANTS_IN_THE_INDIVIDUAL_FEE_GUIDE))
                                .leftJoin(parameterValueGroupAuxiliary)
                                .on(parameterValueGroupAuxiliary.parameter.id.eq(parameterGroupAuxiliary.id)
                                                .and(parameterValueGroupAuxiliary.key.eq(insurance.id.stringValue())))
                                .leftJoin(council).on(council.id.eq(professional.council.id))
                                .leftJoin(person).on(person.id.eq(professional.person.id))
                                .leftJoin(state).on(state.id.eq(professional.state.id))
                                .leftJoin(speciality).on(speciality.id.eq(professional.speciality.id))
                                .leftJoin(occupation).on(occupation.id.eq(speciality.occupation.id))
                                .where(billing.id.eq(billingId).and(agreementInsurance.active.isTrue()));
                var result = query.fetch();
                return result.get(0);
        }

        public List<OrderXmlTO> findOrderXmlTo(List<Long> orderId) {

                QOrderPO order = QOrderPO.orderPO;
                QOrderTissPO orderTiss = QOrderTissPO.orderTissPO;
                QServiceCharacterPO serviceCharacter = QServiceCharacterPO.serviceCharacterPO;
                QTypeAttendancePO typeAttendance = QTypeAttendancePO.typeAttendancePO;
                QAccidentIndicatorPO accidentIndicator = QAccidentIndicatorPO.accidentIndicatorPO;
                QTypeSurveyPO typeSurvey = QTypeSurveyPO.typeSurveyPO;
                QAttendanceRegimePO attendanceRegime = QAttendanceRegimePO.attendanceRegimePO;
                QOccupationalHealthPO occupationalHealth = QOccupationalHealthPO.occupationalHealthPO;
                QSpecialCoveragePO specialCoverage = QSpecialCoveragePO.specialCoveragePO;
                QTypeHospitalizationPO typeHospitalization = QTypeHospitalizationPO.typeHospitalizationPO;
                QHospitalizationRegimePO hospitalizationRegime = QHospitalizationRegimePO.hospitalizationRegimePO;
                QOrderAuthorizationPO orderAuthorization = QOrderAuthorizationPO.orderAuthorizationPO;
                QFirmPO firmAuthorizer = new QFirmPO("firmAuthorizer");
                QRegistryTypePO firmAuthorizerType = new QRegistryTypePO("firmAuthorizerType");
                QOrderPatientDetailPO orderPatientDetail = QOrderPatientDetailPO.orderPatientDetailPO;
                QCompanyCostCenterPO companyCostCenter = QCompanyCostCenterPO.companyCostCenterPO;
                QCompanyPO company = QCompanyPO.companyPO;
                QRegistryTypePO registryType = QRegistryTypePO.registryTypePO;
                QRegistryTypePO firmRegistryType = new QRegistryTypePO("firmRegistryType");
                QPlanPO plan = QPlanPO.planPO;
                QInsurancePO insurance = QInsurancePO.insurancePO;
                QOrderItemPO orderItem = QOrderItemPO.orderItemPO;
                QProfessionalPO professional = QProfessionalPO.professionalPO;
                QSpecialityPO speciality = QSpecialityPO.specialityPO;
                QOccupationPO occupation = QOccupationPO.occupationPO;
                QOccupationPO occupationV2 = new QOccupationPO("professionalRequesterCbosV2");
                QCouncilPO council = QCouncilPO.councilPO;
                QStatePO state = QStatePO.statePO;
                QPersonPO person = QPersonPO.personPO;
                QPatientPO patient = QPatientPO.patientPO;
                QPersonPO patientPerson = new QPersonPO("patientPerson");
                QFirmPO firm = QFirmPO.firmPO;
                QOrderDiagnosisPO orderdisDiagnosis = QOrderDiagnosisPO.orderDiagnosisPO;
                QCidPO cid = QCidPO.cidPO;
                QOrderItemPO orderItemCount = new QOrderItemPO("orderItemCount");
                QOrderClosurePO orderClosure = QOrderClosurePO.orderClosurePO;
                QClosingReasonPO closingReason = QClosingReasonPO.closingReasonPO;
                QPerformerPO performer = QPerformerPO.performerPO;
                QProfessionalPO professionalPerformer = new QProfessionalPO("professionalPerformer");
                QSpecialityPO performerSpeciality = new QSpecialityPO("performerSpeciality");
                QOccupationPO performerOccupation = new QOccupationPO("performerOccupation");
                QOccupationPO performerOccupationV2 = new QOccupationPO("occupationPOV2");
                QCouncilPO performerCouncil = new QCouncilPO("performerCouncil");
                QStatePO performerState = new QStatePO("performerState");
                QPersonPO performerPerson = new QPersonPO("performerPerson");

                JPAQuery<OrderXmlTO> query = new JPAQuery<>(entityManager);

                var totalProcedures = JPAExpressions
                                .select(orderItemCount.value.add(orderItemCount.ucoValue.coalesce(0.0))
                                                .add(orderItemCount.filmValue.coalesce(0.0)).sum())
                                .from(orderItemCount).where(orderItemCount.order.id.eq(order.id));

              

                NumberTemplate<Integer> numberTemplate = Expressions.numberTemplate(
                                Integer.class,
                                "{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{15},{16},{17},{18},{19},{20},{21},{22},{23},{24},{25},{26},{27},{28},{29},{30},{31},{32},{33},{34},{35},{36},{37},{38},{39},{40},{41},{42},{43},{44},{45},{46},{47},{48},{49},{50},{51},{52},{53},{54},{55},{56},{57},{58},{59}",
                                1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24,
                                25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46,
                                47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60);

                query.from(order)
                                .select(Projections.bean(
                                                OrderXmlTO.class,
                                                order.id.as("orderId"),
                                                insurance.ansRegistry.as("insuranceAnsRegistry"),
                                                order.date,
                                                order.hour,
                                                order.number,
                                                orderTiss.operatorGuideNumber,
                                                orderTiss.principalGuideNumber,
                                                orderTiss.requestDate.as("requesterDate"),
                                                orderAuthorization.releaseDate,
                                                orderAuthorization.releaseValidity,
                                                orderAuthorization.releasePassword,
                                                firmAuthorizer.name.as("firmAuthorizerName"),
                                                firmAuthorizerType.code.as("firmAuthorizerRegistryType"),
                                                firmAuthorizer.registry.as("firmAuthorizerRegistry"),
                                                firmAuthorizer.cnes.as("firmAuthorizerCnes"),
                                                order.enrollment,
                                                orderPatientDetail.validity.as("enrollmentDate"),
                                                patientPerson.name.as("patientName"),
                                                orderTiss.newBorn.as("newborn"),
                                                plan.name.as("planName"),
                                                registryType.code.as("companyRegistryType"),
                                                company.cnpj.as("companyRegistry"),
                                                company.name.as("companyName"),
                                                person.name.as("professionalRequesterName"),
                                                professional.councilNumber.as("professionalRequesterCouncilNumber"),
                                                professional.staff.as("professionalRequesterStaff"),
                                                council.code.as("professionalRequesterCouncilType"),
                                                council.initials.as("professionalRequesterCouncilInitials"),
                                                state.code.as("professionalRequesterState"),
                                                state.initials.as("professionalRequesterStateInitials"),
                                                occupation.code.as("professionalRequesterCbos"),
                                                occupationV2.code.as("professionalRequesterCbosV2"),
                                                speciality.name.as("professionalRequesterSpeciality"),
                                                performerPerson.name.as("performerMainName"),
                                                performerPerson.cpf.as("performerCpf"),
                                                professionalPerformer.councilNumber.as("performerMainCouncilNumber"),
                                                professionalPerformer.staff.as("performerMainStaff"),
                                                performerCouncil.code.as("performerMainCouncilType"),
                                                performerCouncil.initials.as("performerMainCouncilInitials"),
                                                performerState.code.as("performerMainState"),
                                                performerState.initials.as("performerMainStateInitials"),
                                                performerOccupation.code.as("performerMainCbos"),
                                                performerOccupationV2.code.as("performerMainCbosv2"),
                                                performerSpeciality.name.as("performerMainSpeciality"),
                                                firm.name.as("firmRequesterName"),
                                                firm.registry.as("firmRequesterRegistry"),
                                                firmRegistryType.code.as("firmRequesterRegistryType"),
                                                serviceCharacter.code.as("serviceCharacter"),
                                                typeAttendance.code.as("typeAttendance"),
                                                accidentIndicator.code.as("accidentIndicator"),
                                                typeSurvey.code.as("typeSurvey"),
                                                attendanceRegime.code.as("attendanceRegime"),
                                                occupationalHealth.code.as("occupationalHealth"),
                                                specialCoverage.code.as("specialCoverage"),
                                                orderTiss.billing_type.as("billingType"),
                                                typeHospitalization.code.as("typeHospitalization"),
                                                hospitalizationRegime.code.as("hospitalizationRegime"),
                                                cid.code.as("cid"),
                                                orderTiss.billingStartDate,
                                                orderTiss.billingStartTime,
                                                orderTiss.billingEndDate,
                                                orderTiss.billingEndTime,
                                                closingReason.code.as("closingReason"),
                                                ExpressionUtils.as(totalProcedures, "totalProcedures"),
                                                order.value.as("grandTotal")))
                                .leftJoin(orderTiss).on(orderTiss.order.id.eq(order.id))
                                .leftJoin(serviceCharacter).on(serviceCharacter.id.eq(orderTiss.serviceCharacter.id))
                                .leftJoin(typeAttendance).on(typeAttendance.id.eq(orderTiss.typeAttendance.id))
                                .leftJoin(accidentIndicator).on(accidentIndicator.id.eq(orderTiss.accidentIndicator.id))
                                .leftJoin(typeSurvey).on(typeSurvey.id.eq(orderTiss.typeSurvey.id))
                                .leftJoin(attendanceRegime).on(attendanceRegime.id.eq(orderTiss.attendanceRegime.id))
                                .leftJoin(occupationalHealth)
                                .on(occupationalHealth.id.eq(orderTiss.occupationalHealth.id))
                                .leftJoin(typeHospitalization)
                                .on(typeHospitalization.id.eq(orderTiss.typeHospitalization.id))
                                .leftJoin(hospitalizationRegime)
                                .on(hospitalizationRegime.id.eq(orderTiss.hospitalizationRegime.id))
                                .leftJoin(specialCoverage).on(specialCoverage.id.eq(orderTiss.specialCoverage.id))
                                .leftJoin(orderAuthorization).on(orderAuthorization.order.id.eq(order.id))
                                .leftJoin(firmAuthorizer).on(firmAuthorizer.id.eq(orderAuthorization.firm.id))
                                .leftJoin(firmAuthorizerType)
                                .on(firmAuthorizerType.id.eq(firmAuthorizer.registryType.id))
                                .leftJoin(orderPatientDetail).on(orderPatientDetail.order.id.eq(order.id))
                                .leftJoin(companyCostCenter).on(companyCostCenter.id.eq(order.companyCostCenter.id))
                                .leftJoin(company).on(company.id.eq(companyCostCenter.company.id))
                                .leftJoin(registryType).on(registryType.id.eq(company.registryType.id))
                                .leftJoin(plan).on(plan.id.eq(order.plan.id))
                                .leftJoin(insurance).on(insurance.id.eq(order.insurance.id))
                                .leftJoin(orderItem).on(orderItem.order.id.eq(order.id).and(orderItem.sequence.eq(1)))
                                .leftJoin(firm).on(firm.id.eq(orderItem.firm.id))
                                .leftJoin(firmRegistryType).on(firmRegistryType.id.eq(firm.registryType.id))
                                .leftJoin(professional).on(professional.id.eq(orderItem.professional.id))
                                .leftJoin(council).on(council.id.eq(professional.council.id))
                                .leftJoin(speciality).on(speciality.id.eq(professional.speciality.id))
                                .leftJoin(occupation).on(occupation.id.eq(speciality.occupation.id))
                                .leftJoin(occupationV2)
                                .on(occupationV2.parent.id.eq(occupation.id).and(occupationV2.version.eq("V2")))
                                .leftJoin(state).on(state.id.eq(professional.state.id))
                                .leftJoin(orderdisDiagnosis)
                                .on(orderdisDiagnosis.order.id.eq(order.id).and(orderdisDiagnosis.main.eq(true)))
                                .leftJoin(cid).on(cid.id.eq(orderdisDiagnosis.cid.id))
                                .leftJoin(person).on(person.id.eq(professional.person.id))
                                .leftJoin(patient).on(patient.id.eq(order.patient.id))
                                .leftJoin(patientPerson).on(patientPerson.id.eq(patient.person.id))
                                .leftJoin(orderClosure).on(orderClosure.order.id.eq(order.id))
                                .leftJoin(closingReason).on(closingReason.id.eq(orderClosure.closingReason.id))
                                .leftJoin(performer).on(performer.id.eq(orderItem.performer.id))
                                .leftJoin(professionalPerformer)
                                .on(professionalPerformer.id.eq(performer.professional.id))
                                .leftJoin(performerPerson).on(performerPerson.id.eq(professionalPerformer.person.id))
                                .leftJoin(performerCouncil).on(performerCouncil.id.eq(professionalPerformer.council.id))
                                .leftJoin(performerSpeciality)
                                .on(performerSpeciality.id.eq(professionalPerformer.speciality.id))
                                .leftJoin(performerOccupation)
                                .on(performerOccupation.id.eq(performerSpeciality.occupation.id))
                                .leftJoin(performerOccupationV2)
                                .on(performerOccupationV2.parent.id.eq(performerOccupation.id)
                                                .and(performerOccupationV2.version.eq("V2")))
                                .leftJoin(performerState).on(performerState.id.eq(professionalPerformer.state.id))
                                .where(order.id.in(orderId))
                                .groupBy(numberTemplate);

                return query.fetch();
        }

        public List<OrderItemXmlTO> findOrderItemXmlTo(Long orderId) {

                QOrderItemPO orderItem = QOrderItemPO.orderItemPO;
                QOrderPO order = QOrderPO.orderPO;
                QAccreditationPO accreditation = QAccreditationPO.accreditationPO;
                QTableTypePO accreditationTableType = QTableTypePO.tableTypePO;
                QInsuranceTissPO insuranceTiss = QInsuranceTissPO.insuranceTissPO;
                QInsuranceDiscountRulePO insuranceDiscountRule = QInsuranceDiscountRulePO.insuranceDiscountRulePO;
                QTableTypePO insuranceTableType = new QTableTypePO("insuranceTableType");
                QPerformerPO performer = QPerformerPO.performerPO;
                QProfessionalPO professional = QProfessionalPO.professionalPO;
                QCouncilPO council = QCouncilPO.councilPO;
                QPersonPO person = QPersonPO.personPO;
                QStatePO state = QStatePO.statePO;
                QSpecialityPO speciality = QSpecialityPO.specialityPO;
                QOccupationPO occupation = QOccupationPO.occupationPO;
                QOccupationPO occupationV2 = new QOccupationPO("occupationV2");
                QOrderItemTissPO orderItemTiss = QOrderItemTissPO.orderItemTissPO;
                QDegreeParticipationPO orderItemDegreeParticipation = QDegreeParticipationPO.degreeParticipationPO;
                QOrderTissPO orderTiss = QOrderTissPO.orderTissPO;
                QDegreeParticipationPO orderDegreeParticipation = new QDegreeParticipationPO(
                                "orderDegreeParticipation");
                QViaAccessPO viaAccess = QViaAccessPO.viaAccessPO;
                QViaAdministrationPO viaAdministration = QViaAdministrationPO.viaAdministrationPO;
                QPerformerPO performerAccreditation = new QPerformerPO("performerAccreditation");
                QProfessionalPO professionalAccreditation = new QProfessionalPO("professionalAccreditation");
                QCouncilPO councilAccreditation = new QCouncilPO("councilAccreditation");
                QPersonPO personAccreditation = new QPersonPO("personAccreditation");
                QStatePO stateAccreditation = new QStatePO("stateAccreditation");
                QSpecialityPO specialityAccreditation = new QSpecialityPO("specialityAccreditation");
                QOccupationPO occupationAccreditation = new QOccupationPO("occupationAccreditation");
                QOccupationPO occupationAccreditationV2 = new QOccupationPO("occupationAccreditationV2");
                QParameterPO parameterNoDegreeParticipation = new QParameterPO("parameterNoDegreeParticipation");
                QParameterValuePO parameterValueNoDegreeParticipation = new QParameterValuePO(
                                "parameterValueNoDegreeParticipation");

                JPAQuery<OrderItemXmlTO> query = new JPAQuery<>(entityManager);

                query.select(Projections.bean(
                                OrderItemXmlTO.class,
                                orderItem.id,
                                orderItem.date,
                                orderItem.hour,
                                orderItem.hourend,
                                orderItem.name.as("procedureName"),
                                accreditationTableType.code.coalesce(insuranceTableType.code).as("typeTable"),
                                orderItem.code.as("procedureCode"),
                                orderItem.quantity.as("quantity"),
                                new CaseBuilder()
                                                .when(insuranceDiscountRule.representationReductionAdditional.eq(false)
                                                                .and(insuranceDiscountRule.representationReductionAdditionalLocation
                                                                                .eq(false)))
                                                .then(orderItem.amount.add(orderItem.ucoValue.coalesce(0.0))
                                                                .add(orderItem.filmValue.coalesce(0.0)))
                                                .when(orderItemTiss.location.ne(LocationTypeEnum.NORMAL.getValue()).and(
                                                                insuranceDiscountRule.representationReductionAdditionalLocation
                                                                                .eq(true)))
                                                .then(orderItem.amount.add(orderItem.ucoValue.coalesce(0.0))
                                                                .add(orderItem.filmValue.coalesce(0.0)))
                                                .when(orderItemTiss.nightShift.eq(true).and(
                                                                insuranceDiscountRule.representationReductionAdditionalLocation
                                                                                .eq(true)))
                                                .then(orderItem.amount.add(orderItem.ucoValue.coalesce(0.0))
                                                                .add(orderItem.filmValue.coalesce(0.0)))
                                                .otherwise(orderItem.value.add(orderItem.ucoValue.coalesce(0.0))
                                                                .add(orderItem.filmValue.coalesce(0.0))
                                                                .divide(orderItem.quantity))
                                                .as("valueUnitary"),
                                orderItem.value.add(orderItem.ucoValue.coalesce(0.0))
                                                .add(orderItem.filmValue.coalesce(0.0)).as("valueTotal"),
                                person.name.as("performerName"),
                                professional.councilNumber.as("performerCouncilNumber"),
                                council.code.as("performerCouncilType"),
                                council.initials.as("performerCouncilInitials"),
                                state.code.as("performerState"),
                                state.initials.as("performerStateInitials"),
                                occupation.code.as("performerCbos"),
                                occupationV2.code.as("performerCbosv2"),
                                person.cpf.as("performerCpf"),
                                speciality.name.as("performerSpeciality"),
                                orderItemDegreeParticipation.code.coalesce(orderDegreeParticipation.code)
                                                .as("degreeParticipation"),
                                viaAccess.code.as("viaAccess"),
                                viaAdministration.code.as("viaAdministration"),
                                orderItemTiss.technique.as("technique"),
                                new CaseBuilder()
                                                .when(insuranceDiscountRule.representationReductionAdditional.eq(false)
                                                                .and(insuranceDiscountRule.representationReductionAdditionalLocation
                                                                                .eq(false)))
                                                .then(orderItemTiss.reductionAddition)
                                                .when(orderItemTiss.location.ne(LocationTypeEnum.NORMAL.getValue()).and(
                                                                insuranceDiscountRule.representationReductionAdditionalLocation
                                                                                .eq(true)))
                                                .then(orderItemTiss.reductionAddition)
                                                .otherwise(1.0)
                                                .as("reductionAddition"),
                                orderTiss.notes,
                                personAccreditation.name.as("performerAccreditationName"),
                                professionalAccreditation.councilNumber.as("performerAccreditationCouncilNumber"),
                                councilAccreditation.code.as("performerAccreditationCouncilType"),
                                councilAccreditation.initials.as("performerAccreditationCouncilInitials"),
                                stateAccreditation.code.as("performerAccreditationState"),
                                stateAccreditation.initials.as("performerAccreditationStateInitials"),
                                occupationAccreditation.code.as("performerAccreditationCbos"),
                                occupationAccreditationV2.code.as("performerAccreditationCbosV2"),
                                personAccreditation.cpf.as("performerAccreditationCpf"),
                                specialityAccreditation.name.as("performerAccreditationSpeciality"),
                                parameterValueNoDegreeParticipation.value.coalesce(parameterNoDegreeParticipation.value)
                                                .as("parameterValueNoDegreeParticipation")))
                                .from(orderItem)
                                .leftJoin(order).on(order.id.eq(orderItem.order.id))
                                .leftJoin(insuranceDiscountRule)
                                .on(insuranceDiscountRule.insurance.id.eq(order.insurance.id))
                                .leftJoin(accreditation)
                                .on(accreditation.agreement.id.eq(order.agreement.id)
                                                .and(accreditation.procedure.id.eq(orderItem.procedure.id)
                                                                .and(accreditation.active.eq(true))))
                                .leftJoin(accreditationTableType)
                                .on(accreditationTableType.id.eq(accreditation.tableType.id))
                                .leftJoin(parameterNoDegreeParticipation)
                                .on(parameterNoDegreeParticipation.name
                                                .eq(GlobalKeyParameterConstants.NO_DEGREE_OF_PARTICIPATION_IN_XML))
                                .leftJoin(parameterValueNoDegreeParticipation)
                                .on(parameterValueNoDegreeParticipation.parameter.id
                                                .eq(parameterNoDegreeParticipation.id)
                                                .and(parameterValueNoDegreeParticipation.key
                                                                .eq(accreditation.id.stringValue())))
                                .leftJoin(insuranceTiss).on(insuranceTiss.insurance.id.eq(order.insurance.id))
                                .leftJoin(insuranceTableType)
                                .on(insuranceTableType.id.eq(insuranceTiss.procedureTableType.id))
                                .leftJoin(performer).on(performer.id.eq(orderItem.performer.id))
                                .leftJoin(professional).on(professional.id.eq(performer.professional.id))
                                .leftJoin(council).on(council.id.eq(professional.council.id))
                                .leftJoin(person).on(person.id.eq(professional.person.id))
                                .leftJoin(state).on(state.id.eq(professional.state.id))
                                .leftJoin(speciality).on(speciality.id.eq(orderItem.speciality.id))
                                .leftJoin(occupation).on(occupation.id.eq(speciality.occupation.id))
                                .leftJoin(occupationV2)
                                .on(occupationV2.parent.id.eq(occupation.id).and(occupationV2.version.eq("V2")))
                                .leftJoin(orderItemTiss).on(orderItemTiss.orderItem.id.eq(orderItem.id))
                                .leftJoin(orderItemDegreeParticipation)
                                .on(orderItemDegreeParticipation.id.eq(orderItemTiss.degreeParticipation.id))
                                .leftJoin(viaAccess).on(viaAccess.id.eq(orderItemTiss.viaAccess.id))
                                .leftJoin(viaAdministration)
                                .on(viaAdministration.id.eq(orderItemTiss.viaAdministration.id))
                                .leftJoin(orderTiss).on(orderTiss.order.id.eq(order.id))
                                .leftJoin(orderDegreeParticipation)
                                .on(orderDegreeParticipation.id.eq(orderTiss.degreeParticipation.id))
                                .leftJoin(performerAccreditation)
                                .on(performerAccreditation.id.eq(orderItem.performerAccredited.id))
                                .leftJoin(professionalAccreditation)
                                .on(professionalAccreditation.id.eq(performerAccreditation.professional.id))
                                .leftJoin(councilAccreditation)
                                .on(councilAccreditation.id.eq(professionalAccreditation.council.id))
                                .leftJoin(personAccreditation)
                                .on(personAccreditation.id.eq(professionalAccreditation.person.id))
                                .leftJoin(stateAccreditation)
                                .on(stateAccreditation.id.eq(professionalAccreditation.state.id))
                                .leftJoin(specialityAccreditation)
                                .on(specialityAccreditation.id.eq(professionalAccreditation.speciality.id))
                                .leftJoin(occupationAccreditation)
                                .on(occupationAccreditation.id.eq(specialityAccreditation.occupation.id))
                                .leftJoin(occupationAccreditationV2)
                                .on(occupationAccreditationV2.parent.id.eq(occupationAccreditation.id)
                                                .and(occupationAccreditationV2.version.eq("V2")))
                                .where(orderItem.order.id.eq(orderId))
                                .orderBy(orderItem.date.desc());

                return query.fetch();
        }

        public List<OrderItemMatMedTaxTO> findMaterialOpme(Long orderId, String type) {

                QOrderItemProductPO orderItemProduct = QOrderItemProductPO.orderItemProductPO;
                QOrderItemPO orderItem = QOrderItemPO.orderItemPO;
                QOrderPO order = QOrderPO.orderPO;
                QAccreditationPO accreditation = QAccreditationPO.accreditationPO;
                QTableTypePO accreditationTableType = QTableTypePO.tableTypePO;
                QInsuranceTissPO insuranceTiss = QInsuranceTissPO.insuranceTissPO;
                QTableTypePO insuranceTableType = new QTableTypePO("insuranceTableType");
                QProductPO product = QProductPO.productPO;
                QMeasureTissPO measureTiss = QMeasureTissPO.measureTissPO;

                List<String> listTypeProduct = new ArrayList<String>();
                if (type.equals("M")) {
                        listTypeProduct.add("M");
                        listTypeProduct.add("O");
                } else {
                        listTypeProduct.add("D");
                }

                StringExpression typeCase = Expressions
                                .stringTemplate("CASE WHEN {0} = 'M' THEN '03' " +
                                                "WHEN {0} = 'D' THEN '02'" +
                                                "WHEN {0} = 'O' THEN '08' END",
                                                orderItemProduct.type);

                JPAQuery<OrderItemMatMedTaxTO> query = new JPAQuery<>(entityManager);

                query.select(Projections.bean(
                                OrderItemMatMedTaxTO.class,
                                orderItemProduct.date,
                                orderItem.hour,
                                orderItem.hourend,
                                orderItemProduct.code.as("code"),
                                orderItemProduct.name.as("name"),
                                orderItemProduct.quantity.as("quantity"),
                                orderItemProduct.value.as("valueUnitary"),
                                orderItemProduct.total.as("valueTotal"),
                                measureTiss.code.coalesce("036").as("measureTiss"),
                                accreditationTableType.code.coalesce(insuranceTableType.code).as("tableType"),
                                typeCase.as("type"))).from(orderItemProduct)
                                .leftJoin(orderItem).on(orderItem.id.eq(orderItemProduct.orderItem.id))
                                .leftJoin(order).on(order.id.eq(orderItem.order.id))
                                .leftJoin(accreditation)
                                .on(accreditation.agreement.id.eq(order.agreement.id)
                                                .and(accreditation.product.id.eq(orderItemProduct.product.id)))
                                .leftJoin(accreditationTableType)
                                .on(accreditationTableType.id.eq(accreditation.tableType.id))
                                .leftJoin(insuranceTiss).on(insuranceTiss.insurance.id.eq(order.insurance.id));

                if (type.equals("M")) {
                        query.leftJoin(insuranceTableType)
                                        .on(insuranceTableType.id.eq(insuranceTiss.materialTableType.id));
                } else {
                        query.leftJoin(insuranceTableType)
                                        .on(insuranceTableType.id.eq(insuranceTiss.medicamentTableType.id));
                }

                query.leftJoin(product).on(product.id.eq(orderItemProduct.product.id))
                                .leftJoin(measureTiss).on(measureTiss.id.eq(product.measureTiss.id))
                                .where(order.id.eq(orderId)
                                                .and(orderItemProduct.type.in(listTypeProduct)));

                return query.fetch();
        }

        public List<OrderItemMatMedTaxTO> findFee(Long orderId) {

                QOrderItemFeePO orderItemFee = QOrderItemFeePO.orderItemFeePO;
                QOrderItemPO orderItem = QOrderItemPO.orderItemPO;
                QOrderPO order = QOrderPO.orderPO;
                QAccreditationPO accreditation = QAccreditationPO.accreditationPO;
                QTableTypePO accreditationTableType = QTableTypePO.tableTypePO;
                QInsuranceTissPO insuranceTiss = QInsuranceTissPO.insuranceTissPO;
                QTableTypePO insuranceTableType = new QTableTypePO("insuranceTableType");
                QFeePO fee = QFeePO.feePO;
                QMeasureTissPO measureTiss = QMeasureTissPO.measureTissPO;

                StringExpression typeCase = Expressions
                                .stringTemplate("CASE WHEN {0} = 'T' THEN '07' " +
                                                "WHEN {0} = 'D' THEN '05'" +
                                                "WHEN {0} = 'G' THEN '01' END",
                                                fee.type);

                JPAQuery<OrderItemMatMedTaxTO> query = new JPAQuery<>(entityManager);

                query.select(Projections.bean(
                                OrderItemMatMedTaxTO.class,
                                orderItemFee.date,
                                orderItem.hour,
                                orderItem.hourend,
                                orderItemFee.code.as("code"),
                                orderItemFee.name.as("name"),
                                orderItemFee.quantity.as("quantity"),
                                orderItemFee.value.as("valueUnitary"),
                                orderItemFee.total.as("valueTotal"),
                                measureTiss.code.coalesce("036").as("measureTiss"),
                                accreditationTableType.code.coalesce(insuranceTableType.code).as("tableType"),
                                typeCase.as("type"))).from(orderItemFee)
                                .leftJoin(orderItem).on(orderItem.id.eq(orderItemFee.orderItem.id))
                                .leftJoin(order).on(order.id.eq(orderItem.order.id))
                                .leftJoin(accreditation)
                                .on(accreditation.agreement.id.eq(order.agreement.id)
                                                .and(accreditation.fee.id.eq(orderItemFee.fee.id)))
                                .leftJoin(accreditationTableType)
                                .on(accreditationTableType.id.eq(accreditation.tableType.id))
                                .leftJoin(insuranceTiss).on(insuranceTiss.insurance.id.eq(order.insurance.id))
                                .leftJoin(insuranceTableType)
                                .on(insuranceTableType.id.eq(insuranceTiss.feeTableType.id))
                                .leftJoin(fee).on(fee.id.eq(orderItemFee.fee.id))
                                .leftJoin(measureTiss).on(measureTiss.id.eq(fee.measureTiss.id))
                                .where(order.id.eq(orderId));

                return query.fetch();
        }

        public List<OrderItemAuxiliaryTO> findAuxiliary(Long orderItemId) {

                QOrderItemAuxiliaryPO orderItemAuxiliary = QOrderItemAuxiliaryPO.orderItemAuxiliaryPO;
                QOrderItemPO orderItem = QOrderItemPO.orderItemPO;
                QAuxiliaryPO auxiliary = QAuxiliaryPO.auxiliaryPO;
                QDegreeParticipationPO degreeParticipation = QDegreeParticipationPO.degreeParticipationPO;
                QPerformerPO performer = QPerformerPO.performerPO;
                QProfessionalPO professional = QProfessionalPO.professionalPO;
                QPersonPO person = QPersonPO.personPO;
                QStatePO state = QStatePO.statePO;
                QSpecialityPO speciality = QSpecialityPO.specialityPO;
                QOccupationPO occupation = QOccupationPO.occupationPO;
                QOccupationPO occupationV2 = new QOccupationPO("occupationV2");
                QCouncilPO council = QCouncilPO.councilPO;

                JPAQuery<OrderItemAuxiliaryTO> query = new JPAQuery<>(entityManager);

                query.select(
                                Projections.bean(
                                                OrderItemAuxiliaryTO.class,
                                                person.name.as("performerName"),
                                                professional.councilNumber.as("performerCouncilNumber"),
                                                council.code.as("performerCouncilType"),
                                                council.initials.as("performerCouncilInitials"),
                                                state.code.as("performerState"),
                                                state.initials.as("performerStateInitials"),
                                                occupation.code.as("performerCbos"),
                                                occupationV2.code.as("performerCbosV2"),
                                                person.cpf.as("performerCpf"),
                                                speciality.name.as("performerSpeciality"),
                                                degreeParticipation.code.as("degreeParticipation"),
                                                orderItemAuxiliary.value.as("valueTotal"),
                                                orderItemAuxiliary.number.as("number")))
                                .from(orderItemAuxiliary)
                                .leftJoin(orderItem).on(orderItem.id.eq(orderItemAuxiliary.orderItem.id))
                                .leftJoin(auxiliary).on(auxiliary.id.eq(orderItemAuxiliary.auxiliary.id))
                                .leftJoin(degreeParticipation)
                                .on(degreeParticipation.id.eq(auxiliary.degreeParticipation.id))
                                .leftJoin(performer).on(performer.id.eq(orderItemAuxiliary.performer.id))
                                .leftJoin(professional).on(professional.id.eq(performer.professional.id))
                                .leftJoin(person).on(person.id.eq(professional.person.id))
                                .leftJoin(state).on(state.id.eq(professional.state.id))
                                .leftJoin(speciality).on(speciality.id.eq(professional.speciality.id))
                                .leftJoin(occupation).on(occupation.id.eq(speciality.occupation.id))
                                .leftJoin(occupationV2)
                                .on(occupationV2.parent.id.eq(occupation.id).and(occupationV2.version.eq("V2")))
                                .leftJoin(council).on(council.id.eq(professional.council.id))
                                .where(orderItem.id.eq(orderItemId))
                                .orderBy(degreeParticipation.code.asc());

                return query.fetch();
        }

        public List<OrderItemAuxiliaryTO> findAuxiliaryByOrder(Long orderId, String degree) {

                QOrderItemAuxiliaryPO orderItemAuxiliary = QOrderItemAuxiliaryPO.orderItemAuxiliaryPO;
                QOrderItemPO orderItem = QOrderItemPO.orderItemPO;
                QOrderItemTissPO orderItemTiss = QOrderItemTissPO.orderItemTissPO;
                QOrderPO order = QOrderPO.orderPO;
                QInsuranceDiscountRulePO insuranceDiscountRule = QInsuranceDiscountRulePO.insuranceDiscountRulePO;
                QAuxiliaryPO auxiliary = QAuxiliaryPO.auxiliaryPO;
                QDegreeParticipationPO degreeParticipation = QDegreeParticipationPO.degreeParticipationPO;
                QPerformerPO performer = QPerformerPO.performerPO;
                QProfessionalPO professional = QProfessionalPO.professionalPO;
                QPersonPO person = QPersonPO.personPO;
                QStatePO state = QStatePO.statePO;
                QSpecialityPO speciality = QSpecialityPO.specialityPO;
                QOccupationPO occupation = QOccupationPO.occupationPO;
                QOccupationPO occupationV2 = new QOccupationPO("occupationV2");
                QCouncilPO council = QCouncilPO.councilPO;

                QPerformerPO performerAccredited = new QPerformerPO("performerAccredited");
                QProfessionalPO professionalAccredited = new QProfessionalPO("professionalAccredited");
                QPersonPO personAccredited = new QPersonPO("personAccredited");
                QStatePO stateAccredited = new QStatePO("stateAccredited");
                QSpecialityPO specialityAccredited = new QSpecialityPO("specialityAccredited");
                QOccupationPO occupationAccredited = new QOccupationPO("occupationAccredited");
                QOccupationPO occupationV2Accredited = new QOccupationPO("occupationV2Accredited");
                QCouncilPO councilAccredited = new QCouncilPO("councilAccredited");

                JPAQuery<OrderItemAuxiliaryTO> query = new JPAQuery<>(entityManager);

                query.select(
                                Projections.bean(
                                                OrderItemAuxiliaryTO.class,
                                                personAccredited.name.coalesce(person.name).as("performerName"),
                                                orderItem.id.as("orderItemId"),
                                                professionalAccredited.councilNumber
                                                                .coalesce(professional.councilNumber)
                                                                .as("performerCouncilNumber"),
                                                councilAccredited.code.coalesce(council.code)
                                                                .as("performerCouncilType"),
                                                councilAccredited.initials.coalesce(council.initials)
                                                                .as("performerCouncilInitials"),
                                                stateAccredited.code.coalesce(state.code).as("performerState"),
                                                stateAccredited.initials.coalesce(state.initials)
                                                                .as("performerStateInitials"),
                                                occupationAccredited.code.coalesce(occupation.code).as("performerCbos"),
                                                occupationV2Accredited.code.coalesce(occupationV2.code)
                                                                .as("performerCbosV2"),
                                                personAccredited.cpf.coalesce(person.cpf).as("performerCpf"),
                                                specialityAccredited.name.coalesce(speciality.name)
                                                                .as("performerSpeciality"),
                                                degreeParticipation.code.as("degreeParticipation"),
                                                orderItemAuxiliary.value.as("valueTotal"),
                                                orderItemAuxiliary.number.as("number"),
                                                orderItem.quantity.as("quantity"),
                                                new CaseBuilder()
                                                                .when(insuranceDiscountRule.representationReductionAdditional
                                                                                .eq(false)
                                                                                .and(insuranceDiscountRule.representationReductionAdditionalLocation
                                                                                                .eq(false)))
                                                                .then(orderItemAuxiliary.value
                                                                                .divide(orderItem.quantity)
                                                                                .divide(orderItemTiss.reductionAddition))
                                                                .when(orderItemTiss.location
                                                                                .ne(LocationTypeEnum.NORMAL.getValue())
                                                                                .and(insuranceDiscountRule.representationReductionAdditionalLocation
                                                                                                .eq(true)
                                                                                                .and(orderItemTiss.reductionAddition
                                                                                                                .gt(1.0))))
                                                                .then(orderItemAuxiliary.value
                                                                                .divide(orderItem.quantity)
                                                                                .divide(orderItemTiss.reductionAddition))
                                                                .when(orderItemTiss.nightShift.eq(true).and(
                                                                                insuranceDiscountRule.representationReductionAdditionalLocation
                                                                                                .eq(true)
                                                                                                .and(orderItemTiss.reductionAddition
                                                                                                                .gt(1.0))))
                                                                .then(orderItemAuxiliary.value
                                                                                .divide(orderItem.quantity)
                                                                                .divide(orderItemTiss.reductionAddition))
                                                                .otherwise(orderItemAuxiliary.value
                                                                                .divide(orderItem.quantity))
                                                                .as("valueUnitary"),
                                                // orderItemAuxiliary.value.divide(orderItem.quantity).as("valueUnitary"),
                                                new CaseBuilder()
                                                                .when(insuranceDiscountRule.representationReductionAdditional
                                                                                .eq(false)
                                                                                .and(insuranceDiscountRule.representationReductionAdditionalLocation
                                                                                                .eq(false)))
                                                                .then(orderItemTiss.reductionAddition)
                                                                .when(orderItemTiss.location
                                                                                .ne(LocationTypeEnum.NORMAL.getValue())
                                                                                .and(insuranceDiscountRule.representationReductionAdditionalLocation
                                                                                                .eq(true)))
                                                                .then(orderItemTiss.reductionAddition)
                                                                .when(orderItemTiss.nightShift.eq(true).and(
                                                                                insuranceDiscountRule.representationReductionAdditionalLocation
                                                                                                .eq(true)))
                                                                .then(orderItemTiss.reductionAddition)
                                                                .otherwise(1.0)
                                                                .as("reductionAddition")

                                ))
                                .from(orderItemAuxiliary)
                                .leftJoin(orderItem).on(orderItem.id.eq(orderItemAuxiliary.orderItem.id))
                                .leftJoin(orderItemTiss).on(orderItemTiss.orderItem.id.eq(orderItem.id))
                                .leftJoin(order).on(order.id.eq(orderItem.order.id))
                                .leftJoin(insuranceDiscountRule)
                                .on(insuranceDiscountRule.insurance.id.eq(order.insurance.id))
                                .leftJoin(auxiliary).on(auxiliary.id.eq(orderItemAuxiliary.auxiliary.id))
                                .leftJoin(degreeParticipation)
                                .on(degreeParticipation.id.eq(auxiliary.degreeParticipation.id))
                                .leftJoin(performer).on(performer.id.eq(orderItemAuxiliary.performer.id))
                                .leftJoin(professional).on(professional.id.eq(performer.professional.id))
                                .leftJoin(person).on(person.id.eq(professional.person.id))
                                .leftJoin(state).on(state.id.eq(professional.state.id))
                                .leftJoin(speciality).on(speciality.id.eq(professional.speciality.id))
                                .leftJoin(occupation).on(occupation.id.eq(speciality.occupation.id))
                                .leftJoin(occupationV2)
                                .on(occupationV2.parent.id.eq(occupation.id).and(occupationV2.version.eq("V2")))
                                .leftJoin(council).on(council.id.eq(professional.council.id))
                                .leftJoin(performerAccredited)
                                .on(performerAccredited.id.eq(orderItemAuxiliary.performerAccredited.id))
                                .leftJoin(professionalAccredited)
                                .on(professionalAccredited.id.eq(performerAccredited.professional.id))
                                .leftJoin(personAccredited).on(personAccredited.id.eq(professionalAccredited.person.id))
                                .leftJoin(stateAccredited).on(stateAccredited.id.eq(professionalAccredited.state.id))
                                .leftJoin(specialityAccredited)
                                .on(specialityAccredited.id.eq(professionalAccredited.speciality.id))
                                .leftJoin(occupationAccredited)
                                .on(occupationAccredited.id.eq(specialityAccredited.occupation.id))
                                .leftJoin(occupationV2Accredited)
                                .on(occupationV2Accredited.parent.id.eq(occupationAccredited.id)
                                                .and(occupationV2Accredited.version.eq("V2")))
                                .leftJoin(councilAccredited)
                                .on(councilAccredited.id.eq(professionalAccredited.council.id))
                                .where(order.id.eq(orderId).and(degreeParticipation.code.eq(degree)))
                                .orderBy(degreeParticipation.code.asc());

                return query.fetch();
        }

        public List<ReportDeclarationTO> findDeclarations(Long orderId) {

                JPAQuery<ReportDeclarationTO> query = new JPAQuery<>(entityManager);

                QOrderTissDeclarationPO orderTissDeclaration = QOrderTissDeclarationPO.orderTissDeclarationPO;
                QOrderTissPO orderTiss = QOrderTissPO.orderTissPO;
                QCidPO cid = QCidPO.cidPO;

                query.select(
                                Projections.bean(
                                                ReportDeclarationTO.class,
                                                orderTissDeclaration.birthDeclaration,
                                                orderTissDeclaration.deathDeclaration,
                                                orderTissDeclaration.dornIndicator,
                                                cid.code.as("cid")))
                                .from(orderTissDeclaration)
                                .join(orderTiss).on(orderTiss.id.eq(orderTissDeclaration.orderTiss.id))
                                .leftJoin(cid).on(cid.id.eq(orderTissDeclaration.cid.id))
                                .where(orderTiss.order.id.eq(orderId));

                return query.fetch();
        }

}
