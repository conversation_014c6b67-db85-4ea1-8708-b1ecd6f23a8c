package br.com.focusts.clinicall.service.modules.register.accreditation.facade;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import br.com.focusts.clinicall.service.modules.register.accreditation.to.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.exception.ApplicationException;
import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.AccreditationPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.AgreementPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.InsuranceDiscountRangePO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.InsurancePO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.InsurancePlanPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.service.AccreditationService;
import br.com.focusts.clinicall.service.modules.register.accreditation.service.AgreementService;
import br.com.focusts.clinicall.service.modules.register.accreditation.service.InsuranceDiscountRangeService;
import br.com.focusts.clinicall.service.modules.register.accreditation.service.InsuranceDiscountRuleService;
import br.com.focusts.clinicall.service.modules.register.accreditation.service.InsurancePlanService;
import br.com.focusts.clinicall.service.modules.register.accreditation.service.InsuranceService;
import br.com.focusts.clinicall.service.modules.system.service.PersonImageService;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.PlanPO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.service.PlanService;
import br.com.focusts.clinicall.service.modules.tables.financial.facade.AccountFacade;
import br.com.focusts.clinicall.service.modules.tables.financial.to.AccountTO;

@Component
@Transactional
public class DefaultInsuranceFacade extends AbstractCrudFacade<InsurancePO, java.lang.Long> implements InsuranceFacade {

	@Autowired
	private InsuranceService insuranceService;

	@Autowired
	private PersonImageService personImageService;

	@Autowired
	private AccreditationService accreditationService;

	@Autowired
	private InsuranceDiscountRuleService insuranceDiscountRuleService;

	@Autowired
	private InsuranceDiscountRangeService insuranceDiscountRangeService;

	@Autowired
	private AgreementService agreementService;

	@Autowired
	private InsurancePlanService insurancePlanService;

	@Autowired
	private PlanService planService;

	@Autowired
	private AccountFacade accountFacade;

	@Override
	public CrudService<InsurancePO, java.lang.Long> getCrudService() {
		return insuranceService;
	}

	@Override
	public Page<InsurancePO> findByNameContainingOrAliasContainingOrCnpjContainingOrAnsRegistryContainingOrCnesContaining(
			PageSearchTO pageSearchTO) {
		return insuranceService
				.findByNameContainingOrAliasContainingOrCnpjContainingOrAnsRegistryContainingOrCnesContaining(
						pageSearchTO);
	}

	@Override
	public Page<InsurancePO> findByNameStartingWithOrAliasStartingWithAndActiveTrue(PageSearchTO pageSearchTO) {
		return insuranceService.findByNameStartingWithOrAliasStartingWithAndActiveTrue(pageSearchTO);
	}

	@Override
	public Page<InsuranceResponseTO> findInsuranceSimpleSearch(PageSearchTO pageSearchTO, Boolean active) {
		return insuranceService.findInsuranceSimpleSearch(pageSearchTO, active);
	}

	@Override
	public Page<InsurancePO> findActivesWithoutAccreditationForProcedure(Long procedureId, PageSearchTO pageSearchTO) {
		return insuranceService.findActivesWithoutAccreditationForProcedure(procedureId, pageSearchTO);
	}

	@Override
	public Page<InsurancePO> findByPaymentModelCoparticipation(PageSearchTO pageSearchTO) {
		return insuranceService.findByPaymentModelCoparticipation(pageSearchTO);
	}

	@Override
	public Page<InsurancePO> findByIdNotAndInsuranceParentNull(Long insuranceId, PageSearchTO pageSearchTO) {
		return insuranceService.findByIdNotAndInsuranceParentNull(insuranceId, pageSearchTO);
	}

	@Override
	public Page<InsurancePO> findByPlanId(Long planId, PageSearchTO pageSearchTO) {
		return insuranceService.findByPlanId(planId, pageSearchTO);
	}

	@Override
	public void deleteImage(Long imageId) {
		personImageService.delete(imageId);
	}

	@Override
	public List<InsurancePO> findByProfessionalId(Long professionalId) {
		return insuranceService.findByProfessionalId(professionalId);
	}

	@Override
	public Page<InsurancePO> findByProfessionalId(Long professionalId, PageSearchTO pageSearchTO) {
		return insuranceService.findByProfessionalId(professionalId, pageSearchTO);
	}

	public AccreditationPO findAccreditationByCompanyIdAndInsuranceIdAndProcedureId(Long companyId, Long insuranceId,
			Long procedureId) {
		AgreementPO agreementPO = agreementService.findByCompanyIdAndInsuranceId(companyId, insuranceId);

		return accreditationService.findByProcedureIdAndAgreementId(procedureId, agreementPO.getId());
	}

	@Override
	public List<AccreditationPO> findAccreditationByCompanyIdAndInsuranceIdAndAuxiliaryNotNull(Long companyId,
			Long insuranceId) {

		List<AccreditationPO> accreditationPOs = new ArrayList<>();

		AgreementPO agreementPO = agreementService.findByCompanyIdAndInsuranceId(companyId, insuranceId);

		if (agreementPO != null) {
			accreditationPOs = agreementPO.getAccreditationList().stream()
					.filter((AccreditationPO a) -> a.getAuxiliary() != null).collect(Collectors.toList());
		}

		return accreditationPOs;

	}

	@Override
	public Page<InsurancePO> findByPerformerId(Long performerId, PageSearchTO pageSearchTO) {
		return insuranceService.findByPerformerlId(performerId, pageSearchTO);
	}

	@Override
	public Page<InsurancePlanPO> findByInsurancePlan(Long insuranceId, PageSearchTO pageSearchTO) {
		return insurancePlanService.findByInsurance_id(insuranceId, pageSearchTO);
	}

	@Override
	public InsurancePlanPO findByInsurancePlanId(Long insurancePlanId) {
		var insurancePLan = insurancePlanService.findById(insurancePlanId);
		if (insurancePLan == null) {
			throw new ApplicationException("Plano do convênio não encontrado!");
		}
		return insurancePLan;
	}

	@Override
	public List<InsurancePO> findInsuranceByMultipleIds(List<Long> insuranceIdList) {
		return insuranceService.findInsuranceByMultipleIds(insuranceIdList);
	}

	@Override
	public Page<InsurancePO> findByWebService(PageSearchTO pageSearchTO) {
		return insuranceService.findByWebService(pageSearchTO);
	}

	@Override
	public void insertDiscountRule(InsuranceDiscountRuleTO insuranceDiscountRuleTO) {
		insuranceDiscountRuleService.insertInsuranceDiscountRule(insuranceDiscountRuleTO);
	}

	@Override
	public InsuranceDiscountRuleTO findByDiscountRuleByInsurance(Long insuranceId) {
		return insuranceDiscountRuleService.findByDiscountRuleByInsurance(insuranceId);
	}

	@Override
	public InsuranceDiscountRuleTO findByDiscountRuleByAccreditation(Long accreditationId) {
		return insuranceDiscountRuleService.findByDiscountRuleByAccreditation(accreditationId);
	}

	@Override
	public PlanPO insertInsurancePlan(InsurancePlanTO insurancePlanTO) {
		InsurancePO insurance = insuranceService.findById(insurancePlanTO.getInsuranceId());

		PlanPO plan = new PlanPO();
		plan.setName(insurancePlanTO.getNamePlan());
		plan = planService.save(plan);

		InsurancePlanPO insurancePlanPO = new InsurancePlanPO();
		insurancePlanPO.setPlan(plan);
		insurancePlanPO.setInsurance(insurance);

		insurancePlanService.save(insurancePlanPO);
		return plan;
	}

	@Override
	public void deleteInsurancePLan(Long insurancePLanId) {
		var insurancePlan = insurancePlanService.findById(insurancePLanId);
		if (insurancePlan == null) {
			throw new ApplicationException("Plano não localizado no convênio");
		}
		insurancePlanService.delete(insurancePLanId);
	}

	@Override
	public void insertInsurancePLan(Long insuranceId, Long planId) {

		Optional<InsurancePlanPO> insurancePlanPO = insurancePlanService.findByInsurance_idAndPlan_id(insuranceId,
				planId);
		if (insurancePlanPO.isEmpty()) {
			var insurance = insuranceService.findById(insuranceId);
			var plan = planService.findById(planId);
			InsurancePlanPO insurancePlan = new InsurancePlanPO();
			insurancePlan.setInsurance(insurance);
			insurancePlan.setPlan(plan);
			insurancePlanService.save(insurancePlan);
		} else {
			throw new ApplicationException("Plano já Vinculado ao convenio");
		}

	}

	@Override
	public List<InsuranceResponseTO> findInsuranceSimpleAll() {
		return insuranceService.findInsuranceSimpleAll();
	}

	@Override
	public void insertDiscountRange(InsuranceDiscountRangeTO discountRangeTO) {
		insuranceDiscountRangeService.insertInsuranceDiscountRange(discountRangeTO);
	}

	@Override
	public List<InsuranceDiscountRangeTO> findByInsuranceDiscountRuleId(Long insuranceDiscountRuleId) {

		return insuranceDiscountRangeService.findByInsuranceDiscountRuleId(insuranceDiscountRuleId);
	}

	@Override
	public InsuranceDiscountRangePO findDiscountRangeById(Long id) {
		return insuranceDiscountRangeService.findById(id);
	}

	@Override
	public void deleteDiscountRangeById(Long id) {
		insuranceDiscountRangeService.delete(id);
	}

	@Override
	public Page<InsuranceTO> findByNameActiveTrue(PageSearchTO pageSearchTO) {
		return insuranceService.findByNameActiveTrue(pageSearchTO);
	}

	@Override
	public Page<AccountTO> searchAccount(PageSearchTO pageSearchTO) {
		return accountFacade.searchAccount(pageSearchTO);
	}

	@Override
	public AccountTO findAccountByInsuranceId(Long insuranceId) {
		return insuranceService.findAccountByInsuranceId(insuranceId);
	}
}
