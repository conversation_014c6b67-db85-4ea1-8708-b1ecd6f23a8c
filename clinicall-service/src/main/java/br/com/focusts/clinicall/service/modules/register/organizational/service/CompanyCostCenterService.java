package br.com.focusts.clinicall.service.modules.register.organizational.service;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.CompanyCostCenterPO;
import br.com.focusts.clinicall.service.modules.register.organizational.to.CompanyCostCenterTO;
import org.springframework.data.domain.Page;

import java.util.List;

public interface CompanyCostCenterService extends CrudService<CompanyCostCenterPO,Long>{

	public CompanyCostCenterPO findByCompanyidAndCostCenterid(Long companyId, Long costCenterId);

	public List<CompanyCostCenterPO> findByCompanyId(Long companyId);

	List<CompanyCostCenterTO> searchCompanyCostCenter(Long companyId);

	CompanyCostCenterTO findCompanyCostCenterTOById(Long id);

}
