
package br.com.focusts.clinicall.service.modules.tables.general.facade;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.general.po.EthnicityPO;

public interface EthnicityFacade extends CrudFacade<EthnicityPO,java.lang.Long>{

    public Page<EthnicityPO> findByCodeContainingOrNameContaining(PageSearchTO pageSearchTO);

}

