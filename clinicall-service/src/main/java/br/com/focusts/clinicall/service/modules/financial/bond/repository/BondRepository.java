package br.com.focusts.clinicall.service.modules.financial.bond.repository;

import java.util.List;

import org.bouncycastle.pqc.jcajce.provider.xmss.XMSSMTSignatureSpi.withShake256andPrehash;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.financial.bond.po.BondPO;
import br.com.focusts.clinicall.service.modules.financial.bond.to.BondListTO;
import br.com.focusts.clinicall.service.modules.operational.billing.to.PayBondPayTO;

@Repository
public interface BondRepository extends CrudRepository<BondPO, java.lang.Long> {
      /**
       * Filtro para Titulos a Pagar
       *
       * @param pageSearchTO
       * @return br.com.focusts.clinicall.service.modules.financial.bond.po.BondPO
       */
      @Query("""
                  SELECT
                  new br.com.focusts.clinicall.service.modules.financial.bond.to.BondListTO(
                  bond.id,
                  COALESCE(insurance.name, companyTarget.alias, firm.name, supplier.name, personPatient.name, personPerformer.name, person.name, personUser.name) as name,
                  bond.value,
                  COALESCE(bondDetail.code, bondDetailParent.code) as document,
                  company.alias as companyName,
                  COALESCE(historic.name, historicParent.name) as historicName,
                  bond.parcelNumber as parcel,
                  COALESCE( bondDetail.parcelQuantity, bondDetailParent.parcelQuantity) as parcelQuantity,
                  bond.dueDate as dueDate,
                  ROUND(COALESCE(SUM(bondPay.value), 0), 2) as totalPay
                  )
                  FROM
                  BondPO bond
                  LEFT JOIN InsurancePO insurance ON insurance.id = bond.insurance.id
                  LEFT JOIN CompanyPO companyTarget ON companyTarget.id = bond.companyTarget.id
                  LEFT JOIN FirmPO firm ON firm.id = bond.firm.id
                  LEFT JOIN SupplierPO supplier ON supplier.id = bond.supplier.id
                  LEFT JOIN PatientPO patient ON patient.id = bond.patient.id
                  LEFT JOIN PersonPO personPatient ON personPatient.id = patient.person.id
                  LEFT JOIN ProfessionalPO professionalPerformer ON professionalPerformer.id = bond.professional.id
                  LEFT JOIN PersonPO personPerformer ON personPerformer.id = professionalPerformer.person.id
                  LEFT JOIN PersonPO person ON person.id = bond.person.id
                  LEFT JOIN UserPO user ON user.id = bond.user.id
                  LEFT JOIN PersonPO personUser ON personUser.id = user.person.id
                  LEFT JOIN CompanyPO company ON company.id = bond.company.id
                  LEFT JOIN BondDetailPO bondDetail ON bondDetail.bond.id = bond.id
                  LEFT JOIN HistoricPO historic ON historic.id = bondDetail.historic.id
                  LEFT JOIN BondDetailPO bondDetailParent ON bondDetailParent.bond.id = bond.bondParent.id
                  LEFT JOIN BondPayPO bondPay ON bondPay.bond.id = bond.id
                  LEFT JOIN HistoricPO historicParent ON historicParent.id = bondDetailParent.historic.id
                  WHERE bond.bondType = 'P'
                  GROUP BY bond.id, insurance.name, companyTarget.alias, firm.name, supplier.name, personPatient.name, personPerformer.name, person.name, personUser.name, bond.value, bondDetail.code, bondDetailParent.code, company.alias, historic.name, historicParent.name, bond.parcelNumber, bondDetail.parcelQuantity, bondDetailParent.parcelQuantity, bond.dueDate
                  """)
      public Page<BondListTO> findByAccountsPayable(Pageable pageable);

      /**
       * Filtro para Titulos a Receber
       *
       * @param pageSearchTO
       * @return br.com.focusts.clinicall.service.modules.financial.bond.po.BondPO
       */
      @Query("""
                  SELECT
                        new br.com.focusts.clinicall.service.modules.financial.bond.to.BondListTO(
                        bond.id,
                        COALESCE(insurance.name, companyTarget.alias, firm.name, supplier.name, personPatient.name, personPerformer.name, person.name, personUser.name) as name,
                        bond.value,
                        COALESCE(bondDetail.code, bondDetailParent.code) as document,
                        company.alias as companyName,
                        COALESCE(historic.name, historicParent.name, bondDetail.note) as historicName,
                        bond.parcelNumber as parcel,
                        COALESCE( bondDetail.parcelQuantity, bondDetailParent.parcelQuantity) as parcelQuantity,
                        bond.dueDate as dueDate,
                         ROUND(COALESCE(SUM(bondPay.value), 0), 2) as totalPay
                        )
                        FROM
                        BondPO bond
                        LEFT JOIN InsurancePO insurance ON insurance.id = bond.insurance.id
                        LEFT JOIN CompanyPO companyTarget ON companyTarget.id = bond.companyTarget.id
                        LEFT JOIN FirmPO firm ON firm.id = bond.firm.id
                        LEFT JOIN SupplierPO supplier ON supplier.id = bond.supplier.id
                        LEFT JOIN PatientPO patient ON patient.id = bond.patient.id
                        LEFT JOIN PersonPO personPatient ON personPatient.id = patient.person.id
                        LEFT JOIN ProfessionalPO professionalPerformer ON professionalPerformer.id = bond.professional.id
                        LEFT JOIN PersonPO personPerformer ON personPerformer.id = professionalPerformer.person.id
                        LEFT JOIN PersonPO person ON person.id = bond.person.id
                        LEFT JOIN UserPO user ON user.id = bond.user.id
                        LEFT JOIN PersonPO personUser ON personUser.id = user.person.id
                        LEFT JOIN CompanyPO company ON company.id = bond.company.id
                        LEFT JOIN BondDetailPO bondDetail ON bondDetail.bond.id = bond.id
                        LEFT JOIN HistoricPO historic ON historic.id = bondDetail.historic.id
                        LEFT JOIN BondDetailPO bondDetailParent ON bondDetailParent.bond.id = bond.bondParent.id
                        LEFT JOIN HistoricPO historicParent ON historicParent.id = bondDetailParent.historic.id
                        LEFT JOIN BondPayPO bondPay ON bondPay.bond.id = bond.id
                        WHERE bond.bondType = 'R'
                        GROUP BY bond.id, insurance.name, companyTarget.alias, firm.name, supplier.name, personPatient.name, personPerformer.name, person.name, personUser.name, bond.value, bondDetail.code, bondDetailParent.code, company.alias, historic.name, historicParent.name, bondDetail.note, bond.parcelNumber, bondDetail.parcelQuantity, bondDetailParent.parcelQuantity, bond.dueDate
                  """)
      public Page<BondListTO> findByAccountsReceivable(Pageable pageable);

      @Modifying
      @Query("""
                  DELETE FROM BondDetailPO bondDetail
                  WHERE bondDetail.bond.id = :id
                  """)
      public void deleteBondDetailByBondId(Long id);

      @Query("""
                   SELECT
                  new br.com.focusts.clinicall.service.modules.operational.billing.to.PayBondPayTO(
                   bond.id,
                  pay.id,
                   billingPay.billing.id,
                  bondDetail.code,
                   bond.value,
                  SUM(COALESCE(billingPayOrderItem.value,0) + COALESCE(billingPayOrderItem.valueBigest,0)),
                   SUM(COALESCE(gloss.value, 0) + COALESCE(gloss.valueAccepted, 0) + COALESCE(gloss.valueAuxiliary, 0) + COALESCE(gloss.valueFee, 0) + COALESCE(gloss.valueMaterial, 0) + COALESCE (gloss.valueMedicament, 0) ),
                   COALESCE(pay.credits,0),
                   pay.creditDate,
                   COALESCE(pay.valueUndefinedGloss,0),
                   (SELECT  SUM(COALESCE(billingPayOrderItemSub.value,0) + COALESCE(billingPayOrderItemSub.valueBigest,0)) FROM BillingPayOrderItemPO billingPayOrderItemSub JOIN BillingPayPO billingPaySub ON billingPaySub.id = billingPayOrderItemSub.billingPay.id WHERE billingPaySub.pay.id = pay.id),
                    bond.dueDate,
                   bondDetail.dated,
                   'B'
                   )
                                     FROM
                   PayPO pay
                   JOIN BillingPayPO billingPay ON billingPay.pay.id = pay.id
                   JOIN BillingPayOrderItemPO billingPayOrderItem ON billingPayOrderItem.billingPay.id = billingPay.id
                   JOIN SendingToFinanceItemPO SendingToFinanceItem ON SendingToFinanceItem.billing.id = billingPay.billing.id
                   JOIN BondPO bond ON bond.id = SendingToFinanceItem.bond.id
                   JOIN BondDetailPO bondDetail ON bondDetail.bond.id = SendingToFinanceItem.bond.id
                   LEFT JOIN GlossPO gloss ON gloss.billingPayOrderItem.id = billingPayOrderItem.id
                   WHERE pay.id = :payId
                   AND SendingToFinanceItem.reversal = false
                   AND NOT EXISTS (
                       SELECT 1 FROM BondPayPO bondPay 
                       WHERE bondPay.bond.id = bond.id
                   )
                   GROUP BY 1,2,3,4
                   """)
      public List<PayBondPayTO> findBondByPayId(Long payId);
}
