package br.com.focusts.clinicall.service.modules.operational.medical.facade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.medical.po.RequestModelPO;
import br.com.focusts.clinicall.service.modules.operational.medical.service.RequestModelService;

@Component
@Transactional
public class DefaultRequestModelFacade extends AbstractCrudFacade<RequestModelPO, Long> implements RequestModelFacade {

	@Autowired
	private RequestModelService requestModelService;

    @Override
	public CrudService<RequestModelPO,java.lang.Long> getCrudService() {
            return requestModelService;
	}
	
    @Override
	public void delete(Long requestId) {
            requestModelService.delete(requestId);
	}

	@Override
	public Page<RequestModelPO> findByNameContaining(PageSearchTO pageSearchTO, String type) {
            return requestModelService.findByNameContaining(pageSearchTO, type);
	}  

}
