
package br.com.focusts.clinicall.service.modules.tables.accreditation.facade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.StandardTableItemPO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.service.StandardTableItemService;

@Component
@Transactional
public class DefaultStandardTableItemFacade extends AbstractCrudFacade <StandardTableItemPO, java.lang.Long> implements StandardTableItemFacade  {

	@Autowired
	private StandardTableItemService standardTableItemService;

	@Override
	public CrudService<StandardTableItemPO,java.lang.Long> getCrudService() {
            return standardTableItemService;
	}

	@Override
	public Page<StandardTableItemPO> findByNameContaining(PageSearchTO pageSearchTO) {
            return standardTableItemService.findByNameContaining(pageSearchTO);
	} 
 

	@Override
	public Page<StandardTableItemPO> findByStandardTableIdAndNameContainingAndCodeContaining(Long standatardTableId,
			PageSearchTO pageSearchTO) {
		return standardTableItemService.findByStandardTableIdAndNameContainingAndCodeContaining(standatardTableId,pageSearchTO);
	}

	@Override
	public Page<StandardTableItemPO> findByStandardTableIdARange(Long standatardTableId, PageSearchTO pageSearchTO,
			Long codeMin, Long codeMax) {
		
				return standardTableItemService.findByStandardTableIdARange(standatardTableId, pageSearchTO, codeMin, codeMax);
	}
	
	
}
