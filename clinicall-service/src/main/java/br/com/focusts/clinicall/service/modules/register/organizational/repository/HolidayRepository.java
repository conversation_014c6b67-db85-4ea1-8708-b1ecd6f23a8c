
package br.com.focusts.clinicall.service.modules.register.organizational.repository;

import java.time.LocalDate;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.register.organizational.po.HolidayPO;

@Repository
public interface HolidayRepository extends CrudRepository<HolidayPO,java.lang.Long>{

    public Page<HolidayPO> findByNameContaining(java.lang.String name, Pageable pageable);
    
    @Query("select h from HolidayPO h where (h.datestart=:datestart and h.company.id is null) or (h.company.id=:companyId and h.datestart=:datestart)")
    public HolidayPO find(LocalDate datestart, Long companyId);

    @Query( "SELECT h " +
            "FROM HolidayPO h " +
            "WHERE (h.dayofmonth  = :dayofmonth AND h.company.id IS NULL) " +
            "OR (h.datestart  = :datestart AND h.company.id IS NULL) " +
            "OR ( h.datestart <= :datestart AND h.dateend >= :dateend AND h.company.id IS NULL) " +
            "OR (h.dayofmonth  = :dayofmonth AND h.company.id  = :companyId)" +
            "OR (h.datestart  = :datestart AND h.company.id = :companyId)" +
            "OR ( h.datestart <= :datestart AND h.dateend >= :dateend AND  h.company.id = :companyId) ")
    public List<HolidayPO> findHolyday(String dayofmonth, LocalDate datestart, LocalDate dateend,  Long companyId);


}



