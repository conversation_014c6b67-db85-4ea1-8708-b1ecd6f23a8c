package br.com.focusts.clinicall.service.modules.operational.billing.po;

import java.time.LocalDate;
import java.util.List;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

import br.com.focusts.clinicall.service.modules.tables.accreditation.po.GlossTypePO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderItemProductPO;

@Entity
@Table(name = "gloss_order_item_product")
public class GlossOrderItemProductPO extends AbstractPO<Long> {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "gloss_order_item_product_id")
    private Long id;

    @NotNull
    @JoinColumn(name = "order_item_product_id", referencedColumnName = "order_item_product_id")
    @ManyToOne
    private OrderItemProductPO orderItemProduct;

    @NotNull
    @JoinColumn(name = "gloss_id", referencedColumnName = "gloss_id")
    @ManyToOne
    private GlossPO gloss;

//    @JoinColumn(name = "gloss_type_id", referencedColumnName = "gloss_type_id")
//    @ManyToOne
//    private GlossTypePO glossType;

    @OneToMany(mappedBy = "glossOrderItemProduct", cascade = { CascadeType.ALL, CascadeType.DETACH, CascadeType.PERSIST }, orphanRemoval = true)
    private List<GlossTypeItem> glossTypeList;

    @NotNull
    private Double value;
    
    @JsonFormat( pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate datePaid;

    private Double valuePaid;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public OrderItemProductPO getOrderItemProduct() {
        return orderItemProduct;
    }

    public void setOrderItemProduct(OrderItemProductPO orderItemProduct) {
        this.orderItemProduct = orderItemProduct;
    }

    public GlossPO getGloss() {
        return gloss;
    }

    public void setGloss(GlossPO gloss) {
        this.gloss = gloss;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public LocalDate getDatePaid() {
        return datePaid;
    }

    public void setDatePaid(LocalDate datePaid) {
        this.datePaid = datePaid;
    }

    public Double getValuePaid() {
        return valuePaid;
    }

    public void setValuePaid(Double valuePaid) {
        this.valuePaid = valuePaid;
    }

    public List<GlossTypeItem> getGlossTypeList() {
        return glossTypeList;
    }

    public void setGlossTypeList(List<GlossTypeItem> glossTypeList) {
        this.glossTypeList = glossTypeList;
    }
}
