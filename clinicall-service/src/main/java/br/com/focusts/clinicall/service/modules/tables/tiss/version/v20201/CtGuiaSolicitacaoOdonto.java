//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2023.03.30 at 09:43:38 PM BRT 
//


package br.com.focusts.clinicall.service.modules.tables.tiss.version.v20201;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for ct_guiaSolicitacaoOdonto complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_guiaSolicitacaoOdonto">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="cabecalhoGuia" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_cabecalhoGuiaOdonto"/>
 *         &lt;element name="dadosBeneficiario">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_beneficiario">
 *                 &lt;sequence>
 *                   &lt;element name="nomeEmpresa" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_descricao40" minOccurs="0"/>
 *                   &lt;element name="numeroTelefone" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_numeroTel" minOccurs="0"/>
 *                   &lt;element name="nomeTitular" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_nome" minOccurs="0"/>
 *                 &lt;/sequence>
 *               &lt;/extension>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="dadosSolicitante">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="dadosContratado">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_contratado">
 *                           &lt;sequence>
 *                             &lt;element name="conselhoProfissional" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_conselhoProfissional"/>
 *                           &lt;/sequence>
 *                         &lt;/extension>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="dadosProfissional" minOccurs="0">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_profissionalExecutante">
 *                         &lt;/extension>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="planoTratamento">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="procedimentoSolicitado" maxOccurs="unbounded">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_procedimentoOdontologia">
 *                           &lt;sequence>
 *                             &lt;element name="totalFranquiaCoparticipacao" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_valorMonetario" minOccurs="0"/>
 *                           &lt;/sequence>
 *                         &lt;/extension>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="tipoAtendimento" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_atendimentoOdonto" minOccurs="0"/>
 *         &lt;element name="dataTerminoTratamento" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_data" minOccurs="0"/>
 *         &lt;element name="observacao" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_observacao" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_guiaSolicitacaoOdonto", propOrder = {
    "cabecalhoGuia",
    "dadosBeneficiario",
    "dadosSolicitante",
    "planoTratamento",
    "tipoAtendimento",
    "dataTerminoTratamento",
    "observacao"
})
public class CtGuiaSolicitacaoOdonto {

    @XmlElement(required = true)
    protected CtCabecalhoGuiaOdonto cabecalhoGuia;
    @XmlElement(required = true)
    protected CtGuiaSolicitacaoOdonto.DadosBeneficiario dadosBeneficiario;
    @XmlElement(required = true)
    protected CtGuiaSolicitacaoOdonto.DadosSolicitante dadosSolicitante;
    @XmlElement(required = true)
    protected CtGuiaSolicitacaoOdonto.PlanoTratamento planoTratamento;
    protected String tipoAtendimento;
    protected XMLGregorianCalendar dataTerminoTratamento;
    protected String observacao;

    /**
     * Gets the value of the cabecalhoGuia property.
     * 
     * @return
     *     possible object is
     *     {@link CtCabecalhoGuiaOdonto }
     *     
     */
    public CtCabecalhoGuiaOdonto getCabecalhoGuia() {
        return cabecalhoGuia;
    }

    /**
     * Sets the value of the cabecalhoGuia property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtCabecalhoGuiaOdonto }
     *     
     */
    public void setCabecalhoGuia(CtCabecalhoGuiaOdonto value) {
        this.cabecalhoGuia = value;
    }

    /**
     * Gets the value of the dadosBeneficiario property.
     * 
     * @return
     *     possible object is
     *     {@link CtGuiaSolicitacaoOdonto.DadosBeneficiario }
     *     
     */
    public CtGuiaSolicitacaoOdonto.DadosBeneficiario getDadosBeneficiario() {
        return dadosBeneficiario;
    }

    /**
     * Sets the value of the dadosBeneficiario property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtGuiaSolicitacaoOdonto.DadosBeneficiario }
     *     
     */
    public void setDadosBeneficiario(CtGuiaSolicitacaoOdonto.DadosBeneficiario value) {
        this.dadosBeneficiario = value;
    }

    /**
     * Gets the value of the dadosSolicitante property.
     * 
     * @return
     *     possible object is
     *     {@link CtGuiaSolicitacaoOdonto.DadosSolicitante }
     *     
     */
    public CtGuiaSolicitacaoOdonto.DadosSolicitante getDadosSolicitante() {
        return dadosSolicitante;
    }

    /**
     * Sets the value of the dadosSolicitante property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtGuiaSolicitacaoOdonto.DadosSolicitante }
     *     
     */
    public void setDadosSolicitante(CtGuiaSolicitacaoOdonto.DadosSolicitante value) {
        this.dadosSolicitante = value;
    }

    /**
     * Gets the value of the planoTratamento property.
     * 
     * @return
     *     possible object is
     *     {@link CtGuiaSolicitacaoOdonto.PlanoTratamento }
     *     
     */
    public CtGuiaSolicitacaoOdonto.PlanoTratamento getPlanoTratamento() {
        return planoTratamento;
    }

    /**
     * Sets the value of the planoTratamento property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtGuiaSolicitacaoOdonto.PlanoTratamento }
     *     
     */
    public void setPlanoTratamento(CtGuiaSolicitacaoOdonto.PlanoTratamento value) {
        this.planoTratamento = value;
    }

    /**
     * Gets the value of the tipoAtendimento property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoAtendimento() {
        return tipoAtendimento;
    }

    /**
     * Sets the value of the tipoAtendimento property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoAtendimento(String value) {
        this.tipoAtendimento = value;
    }

    /**
     * Gets the value of the dataTerminoTratamento property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataTerminoTratamento() {
        return dataTerminoTratamento;
    }

    /**
     * Sets the value of the dataTerminoTratamento property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataTerminoTratamento(XMLGregorianCalendar value) {
        this.dataTerminoTratamento = value;
    }

    /**
     * Gets the value of the observacao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getObservacao() {
        return observacao;
    }

    /**
     * Sets the value of the observacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setObservacao(String value) {
        this.observacao = value;
    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_beneficiario">
     *       &lt;sequence>
     *         &lt;element name="nomeEmpresa" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_descricao40" minOccurs="0"/>
     *         &lt;element name="numeroTelefone" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_numeroTel" minOccurs="0"/>
     *         &lt;element name="nomeTitular" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_nome" minOccurs="0"/>
     *       &lt;/sequence>
     *     &lt;/extension>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "nomeEmpresa",
        "numeroTelefone",
        "nomeTitular"
    })
    public static class DadosBeneficiario
        extends CtBeneficiario
    {

        protected String nomeEmpresa;
        protected String numeroTelefone;
        protected String nomeTitular;

        /**
         * Gets the value of the nomeEmpresa property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getNomeEmpresa() {
            return nomeEmpresa;
        }

        /**
         * Sets the value of the nomeEmpresa property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setNomeEmpresa(String value) {
            this.nomeEmpresa = value;
        }

        /**
         * Gets the value of the numeroTelefone property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getNumeroTelefone() {
            return numeroTelefone;
        }

        /**
         * Sets the value of the numeroTelefone property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setNumeroTelefone(String value) {
            this.numeroTelefone = value;
        }

        /**
         * Gets the value of the nomeTitular property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getNomeTitular() {
            return nomeTitular;
        }

        /**
         * Sets the value of the nomeTitular property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setNomeTitular(String value) {
            this.nomeTitular = value;
        }

    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="dadosContratado">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_contratado">
     *                 &lt;sequence>
     *                   &lt;element name="conselhoProfissional" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_conselhoProfissional"/>
     *                 &lt;/sequence>
     *               &lt;/extension>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="dadosProfissional" minOccurs="0">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_profissionalExecutante">
     *               &lt;/extension>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "dadosContratado",
        "dadosProfissional"
    })
    public static class DadosSolicitante {

        @XmlElement(required = true)
        protected CtGuiaSolicitacaoOdonto.DadosSolicitante.DadosContratado dadosContratado;
        protected CtGuiaSolicitacaoOdonto.DadosSolicitante.DadosProfissional dadosProfissional;

        /**
         * Gets the value of the dadosContratado property.
         * 
         * @return
         *     possible object is
         *     {@link CtGuiaSolicitacaoOdonto.DadosSolicitante.DadosContratado }
         *     
         */
        public CtGuiaSolicitacaoOdonto.DadosSolicitante.DadosContratado getDadosContratado() {
            return dadosContratado;
        }

        /**
         * Sets the value of the dadosContratado property.
         * 
         * @param value
         *     allowed object is
         *     {@link CtGuiaSolicitacaoOdonto.DadosSolicitante.DadosContratado }
         *     
         */
        public void setDadosContratado(CtGuiaSolicitacaoOdonto.DadosSolicitante.DadosContratado value) {
            this.dadosContratado = value;
        }

        /**
         * Gets the value of the dadosProfissional property.
         * 
         * @return
         *     possible object is
         *     {@link CtGuiaSolicitacaoOdonto.DadosSolicitante.DadosProfissional }
         *     
         */
        public CtGuiaSolicitacaoOdonto.DadosSolicitante.DadosProfissional getDadosProfissional() {
            return dadosProfissional;
        }

        /**
         * Sets the value of the dadosProfissional property.
         * 
         * @param value
         *     allowed object is
         *     {@link CtGuiaSolicitacaoOdonto.DadosSolicitante.DadosProfissional }
         *     
         */
        public void setDadosProfissional(CtGuiaSolicitacaoOdonto.DadosSolicitante.DadosProfissional value) {
            this.dadosProfissional = value;
        }


        /**
         * <p>Java class for anonymous complex type.
         * 
         * <p>The following schema fragment specifies the expected content contained within this class.
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_contratado">
         *       &lt;sequence>
         *         &lt;element name="conselhoProfissional" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_conselhoProfissional"/>
         *       &lt;/sequence>
         *     &lt;/extension>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "conselhoProfissional"
        })
        public static class DadosContratado
            extends CtContratado
        {

            @XmlElement(required = true)
            protected CtConselhoProfissional conselhoProfissional;

            /**
             * Gets the value of the conselhoProfissional property.
             * 
             * @return
             *     possible object is
             *     {@link CtConselhoProfissional }
             *     
             */
            public CtConselhoProfissional getConselhoProfissional() {
                return conselhoProfissional;
            }

            /**
             * Sets the value of the conselhoProfissional property.
             * 
             * @param value
             *     allowed object is
             *     {@link CtConselhoProfissional }
             *     
             */
            public void setConselhoProfissional(CtConselhoProfissional value) {
                this.conselhoProfissional = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         * 
         * <p>The following schema fragment specifies the expected content contained within this class.
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_profissionalExecutante">
         *     &lt;/extension>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "")
        public static class DadosProfissional
            extends CtProfissionalExecutante
        {


        }

    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="procedimentoSolicitado" maxOccurs="unbounded">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_procedimentoOdontologia">
     *                 &lt;sequence>
     *                   &lt;element name="totalFranquiaCoparticipacao" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_valorMonetario" minOccurs="0"/>
     *                 &lt;/sequence>
     *               &lt;/extension>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "procedimentoSolicitado"
    })
    public static class PlanoTratamento {

        @XmlElement(required = true)
        protected List<CtGuiaSolicitacaoOdonto.PlanoTratamento.ProcedimentoSolicitado> procedimentoSolicitado;

        /**
         * Gets the value of the procedimentoSolicitado property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the procedimentoSolicitado property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getProcedimentoSolicitado().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link CtGuiaSolicitacaoOdonto.PlanoTratamento.ProcedimentoSolicitado }
         * 
         * 
         */
        public List<CtGuiaSolicitacaoOdonto.PlanoTratamento.ProcedimentoSolicitado> getProcedimentoSolicitado() {
            if (procedimentoSolicitado == null) {
                procedimentoSolicitado = new ArrayList<CtGuiaSolicitacaoOdonto.PlanoTratamento.ProcedimentoSolicitado>();
            }
            return this.procedimentoSolicitado;
        }


        /**
         * <p>Java class for anonymous complex type.
         * 
         * <p>The following schema fragment specifies the expected content contained within this class.
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_procedimentoOdontologia">
         *       &lt;sequence>
         *         &lt;element name="totalFranquiaCoparticipacao" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_valorMonetario" minOccurs="0"/>
         *       &lt;/sequence>
         *     &lt;/extension>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "totalFranquiaCoparticipacao"
        })
        public static class ProcedimentoSolicitado
            extends CtProcedimentoOdontologia
        {

            protected BigDecimal totalFranquiaCoparticipacao;

            /**
             * Gets the value of the totalFranquiaCoparticipacao property.
             * 
             * @return
             *     possible object is
             *     {@link BigDecimal }
             *     
             */
            public BigDecimal getTotalFranquiaCoparticipacao() {
                return totalFranquiaCoparticipacao;
            }

            /**
             * Sets the value of the totalFranquiaCoparticipacao property.
             * 
             * @param value
             *     allowed object is
             *     {@link BigDecimal }
             *     
             */
            public void setTotalFranquiaCoparticipacao(BigDecimal value) {
                this.totalFranquiaCoparticipacao = value;
            }

        }

    }

}
