package br.com.focusts.clinicall.service.modules.operational.medical.facade;

import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import br.com.focusts.clinicall.fw.exception.ApplicationException;
import br.com.focusts.clinicall.service.modules.operational.billing.service.EartefactService;
import br.com.focusts.clinicall.service.modules.operational.general.repository.DocumentModelRepository;
import br.com.focusts.clinicall.service.modules.operational.medical.po.DocumentDiagnosisPO;
import br.com.focusts.clinicall.service.modules.operational.medical.service.ExpressionService;
import br.com.focusts.clinicall.service.modules.operational.medical.to.*;
import br.com.focusts.clinicall.service.modules.system.constants.GlobalKeyParameterConstants;
import br.com.focusts.clinicall.service.modules.system.service.ParameterService;
import br.com.focusts.clinicall.util.FormatUtil;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.onlyoffice.manager.DocxManager;
import br.com.focusts.clinicall.service.modules.onlyoffice.service.DocumentServerService;
import br.com.focusts.clinicall.service.modules.onlyoffice.to.Track;
import br.com.focusts.clinicall.service.modules.operational.general.po.DocumentModelPO;
import br.com.focusts.clinicall.service.modules.operational.general.service.DocumentModelService;
import br.com.focusts.clinicall.service.modules.operational.medical.po.DocumentPO;
import br.com.focusts.clinicall.service.modules.operational.medical.service.DocumentService;
import br.com.focusts.clinicall.service.modules.reports.to.ReportResultTO;
import br.com.focusts.clinicall.util.FileUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.apachecommons.CommonsLog;

import java.io.File;
import java.io.IOException;
import java.util.*;

@Component
@Transactional
@CommonsLog
@RequiredArgsConstructor
public class DefaultDocumentFacade extends AbstractCrudFacade<DocumentPO, Long> implements DocumentFacade {

    private final DocumentService documentService;
    private final DocumentModelService documentModelService;
    private final DocumentModelRepository documentModelRepository;
    private final DocumentDiagnosisFacade documentDiagnosisFacade;
    private final DocumentServerService documentServerService;
    private final ExpressionService expressionService;
    private final DocxManager docxManager;
    private final ParameterService parameterService;
    private final EartefactService eartefactService;

    @Override
    public CrudService<DocumentPO, Long> getCrudService() {

        return documentService;
    }

    // Document Saving
    public void saveFile(Track body, Map<String, String> params) throws Exception {
        Long idDocument = Long.parseLong(params.get("idDocument"));
        DocumentPO document = documentService.findById(idDocument);

        if (document != null ) {

            byte[] documentInBytes = documentServerService.saveFile(body);

            if (isDataBaseTarget()) {
                document.setDocument(documentInBytes);
                log.info("Persistindo documento no banco com o formato docx id:" +  idDocument);
                documentService.save(document);
            }else {
                log.info("Persistindo o document no storage com o formato docx id:" +  idDocument);
                document.setDocument(null);
                document = documentService.save(document);
                documentService.saveDocumentFileStorage(document,documentInBytes);
            }
        }

    }

    // File Loading
    private File loadFullFile(DocumentPO document) throws Exception {
        byte[] body = document.getDocument();

        if (document.getHeaderId() == null && document.getFooterId() == null) {
            return FileUtil.convertByteToFileDocx(body);
        }

        byte[] header = getHeaderContent(document.getHeaderId());
        byte[] footer = getFooterContent(document.getFooterId());

        Path path = docxManager.merge(header, body, footer);
        return path.toFile();
    }

    private File loadSingleFile(DocumentPO document) throws Exception {
        return FileUtil.convertByteToFileDocx(document.getDocument());
    }

    // Resource Loading
    public Resource loadSingleFileAsResource(Long idDocument) throws IOException {
        DocumentPO document = documentService.findById(idDocument);
        return (document != null && document.getDocument() != null)
                ? new ByteArrayResource(document.getDocument())
                : getBlankFileUrlResource();
    }

    @Override
    public Resource loadFullFileAndProcessFieldsAsResource(Long idDocument, Map<String, String> params) throws Exception {
        DocumentPO document = documentService.findById(idDocument);
        return processFileAsResource(document, params);
    }

    @Override
    public Resource loadFullFileAndProcessFieldsAsResource(DocumentPO document, Map<String, String> params) throws Exception {
        File file = loadSingleFile(document);
        byte[] fileByte = docxManager.processFields(file.getPath(), params);
        return new ByteArrayResource(fileByte);
    }

    @Override
    public Resource loadSingleFileAndProcessFieldsAsResource(Long idDocument, Map<String, String> params) throws Exception {
        DocumentPO document = documentService.findById(idDocument);
        return loadSingleFileAndProcessFieldsAsResource(document, params);
    }

    @Override
    public Resource loadSingleFileAndProcessFieldsAsResource(DocumentPO document, Map<String, String> params) throws Exception {
        return (document != null && document.getDocument() != null)
                ? processFileAsResource(document, params)
                : getBlankFileUrlResource();
    }

    // Document Queries
    @Override
    public Page<DocumentPO> findByPatientIdAndType(Long patientId, Integer type, Long performerId, PageSearchTO pageSearchTO) {
        return documentService.findByPatientIdAndType(patientId, type, performerId, pageSearchTO);
    }

    @Override
    public Page<DocumentPO> findByPatientIdAndType(Long patientId, Integer type, PageSearchTO pageSearchTO) {
        return documentService.findByPatientIdAndType(patientId, type, pageSearchTO);
    }

    @Override
    public List<DocumentPO> findByPatientIdAndType(Long patientId, Integer type) {
        return documentService.findByPatientIdAndTypeList(patientId, patientId, type);
    }

    @Override
    public List<DocumentAppTO> getDocumentListByPatient(Long patientId) {
        return documentService.getDocumentListByPatient(patientId);
    }

    // Document Creation and Update
    @Override
    public DocumentPO saveNewDocumentFromDocumentModel(DocumentPO document, Map<String, String> params) throws Exception {
        DocumentModelPO model = documentModelService.findById(document.getDocumentModel().getId());
        document.setDocumentModel(model);
        document.setDocument(model.getDocument());
        setHeaderAndFooterIds(document, model);

        if (isTextOrHtmlFormat(model.getFormat())) {
            return documentService.save(document);
        }

        if (expressionService.checkDocumentModelIsExpression(model.getId())) {
            return documentService.save(document);
        }

        return processAndSaveDocument(document, params);
    }

    @Override
    public DocumentPO saveDocumentFromDocumentModel(DocumentPO document, Map<String, String> params) throws Exception {
        document = documentService.findById(document.getId());

        if (isTextOrHtmlFormat(document.getFormat())) {
            return documentService.save(document);
        }

        return processAndSaveDocument(document, params);
    }

    @Override
    public DocumentPO saveDocumentEditorBasic(DocumentTO documentTO) {
        return documentService.saveDocumentEditorBasic(documentTO);
    }

    @Override
    public RequestAndDocumentDTO repeatDocument(Long documentId, Long performerId) {
        return documentService.repeatDocument(documentId, performerId);
    }

    // Document Processing
    public void closingDocument(Long documentId) {
        documentService.closingDocument(documentId);
    }

    @Override
    public String convertAndReplaceDocumentToText(Long documentModelId, Long patientId, Long performerId) {
        return documentService.convertAndReplaceDocumentToText(documentModelId, patientId, performerId);
    }

    @Override
    public String convertReplaceDocumentToText(Long documentModelId, Long patientId) {
        return documentService.convertReplaceDocumentToText(documentModelId, patientId);
    }

    @Override
    public List<String> findOrderVariablesList(String context, String date, Long patientId, Long documentModelId) throws IOException {
        return documentService.findOrderVariablesList(context, date, patientId, documentModelId);
    }

    // PDF Generation
    @Override
    public ReportResultTO loadPDF(Map<String, String> params) throws Exception {
        Long idDocument = Long.parseLong(params.get("idDocument"));
        DocumentPO document = documentService.findById(idDocument);

        ReportResultTO result = new ReportResultTO();

        if (document.getSigned() != null) {
            result.setPdf(document.getSigned());
            return result;
        }

        String pathFile = generateDocumentPDF(params);
        File filePDF = new File(pathFile);

        result.setName(filePDF.getName());
        result.setPdf(Base64.getEncoder().encodeToString(FileUtil.convertFileToByteArray(filePDF)));
        result.setPath(pathFile);

        return result;
    }

    public String generateDocumentPDF(Map<String, String> params) throws Exception {
        DocumentPO document = documentService.findById(Long.parseLong(params.get("idDocument")));

        if(document.getDocument()==null){
            throw new RuntimeException("Arquivo não encontrado: " + document.getTitle());
        }

        String format = document.getFormat() != null ? document.getFormat() : "docx";

        byte[] doc = processDocumentWithHeaderAndFooter(document);
        File file = FileUtil.convertByteToFile(doc, format);

        String pathPdf = FileUtil.generateRandonFilename("pdf");
        FileUtil.convertToPDF(file.getPath(), pathPdf);
        FileUtil.verifyLastPageIsEmptyAndRemove(new File(pathPdf));

        return pathPdf;
    }

    public File generateDocumentPDFWithIdentifier(Map<String, String> params) throws Exception {
        DocumentPO document =documentService.findById(Long.parseLong(params.get("idDocument")));
        String format = document.getFormat() != null ? document.getFormat() : "docx";
        File file = FileUtil.convertByteToFile(document.getDocument(), format);

        String textHeader = buildHeaderText(document);
        FileUtil.addTextHeaderDocx(file.getPath(), textHeader);

        String pathPdf = FileUtil.generateRandonFilename("pdf");
        FileUtil.convertToPDF(file.getPath(), pathPdf);
        FileUtil.verifyLastPageIsEmptyAndRemove(new File(pathPdf));

        return new File(pathPdf);
    }

    @Override
    public ReportResultTO loadFilesByPatientIdAndTypeAsPDF(Long idPatient, Long idPerformer, Integer type,
                                                           Map<String, String> params) throws Exception {
        ReportResultTO result = new ReportResultTO();
        List<File> files = generatePatientDocumentFiles(idPatient, idPerformer, type, params);

        byte[] docBytesResult = files.isEmpty()
                ? getBlankFileAsBytes()
                : FileUtil.convertFileToByteArray(FileUtil.mergePDF(files));

        result.setName("filesPatient");
        result.setPdf(Base64.getEncoder().encodeToString(docBytesResult));

        return result;
    }

    // Document Signing
    @Override
    public ResponseSignatureTO signatureDocument(SignatureArtefactsTO signatureArtefactsTO) throws Exception {
        return documentService.signatureDocument(signatureArtefactsTO);
    }

    @Override
    public String signDocument(String documentBase64, Long idProfessional, Long idDocument) throws Exception {
        return documentService.signDocument(documentBase64, idProfessional, idDocument);
    }

    // Helper Methods
    private byte[] processDocumentWithHeaderAndFooter(DocumentPO document) throws Exception {
        byte[] doc = document.getDocument();
        DocumentModelPO header = getFirstMatchingHeader(document);
        if (header != null) {
            doc = FileUtil.processDocumentHeaderBody(doc, header.getDocument());
        }

        DocumentModelPO footer = getFirstMatchingFooter(document);
        if (footer != null) {
            doc = FileUtil.processDocumentFooter(doc, footer.getDocument());
        }
        return doc;
    }

    private DocumentPO processAndSaveDocument(DocumentPO document, Map<String, String> params) throws Exception {
        document = documentService.save(document);
        File file = loadFullFile(document);
        params.put("idDocument", document.getId().toString());
        byte[] fileByte = docxManager.processFields(file.getPath(), params);
        document.setDocument(fileByte);
        return documentService.save(document);
    }

    private Resource processFileAsResource(DocumentPO document, Map<String, String> params) throws Exception {
        File file = loadSingleFile(document);
        byte[] fileByte = docxManager.processFields(file.getPath(), params);
        return new ByteArrayResource(fileByte);
    }

    private List<File> generatePatientDocumentFiles(Long idPatient, Long idPerformer, Integer type,
                                                    Map<String, String> params) throws Exception {
        List<File> files = new ArrayList<>();
        List<DocumentPO> documents = getDocumentsByPatientAndType(idPatient, idPerformer, type, params);

        for (DocumentPO document : documents) {
            Map<String, String> requestParams = new HashMap<>(params);
            requestParams.put("idDocument", document.getId().toString());
            files.add(generateDocumentPDFWithIdentifier(requestParams));
        }
        return files;
    }

    private List<DocumentPO> getDocumentsByPatientAndType(Long idPatient, Long idPerformer, Integer type,
                                                          Map<String, String> params) {
        String dateStart = params.get("startDate");
        String dateEnd = params.get("startEnd");

        return (dateStart == null && dateEnd == null)
                ? documentService.findByPatientIdAndTypeList(idPatient, idPerformer, type)
                : documentService.findByHistoryDocumentAnamnese(idPatient, type, idPerformer, dateStart, dateEnd);
    }

    private String buildHeaderText(DocumentPO document) {
        return FormatUtil.getDate(document.getDateAt(), "dd/MM/yyyy") + " " +
                document.getHourAt() + " - " +
                document.getPerformer().getProfessional().getPerson().getName() + " - Paciente: " +
                document.getPatient().getPerson().getName();
    }

    private boolean isTextOrHtmlFormat(String format) {
        return "txt".equals(format) || "html".equals(format);
    }

    @Override
    public List<DocumentDiagnosisPO> saveDocumentDiagnosis(List<DocumentDiagnosisPO> cidList, Long documentId) {
        return documentService.saveDocumentDiagnosis(cidList, documentId);
    }

    @Override
    public String validationPatient(Long patientId) {
        return documentService.validationPatient(patientId);
    }

    @Override
    public String checkValuePay(Long scheduleId) {
        return documentService.checkValuePay(scheduleId);
    }

    @Override
    public void deleteDocumentDiagnosis(Long cidId, Long documentId) {
        documentService.deleteDocumentDiagnosis(cidId, documentId);
    }

    private void setHeaderAndFooterIds(DocumentPO document, DocumentModelPO model) {
        Optional.ofNullable(model.getHeader()).ifPresent(header -> document.setHeaderId(header.getId()));
        Optional.ofNullable(model.getFooter()).ifPresent(footer -> document.setFooterId(footer.getId()));
    }

    private byte[] getHeaderContent(Long headerId) {
        return (headerId != null && documentModelService.existsById(headerId))
                ? documentModelService.findById(headerId).getDocument()
                : null;
    }

    private byte[] getFooterContent(Long footerId) {
        return (footerId != null && documentModelService.existsById(footerId))
                ? documentModelService.findById(footerId).getDocument()
                : null;
    }

    private DocumentModelPO getFirstMatchingHeader(DocumentPO document) {
        List<DocumentModelPO> headers = documentModelRepository.findDocumentModelHeader(
                getId(document.getPerformer()),
                getId(document.getSpeciality()),
                getId(document.getInsurance()),
                getId(document.getCompany()),
                document.getHeaderId()
        );
        return headers.isEmpty() ? null : documentModelService.findById(headers.get(0).getId());
    }

    private DocumentModelPO getFirstMatchingFooter(DocumentPO document) {
        List<DocumentModelPO> footers = documentModelRepository.findDocumentModelFooter(
                getId(document.getPerformer()),
                getId(document.getSpeciality()),
                getId(document.getInsurance()),
                getId(document.getCompany()),
                document.getFooterId()
        );
        return footers.isEmpty() ? null : documentModelService.findById(footers.get(0).getId());
    }

    private Long getId(AbstractPO entity) {
        return   entity != null ? (Long) entity.getId() : null;
    }

    private byte[] getBlankFileAsBytes() {
        String blankFileBase64 = eartefactService.findById(
                Long.parseLong(parameterService.findByName(GlobalKeyParameterConstants.BLANK_FILE).getValue())
        ).getContent();
        return Base64.getDecoder().decode(blankFileBase64);
    }

    private Resource getBlankFileUrlResource() throws IOException {
        return new ByteArrayResource(getBlankFileAsBytes());
    }

    private boolean isDataBaseTarget() {
        var target = parameterService.findByName(GlobalKeyParameterConstants.DOCUMENT_STORAGE_TARGET);
        return target == null || target.getValue().equals(GlobalKeyParameterConstants.TARGET_DATABASE);
    }
}