package br.com.focusts.clinicall.service.modules.operational.medical.repository;

import br.com.focusts.clinicall.fw.response.Response;
import br.com.focusts.clinicall.service.modules.operational.medical.po.AttachmentPO;
import br.com.focusts.clinicall.service.modules.operational.medical.po.RequestPO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Repository
public interface AttachmentRepository extends CrudRepository<AttachmentPO, Long>{

    public Page<AttachmentPO> findByDatedAndPatient_Id(LocalDate date, java.lang.Long id, Pageable pageable);

    public Page<AttachmentPO> findByPatient_Id(java.lang.Long id, Pageable pageable);
}
