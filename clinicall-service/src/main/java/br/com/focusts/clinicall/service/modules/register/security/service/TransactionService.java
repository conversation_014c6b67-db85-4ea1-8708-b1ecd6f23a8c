package br.com.focusts.clinicall.service.modules.register.security.service;

import br.com.focusts.clinicall.service.modules.system.po.TransactionFilterPO;
import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.security.po.TransactionPO;

import java.util.List;


public interface TransactionService extends CrudService<TransactionPO, Long>{

    public Page<TransactionPO> findByNameContainingOrCodeContainingOrViewNameContaining(PageSearchTO pageSearchTO);
    public List<TransactionFilterPO> findByTransaction_id(Long id);

}
