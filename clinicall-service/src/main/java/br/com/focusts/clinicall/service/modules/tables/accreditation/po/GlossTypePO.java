package br.com.focusts.clinicall.service.modules.tables.accreditation.po;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import br.com.focusts.clinicall.fw.po.AbstractPO;

@Entity
@Table(name = "gloss_type")
public class GlossTypePO extends AbstractPO<Long>{
    private static final long serialVersionUID = 1L;
    
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "gloss_type_id")
    private Long id;

    @NotNull
    @Size(max =4)
    private String code;

    @NotNull
    @Size(max =255)
    private String name;

    @Override
    public Long getId() {
        return id;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
    
}
