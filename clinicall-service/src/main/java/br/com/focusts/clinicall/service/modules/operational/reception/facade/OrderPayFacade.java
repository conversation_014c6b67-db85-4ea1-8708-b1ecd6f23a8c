
package br.com.focusts.clinicall.service.modules.operational.reception.facade;

import java.util.List;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPayPO;
import br.com.focusts.clinicall.service.modules.operational.reception.to.ApplyDiscountTO;
import br.com.focusts.clinicall.service.modules.operational.reception.to.OrderPayTO;

public interface OrderPayFacade extends CrudFacade<OrderPayPO,java.lang.Long>{

	public List<OrderPayPO> findByOrderId(Long orderId);

	public void generateOrderPay(OrderPayTO orderPayTO);

	public Double getMissingValueOrderPayInOrder(Long orderId);

	public void applyDiscount(ApplyDiscountTO applyDiscountTO);

}

