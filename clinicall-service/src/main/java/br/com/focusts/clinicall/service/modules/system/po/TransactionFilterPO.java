package br.com.focusts.clinicall.service.modules.system.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.FilterPO;
import br.com.focusts.clinicall.service.modules.register.security.po.TransactionPO;

@Entity
@Table(name = "transaction_filter")
public class TransactionFilterPO {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "transaction_filter_id")
    private Long id;

    @ManyToOne
    @JoinColumn(name = "transaction_id", referencedColumnName = "transaction_id")
    private TransactionPO transaction;

    @ManyToOne
    @JoinColumn(name = "filter_id", referencedColumnName = "filter_id")
    private FilterPO filter;

    private String order;

    @Column(name = "param_name")
    private String paramName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public TransactionPO getTransaction() {
        return transaction;
    }

    public void setTransaction(TransactionPO transaction) {
        this.transaction = transaction;
    }

    public FilterPO getFilter() {
        return filter;
    }

    public void setFilter(FilterPO filter) {
        this.filter = filter;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

}
