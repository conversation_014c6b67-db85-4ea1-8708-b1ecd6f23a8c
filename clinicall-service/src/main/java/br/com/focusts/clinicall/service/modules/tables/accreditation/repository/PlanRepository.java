
package br.com.focusts.clinicall.service.modules.tables.accreditation.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.register.accreditation.po.InsurancePO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.PlanPO;

@Repository
public interface PlanRepository extends CrudRepository<PlanPO,java.lang.Long>{

    public Page<PlanPO> findByNameContaining(java.lang.String name, Pageable pageable);
    @Query(value = """
            SELECT new PlanPO(plan.id, plan.name)
            FROM PlanPO plan
            JOIN InsurancePlanPO insurance_plan ON insurance_plan.plan.id = plan.id
            WHERE insurance_plan.insurance.id = :insurance
            AND plan.name like :name%
            """)
    public Page<PlanPO> findByNameContainingAndInsuranceListContaining(String name, Long insurance, Pageable pageable);

}



