package br.com.focusts.clinicall.service.modules.financial.bond.service;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.financial.bond.po.BondPayPO;
import br.com.focusts.clinicall.service.modules.financial.bond.to.BondPayListTO;
import br.com.focusts.clinicall.service.modules.financial.bond.to.BondPayTO;

public interface BondPayService extends CrudService<BondPayPO, Long> {


    public void insertBondPay(BondPayTO bondPayTO);

    public Page<BondPayListTO> findBondPayByBondId(Long bondId, PageSearchTO pageSearchTO);

    public BondPayTO findBondPayById(Long bondPayId);

    public Double findByTotalPay(Long bondId);

}
