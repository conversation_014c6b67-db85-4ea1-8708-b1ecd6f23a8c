package br.com.focusts.clinicall.service.modules.partners.to;

import br.com.focusts.clinicall.service.modules.register.accreditation.po.SpecialityPO;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateDeserializer;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateSerializer;
import br.com.focusts.clinicall.service.modules.tables.general.po.CouncilPO;
import br.com.focusts.clinicall.service.modules.tables.general.po.StatePO;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
public class PartherPerformerTO {

    private Long performerId;
    private Long professionalId;
    private String name;
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate birthday;
    private String councilNumber;
    private String specialityName;
    private Boolean sendWhatsapp;
    private Boolean orderArrival;
    private Boolean active;
    private String councilType;
    private CouncilPO council;
    private StatePO state;
    private SpecialityPO speciality;
}
