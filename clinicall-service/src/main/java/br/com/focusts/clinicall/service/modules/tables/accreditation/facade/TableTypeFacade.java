
package br.com.focusts.clinicall.service.modules.tables.accreditation.facade;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.TableTypePO;

public interface TableTypeFacade extends CrudFacade<TableTypePO,java.lang.Long>{

    public Page<TableTypePO> findByCodeContainingOrNameContaining(PageSearchTO pageSearchTO);

}

