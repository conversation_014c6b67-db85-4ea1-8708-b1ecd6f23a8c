
package br.com.focusts.clinicall.service.modules.tables.accreditation.service;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.TableTypePO;

public interface TableTypeService extends CrudService<TableTypePO,java.lang.Long>{

    public Page<TableTypePO> findByCodeContainingOrNameContaining(PageSearchTO pageSearchTO);

}
