
package br.com.focusts.clinicall.service.modules.operational.reception.facade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPayDetailPO;
import br.com.focusts.clinicall.service.modules.operational.reception.service.OrderPayDetailService;

@Component
@Transactional
public class DefaultOrderPayDetailFacade extends AbstractCrudFacade<OrderPayDetailPO, java.lang.Long>
		implements OrderPayDetailFacade {

	@Autowired
	private OrderPayDetailService orderPayDetailService;

	@Override
	public CrudService<OrderPayDetailPO, java.lang.Long> getCrudService() {
		return orderPayDetailService;
	}

}
