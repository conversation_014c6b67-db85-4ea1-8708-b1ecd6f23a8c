package br.com.focusts.clinicall.service.modules.tables.general.po;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import br.com.focusts.clinicall.service.modules.register.operational.po.PatientPO;
import lombok.Getter;
import org.hibernate.envers.Audited;
import org.hibernate.validator.constraints.br.CPF;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.ProfessionalPO;
import br.com.focusts.clinicall.service.modules.register.security.po.UserPO;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateDeserializer;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateSerializer;
import br.com.focusts.clinicall.util.DateUtil;
import br.com.focusts.clinicall.util.validator.NIS;

@Entity
@Audited
@Table(name = "person")
public class PersonPO extends AbstractPO<Long> {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "person_id")
	private Long id;

	@Size(max = 250)
	@Column(name = "name")
	@NotBlank
	private String name;

	@JsonSerialize(using = LocalDateSerializer.class)
	@JsonDeserialize(using = LocalDateDeserializer.class)
	private LocalDate birthday;

	@Transient
	private String age;

	@Getter
	@Transient
	private String photoMain;

	@Transient
	private String phoneStandart;

	@Transient
	private String emailStandart;

	@Transient
	private Boolean isBirthday;

	@JoinColumn(name = "address_id")
	@ManyToOne(cascade = CascadeType.ALL)
	private AddressPO address;

	@JoinColumn(name = "gender_id")
	@ManyToOne(cascade = CascadeType.MERGE)
	private GenderPO gender;

	@JoinColumn(name = "breed_id")
	@ManyToOne(cascade = CascadeType.MERGE)
	private BreedPO breed;

	@JoinColumn(name = "ethnicity_id")
	@ManyToOne(cascade = CascadeType.MERGE)
	private EthnicityPO ethnicity;

	@JoinColumn(name = "city_id")
	@ManyToOne(cascade = CascadeType.MERGE)
	private CityPO city;

	@JoinColumn(name = "country_id")
	@ManyToOne(cascade = CascadeType.MERGE)
	private CountryPO country;

	@JoinColumn(name = "civil_status_id")
	@ManyToOne(cascade = CascadeType.MERGE)
	private CivilStatusPO civilStatus;

	@JoinColumn(name = "schooling_id")
	@ManyToOne(cascade = CascadeType.MERGE)
	private SchoolingPO schooling;

	@JoinColumn(name = "occupation_id")
	@ManyToOne(cascade = CascadeType.MERGE)
	private OccupationPO occupation;

	@JoinColumn(name = "contact_list_id")
	@ManyToOne(cascade = { CascadeType.ALL, CascadeType.DETACH, CascadeType.MERGE })
	private ContactListPO contactList;

	@Size(max = 250)
	private String alias;

	@Size(max = 250)
	@Column(name = "social_name")
	private String socialName;

	@Size(max = 11)
	@CPF(message = "invalid")
	private String cpf;

	@Size(max = 25)
	private String cns;

	@Size(max = 20)
	@NIS
	private String nis;

	@Column(name = "attend_school")
	private Boolean attendSchool;

	@Size(max = 250)
	private String mother;

	@Size(max = 250)
	private String dad;

	@Size(max = 50)
	private String treatment;

	@Column(name = "PCD")
	private Boolean pcd;

	@OneToOne(cascade = CascadeType.ALL, mappedBy = "person")
	private PersonCertificatePO personCertificate;

	@OneToOne(cascade = CascadeType.ALL, mappedBy = "person")
	private PersonIdentityPO personIdentity;

	@OneToOne(cascade = CascadeType.ALL, mappedBy = "person")
	private PersonElectorPO personElector;

	@OneToMany(cascade = CascadeType.ALL, mappedBy = "person")
	private List<PersonImagePO> personImageList;

	@OneToOne(cascade = CascadeType.ALL, mappedBy = "person")
	private PersonWorkCardPO personWorkCard;

	@OneToOne(cascade = CascadeType.ALL, mappedBy = "person", orphanRemoval = true)
	private ForeignPO foreign;

	@OneToOne(mappedBy = "person")
	private UserPO user;

	@OneToOne(cascade = CascadeType.ALL, mappedBy = "person",fetch= FetchType.LAZY)
	private ProfessionalPO professional;

	@OneToOne(cascade = CascadeType.ALL, mappedBy = "person", fetch= FetchType.LAZY)
	private PatientPO patient;

	public Long getId() {
		return id;
	}

	public PersonPO(Long id) {
		this.id = id;
	}

	public PersonPO() {
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name.trim();
	}

	public LocalDate getBirthday() {
		return birthday;
	}

	public void setBirthday(LocalDate birthday) {
		this.birthday = birthday;
	}

	public AddressPO getAddress() {
		return address;
	}

	public void setAddress(AddressPO address) {
		this.address = address;
	}

	public GenderPO getGender() {
		return gender;
	}

	public void setGender(GenderPO gender) {
		this.gender = gender;
	}

	public BreedPO getBreed() {
		return breed;
	}

	public void setBreed(BreedPO breed) {
		this.breed = breed;
	}

	public SchoolingPO getSchooling() {
		return schooling;
	}

	public void setSchooling(SchoolingPO schooling) {
		this.schooling = schooling;
	}

	public EthnicityPO getEthnicity() {
		return ethnicity;
	}

	public void setEthnicity(EthnicityPO ethnicity) {
		this.ethnicity = ethnicity;
	}

	public CityPO getCity() {
		return city;
	}

	public void setCity(CityPO city) {
		this.city = city;
	}

	public CountryPO getCountry() {
		return country;
	}

	public void setCountry(CountryPO country) {
		this.country = country;
	}

	public CivilStatusPO getCivilStatus() {
		return civilStatus;
	}

	public void setCivilStatus(CivilStatusPO civilStatus) {
		this.civilStatus = civilStatus;
	}

	public OccupationPO getOccupation() {
		return occupation;
	}

	public void setOccupation(OccupationPO occupation) {
		this.occupation = occupation;
	}

	public ContactListPO getContactList() {
		return contactList;
	}

	public void setContactList(ContactListPO contactList) {
		this.contactList = contactList;
	}

	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	public String getSocialName() {
		return socialName;
	}

	public void setSocialName(String socialName) {
		this.socialName = socialName;
	}

	public String getCpf() {
		return cpf;
	}

	public void setCpf(String cpf) {
		this.cpf = cpf;
	}

	public String getCns() {
		return cns;
	}

	public void setCns(String cns) {
		this.cns = cns;
	}

	public String getNis() {
		return nis;
	}

	public void setNis(String nis) {
		this.nis = nis;
	}

	public Boolean getAttendSchool() {
		return attendSchool;
	}

	public void setAttendSchool(Boolean attendSchool) {
		this.attendSchool = attendSchool;
	}

	public String getMother() {
		return mother;
	}

	public void setMother(String mother) {
		this.mother = mother;
	}

	public String getDad() {
		return dad;
	}

	public void setDad(String dad) {
		this.dad = dad;
	}

	public String getTreatment() {
		return treatment;
	}

	public void setTreatment(String treatment) {
		this.treatment = treatment;
	}

	public PersonCertificatePO getPersonCertificate() {
		return personCertificate;
	}

	public void setPersonCertificate(PersonCertificatePO personCertificate) {
		if (personCertificate != null) {
			personCertificate.setPerson(this);
		}
		this.personCertificate = personCertificate;
	}

	public PersonIdentityPO getPersonIdentity() {
		return personIdentity;
	}

	public void setPersonIdentity(PersonIdentityPO personIdentity) {
		if (personIdentity != null) {
			personIdentity.setPerson(this);
		}
		this.personIdentity = personIdentity;
	}

	public PersonWorkCardPO getPersonWorkCard() {
		return personWorkCard;
	}

	public void setPersonWorkCard(PersonWorkCardPO personWorkCard) {
		if (personWorkCard != null) {
			personWorkCard.setPerson(this);
		}
		this.personWorkCard = personWorkCard;
	}

	public PersonElectorPO getPersonElector() {
		return personElector;
	}

	public void setPersonElector(PersonElectorPO personElector) {
		if (personElector != null) {
			personElector.setPerson(this);
		}
		this.personElector = personElector;
	}

	public List<PersonImagePO> getPersonImageList() {
		return personImageList;
	}

	public void setPersonImageList(List<PersonImagePO> personImageList) {
		this.personImageList = personImageList;
	}

	public ForeignPO getForeign() {
		return foreign;
	}

	public void setForeign(ForeignPO foreign) {
		if (foreign != null) {
			foreign.setPerson(this);
		}
		this.foreign = foreign;
	}

	public UserPO getUser() {
		return user;
	}

	public void setUser(UserPO user) {
		this.user = user;
	}

	public ProfessionalPO getProfessional() {
		return professional;
	}

	public void setProfessional(ProfessionalPO professional) {
		this.professional = professional;
	}

	@JsonIgnore
	public String getPhoto() {
		if (personImageList == null) {
			return null;
		}
		String photo = null;
		for (PersonImagePO personImagePO : personImageList) {
			if (personImagePO.getMain()) {
				photo = personImagePO.getImage().getContent();
				break;
			}
		}
		return photo;
	}

	public Boolean getPcd() {
		return pcd;
	}

	public void setPcd(Boolean pcd) {
		this.pcd = pcd;
	}

	public String getPhoneStandart() {
		String phone = "";
		Optional<ContactListPO> phoneFilter = Optional.empty();
		if (getContactList() != null) {
			if (getContactList().getContactItem() != null && !getContactList().getContactItem().isEmpty()){
				phoneFilter = getContactList().getContactItem().stream().filter(c->c.getType().equals("S")).findFirst();
				if (phoneFilter.isPresent()){
					phone = phoneFilter.get().getPhone();
				}else{
					phoneFilter = getContactList().getContactItem().stream().filter(c->c.getType().equals("P")).findFirst();
					if (phoneFilter.isPresent()){
						phone = phoneFilter.get().getPhone();
					}
				}
			}
		}
		return phone;
	}

	public String getEmailStandart() {
		String email = "";
		if (getContactList() != null) {
			if (getContactList().getContactItem() != null && !getContactList().getContactItem().isEmpty()){
				var emailFilter =	getContactList().getContactItem().stream().filter(c->c.getType().equals("M")).findFirst();
				if (emailFilter.isPresent()){
					email = emailFilter.get().getEmail();
				}
			}
		}
		return email;
	}
	public String getAge() {

		if (getBirthday() != null) {

			return DateUtil.getAge(getBirthday());
		} else {
			return null;
		}

	}

	public Boolean getIsBirthday() {

		if (getBirthday() != null) {

			return DateUtil.isBirthDay(getBirthday());
		} else {
			return null;
		}

	}

	public void setPhotoMain(String photoMain) {
		this.photoMain = photoMain;
	}

	public void setPhoneStandart(String phoneStandart) {
		this.phoneStandart = phoneStandart;
	}

	public void setEmailStandart(String emailStandart) {
		this.emailStandart = emailStandart;
	}

	public PatientPO getPatient() {
		return patient;
	}

	public void setPatient(PatientPO patient) {
		this.patient = patient;
	}
}
