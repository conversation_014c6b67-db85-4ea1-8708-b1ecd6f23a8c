
package br.com.focusts.clinicall.service.modules.register.accreditation.facade;

import br.com.focusts.clinicall.service.modules.operational.reception.to.QuotaLackTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QuotaLackControlPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.service.QuotaLackControlService;

@Component
@Transactional
public class DefaultQuotaLackControlFacade extends AbstractCrudFacade <QuotaLackControlPO, java.lang.Long> implements QuotaLackControlFacade  {

	@Autowired
	private QuotaLackControlService quotaLackControlService;

	@Override
	public CrudService<QuotaLackControlPO,java.lang.Long> getCrudService() {
            return quotaLackControlService;
	}

	@Override
	public Page<QuotaLackControlPO> findByName(PageSearchTO pageSearchTO) {
            return quotaLackControlService.findByName(pageSearchTO);
	}

}
