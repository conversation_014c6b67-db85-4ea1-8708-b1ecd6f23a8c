
package br.com.focusts.clinicall.service.modules.operational.reception.facade;

import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderItemPO;
import br.com.focusts.clinicall.service.modules.operational.reception.service.OrderItemService;
import br.com.focusts.clinicall.service.modules.register.organizational.po.ThirdPartyPO;
import br.com.focusts.clinicall.service.modules.register.organizational.to.OrderItemThirdPartyTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderItemThirdPartyPO;
import br.com.focusts.clinicall.service.modules.operational.reception.service.OrderItemThirdPartyService;

@Component
@Transactional
public class DefaultOrderItemThirdPartyFacade extends AbstractCrudFacade <OrderItemThirdPartyPO, java.lang.Long> implements OrderItemThirdPartyFacade  {

	@Autowired
	private OrderItemThirdPartyService orderItemThirdPartyService;

	@Autowired
	private OrderItemService orderItemService;

	@Override
	public CrudService<OrderItemThirdPartyPO,java.lang.Long> getCrudService() {
            return orderItemThirdPartyService;
	}

	@Override
	public Page<OrderItemThirdPartyTO> find(PageSearchTO pageSearchTO, Long orderItemId) {
            return orderItemThirdPartyService.find(pageSearchTO, orderItemId);
	}

	@Override
	public OrderItemThirdPartyPO save(OrderItemThirdPartyPO orderItemThirdPartyPO) {
		Long orderItemId = orderItemThirdPartyPO.getOrderItem().getId();
		OrderItemPO orderItemPO = orderItemService.findById(orderItemId);
		orderItemThirdPartyPO.setOrderItem(orderItemPO);
		return orderItemThirdPartyService.save(orderItemThirdPartyPO);
	}
      
}
