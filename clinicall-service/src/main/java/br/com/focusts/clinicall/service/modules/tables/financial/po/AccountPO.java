package br.com.focusts.clinicall.service.modules.tables.financial.po;

import java.time.LocalDate;

import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.CompanyCostCenterPO;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateDeserializer;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateSerializer;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

@Entity
@Audited
@Table(name = "account")
public class AccountPO extends AbstractPO<Long> {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "account_id")
	private Long id;

	@JoinColumn(name = "agency_id", referencedColumnName = "agency_id")
	@ManyToOne(cascade = CascadeType.PERSIST)
	private AgencyPO agency;

	@JoinColumn(name = "company_cost_center_id", referencedColumnName = "company_cost_center_id")
	@ManyToOne(cascade = CascadeType.PERSIST)
	@NotAudited
	private CompanyCostCenterPO companyCostCenter;

	@JoinColumn(name = "company_account_plan_id", referencedColumnName = "company_account_plan_id")
	@ManyToOne(cascade = CascadeType.PERSIST)
	@NotAudited
	private CompanyAccountPlanPO companyAccountPlan;

	@NotBlank
	@Size(max = 20)
	private String code;

	@NotBlank
	@Size(max = 1)
	private String type;

	@NotNull
	private Boolean active;

	@NotNull
	private Boolean spin;

	@NotNull
	private Boolean appliance;

	@NotNull
	private Double limit;

	@NotNull
	@JsonSerialize(using = LocalDateSerializer.class)
	@JsonDeserialize(using = LocalDateDeserializer.class)
	private LocalDate since;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public AgencyPO getAgency() {
		return agency;
	}

	public void setAgency(AgencyPO agency) {
		this.agency = agency;
	}

	public CompanyCostCenterPO getCompanyCostCenter() {
		return companyCostCenter;
	}

	public void setCompanyCostCenter(CompanyCostCenterPO companyCostCenter) {
		this.companyCostCenter = companyCostCenter;
	}

	public CompanyAccountPlanPO getCompanyAccountPlan() {
		return companyAccountPlan;
	}

	public void setCompanyAccountPlan(CompanyAccountPlanPO companyAccountPlan) {
		this.companyAccountPlan = companyAccountPlan;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	public Boolean getSpin() {
		return spin;
	}

	public void setSpin(Boolean spin) {
		this.spin = spin;
	}

	public Boolean getAppliance() {
		return appliance;
	}

	public void setAppliance(Boolean appliance) {
		this.appliance = appliance;
	}

	public Double getLimit() {
		return limit;
	}

	public void setLimit(Double limit) {
		this.limit = limit;
	}

	public LocalDate getSince() {
		return since;
	}

	public void setSince(LocalDate since) {
		this.since = since;
	}

}
