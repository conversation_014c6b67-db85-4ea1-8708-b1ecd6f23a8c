
package br.com.focusts.clinicall.service.modules.tables.tiss.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.TypeGuidePO;
import br.com.focusts.clinicall.service.modules.tables.tiss.repository.TypeGuideRepository;


@Service
public class DefaultTypeGuideService extends AbstractCrudService<TypeGuidePO,java.lang.Long> implements TypeGuideService {

	@Autowired
	private TypeGuideRepository typeGuideRepository;

	@Override
	public CrudRepository<TypeGuidePO,java.lang.Long> getCrudRepository() {
	    return typeGuideRepository;
	}

	@Override
	public Page<TypeGuidePO> findByCodeContainingOrNameContaining(PageSearchTO pageSearchTO) {

            PageRequest pageRequest =  PageRequest.of(pageSearchTO.getPage(),pageSearchTO.getSizePage(), Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());

	    return typeGuideRepository.findByCodeContainingOrNameContaining(pageSearchTO.getArgument(), pageSearchTO.getArgument(),  pageRequest);
	}
	
}
