package br.com.focusts.clinicall.service.modules.operational.medical.service;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.medical.po.RequestModelPO;
import br.com.focusts.clinicall.service.modules.operational.medical.po.RequestPO;
import br.com.focusts.clinicall.service.modules.operational.medical.po.RequestRequestModelPO;
import br.com.focusts.clinicall.service.modules.operational.medical.to.RequestAndDocumentDTO;
import br.com.focusts.clinicall.service.modules.operational.medical.to.RequestAndDocumentTO;
import br.com.focusts.clinicall.service.modules.operational.medical.to.RequestDataTO;
import org.springframework.data.domain.Page;

import java.util.List;

public interface RequestRequestModelService extends CrudService<RequestRequestModelPO, Long> {

    List<Long> findByRequestId(Long requestId);

}
