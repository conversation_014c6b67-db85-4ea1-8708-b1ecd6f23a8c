
package br.com.focusts.clinicall.service.modules.register.organizational.facade;

import java.util.List;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.to.PerformerShiftTO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.to.ScheduleFilterTO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.PerformerPO;
import br.com.focusts.clinicall.service.modules.register.organizational.to.PerformerHorarySearchTO;
import br.com.focusts.clinicall.service.modules.register.organizational.to.PerformerHoraryTO;
import br.com.focusts.clinicall.service.modules.register.organizational.to.PerformerSearchTO;
import br.com.focusts.clinicall.service.modules.register.organizational.to.PerformerTO;

public interface PerformerFacade extends CrudFacade<PerformerPO,Long>{

	public Page<PerformerPO> search(PageSearchTO pageSearchTO);

	public Page<PerformerPO> searchAll(PageSearchTO pageSearchTO);

	public Page<PerformerTO> findByName(PageSearchTO pageSearchTO);
	
	public List<PerformerHoraryTO> findPerformerHorarys(PerformerHorarySearchTO performerHorarySearchTO);
	
	public List<PerformerShiftTO> findPerformerShift(Long companyId, Long performerId);
	
	public Page<PerformerPO> findByFilter(ScheduleFilterTO scheduleFilterTO);
	
	public Page<PerformerPO> findByFilter(PerformerSearchTO performerSearchTO);

	public Page<PerformerTO> searchPatientByParameterPatientActived(PageSearchTO pageSearchTO, Long patientId);
	public Page<PerformerTO> findByPerformerWithScheduleBySpecialityId(PerformerSearchTO performerSearchTO);

}

