package br.com.focusts.clinicall.service.modules.tables.tiss.repository;


import br.com.focusts.clinicall.service.modules.tables.tiss.po.SpecialCoveragePO;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface SpecialCoverageRepository extends CrudRepository<SpecialCoveragePO, Long> {
    public SpecialCoveragePO findByCode(String code);
}
