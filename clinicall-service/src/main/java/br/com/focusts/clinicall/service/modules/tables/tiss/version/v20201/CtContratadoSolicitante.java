//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2023.03.30 at 09:43:38 PM BRT 
//


package br.com.focusts.clinicall.service.modules.tables.tiss.version.v20201;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ct_contratadoSolicitante complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_contratadoSolicitante">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="identificacao" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_identificacaoPrestador"/>
 *         &lt;element name="nomeContratado" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_nome"/>
 *         &lt;element name="enderecoContratado" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_endereco" minOccurs="0"/>
 *         &lt;element name="numeroCNES" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_CNES" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_contratadoSolicitante", propOrder = {
    "identificacao",
    "nomeContratado",
    "enderecoContratado",
    "numeroCNES"
})
public class CtContratadoSolicitante {

    @XmlElement(required = true)
    protected CtIdentificacaoPrestador identificacao;
    @XmlElement(required = true)
    protected String nomeContratado;
    protected CtEndereco enderecoContratado;
    protected String numeroCNES;

    /**
     * Gets the value of the identificacao property.
     * 
     * @return
     *     possible object is
     *     {@link CtIdentificacaoPrestador }
     *     
     */
    public CtIdentificacaoPrestador getIdentificacao() {
        return identificacao;
    }

    /**
     * Sets the value of the identificacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtIdentificacaoPrestador }
     *     
     */
    public void setIdentificacao(CtIdentificacaoPrestador value) {
        this.identificacao = value;
    }

    /**
     * Gets the value of the nomeContratado property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeContratado() {
        return nomeContratado;
    }

    /**
     * Sets the value of the nomeContratado property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeContratado(String value) {
        this.nomeContratado = value;
    }

    /**
     * Gets the value of the enderecoContratado property.
     * 
     * @return
     *     possible object is
     *     {@link CtEndereco }
     *     
     */
    public CtEndereco getEnderecoContratado() {
        return enderecoContratado;
    }

    /**
     * Sets the value of the enderecoContratado property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtEndereco }
     *     
     */
    public void setEnderecoContratado(CtEndereco value) {
        this.enderecoContratado = value;
    }

    /**
     * Gets the value of the numeroCNES property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroCNES() {
        return numeroCNES;
    }

    /**
     * Sets the value of the numeroCNES property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroCNES(String value) {
        this.numeroCNES = value;
    }

}
