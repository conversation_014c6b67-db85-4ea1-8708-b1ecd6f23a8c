
package br.com.focusts.clinicall.service.modules.tables.general.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.tables.general.po.FirmPO;
import br.com.focusts.clinicall.service.modules.tables.general.to.FirmTO;

@Repository
public interface FirmRepository extends CrudRepository<FirmPO,java.lang.Long>{

    public Page<FirmPO> findByNameContainingOrFullNameContainingOrRegistryContainingOrCnesContainingOrRegistryType_nameContaining(java.lang.String name,java.lang.String fullName,java.lang.String registry,java.lang.String cnes,String registryTypeName , Pageable pageable);

    List<FirmPO> findByRegistry(String registry);

    @Query("""
            SELECT
            new br.com.focusts.clinicall.service.modules.tables.general.to.FirmTO(
            firm.id,
            firm.name,
            firm.fullName,
            firm.registry,
            firm.cnes
              )
            FROM
            FirmPO firm
            WHERE 
            firm.name like :name%
            """)
    public Page<FirmTO> findByName(String name, Pageable pageable);

}



