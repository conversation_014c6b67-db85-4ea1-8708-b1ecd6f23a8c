
package br.com.focusts.clinicall.service.modules.register.accreditation.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.register.accreditation.po.AuxiliaryPO;

@Repository
public interface AuxiliaryRepository extends CrudRepository<AuxiliaryPO, java.lang.Long> {

    public Page<AuxiliaryPO> findByNameContainingOrCodeContainingOrDegreeParticipation_nameContaining(
            java.lang.String name, java.lang.String code, String degreeParticipationName, Pageable pageable);

    @Query("select ax from AuxiliaryPO ax where (ax.name like :name or ax.code like :code) and not exists ( select a.auxiliary.id from AccreditationPO a where a.agreement.id=:agreementId and ax.id=a.auxiliary.id)")
    public Page<AuxiliaryPO> findAuxiliaryWithoutAccreditation(Long agreementId, String name, String code,
            Pageable pageable);

    public AuxiliaryPO findByName(String name);
}
