
package br.com.focusts.clinicall.service.modules.register.organizational.service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyStore;
import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import br.com.focusts.clinicall.authoriertiss.tiss.util.TissUtil;
import br.com.focusts.clinicall.service.modules.operational.billing.po.EartefactPO;
import br.com.focusts.clinicall.service.modules.operational.billing.repository.EartefactRepository;
import br.com.focusts.clinicall.service.modules.partners.to.PartherPerformerTO;
import br.com.focusts.clinicall.service.modules.register.organizational.repository.PerformerQueryDslRepository;
import br.com.focusts.clinicall.service.modules.register.organizational.repository.ProfessionalQueryDslRepository;
import br.com.focusts.clinicall.service.modules.register.organizational.to.PerformerTO;
import br.com.focusts.clinicall.service.modules.register.security.facade.UserFacade;
import br.com.focusts.clinicall.service.modules.register.security.po.UserPO;
import br.com.focusts.clinicall.service.modules.register.security.service.UserService;
import br.com.focusts.clinicall.service.modules.system.constants.GlobalKeyParameterConstants;
import br.com.focusts.clinicall.service.modules.system.po.ParameterValuePO;
import br.com.focusts.clinicall.service.modules.system.repository.ParameterRepository;
import br.com.focusts.clinicall.service.modules.system.repository.ParameterValueRepository;
import br.com.focusts.clinicall.service.modules.tables.general.facade.FirmFacade;
import br.com.focusts.clinicall.service.modules.tables.general.po.FirmPO;
import br.com.focusts.clinicall.service.modules.tables.general.repository.OccupationRepository;
import br.com.focusts.clinicall.util.AESUtil;
import br.com.focusts.clinicall.util.StringUtil;
import br.com.focusts.clinicall.util.ValidateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.querydsl.core.types.dsl.BooleanExpression;

import br.com.focusts.clinicall.fw.exception.ApplicationException;
import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.InsurancePO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.ProcedurePO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.SpecialityPO;
import br.com.focusts.clinicall.service.modules.register.organizational.constants.RegisterOrganizationalKeyMessagesConstants;
import br.com.focusts.clinicall.service.modules.register.organizational.po.HoraryPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.ProfessionalPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.QProfessionalPO;
import br.com.focusts.clinicall.service.modules.register.organizational.repository.HoraryRepository;
import br.com.focusts.clinicall.service.modules.register.organizational.repository.ProfessionalRepository;
import br.com.focusts.clinicall.service.modules.register.organizational.to.ProfessionalSearchTO;
import br.com.focusts.clinicall.service.modules.register.organizational.to.ProfessionalTO;
import br.com.focusts.clinicall.service.modules.tables.general.facade.PersonFacade;
import br.com.focusts.clinicall.service.modules.tables.general.repository.PersonRepository;

@Service
public class DefaultProfessionalService extends AbstractCrudService<ProfessionalPO, java.lang.Long>
		implements ProfessionalService {

	@Autowired
	private ProfessionalRepository professionalRepository;

	@Autowired
	private HoraryRepository horaryRepository;

	@Autowired
	private PersonRepository personRepository;

	@Autowired
	private ParameterValueRepository parameterValueRepository;

	@Autowired
	private EartefactRepository eartefactRepository;

	@Autowired
	private PersonFacade personFacade;

	@Autowired
	private FirmFacade firmFacade;

	@Autowired
	private UserService userService;
	@Autowired
	private PerformerQueryDslRepository performerQueryDslRepository;

	@Autowired
	private ParameterRepository parameterRepository;

	@Autowired
	private ProfessionalQueryDslRepository professionalQueryDslRepositordy;

	@Autowired
	private OccupationRepository occupationRepository;

	@Override
	public CrudRepository<ProfessionalPO, java.lang.Long> getCrudRepository() {
		return professionalRepository;
	}

	@Override
	public Page<ProfessionalPO> search(PageSearchTO pageSearchTO, String type) {

		// I - internal Professional
		// E- External Professional
		Boolean interalStaff = type.equalsIgnoreCase("I") ? true : false;
		Boolean externalStaff;

		if (interalStaff.booleanValue() == true) {
			externalStaff = true;
		} else {
			externalStaff = true;
			interalStaff = false;
		}
		PageRequest pageRequest = null;
		if (interalStaff == true){
			 pageRequest = PageRequest.of(
					pageSearchTO.getPage(),
					pageSearchTO.getSizePage(),
					Sort.by(
							new Sort.Order(Sort.Direction.DESC, "performer.active"), // Segunda ordenação
							new Sort.Order(Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort())
					)
			);
		}else{
			 pageRequest = PageRequest.of(
					pageSearchTO.getPage(),
					pageSearchTO.getSizePage(),
					Sort.by(
							new Sort.Order(Sort.Direction.DESC, "active"), // Segunda ordenação
							new Sort.Order(Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort())
					)
			);
		}



		return professionalRepository.search(interalStaff, externalStaff, pageSearchTO.getArgument(),
				"%" + pageSearchTO.getArgument() + "%", "%" + pageSearchTO.getArgument() + "%",
				"%" + pageSearchTO.getArgument() + "%", pageRequest);
	}

	@Override
	public Page<ProfessionalPO> search(PageSearchTO pageSearchTO) {

		PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage(),
				Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());

		return professionalRepository.search(pageSearchTO.getArgument(),pageSearchTO.getArgument(), pageRequest);
	}

	@Override
	public List<ProfessionalPO> findListById(List<Long> professionalTargetIdList) {
		List<ProfessionalPO> professionalPOList = new ArrayList<>();
		var professionalResultList = professionalRepository.findAllById(professionalTargetIdList);
		professionalResultList.iterator().forEachRemaining(professionalPOList::add);
		return professionalPOList;
	}

	@Override
	public List<ProfessionalPO> findProfessionalByMultipleIds(List<Long> professionalIdList) {
		return professionalRepository.findByIdIn(professionalIdList);
	}

	@Override
	public void uploadCetification(String certificadoBase64, String password, Long performerId) throws Exception {
		var parameterCertification = parameterRepository.findByName(GlobalKeyParameterConstants.CERTIFICATION_PROFESSIONAL);
		var parameterCertificationPassword = parameterRepository.findByName(GlobalKeyParameterConstants.CERTIFICATION_PROFESSIONAL_PASSWORD);
		var passWordEncrypt = AESUtil.encrypt(password);
		KeyStore keystore  = TissUtil.validateKeyStoreCertificate(certificadoBase64, password);
		String alias = "";
		Enumeration
				<String> aliases = keystore.aliases();
		while (aliases.hasMoreElements()) {
			alias = aliases.nextElement();
		}
		TissUtil.validateKeyStoreExpiration(keystore, alias);
		EartefactPO eartefactPO = new EartefactPO();
		eartefactPO.setName("Certificado Profissional");
		eartefactPO.setType("CERTIFICATION_PROFES");
		eartefactPO.setDateAt(LocalDateTime.now());
		eartefactPO.setContent(certificadoBase64);
		eartefactRepository.save(eartefactPO);

		ParameterValuePO parameterValueCertificationPassword =  new ParameterValuePO();
		parameterValueCertificationPassword.setParameter(parameterCertificationPassword);
		parameterValueCertificationPassword.setValue(passWordEncrypt);
		parameterValueCertificationPassword.setKey(performerId.toString());

		ParameterValuePO parameterValueCertification = new ParameterValuePO();
		parameterValueCertification.setParameter(parameterCertification);
		parameterValueCertification.setKey(performerId.toString());
		parameterValueCertification.setValue(eartefactPO.getId().toString());

		List<ParameterValuePO> parameterValuePOList = new ArrayList<>();
		parameterValuePOList.add(parameterValueCertification);
		parameterValuePOList.add(parameterValueCertificationPassword);
		parameterValueRepository.saveAll(parameterValuePOList);
	}

	@Override
	public EartefactPO findCetification(Long key) {
		var parameterValue = parameterValueRepository.findFirstByParameter_NameAndKey(GlobalKeyParameterConstants.CERTIFICATION_PROFESSIONAL, key.toString());
		if (parameterValue.isEmpty()){
			EartefactPO  eartefactPO =  new EartefactPO();
			return eartefactPO;
		}
		String eartefactId = parameterValue.get().getValue();
		Optional<EartefactPO> eartefactPO = eartefactRepository.findById(Long.parseLong(eartefactId));
		if (eartefactPO.isEmpty()){
			throw new ApplicationException("Certificado não encontrado");
		}
		return eartefactPO.get();
	}

	@Override
	public void deletCetification(Long companyId) {
		var parameterValueCertification = parameterValueRepository.findFirstByParameter_NameAndKey(GlobalKeyParameterConstants.CERTIFICATION_PROFESSIONAL, companyId.toString());
		var parameterValueCertificationPassword = parameterValueRepository.findFirstByParameter_NameAndKey(GlobalKeyParameterConstants.CERTIFICATION_PROFESSIONAL_PASSWORD, companyId.toString());
		eartefactRepository.deleteById(Long.parseLong(parameterValueCertification.get().getValue()));
		parameterValueRepository.delete(parameterValueCertification.get());
		parameterValueRepository.delete(parameterValueCertificationPassword.get());
	}

	@Override
	public Page<PerformerTO> findPerformerList(PageSearchTO pageSearchTO) {
		return professionalQueryDslRepositordy.findPerformerList(pageSearchTO);
	}

	@Override
	public List<PartherPerformerTO> findList() {
		return performerQueryDslRepository.findList();
	}

	@Override
	public ProfessionalPO save(ProfessionalPO professionalPO) {


		if(professionalPO.getPerson().getCpf() == null && professionalPO.getStaff().equals(false) ){
			professionalPO.getPerson().setCpf(GlobalKeyParameterConstants.CPF_IGNORED);
		} else if (professionalPO.getStaff().equals(true)) {
			if ( StringUtil.isNullOrBlankOrEmpty(professionalPO.getPerson().getCpf()) ){
				throw new ApplicationException("CPF obrigatório");
			}else{
				var validRegistry = ValidateUtil.isCpfValid(professionalPO.getPerson().getCpf());
				if (!validRegistry){
					throw new ApplicationException("CPF Informado inválido");
				}
			}
		}

		if (professionalPO.getId() == null) {
			if (professionalPO.getSpecialityList() != null) {
				if (!professionalPO.getSpecialityList().contains(professionalPO.getSpeciality())) {
					professionalPO.getSpecialityList().add(professionalPO.getSpeciality());
				}
			} else {
				Set<SpecialityPO> specialityList = new HashSet<SpecialityPO>();
				specialityList.add((professionalPO.getSpeciality()));
				professionalPO.setSpecialityList(specialityList);
			}
			var parameterValue = parameterRepository.findByValueParameterSystem(GlobalKeyParameterConstants.UPPERCASE_REGISTRATION);
			if (parameterValue.equals("1")) {
				professionalPO.getPerson().setName(professionalPO.getPerson().getName().toUpperCase());
				if (!StringUtil.isNullOrBlankOrEmpty(professionalPO.getPerson().getSocialName())){
					professionalPO.getPerson().setSocialName(professionalPO.getPerson().getSocialName().toUpperCase());
				}
				if (!StringUtil.isNullOrBlankOrEmpty(professionalPO.getPerson().getAlias())){
					professionalPO.getPerson().setAlias(professionalPO.getPerson().getAlias().toUpperCase());
				}

			}
		}

		if (professionalPO.getPerformer() != null && professionalPO.getPerformer().getAdmission() == null
				&& professionalPO.getPerformer().getDemission() != null) {
			throw new ApplicationException(RegisterOrganizationalKeyMessagesConstants.ADMISSION_CAN_NOT_NULL);
		}

//		if (professionalPO.getPerson() != null && professionalPO.getPerson().getOccupation() != null){
//			var occupation =  occupationRepository.findById(professionalPO.getPerson().getOccupation().getId());
//			if (occupation.isPresent()){
//				professionalPO.getPerson().setOccupation(occupation.get());
//			}else{
//				professionalPO.getPerson().setOccupation(null);
//			}
//		}

		if (professionalPO.getPerformer() != null && professionalPO.getPerformer().getAdmission() != null
				&& professionalPO.getPerformer().getDemission() != null
				&& professionalPO.getPerformer().getDemission().isAfter(LocalDate.now())) {
			throw new ApplicationException(RegisterOrganizationalKeyMessagesConstants.DEMISSION_CAN_NOT_GREATER_TODAY);
		}

		if (professionalPO.getPerformer() != null && professionalPO.getPerformer().getAdmission() != null
				&& professionalPO.getPerformer().getDemission() != null
				&& professionalPO.getPerformer().getAdmission().isAfter(professionalPO.getPerformer().getDemission())) {
			throw new ApplicationException(
					RegisterOrganizationalKeyMessagesConstants.ADMISSION_CAN_NOT_GREATER_DEMISSION);
		}

		if (professionalPO.getPerformer() != null && professionalPO.getPerformer().getId() != null){
			professionalPO.getPerformer()
					.setHoraryList(horaryRepository.findByPerformer_Id(professionalPO.getPerformer().getId()));
		}

		if (professionalPO.getFirm() != null){
			FirmPO firm = firmFacade.findById(professionalPO.getFirm().getId());
			professionalPO.setFirm(firm);
		}

		if (professionalPO.getLogin() != null || professionalPO.getCompany() != null || professionalPO.getEmail() != null) {
			UserPO user = new UserPO();

			if (professionalPO.getLogin() == null || professionalPO.getLogin().isBlank()) {
				throw new ApplicationException("Usuário é obrigatório se informar os dados de login!");
			}

			String login = professionalPO.getLogin().trim();

			if (!login.matches("^[a-z0-9.]+$")) {
				throw new ApplicationException("Usuário inválido! Não pode conter espaços ou caracteres especiais.");
			}

			if (professionalPO.getEmail() == null || professionalPO.getEmail().isBlank()) {
				throw new ApplicationException("E-mail é obrigatório se informar os dados de login!");
			}
			if (professionalPO.getCompany() == null) {
				throw new ApplicationException("Unidade é obrigatório se informar os dados de login!");
			}
			if (professionalPO.getRoles() == null || professionalPO.getRoles().isEmpty()) {
				throw new ApplicationException("Perfil é obrigatório se informar os dados de login!");
			}

			user.setEmail(professionalPO.getEmail());
			user.setLogin(login);
			user.setRoles(professionalPO.getRoles());
			user.setPerson(professionalPO.getPerson());
			user.setCompany(professionalPO.getCompany());
			userService.register(user);
		}


		if (professionalPO.getPerson().getId() != null) {
			var person = personFacade.update(professionalPO.getPerson());
			person = personRepository.findById(professionalPO.getPerson().getId()).get();
			professionalPO.setPerson(person);

			return super.save(professionalPO);
		}else{
			return super.save(professionalPO);
		}

		
	}

	@Override
	public Page<ProfessionalPO> searchBySpecialityId(Long specialityId, PageSearchTO pageSearchTO) {

		PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage(),
				Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());

		return professionalRepository.searchBySpecialityId(new SpecialityPO(specialityId),
				pageSearchTO.getArgument() + "%", pageRequest);
	}

	public Page<ProfessionalPO> findByFilter(ProfessionalSearchTO professionalSearchTO) {

		BooleanExpression booleanExpression = QProfessionalPO.professionalPO.id.isNotNull();

		if (professionalSearchTO.getCompanyId() != null) {
			List<HoraryPO> horaryList = horaryRepository
					.findByActiveTrueAndCompany_id(professionalSearchTO.getCompanyId());
			Set<Long> performerIdList = horaryList.stream().collect(Collectors.groupingBy(HoraryPO::getPerformerId))
					.keySet();
			booleanExpression = booleanExpression.and(QProfessionalPO.professionalPO.id.in(performerIdList));
		}

		if (professionalSearchTO.getInsuranceId() != null) {
			booleanExpression = booleanExpression.and(QProfessionalPO.professionalPO.performer.unacceptedInsuranceList
					.contains(new InsurancePO(professionalSearchTO.getInsuranceId())).not());
		}

		if (professionalSearchTO.getSpecialityId() != null) {
			booleanExpression = booleanExpression
					.and(QProfessionalPO.professionalPO.speciality.id.eq(professionalSearchTO.getSpecialityId()))
					.or(QProfessionalPO.professionalPO.specialityList
							.contains(new SpecialityPO(professionalSearchTO.getSpecialityId())));
		}

		if (professionalSearchTO.getProcedureId() != null) {
			BooleanExpression booleanExpressionProcedureMainSpeciality = QProfessionalPO.professionalPO.speciality.procedureList
					.contains(new ProcedurePO(professionalSearchTO.getProcedureId()));
			BooleanExpression booleanExpressionProcedureSpecialityList = QProfessionalPO.professionalPO.specialityList
					.any().procedureList.any().eq(new ProcedurePO(professionalSearchTO.getProcedureId()));

			booleanExpression = booleanExpression
					.and(booleanExpressionProcedureMainSpeciality.or(booleanExpressionProcedureSpecialityList));
		}

		PageSearchTO pageSearchTO = professionalSearchTO.getPageSearchTO();

		PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage(),
				Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());

		if (!StringUtils.isEmpty(pageSearchTO.getArgument())) {
			booleanExpression = booleanExpression
					.and(QProfessionalPO.professionalPO.person.name.startsWith(pageSearchTO.getArgument()));

		}

		Page<ProfessionalPO> page = professionalRepository.findAll(booleanExpression, pageRequest);

		return page;
	}

	@Override
	public ProfessionalPO findByCouncilNumber(String councilNumber) {
		return professionalRepository.findByCouncilNumber(councilNumber);
	}

	@Override
	public Page<ProfessionalTO> findByName(PageSearchTO pageSearchTO) {
		PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage(),
				Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());
		return professionalRepository.findByname(pageSearchTO.getArgument(), pageRequest);
	}

	@Override
	public Page<PerformerTO> findModuleProfessional(String type, PageSearchTO pageSearchTO) {
			return professionalQueryDslRepositordy.findModuleProfessional(type, pageSearchTO);
	}



	/*
	 * @Override
	 * public Page<ProfessionalPO> findByFilter(ProfessionalSearchTO
	 * professionalSearchTO) {
	 * Page<ProfessionalPO> page = null;
	 * 
	 * PageSearchTO pageSearchTO = professionalSearchTO.getPageSearchTO();
	 * 
	 * PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(),
	 * pageSearchTO.getSizePage(),
	 * Sort.Direction.fromString(pageSearchTO.getSortDirection()),
	 * pageSearchTO.getFieldSort());
	 * 
	 * if (Objects.nonNull(professionalSearchTO.getInsuranceId())) {
	 * 
	 * page = professionalRepository.search(pageSearchTO.getArgument(),new
	 * InsurancePO(professionalSearchTO.getInsuranceId()), pageRequest);
	 * 
	 * }
	 * 
	 * return page;
	 * }
	 */

}
