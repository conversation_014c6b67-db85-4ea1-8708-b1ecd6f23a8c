
package br.com.focusts.clinicall.service.modules.register.accreditation.service;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.MeasureTissPO;

public interface MeasureTissService extends CrudService<MeasureTissPO,java.lang.Long>{

    public Page<MeasureTissPO> findByNameContainingOrInitialsContaining(PageSearchTO pageSearchTO);

}
