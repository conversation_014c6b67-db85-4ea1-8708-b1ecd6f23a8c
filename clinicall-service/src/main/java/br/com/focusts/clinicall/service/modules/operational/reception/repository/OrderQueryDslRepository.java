package br.com.focusts.clinicall.service.modules.operational.reception.repository;

import java.util.List;

import br.com.focusts.clinicall.service.modules.operational.reception.to.*;
import br.com.focusts.clinicall.service.modules.operational.scheduling.to.ScheduleTO;
import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.billing.to.FilterTO;

public interface OrderQueryDslRepository {
    public OrderHistoricTO findByPatient_idAndNumberStartingWith(Long patientId, PageSearchTO pageSearchTO);
    public Page<AutorizationControlTO> findByOrderAuthorizer(FilterTO filterTo);
    public List<OrderCustonTO> findByOrderSendEmailAndDownload(FilterTO filterTo, String type);
    public Page<OrderPendingExclusionTO> fingOrderPending(Long performerId, PageSearchTO pageSearchTO);
    public Page<OrderTO> findNonBillableOrders(PageSearchTO pageSearchTO, Long patientId);
    public Page<OrderPayPendingTO> findOrderPayPending(ParametersOrderPayPendingTO parametersOrderPayPendingTO);
    public int checkOrderPayPending(ParametersOrderPayPendingTO parametersOrderPayPendingTO);
}
