package br.com.focusts.clinicall.service.modules.operational.billing.po;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.GlossTypePO;

import javax.persistence.*;

@Entity
@Table(name = "gloss_type_item")
public class GlossTypeItem extends AbstractPO<Long> {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "gloss_type_item_id")
    private Long id;

    @JoinColumn(name = "gloss_type_id", referencedColumnName = "gloss_type_id")
    @ManyToOne
    private GlossTypePO glossType;

    @JoinColumn(name = "gloss_id", referencedColumnName = "gloss_id")
    @ManyToOne
    private GlossPO gloss;

    @JoinColumn(name = "gloss_order_item_product_id", referencedColumnName = "gloss_order_item_product_id")
    @ManyToOne
    private GlossOrderItemProductPO glossOrderItemProduct;

    @JoinColumn(name = "gloss_order_item_fee_id", referencedColumnName = "gloss_order_item_fee_id")
    @ManyToOne
    private GlossOrderItemFeePO glossOrderItemFee;

    @JoinColumn(name = "gloss_order_item_auxiliary_id", referencedColumnName = "gloss_order_item_auxiliary_id")
    @ManyToOne
    private GlossOrderItemAuxiliaryPO glossOrderItemAuxiliary;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public GlossTypePO getGlossType() {
        return glossType;
    }

    public void setGlossType(GlossTypePO glossType) {
        this.glossType = glossType;
    }

    public GlossPO getGloss() {
        return gloss;
    }

    public void setGloss(GlossPO gloss) {
        this.gloss = gloss;
    }

    public GlossOrderItemProductPO getGlossOrderItemProduct() {
        return glossOrderItemProduct;
    }

    public void setGlossOrderItemProduct(GlossOrderItemProductPO glossOrderItemProduct) {
        this.glossOrderItemProduct = glossOrderItemProduct;
    }

    public GlossOrderItemFeePO getGlossOrderItemFee() {
        return glossOrderItemFee;
    }

    public void setGlossOrderItemFee(GlossOrderItemFeePO glossOrderItemFee) {
        this.glossOrderItemFee = glossOrderItemFee;
    }

    public GlossOrderItemAuxiliaryPO getGlossOrderItemAuxiliary() {
        return glossOrderItemAuxiliary;
    }

    public void setGlossOrderItemAuxiliary(GlossOrderItemAuxiliaryPO glossOrderItemAuxiliary) {
        this.glossOrderItemAuxiliary = glossOrderItemAuxiliary;
    }
}

