//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2022.06.06 at 11:08:30 AM BRT 
//


package br.com.focusts.clinicall.service.modules.tables.tiss.version.v30500;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ct_loginSenha complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_loginSenha">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="loginPrestador" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_texto20"/>
 *         &lt;element name="senhaPrestador" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_texto32"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_loginSenha", propOrder = {
    "loginPrestador",
    "senhaPrestador"
})
public class CtLoginSenha {

    @XmlElement(required = true)
    protected String loginPrestador;
    @XmlElement(required = true)
    protected String senhaPrestador;

    /**
     * Gets the value of the loginPrestador property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLoginPrestador() {
        return loginPrestador;
    }

    /**
     * Sets the value of the loginPrestador property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLoginPrestador(String value) {
        this.loginPrestador = value;
    }

    /**
     * Gets the value of the senhaPrestador property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSenhaPrestador() {
        return senhaPrestador;
    }

    /**
     * Sets the value of the senhaPrestador property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSenhaPrestador(String value) {
        this.senhaPrestador = value;
    }

}
