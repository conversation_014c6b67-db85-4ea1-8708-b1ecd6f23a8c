
package br.com.focusts.clinicall.service.modules.operational.scheduling.service;

import br.com.focusts.clinicall.fw.exception.ApplicationException;
import br.com.focusts.clinicall.service.modules.operational.scheduling.po.SurgeryScheduleCustomPO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.repository.SurgeryScheduleCustomRepository;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.InsurancePO;
import br.com.focusts.clinicall.service.modules.register.operational.po.PatientPO;
import br.com.focusts.clinicall.service.modules.tables.general.po.ContactListPO;
import br.com.focusts.clinicall.service.modules.tables.general.po.PersonPO;
import br.com.focusts.clinicall.service.modules.tables.general.service.PersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.po.SurgerySchedulePO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.repository.SurgeryScheduleRepository;

import java.util.HashSet;
import java.util.List;


@Service
public class DefaultSurgeryScheduleService extends AbstractCrudService<SurgerySchedulePO,java.lang.Long> implements SurgeryScheduleService {

	@Autowired
	private SurgeryScheduleRepository surgeryScheduleRepository;

	@Autowired
	private SurgeryScheduleCustomRepository surgeryScheduleCustomRepository;

	@Autowired
	private PersonService personService;

	@Autowired
	private ConfirmService confirmService;

	@Override
	public CrudRepository<SurgerySchedulePO,java.lang.Long> getCrudRepository() {
	    return surgeryScheduleRepository;
	}

	@Override
	public Page<SurgerySchedulePO> search(PageSearchTO pageSearchTO) {

		PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage(),
				Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());

		return surgeryScheduleRepository.findByPatient_person_nameOrPerson_nameStartingWith(pageSearchTO.getArgument(),pageSearchTO.getArgument(), pageRequest);
	}

	@Override
	public List<SurgerySchedulePO> findMapSurgery(String date, String professionalName, String personName) {
		List<SurgerySchedulePO> surgeryScheduleList = surgeryScheduleRepository.findMapSurgery(date, professionalName, personName);
		return surgeryScheduleList;
	}

	@Override
	public SurgerySchedulePO save(SurgerySchedulePO surgerySchedulePO) {

		if (surgerySchedulePO.getPerson().getId() == null){
			PersonPO person = new PersonPO();
			person.setName(surgerySchedulePO.getPerson().getName());
			person.setCpf("00000000015");
			person.setContactList(createContact(surgerySchedulePO.getPhone()));

			surgerySchedulePO.setPerson(personService.save(person));
		}

		if (surgerySchedulePO.getPerson().getPhoneStandart() == "" || surgerySchedulePO.getPerson().getPhoneStandart() == null){
			PersonPO person = personService.findById(surgerySchedulePO.getPerson().getId());
			person.setContactList(createContact(surgerySchedulePO.getPhone()));
			surgerySchedulePO.setPerson(personService.save(person));
		}

		if(surgerySchedulePO.getConfirm() != null){
			surgerySchedulePO.setConfirm(confirmService.saveConfirmSurgery(surgerySchedulePO.getConfirm()));
		}

		super.save(surgerySchedulePO);

		if (surgerySchedulePO.getWeight() != null){
			SurgeryScheduleCustomPO surgeryScheduleCustomPO = surgeryScheduleCustomRepository.findByKey("WEIGHT",surgerySchedulePO.getId());

			if (surgeryScheduleCustomPO != null){
				surgeryScheduleCustomPO.setValue(surgerySchedulePO.getWeight());
				surgeryScheduleCustomRepository.save(surgeryScheduleCustomPO);
			}else{
				surgeryScheduleCustomPO = new SurgeryScheduleCustomPO();
				surgeryScheduleCustomPO.setSurgerySchedulePO(surgerySchedulePO);
				surgeryScheduleCustomPO.setKey("WEIGHT");
				surgeryScheduleCustomPO.setValue(surgerySchedulePO.getWeight());
				surgeryScheduleCustomRepository.save(surgeryScheduleCustomPO);
			}

		}

		return super.save(surgerySchedulePO);
	}

	public ContactListPO createContact(String phone) {
		ContactListPO contactList = new ContactListPO();
		ContactListPO contactItem = new ContactListPO();

		contactList.setType("G");
		contactList.setDescription("Grupo de Contatos");

		contactItem.setPhone(phone);
		contactItem.setDescription("Celular Principal");
		contactItem.setType("S");

		contactList.setContactItem(new HashSet<>());
		contactList.getContactItem().add(contactItem);

		return contactList;
	}
}
