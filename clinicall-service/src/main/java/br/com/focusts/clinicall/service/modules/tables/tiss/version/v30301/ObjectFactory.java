//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2023.04.01 at 01:57:32 PM BRT 
//


package br.com.focusts.clinicall.service.modules.tables.tiss.version.v30301;

import java.math.BigInteger;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the br.com.focusts.clinicall.service.modules.tables.tiss.version.v30301 package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _SPKIData_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "SPKIData");
    private final static QName _KeyName_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "KeyName");
    private final static QName _RSAKeyValue_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "RSAKeyValue");
    private final static QName _Signature_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "Signature");
    private final static QName _KeyInfo_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "KeyInfo");
    private final static QName _SignatureValue_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "SignatureValue");
    private final static QName _MgmtData_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "MgmtData");
    private final static QName _SignatureMethod_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "SignatureMethod");
    private final static QName _Object_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "Object");
    private final static QName _SignatureProperties_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "SignatureProperties");
    private final static QName _Transform_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "Transform");
    private final static QName _KeyValue_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "KeyValue");
    private final static QName _PGPData_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "PGPData");
    private final static QName _Transforms_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "Transforms");
    private final static QName _Reference_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "Reference");
    private final static QName _RetrievalMethod_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "RetrievalMethod");
    private final static QName _DSAKeyValue_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "DSAKeyValue");
    private final static QName _DigestMethod_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "DigestMethod");
    private final static QName _DigestValue_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "DigestValue");
    private final static QName _CanonicalizationMethod_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "CanonicalizationMethod");
    private final static QName _SignedInfo_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "SignedInfo");
    private final static QName _X509Data_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "X509Data");
    private final static QName _Manifest_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "Manifest");
    private final static QName _SignatureProperty_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "SignatureProperty");
    private final static QName _SPKIDataTypeSPKISexp_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "SPKISexp");
    private final static QName _TransformTypeXPath_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "XPath");
    private final static QName _X509DataTypeX509IssuerSerial_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "X509IssuerSerial");
    private final static QName _X509DataTypeX509CRL_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "X509CRL");
    private final static QName _X509DataTypeX509SubjectName_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "X509SubjectName");
    private final static QName _X509DataTypeX509SKI_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "X509SKI");
    private final static QName _X509DataTypeX509Certificate_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "X509Certificate");
    private final static QName _PGPDataTypePGPKeyID_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "PGPKeyID");
    private final static QName _PGPDataTypePGPKeyPacket_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "PGPKeyPacket");
    private final static QName _SignatureMethodTypeHMACOutputLength_QNAME = new QName("http://www.w3.org/2000/09/xmldsig#", "HMACOutputLength");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: br.com.focusts.clinicall.service.modules.tables.tiss.version.v30301
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia.CabecalhoDemonstrativoOdonto }
     * 
     */
    public CtoDemonstrativoOdontologia.CabecalhoDemonstrativoOdonto createCtoDemonstrativoOdontologiaCabecalhoDemonstrativoOdonto() {
        return new CtoDemonstrativoOdontologia.CabecalhoDemonstrativoOdonto();
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia.TotaisBrutoDemonstrativo }
     * 
     */
    public CtoDemonstrativoOdontologia.TotaisBrutoDemonstrativo createCtoDemonstrativoOdontologiaTotaisBrutoDemonstrativo() {
        return new CtoDemonstrativoOdontologia.TotaisBrutoDemonstrativo();
    }

    /**
     * Create an instance of {@link CtGuiaRecursoLote }
     * 
     */
    public CtGuiaRecursoLote createCtGuiaRecursoLote() {
        return new CtGuiaRecursoLote();
    }

    /**
     * Create an instance of {@link CtRecursoGlosaRecebimento.GuiasRecurso.OpcaoRecursoGuia.ItensGuia.DenteRegiao }
     * 
     */
    public CtRecursoGlosaRecebimento.GuiasRecurso.OpcaoRecursoGuia.ItensGuia.DenteRegiao createCtRecursoGlosaRecebimentoGuiasRecursoOpcaoRecursoGuiaItensGuiaDenteRegiao() {
        return new CtRecursoGlosaRecebimento.GuiasRecurso.OpcaoRecursoGuia.ItensGuia.DenteRegiao();
    }

    /**
     * Create an instance of {@link CtmConsultaAtendimento.Procedimento }
     * 
     */
    public CtmConsultaAtendimento.Procedimento createCtmConsultaAtendimentoProcedimento() {
        return new CtmConsultaAtendimento.Procedimento();
    }

    /**
     * Create an instance of {@link CtmRecursoGlosa.OpcaoRecurso }
     * 
     */
    public CtmRecursoGlosa.OpcaoRecurso createCtmRecursoGlosaOpcaoRecurso() {
        return new CtmRecursoGlosa.OpcaoRecurso();
    }

    /**
     * Create an instance of {@link CtGuiaDadosOdonto.ProcedimentosRealizados.ProcedimentoRealizado }
     * 
     */
    public CtGuiaDadosOdonto.ProcedimentosRealizados.ProcedimentoRealizado createCtGuiaDadosOdontoProcedimentosRealizadosProcedimentoRealizado() {
        return new CtGuiaDadosOdonto.ProcedimentosRealizados.ProcedimentoRealizado();
    }

    /**
     * Create an instance of {@link CtGlosaReciboOdonto.OpcaoRecurso }
     * 
     */
    public CtGlosaReciboOdonto.OpcaoRecurso createCtGlosaReciboOdontoOpcaoRecurso() {
        return new CtGlosaReciboOdonto.OpcaoRecurso();
    }

    /**
     * Create an instance of {@link CtGuiaDados.ProcedimentosRealizados }
     * 
     */
    public CtGuiaDados.ProcedimentosRealizados createCtGuiaDadosProcedimentosRealizados() {
        return new CtGuiaDados.ProcedimentosRealizados();
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia.DadosPagamentoPorData.Protocolos.DadosPagamentoGuia.DadosPagamento.DenteRegiao }
     * 
     */
    public CtoDemonstrativoOdontologia.DadosPagamentoPorData.Protocolos.DadosPagamentoGuia.DadosPagamento.DenteRegiao createCtoDemonstrativoOdontologiaDadosPagamentoPorDataProtocolosDadosPagamentoGuiaDadosPagamentoDenteRegiao() {
        return new CtoDemonstrativoOdontologia.DadosPagamentoPorData.Protocolos.DadosPagamentoGuia.DadosPagamento.DenteRegiao();
    }

    /**
     * Create an instance of {@link CtContaMedicaResumo }
     * 
     */
    public CtContaMedicaResumo createCtContaMedicaResumo() {
        return new CtContaMedicaResumo();
    }

    /**
     * Create an instance of {@link CtDadosComplementaresBeneficiarioRadio }
     * 
     */
    public CtDadosComplementaresBeneficiarioRadio createCtDadosComplementaresBeneficiarioRadio() {
        return new CtDadosComplementaresBeneficiarioRadio();
    }

    /**
     * Create an instance of {@link CtmDemonstrativoPagamento.DadosContratado }
     * 
     */
    public CtmDemonstrativoPagamento.DadosContratado createCtmDemonstrativoPagamentoDadosContratado() {
        return new CtmDemonstrativoPagamento.DadosContratado();
    }

    /**
     * Create an instance of {@link CtDemonstrativoSolicitacao.DemonstrativoAnalise }
     * 
     */
    public CtDemonstrativoSolicitacao.DemonstrativoAnalise createCtDemonstrativoSolicitacaoDemonstrativoAnalise() {
        return new CtDemonstrativoSolicitacao.DemonstrativoAnalise();
    }

    /**
     * Create an instance of {@link CtmHonorarioIndividualGuia.DadosInternacao }
     * 
     */
    public CtmHonorarioIndividualGuia.DadosInternacao createCtmHonorarioIndividualGuiaDadosInternacao() {
        return new CtmHonorarioIndividualGuia.DadosInternacao();
    }

    /**
     * Create an instance of {@link CtmSpSadtAtendimento }
     * 
     */
    public CtmSpSadtAtendimento createCtmSpSadtAtendimento() {
        return new CtmSpSadtAtendimento();
    }

    /**
     * Create an instance of {@link CtoGuiaOdontologia.DadosProfissionaisResponsaveis }
     * 
     */
    public CtoGuiaOdontologia.DadosProfissionaisResponsaveis createCtoGuiaOdontologiaDadosProfissionaisResponsaveis() {
        return new CtoGuiaOdontologia.DadosProfissionaisResponsaveis();
    }

    /**
     * Create an instance of {@link SignaturePropertiesType }
     * 
     */
    public SignaturePropertiesType createSignaturePropertiesType() {
        return new SignaturePropertiesType();
    }

    /**
     * Create an instance of {@link CtAutorizacaoInternacao }
     * 
     */
    public CtAutorizacaoInternacao createCtAutorizacaoInternacao() {
        return new CtAutorizacaoInternacao();
    }

    /**
     * Create an instance of {@link CtProcedimentoExecutadoHonorIndiv }
     * 
     */
    public CtProcedimentoExecutadoHonorIndiv createCtProcedimentoExecutadoHonorIndiv() {
        return new CtProcedimentoExecutadoHonorIndiv();
    }

    /**
     * Create an instance of {@link CtmHonorarioIndividualGuia.Beneficiario }
     * 
     */
    public CtmHonorarioIndividualGuia.Beneficiario createCtmHonorarioIndividualGuiaBeneficiario() {
        return new CtmHonorarioIndividualGuia.Beneficiario();
    }

    /**
     * Create an instance of {@link CtPagamentoDados }
     * 
     */
    public CtPagamentoDados createCtPagamentoDados() {
        return new CtPagamentoDados();
    }

    /**
     * Create an instance of {@link CtmGuiaLote.GuiasTISS }
     * 
     */
    public CtmGuiaLote.GuiasTISS createCtmGuiaLoteGuiasTISS() {
        return new CtmGuiaLote.GuiasTISS();
    }

    /**
     * Create an instance of {@link CtLoteAnexoStatus }
     * 
     */
    public CtLoteAnexoStatus createCtLoteAnexoStatus() {
        return new CtLoteAnexoStatus();
    }

    /**
     * Create an instance of {@link SignatureType }
     * 
     */
    public SignatureType createSignatureType() {
        return new SignatureType();
    }

    /**
     * Create an instance of {@link CtmDemonstrativoAnaliseConta.DadosConta }
     * 
     */
    public CtmDemonstrativoAnaliseConta.DadosConta createCtmDemonstrativoAnaliseContaDadosConta() {
        return new CtmDemonstrativoAnaliseConta.DadosConta();
    }

    /**
     * Create an instance of {@link OperadoraPrestador }
     * 
     */
    public OperadoraPrestador createOperadoraPrestador() {
        return new OperadoraPrestador();
    }

    /**
     * Create an instance of {@link CtPagamentoResumo }
     * 
     */
    public CtPagamentoResumo createCtPagamentoResumo() {
        return new CtPagamentoResumo();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoQuimio.DiagnosticoOncologicoQuimioterapia }
     * 
     */
    public CtmAutorizacaoQuimio.DiagnosticoOncologicoQuimioterapia createCtmAutorizacaoQuimioDiagnosticoOncologicoQuimioterapia() {
        return new CtmAutorizacaoQuimio.DiagnosticoOncologicoQuimioterapia();
    }

    /**
     * Create an instance of {@link CtoAutorizacaoServico.ProcedimentosAutorizados }
     * 
     */
    public CtoAutorizacaoServico.ProcedimentosAutorizados createCtoAutorizacaoServicoProcedimentosAutorizados() {
        return new CtoAutorizacaoServico.ProcedimentosAutorizados();
    }

    /**
     * Create an instance of {@link CtmSpSadtSolicitacaoGuia.DadosSolicitante }
     * 
     */
    public CtmSpSadtSolicitacaoGuia.DadosSolicitante createCtmSpSadtSolicitacaoGuiaDadosSolicitante() {
        return new CtmSpSadtSolicitacaoGuia.DadosSolicitante();
    }

    /**
     * Create an instance of {@link CtElegibilidadeRecibo }
     * 
     */
    public CtElegibilidadeRecibo createCtElegibilidadeRecibo() {
        return new CtElegibilidadeRecibo();
    }

    /**
     * Create an instance of {@link CtoAnexoSituacaoInicial }
     * 
     */
    public CtoAnexoSituacaoInicial createCtoAnexoSituacaoInicial() {
        return new CtoAnexoSituacaoInicial();
    }

    /**
     * Create an instance of {@link CtAnexoCabecalho }
     * 
     */
    public CtAnexoCabecalho createCtAnexoCabecalho() {
        return new CtAnexoCabecalho();
    }

    /**
     * Create an instance of {@link CtSituacaoAutorizacao }
     * 
     */
    public CtSituacaoAutorizacao createCtSituacaoAutorizacao() {
        return new CtSituacaoAutorizacao();
    }

    /**
     * Create an instance of {@link CtmSpSadtGuia.ProcedimentosExecutados }
     * 
     */
    public CtmSpSadtGuia.ProcedimentosExecutados createCtmSpSadtGuiaProcedimentosExecutados() {
        return new CtmSpSadtGuia.ProcedimentosExecutados();
    }

    /**
     * Create an instance of {@link CtRecursoGlosaRecebimento.GuiasRecurso.OpcaoRecursoGuia }
     * 
     */
    public CtRecursoGlosaRecebimento.GuiasRecurso.OpcaoRecursoGuia createCtRecursoGlosaRecebimentoGuiasRecursoOpcaoRecursoGuia() {
        return new CtRecursoGlosaRecebimento.GuiasRecurso.OpcaoRecursoGuia();
    }

    /**
     * Create an instance of {@link CtLoteStatus }
     * 
     */
    public CtLoteStatus createCtLoteStatus() {
        return new CtLoteStatus();
    }

    /**
     * Create an instance of {@link CtOpmUtilizada }
     * 
     */
    public CtOpmUtilizada createCtOpmUtilizada() {
        return new CtOpmUtilizada();
    }

    /**
     * Create an instance of {@link CtmAnexoSolicitacaoQuimio.TratamentosAnteriores }
     * 
     */
    public CtmAnexoSolicitacaoQuimio.TratamentosAnteriores createCtmAnexoSolicitacaoQuimioTratamentosAnteriores() {
        return new CtmAnexoSolicitacaoQuimio.TratamentosAnteriores();
    }

    /**
     * Create an instance of {@link CtmInternacaoResumoGuia }
     * 
     */
    public CtmInternacaoResumoGuia createCtmInternacaoResumoGuia() {
        return new CtmInternacaoResumoGuia();
    }

    /**
     * Create an instance of {@link SignedInfoType }
     * 
     */
    public SignedInfoType createSignedInfoType() {
        return new SignedInfoType();
    }

    /**
     * Create an instance of {@link MensagemTISS }
     * 
     */
    public MensagemTISS createMensagemTISS() {
        return new MensagemTISS();
    }

    /**
     * Create an instance of {@link CtProtocoloAnexoStatus.LoteAnexo }
     * 
     */
    public CtProtocoloAnexoStatus.LoteAnexo createCtProtocoloAnexoStatusLoteAnexo() {
        return new CtProtocoloAnexoStatus.LoteAnexo();
    }

    /**
     * Create an instance of {@link DigestMethodType }
     * 
     */
    public DigestMethodType createDigestMethodType() {
        return new DigestMethodType();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoServico.MotivosNegativa }
     * 
     */
    public CtmAutorizacaoServico.MotivosNegativa createCtmAutorizacaoServicoMotivosNegativa() {
        return new CtmAutorizacaoServico.MotivosNegativa();
    }

    /**
     * Create an instance of {@link CtGuiaCancelamentoRecibo }
     * 
     */
    public CtGuiaCancelamentoRecibo createCtGuiaCancelamentoRecibo() {
        return new CtGuiaCancelamentoRecibo();
    }

    /**
     * Create an instance of {@link CtProcedimentoExecutadoHonorIndiv.Profissionais }
     * 
     */
    public CtProcedimentoExecutadoHonorIndiv.Profissionais createCtProcedimentoExecutadoHonorIndivProfissionais() {
        return new CtProcedimentoExecutadoHonorIndiv.Profissionais();
    }

    /**
     * Create an instance of {@link CtProtocoloRecebimentoRecurso }
     * 
     */
    public CtProtocoloRecebimentoRecurso createCtProtocoloRecebimentoRecurso() {
        return new CtProtocoloRecebimentoRecurso();
    }

    /**
     * Create an instance of {@link CtmAnexoSolicitacaoOPME.OpmeSolicitadas.OpmeSolicitada }
     * 
     */
    public CtmAnexoSolicitacaoOPME.OpmeSolicitadas.OpmeSolicitada createCtmAnexoSolicitacaoOPMEOpmeSolicitadasOpmeSolicitada() {
        return new CtmAnexoSolicitacaoOPME.OpmeSolicitadas.OpmeSolicitada();
    }

    /**
     * Create an instance of {@link CtGuiaDadosOdonto }
     * 
     */
    public CtGuiaDadosOdonto createCtGuiaDadosOdonto() {
        return new CtGuiaDadosOdonto();
    }

    /**
     * Create an instance of {@link CtDemonstrativoSolicitacao }
     * 
     */
    public CtDemonstrativoSolicitacao createCtDemonstrativoSolicitacao() {
        return new CtDemonstrativoSolicitacao();
    }

    /**
     * Create an instance of {@link CtmInternacaoSolicitacaoGuia.DadosInternacao }
     * 
     */
    public CtmInternacaoSolicitacaoGuia.DadosInternacao createCtmInternacaoSolicitacaoGuiaDadosInternacao() {
        return new CtmInternacaoSolicitacaoGuia.DadosInternacao();
    }

    /**
     * Create an instance of {@link CtoRecursoGlosaOdonto.OpcaoRecurso.RecursoGuia.RecursoProcedimento }
     * 
     */
    public CtoRecursoGlosaOdonto.OpcaoRecurso.RecursoGuia.RecursoProcedimento createCtoRecursoGlosaOdontoOpcaoRecursoRecursoGuiaRecursoProcedimento() {
        return new CtoRecursoGlosaOdonto.OpcaoRecurso.RecursoGuia.RecursoProcedimento();
    }

    /**
     * Create an instance of {@link CtRecebimentoLote }
     * 
     */
    public CtRecebimentoLote createCtRecebimentoLote() {
        return new CtRecebimentoLote();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoInternacao }
     * 
     */
    public CtmAutorizacaoInternacao createCtmAutorizacaoInternacao() {
        return new CtmAutorizacaoInternacao();
    }

    /**
     * Create an instance of {@link CtoOdontoSolicitacaoGuia }
     * 
     */
    public CtoOdontoSolicitacaoGuia createCtoOdontoSolicitacaoGuia() {
        return new CtoOdontoSolicitacaoGuia();
    }

    /**
     * Create an instance of {@link CtmProrrogacaoSolicitacaoGuia.DadosBeneficiario }
     * 
     */
    public CtmProrrogacaoSolicitacaoGuia.DadosBeneficiario createCtmProrrogacaoSolicitacaoGuiaDadosBeneficiario() {
        return new CtmProrrogacaoSolicitacaoGuia.DadosBeneficiario();
    }

    /**
     * Create an instance of {@link CtProcedimentosComplementares }
     * 
     */
    public CtProcedimentosComplementares createCtProcedimentosComplementares() {
        return new CtProcedimentosComplementares();
    }

    /**
     * Create an instance of {@link CtGuiaDadosAnexo.ProcedimentosSolicitados.ProcedimentoSolicitado }
     * 
     */
    public CtGuiaDadosAnexo.ProcedimentosSolicitados.ProcedimentoSolicitado createCtGuiaDadosAnexoProcedimentosSolicitadosProcedimentoSolicitado() {
        return new CtGuiaDadosAnexo.ProcedimentosSolicitados.ProcedimentoSolicitado();
    }

    /**
     * Create an instance of {@link CtmAnexoSolicitacaoOPME.OpmeSolicitadas }
     * 
     */
    public CtmAnexoSolicitacaoOPME.OpmeSolicitadas createCtmAnexoSolicitacaoOPMEOpmeSolicitadas() {
        return new CtmAnexoSolicitacaoOPME.OpmeSolicitadas();
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia.DadosPagamentoPorData.Protocolos }
     * 
     */
    public CtoDemonstrativoOdontologia.DadosPagamentoPorData.Protocolos createCtoDemonstrativoOdontologiaDadosPagamentoPorDataProtocolos() {
        return new CtoDemonstrativoOdontologia.DadosPagamentoPorData.Protocolos();
    }

    /**
     * Create an instance of {@link CtoAnexoSituacaoInicial.CtSituacaoInicial }
     * 
     */
    public CtoAnexoSituacaoInicial.CtSituacaoInicial createCtoAnexoSituacaoInicialCtSituacaoInicial() {
        return new CtoAnexoSituacaoInicial.CtSituacaoInicial();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoQuimio.TratamentosAnteriores }
     * 
     */
    public CtmAutorizacaoQuimio.TratamentosAnteriores createCtmAutorizacaoQuimioTratamentosAnteriores() {
        return new CtmAutorizacaoQuimio.TratamentosAnteriores();
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia.DebCredDemonstrativo }
     * 
     */
    public CtoDemonstrativoOdontologia.DebCredDemonstrativo createCtoDemonstrativoOdontologiaDebCredDemonstrativo() {
        return new CtoDemonstrativoOdontologia.DebCredDemonstrativo();
    }

    /**
     * Create an instance of {@link CtProtocoloDetalheAnexo }
     * 
     */
    public CtProtocoloDetalheAnexo createCtProtocoloDetalheAnexo() {
        return new CtProtocoloDetalheAnexo();
    }

    /**
     * Create an instance of {@link CtReciboComunicacao }
     * 
     */
    public CtReciboComunicacao createCtReciboComunicacao() {
        return new CtReciboComunicacao();
    }

    /**
     * Create an instance of {@link CtGuiaCancelamentoRecibo.GuiasCanceladas.DadosGuia }
     * 
     */
    public CtGuiaCancelamentoRecibo.GuiasCanceladas.DadosGuia createCtGuiaCancelamentoReciboGuiasCanceladasDadosGuia() {
        return new CtGuiaCancelamentoRecibo.GuiasCanceladas.DadosGuia();
    }

    /**
     * Create an instance of {@link OperadoraPrestador.RespostaElegibilidade }
     * 
     */
    public OperadoraPrestador.RespostaElegibilidade createOperadoraPrestadorRespostaElegibilidade() {
        return new OperadoraPrestador.RespostaElegibilidade();
    }

    /**
     * Create an instance of {@link CtoRecursoGlosaOdonto.OpcaoRecurso.RecursoGuia.RecursoProcedimento.DenteRegiao }
     * 
     */
    public CtoRecursoGlosaOdonto.OpcaoRecurso.RecursoGuia.RecursoProcedimento.DenteRegiao createCtoRecursoGlosaOdontoOpcaoRecursoRecursoGuiaRecursoProcedimentoDenteRegiao() {
        return new CtoRecursoGlosaOdonto.OpcaoRecurso.RecursoGuia.RecursoProcedimento.DenteRegiao();
    }

    /**
     * Create an instance of {@link ObjectType }
     * 
     */
    public ObjectType createObjectType() {
        return new ObjectType();
    }

    /**
     * Create an instance of {@link CtProcedimentoExecutadoSadt }
     * 
     */
    public CtProcedimentoExecutadoSadt createCtProcedimentoExecutadoSadt() {
        return new CtProcedimentoExecutadoSadt();
    }

    /**
     * Create an instance of {@link CtProcedimentoExecutadoHonorIndiv.Profissionais.CodProfissional }
     * 
     */
    public CtProcedimentoExecutadoHonorIndiv.Profissionais.CodProfissional createCtProcedimentoExecutadoHonorIndivProfissionaisCodProfissional() {
        return new CtProcedimentoExecutadoHonorIndiv.Profissionais.CodProfissional();
    }

    /**
     * Create an instance of {@link CtmHonorarioIndividualGuia.LocalContratado }
     * 
     */
    public CtmHonorarioIndividualGuia.LocalContratado createCtmHonorarioIndividualGuiaLocalContratado() {
        return new CtmHonorarioIndividualGuia.LocalContratado();
    }

    /**
     * Create an instance of {@link CtLoteStatus.GuiasTISS }
     * 
     */
    public CtLoteStatus.GuiasTISS createCtLoteStatusGuiasTISS() {
        return new CtLoteStatus.GuiasTISS();
    }

    /**
     * Create an instance of {@link CtGuiaValorTotal }
     * 
     */
    public CtGuiaValorTotal createCtGuiaValorTotal() {
        return new CtGuiaValorTotal();
    }

    /**
     * Create an instance of {@link CtBeneficiarioDados }
     * 
     */
    public CtBeneficiarioDados createCtBeneficiarioDados() {
        return new CtBeneficiarioDados();
    }

    /**
     * Create an instance of {@link CtmRecursoGlosa.OpcaoRecurso.RecursoProtocolo }
     * 
     */
    public CtmRecursoGlosa.OpcaoRecurso.RecursoProtocolo createCtmRecursoGlosaOpcaoRecursoRecursoProtocolo() {
        return new CtmRecursoGlosa.OpcaoRecurso.RecursoProtocolo();
    }

    /**
     * Create an instance of {@link CtProcedimentoExecutadoOdonto }
     * 
     */
    public CtProcedimentoExecutadoOdonto createCtProcedimentoExecutadoOdonto() {
        return new CtProcedimentoExecutadoOdonto();
    }

    /**
     * Create an instance of {@link CtCreditoOdonto }
     * 
     */
    public CtCreditoOdonto createCtCreditoOdonto() {
        return new CtCreditoOdonto();
    }

    /**
     * Create an instance of {@link CtFontePagadora }
     * 
     */
    public CtFontePagadora createCtFontePagadora() {
        return new CtFontePagadora();
    }

    /**
     * Create an instance of {@link CtDadosComplementaresBeneficiario }
     * 
     */
    public CtDadosComplementaresBeneficiario createCtDadosComplementaresBeneficiario() {
        return new CtDadosComplementaresBeneficiario();
    }

    /**
     * Create an instance of {@link CtRespostaGlosaGuiaMedica }
     * 
     */
    public CtRespostaGlosaGuiaMedica createCtRespostaGlosaGuiaMedica() {
        return new CtRespostaGlosaGuiaMedica();
    }

    /**
     * Create an instance of {@link CtGuiaDados.ProcedimentosRealizados.ProcedimentoRealizado.GlosasProcedimento }
     * 
     */
    public CtGuiaDados.ProcedimentosRealizados.ProcedimentoRealizado.GlosasProcedimento createCtGuiaDadosProcedimentosRealizadosProcedimentoRealizadoGlosasProcedimento() {
        return new CtGuiaDados.ProcedimentosRealizados.ProcedimentoRealizado.GlosasProcedimento();
    }

    /**
     * Create an instance of {@link CtmHonorarioIndividualGuia }
     * 
     */
    public CtmHonorarioIndividualGuia createCtmHonorarioIndividualGuia() {
        return new CtmHonorarioIndividualGuia();
    }

    /**
     * Create an instance of {@link SignatureMethodType }
     * 
     */
    public SignatureMethodType createSignatureMethodType() {
        return new SignatureMethodType();
    }

    /**
     * Create an instance of {@link CtmInternacaoDados }
     * 
     */
    public CtmInternacaoDados createCtmInternacaoDados() {
        return new CtmInternacaoDados();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoProrrogacao }
     * 
     */
    public CtmAutorizacaoProrrogacao createCtmAutorizacaoProrrogacao() {
        return new CtmAutorizacaoProrrogacao();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoOPME.MotivosNegativa }
     * 
     */
    public CtmAutorizacaoOPME.MotivosNegativa createCtmAutorizacaoOPMEMotivosNegativa() {
        return new CtmAutorizacaoOPME.MotivosNegativa();
    }

    /**
     * Create an instance of {@link CtmAnexoSolicitante }
     * 
     */
    public CtmAnexoSolicitante createCtmAnexoSolicitante() {
        return new CtmAnexoSolicitante();
    }

    /**
     * Create an instance of {@link CtOutrasDespesas }
     * 
     */
    public CtOutrasDespesas createCtOutrasDespesas() {
        return new CtOutrasDespesas();
    }

    /**
     * Create an instance of {@link CtmAnexoSolicitacaoQuimio.DiagnosticoOncologicoQuimioterapia }
     * 
     */
    public CtmAnexoSolicitacaoQuimio.DiagnosticoOncologicoQuimioterapia createCtmAnexoSolicitacaoQuimioDiagnosticoOncologicoQuimioterapia() {
        return new CtmAnexoSolicitacaoQuimio.DiagnosticoOncologicoQuimioterapia();
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia.DadosPrestador }
     * 
     */
    public CtoDemonstrativoOdontologia.DadosPrestador createCtoDemonstrativoOdontologiaDadosPrestador() {
        return new CtoDemonstrativoOdontologia.DadosPrestador();
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia.DadosPagamentoPorData.TotaisPorData }
     * 
     */
    public CtoDemonstrativoOdontologia.DadosPagamentoPorData.TotaisPorData createCtoDemonstrativoOdontologiaDadosPagamentoPorDataTotaisPorData() {
        return new CtoDemonstrativoOdontologia.DadosPagamentoPorData.TotaisPorData();
    }

    /**
     * Create an instance of {@link CtGuiaDadosAnexo }
     * 
     */
    public CtGuiaDadosAnexo createCtGuiaDadosAnexo() {
        return new CtGuiaDadosAnexo();
    }

    /**
     * Create an instance of {@link DSAKeyValueType }
     * 
     */
    public DSAKeyValueType createDSAKeyValueType() {
        return new DSAKeyValueType();
    }

    /**
     * Create an instance of {@link KeyValueType }
     * 
     */
    public KeyValueType createKeyValueType() {
        return new KeyValueType();
    }

    /**
     * Create an instance of {@link CtoAnexoSituacaoInicialnaGTO.CtSituacaoInicial }
     * 
     */
    public CtoAnexoSituacaoInicialnaGTO.CtSituacaoInicial createCtoAnexoSituacaoInicialnaGTOCtSituacaoInicial() {
        return new CtoAnexoSituacaoInicialnaGTO.CtSituacaoInicial();
    }

    /**
     * Create an instance of {@link CtmDemonstrativoPagamento.Pagamentos.PagamentosPorData.TotaisBrutosPorData }
     * 
     */
    public CtmDemonstrativoPagamento.Pagamentos.PagamentosPorData.TotaisBrutosPorData createCtmDemonstrativoPagamentoPagamentosPagamentosPorDataTotaisBrutosPorData() {
        return new CtmDemonstrativoPagamento.Pagamentos.PagamentosPorData.TotaisBrutosPorData();
    }

    /**
     * Create an instance of {@link CtRespostaRecursoItemOdonto.RecursoProcedimento.DenteRegiao }
     * 
     */
    public CtRespostaRecursoItemOdonto.RecursoProcedimento.DenteRegiao createCtRespostaRecursoItemOdontoRecursoProcedimentoDenteRegiao() {
        return new CtRespostaRecursoItemOdonto.RecursoProcedimento.DenteRegiao();
    }

    /**
     * Create an instance of {@link CtmDemonstrativoPagamento.TotaisDemonstrativo }
     * 
     */
    public CtmDemonstrativoPagamento.TotaisDemonstrativo createCtmDemonstrativoPagamentoTotaisDemonstrativo() {
        return new CtmDemonstrativoPagamento.TotaisDemonstrativo();
    }

    /**
     * Create an instance of {@link CtRespostaGlosa }
     * 
     */
    public CtRespostaGlosa createCtRespostaGlosa() {
        return new CtRespostaGlosa();
    }

    /**
     * Create an instance of {@link CabecalhoTransacao }
     * 
     */
    public CabecalhoTransacao createCabecalhoTransacao() {
        return new CabecalhoTransacao();
    }

    /**
     * Create an instance of {@link CtContaMedicaResumo.RelacaoGuias.DetalhesGuia }
     * 
     */
    public CtContaMedicaResumo.RelacaoGuias.DetalhesGuia createCtContaMedicaResumoRelacaoGuiasDetalhesGuia() {
        return new CtContaMedicaResumo.RelacaoGuias.DetalhesGuia();
    }

    /**
     * Create an instance of {@link CtDemonstrativoSolicitacao.DemonstrativoAnalise.Protocolos }
     * 
     */
    public CtDemonstrativoSolicitacao.DemonstrativoAnalise.Protocolos createCtDemonstrativoSolicitacaoDemonstrativoAnaliseProtocolos() {
        return new CtDemonstrativoSolicitacao.DemonstrativoAnalise.Protocolos();
    }

    /**
     * Create an instance of {@link CtSolicitacaoProcedimento }
     * 
     */
    public CtSolicitacaoProcedimento createCtSolicitacaoProcedimento() {
        return new CtSolicitacaoProcedimento();
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia }
     * 
     */
    public CtoDemonstrativoOdontologia createCtoDemonstrativoOdontologia() {
        return new CtoDemonstrativoOdontologia();
    }

    /**
     * Create an instance of {@link CtGuiaRecurso }
     * 
     */
    public CtGuiaRecurso createCtGuiaRecurso() {
        return new CtGuiaRecurso();
    }

    /**
     * Create an instance of {@link CtmDemonstrativoPagamento.TotaisDemonstrativo.TotaisLiquidosDemonstrativo }
     * 
     */
    public CtmDemonstrativoPagamento.TotaisDemonstrativo.TotaisLiquidosDemonstrativo createCtmDemonstrativoPagamentoTotaisDemonstrativoTotaisLiquidosDemonstrativo() {
        return new CtmDemonstrativoPagamento.TotaisDemonstrativo.TotaisLiquidosDemonstrativo();
    }

    /**
     * Create an instance of {@link RSAKeyValueType }
     * 
     */
    public RSAKeyValueType createRSAKeyValueType() {
        return new RSAKeyValueType();
    }

    /**
     * Create an instance of {@link CtRecebimentoRecurso }
     * 
     */
    public CtRecebimentoRecurso createCtRecebimentoRecurso() {
        return new CtRecebimentoRecurso();
    }

    /**
     * Create an instance of {@link CtElegibilidadeRecibo.MotivosNegativa }
     * 
     */
    public CtElegibilidadeRecibo.MotivosNegativa createCtElegibilidadeReciboMotivosNegativa() {
        return new CtElegibilidadeRecibo.MotivosNegativa();
    }

    /**
     * Create an instance of {@link CtmBeneficiarioComunicacao }
     * 
     */
    public CtmBeneficiarioComunicacao createCtmBeneficiarioComunicacao() {
        return new CtmBeneficiarioComunicacao();
    }

    /**
     * Create an instance of {@link CtLoteAnexoStatus.AnexosClinicos }
     * 
     */
    public CtLoteAnexoStatus.AnexosClinicos createCtLoteAnexoStatusAnexosClinicos() {
        return new CtLoteAnexoStatus.AnexosClinicos();
    }

    /**
     * Create an instance of {@link CtRespostaGlosa.ReciboGlosaStatus }
     * 
     */
    public CtRespostaGlosa.ReciboGlosaStatus createCtRespostaGlosaReciboGlosaStatus() {
        return new CtRespostaGlosa.ReciboGlosaStatus();
    }

    /**
     * Create an instance of {@link CtmInternacaoSolicitacaoGuia.IdentificacaoSolicitante }
     * 
     */
    public CtmInternacaoSolicitacaoGuia.IdentificacaoSolicitante createCtmInternacaoSolicitacaoGuiaIdentificacaoSolicitante() {
        return new CtmInternacaoSolicitacaoGuia.IdentificacaoSolicitante();
    }

    /**
     * Create an instance of {@link CtoAnexoSituacaoInicialnaGTO }
     * 
     */
    public CtoAnexoSituacaoInicialnaGTO createCtoAnexoSituacaoInicialnaGTO() {
        return new CtoAnexoSituacaoInicialnaGTO();
    }

    /**
     * Create an instance of {@link CtmGuiaLote }
     * 
     */
    public CtmGuiaLote createCtmGuiaLote() {
        return new CtmGuiaLote();
    }

    /**
     * Create an instance of {@link CabecalhoTransacao.IdentificacaoTransacao }
     * 
     */
    public CabecalhoTransacao.IdentificacaoTransacao createCabecalhoTransacaoIdentificacaoTransacao() {
        return new CabecalhoTransacao.IdentificacaoTransacao();
    }

    /**
     * Create an instance of {@link CabecalhoTransacao.Origem }
     * 
     */
    public CabecalhoTransacao.Origem createCabecalhoTransacaoOrigem() {
        return new CabecalhoTransacao.Origem();
    }

    /**
     * Create an instance of {@link CtContaMedicaResumo.RelacaoGuias }
     * 
     */
    public CtContaMedicaResumo.RelacaoGuias createCtContaMedicaResumoRelacaoGuias() {
        return new CtContaMedicaResumo.RelacaoGuias();
    }

    /**
     * Create an instance of {@link CtOutrasDespesas.Despesa }
     * 
     */
    public CtOutrasDespesas.Despesa createCtOutrasDespesasDespesa() {
        return new CtOutrasDespesas.Despesa();
    }

    /**
     * Create an instance of {@link CtmProrrogacaoSolicitacaoGuia.AnexoClinicoProrrogacao }
     * 
     */
    public CtmProrrogacaoSolicitacaoGuia.AnexoClinicoProrrogacao createCtmProrrogacaoSolicitacaoGuiaAnexoClinicoProrrogacao() {
        return new CtmProrrogacaoSolicitacaoGuia.AnexoClinicoProrrogacao();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoServico }
     * 
     */
    public CtmAutorizacaoServico createCtmAutorizacaoServico() {
        return new CtmAutorizacaoServico();
    }

    /**
     * Create an instance of {@link TransformsType }
     * 
     */
    public TransformsType createTransformsType() {
        return new TransformsType();
    }

    /**
     * Create an instance of {@link CtmDemonstrativoAnaliseConta.DadosPrestador }
     * 
     */
    public CtmDemonstrativoAnaliseConta.DadosPrestador createCtmDemonstrativoAnaliseContaDadosPrestador() {
        return new CtmDemonstrativoAnaliseConta.DadosPrestador();
    }

    /**
     * Create an instance of {@link CtmBeneficiarioComunicacaoRecibo }
     * 
     */
    public CtmBeneficiarioComunicacaoRecibo createCtmBeneficiarioComunicacaoRecibo() {
        return new CtmBeneficiarioComunicacaoRecibo();
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia.DadosPagamentoPorData.Protocolos.DadosPagamentoGuia.DadosPagamento }
     * 
     */
    public CtoDemonstrativoOdontologia.DadosPagamentoPorData.Protocolos.DadosPagamentoGuia.DadosPagamento createCtoDemonstrativoOdontologiaDadosPagamentoPorDataProtocolosDadosPagamentoGuiaDadosPagamento() {
        return new CtoDemonstrativoOdontologia.DadosPagamentoPorData.Protocolos.DadosPagamentoGuia.DadosPagamento();
    }

    /**
     * Create an instance of {@link CtDemonstrativoSolicitacao.DemonstrativoPagamento.Periodo }
     * 
     */
    public CtDemonstrativoSolicitacao.DemonstrativoPagamento.Periodo createCtDemonstrativoSolicitacaoDemonstrativoPagamentoPeriodo() {
        return new CtDemonstrativoSolicitacao.DemonstrativoPagamento.Periodo();
    }

    /**
     * Create an instance of {@link CtGuiaCancelamento }
     * 
     */
    public CtGuiaCancelamento createCtGuiaCancelamento() {
        return new CtGuiaCancelamento();
    }

    /**
     * Create an instance of {@link CtPrestadorIdentificacao }
     * 
     */
    public CtPrestadorIdentificacao createCtPrestadorIdentificacao() {
        return new CtPrestadorIdentificacao();
    }

    /**
     * Create an instance of {@link CtProtocoloDetalhe }
     * 
     */
    public CtProtocoloDetalhe createCtProtocoloDetalhe() {
        return new CtProtocoloDetalhe();
    }

    /**
     * Create an instance of {@link CtGlosaRecibo.OpcaoRecurso.RecursoProtocolo }
     * 
     */
    public CtGlosaRecibo.OpcaoRecurso.RecursoProtocolo createCtGlosaReciboOpcaoRecursoRecursoProtocolo() {
        return new CtGlosaRecibo.OpcaoRecurso.RecursoProtocolo();
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia.DadosPagamentoPorData.DadosPagamento }
     * 
     */
    public CtoDemonstrativoOdontologia.DadosPagamentoPorData.DadosPagamento createCtoDemonstrativoOdontologiaDadosPagamentoPorDataDadosPagamento() {
        return new CtoDemonstrativoOdontologia.DadosPagamentoPorData.DadosPagamento();
    }

    /**
     * Create an instance of {@link CabecalhoTransacao.Destino }
     * 
     */
    public CabecalhoTransacao.Destino createCabecalhoTransacaoDestino() {
        return new CabecalhoTransacao.Destino();
    }

    /**
     * Create an instance of {@link CtIntervaloCiclos }
     * 
     */
    public CtIntervaloCiclos createCtIntervaloCiclos() {
        return new CtIntervaloCiclos();
    }

    /**
     * Create an instance of {@link CtIdentEquipeSADT }
     * 
     */
    public CtIdentEquipeSADT createCtIdentEquipeSADT() {
        return new CtIdentEquipeSADT();
    }

    /**
     * Create an instance of {@link CtProcedimentoExecutadoInt }
     * 
     */
    public CtProcedimentoExecutadoInt createCtProcedimentoExecutadoInt() {
        return new CtProcedimentoExecutadoInt();
    }

    /**
     * Create an instance of {@link CtoOdontoSolicitacaoGuia.ProcedimentosSolicitados }
     * 
     */
    public CtoOdontoSolicitacaoGuia.ProcedimentosSolicitados createCtoOdontoSolicitacaoGuiaProcedimentosSolicitados() {
        return new CtoOdontoSolicitacaoGuia.ProcedimentosSolicitados();
    }

    /**
     * Create an instance of {@link KeyInfoType }
     * 
     */
    public KeyInfoType createKeyInfoType() {
        return new KeyInfoType();
    }

    /**
     * Create an instance of {@link CtmBeneficiarioComunicacao.DadosInternacao }
     * 
     */
    public CtmBeneficiarioComunicacao.DadosInternacao createCtmBeneficiarioComunicacaoDadosInternacao() {
        return new CtmBeneficiarioComunicacao.DadosInternacao();
    }

    /**
     * Create an instance of {@link CtHipoteseDiagnostica }
     * 
     */
    public CtHipoteseDiagnostica createCtHipoteseDiagnostica() {
        return new CtHipoteseDiagnostica();
    }

    /**
     * Create an instance of {@link CtProtocoloDetalhe.GlosaProtocolo }
     * 
     */
    public CtProtocoloDetalhe.GlosaProtocolo createCtProtocoloDetalheGlosaProtocolo() {
        return new CtProtocoloDetalhe.GlosaProtocolo();
    }

    /**
     * Create an instance of {@link CtoAutorizacaoServico.ProcedimentosAutorizados.DenteRegiao }
     * 
     */
    public CtoAutorizacaoServico.ProcedimentosAutorizados.DenteRegiao createCtoAutorizacaoServicoProcedimentosAutorizadosDenteRegiao() {
        return new CtoAutorizacaoServico.ProcedimentosAutorizados.DenteRegiao();
    }

    /**
     * Create an instance of {@link CtGlosaReciboOdonto.DadosContratado }
     * 
     */
    public CtGlosaReciboOdonto.DadosContratado createCtGlosaReciboOdontoDadosContratado() {
        return new CtGlosaReciboOdonto.DadosContratado();
    }

    /**
     * Create an instance of {@link CtContratadoDados }
     * 
     */
    public CtContratadoDados createCtContratadoDados() {
        return new CtContratadoDados();
    }

    /**
     * Create an instance of {@link CtSituacaoProtocolo }
     * 
     */
    public CtSituacaoProtocolo createCtSituacaoProtocolo() {
        return new CtSituacaoProtocolo();
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia.DadosPagamentoPorData.DebCredPorDataPagamento }
     * 
     */
    public CtoDemonstrativoOdontologia.DadosPagamentoPorData.DebCredPorDataPagamento createCtoDemonstrativoOdontologiaDadosPagamentoPorDataDebCredPorDataPagamento() {
        return new CtoDemonstrativoOdontologia.DadosPagamentoPorData.DebCredPorDataPagamento();
    }

    /**
     * Create an instance of {@link CtProtocoloDetalhe.DadosGuiasProtocolo }
     * 
     */
    public CtProtocoloDetalhe.DadosGuiasProtocolo createCtProtocoloDetalheDadosGuiasProtocolo() {
        return new CtProtocoloDetalhe.DadosGuiasProtocolo();
    }

    /**
     * Create an instance of {@link CtGuiaCancelamento.GuiasCancelamento }
     * 
     */
    public CtGuiaCancelamento.GuiasCancelamento createCtGuiaCancelamentoGuiasCancelamento() {
        return new CtGuiaCancelamento.GuiasCancelamento();
    }

    /**
     * Create an instance of {@link PrestadorOperadora }
     * 
     */
    public PrestadorOperadora createPrestadorOperadora() {
        return new PrestadorOperadora();
    }

    /**
     * Create an instance of {@link CtValorCreditoDesconto }
     * 
     */
    public CtValorCreditoDesconto createCtValorCreditoDesconto() {
        return new CtValorCreditoDesconto();
    }

    /**
     * Create an instance of {@link CtGuiaDadosAnexo.GlosaAnexo }
     * 
     */
    public CtGuiaDadosAnexo.GlosaAnexo createCtGuiaDadosAnexoGlosaAnexo() {
        return new CtGuiaDadosAnexo.GlosaAnexo();
    }

    /**
     * Create an instance of {@link CtRespostaGlosaItemMedico }
     * 
     */
    public CtRespostaGlosaItemMedico createCtRespostaGlosaItemMedico() {
        return new CtRespostaGlosaItemMedico();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoOPME }
     * 
     */
    public CtmAutorizacaoOPME createCtmAutorizacaoOPME() {
        return new CtmAutorizacaoOPME();
    }

    /**
     * Create an instance of {@link CtmSpSadtGuia.CabecalhoGuia }
     * 
     */
    public CtmSpSadtGuia.CabecalhoGuia createCtmSpSadtGuiaCabecalhoGuia() {
        return new CtmSpSadtGuia.CabecalhoGuia();
    }

    /**
     * Create an instance of {@link CtProtocoloDetalhe.GlosaProtocolo.MotivosGlosa }
     * 
     */
    public CtProtocoloDetalhe.GlosaProtocolo.MotivosGlosa createCtProtocoloDetalheGlosaProtocoloMotivosGlosa() {
        return new CtProtocoloDetalhe.GlosaProtocolo.MotivosGlosa();
    }

    /**
     * Create an instance of {@link CtmConsultaGuia.ContratadoExecutante }
     * 
     */
    public CtmConsultaGuia.ContratadoExecutante createCtmConsultaGuiaContratadoExecutante() {
        return new CtmConsultaGuia.ContratadoExecutante();
    }

    /**
     * Create an instance of {@link CtmSpSadtGuia.DadosExecutante }
     * 
     */
    public CtmSpSadtGuia.DadosExecutante createCtmSpSadtGuiaDadosExecutante() {
        return new CtmSpSadtGuia.DadosExecutante();
    }

    /**
     * Create an instance of {@link CtProtocoloStatus }
     * 
     */
    public CtProtocoloStatus createCtProtocoloStatus() {
        return new CtProtocoloStatus();
    }

    /**
     * Create an instance of {@link CtAutorizacaoDados }
     * 
     */
    public CtAutorizacaoDados createCtAutorizacaoDados() {
        return new CtAutorizacaoDados();
    }

    /**
     * Create an instance of {@link CtoGuiaOdontologia.ProcedimentosExecutados.DenteRegiao }
     * 
     */
    public CtoGuiaOdontologia.ProcedimentosExecutados.DenteRegiao createCtoGuiaOdontologiaProcedimentosExecutadosDenteRegiao() {
        return new CtoGuiaOdontologia.ProcedimentosExecutados.DenteRegiao();
    }

    /**
     * Create an instance of {@link CtmConsultaAtendimento }
     * 
     */
    public CtmConsultaAtendimento createCtmConsultaAtendimento() {
        return new CtmConsultaAtendimento();
    }

    /**
     * Create an instance of {@link CtProtocoloRecurso }
     * 
     */
    public CtProtocoloRecurso createCtProtocoloRecurso() {
        return new CtProtocoloRecurso();
    }

    /**
     * Create an instance of {@link CtElegibilidadeVerifica }
     * 
     */
    public CtElegibilidadeVerifica createCtElegibilidadeVerifica() {
        return new CtElegibilidadeVerifica();
    }

    /**
     * Create an instance of {@link CtGuiaDados.GlosaGuia }
     * 
     */
    public CtGuiaDados.GlosaGuia createCtGuiaDadosGlosaGuia() {
        return new CtGuiaDados.GlosaGuia();
    }

    /**
     * Create an instance of {@link CtProcedimentoAutorizado }
     * 
     */
    public CtProcedimentoAutorizado createCtProcedimentoAutorizado() {
        return new CtProcedimentoAutorizado();
    }

    /**
     * Create an instance of {@link CtoAutorizacaoServico.MotivosNegativa }
     * 
     */
    public CtoAutorizacaoServico.MotivosNegativa createCtoAutorizacaoServicoMotivosNegativa() {
        return new CtoAutorizacaoServico.MotivosNegativa();
    }

    /**
     * Create an instance of {@link CtoAutorizacaoServico }
     * 
     */
    public CtoAutorizacaoServico createCtoAutorizacaoServico() {
        return new CtoAutorizacaoServico();
    }

    /**
     * Create an instance of {@link CtoRecursoGlosaOdonto.OpcaoRecurso.RecursoGuia }
     * 
     */
    public CtoRecursoGlosaOdonto.OpcaoRecurso.RecursoGuia createCtoRecursoGlosaOdontoOpcaoRecursoRecursoGuia() {
        return new CtoRecursoGlosaOdonto.OpcaoRecurso.RecursoGuia();
    }

    /**
     * Create an instance of {@link CtAnexoLote }
     * 
     */
    public CtAnexoLote createCtAnexoLote() {
        return new CtAnexoLote();
    }

    /**
     * Create an instance of {@link CtOpmeDados }
     * 
     */
    public CtOpmeDados createCtOpmeDados() {
        return new CtOpmeDados();
    }

    /**
     * Create an instance of {@link CtmInternacaoResumoGuia.ProcedimentosExecutados }
     * 
     */
    public CtmInternacaoResumoGuia.ProcedimentosExecutados createCtmInternacaoResumoGuiaProcedimentosExecutados() {
        return new CtmInternacaoResumoGuia.ProcedimentosExecutados();
    }

    /**
     * Create an instance of {@link CtoOdontoSolicitacaoGuia.ProcedimentosSolicitados.DenteRegiao }
     * 
     */
    public CtoOdontoSolicitacaoGuia.ProcedimentosSolicitados.DenteRegiao createCtoOdontoSolicitacaoGuiaProcedimentosSolicitadosDenteRegiao() {
        return new CtoOdontoSolicitacaoGuia.ProcedimentosSolicitados.DenteRegiao();
    }

    /**
     * Create an instance of {@link CtProcedimentoExecutadoOdonto.DenteRegiao }
     * 
     */
    public CtProcedimentoExecutadoOdonto.DenteRegiao createCtProcedimentoExecutadoOdontoDenteRegiao() {
        return new CtProcedimentoExecutadoOdonto.DenteRegiao();
    }

    /**
     * Create an instance of {@link CtmSpSadtSolicitacaoGuia }
     * 
     */
    public CtmSpSadtSolicitacaoGuia createCtmSpSadtSolicitacaoGuia() {
        return new CtmSpSadtSolicitacaoGuia();
    }

    /**
     * Create an instance of {@link CtProcedimentoExecutadoOutras }
     * 
     */
    public CtProcedimentoExecutadoOutras createCtProcedimentoExecutadoOutras() {
        return new CtProcedimentoExecutadoOutras();
    }

    /**
     * Create an instance of {@link CtAnexoRecebimento.AnexosClinicos }
     * 
     */
    public CtAnexoRecebimento.AnexosClinicos createCtAnexoRecebimentoAnexosClinicos() {
        return new CtAnexoRecebimento.AnexosClinicos();
    }

    /**
     * Create an instance of {@link CtIdentEquipe.CodProfissional }
     * 
     */
    public CtIdentEquipe.CodProfissional createCtIdentEquipeCodProfissional() {
        return new CtIdentEquipe.CodProfissional();
    }

    /**
     * Create an instance of {@link CtProtocoloSolicitacaoStatus }
     * 
     */
    public CtProtocoloSolicitacaoStatus createCtProtocoloSolicitacaoStatus() {
        return new CtProtocoloSolicitacaoStatus();
    }

    /**
     * Create an instance of {@link CtmSpSadtGuia.DadosSolicitacao }
     * 
     */
    public CtmSpSadtGuia.DadosSolicitacao createCtmSpSadtGuiaDadosSolicitacao() {
        return new CtmSpSadtGuia.DadosSolicitacao();
    }

    /**
     * Create an instance of {@link OperadoraPrestador.RecebimentoAnexo }
     * 
     */
    public OperadoraPrestador.RecebimentoAnexo createOperadoraPrestadorRecebimentoAnexo() {
        return new OperadoraPrestador.RecebimentoAnexo();
    }

    /**
     * Create an instance of {@link CtRespostaRecursoItemOdonto.RecursoProcedimento }
     * 
     */
    public CtRespostaRecursoItemOdonto.RecursoProcedimento createCtRespostaRecursoItemOdontoRecursoProcedimento() {
        return new CtRespostaRecursoItemOdonto.RecursoProcedimento();
    }

    /**
     * Create an instance of {@link CtRecursoGlosaRecebimento }
     * 
     */
    public CtRecursoGlosaRecebimento createCtRecursoGlosaRecebimento() {
        return new CtRecursoGlosaRecebimento();
    }

    /**
     * Create an instance of {@link CtLoteStatus.GuiasTISS.GuiasOdonto }
     * 
     */
    public CtLoteStatus.GuiasTISS.GuiasOdonto createCtLoteStatusGuiasTISSGuiasOdonto() {
        return new CtLoteStatus.GuiasTISS.GuiasOdonto();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoQuimio }
     * 
     */
    public CtmAutorizacaoQuimio createCtmAutorizacaoQuimio() {
        return new CtmAutorizacaoQuimio();
    }

    /**
     * Create an instance of {@link CtoRecursoGlosaOdonto.OpcaoRecurso }
     * 
     */
    public CtoRecursoGlosaOdonto.OpcaoRecurso createCtoRecursoGlosaOdontoOpcaoRecurso() {
        return new CtoRecursoGlosaOdonto.OpcaoRecurso();
    }

    /**
     * Create an instance of {@link CtProcedimentoDados }
     * 
     */
    public CtProcedimentoDados createCtProcedimentoDados() {
        return new CtProcedimentoDados();
    }

    /**
     * Create an instance of {@link CtmInternacaoResumoGuia.DadosExecutante }
     * 
     */
    public CtmInternacaoResumoGuia.DadosExecutante createCtmInternacaoResumoGuiaDadosExecutante() {
        return new CtmInternacaoResumoGuia.DadosExecutante();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoQuimio.DrogasSolicitadas }
     * 
     */
    public CtmAutorizacaoQuimio.DrogasSolicitadas createCtmAutorizacaoQuimioDrogasSolicitadas() {
        return new CtmAutorizacaoQuimio.DrogasSolicitadas();
    }

    /**
     * Create an instance of {@link CabecalhoTransacao.Origem.IdentificacaoPrestador }
     * 
     */
    public CabecalhoTransacao.Origem.IdentificacaoPrestador createCabecalhoTransacaoOrigemIdentificacaoPrestador() {
        return new CabecalhoTransacao.Origem.IdentificacaoPrestador();
    }

    /**
     * Create an instance of {@link CtmRecursoGlosa.OpcaoRecurso.RecursoGuia.OpcaoRecursoGuia.ItensGuia }
     * 
     */
    public CtmRecursoGlosa.OpcaoRecurso.RecursoGuia.OpcaoRecursoGuia.ItensGuia createCtmRecursoGlosaOpcaoRecursoRecursoGuiaOpcaoRecursoGuiaItensGuia() {
        return new CtmRecursoGlosa.OpcaoRecurso.RecursoGuia.OpcaoRecursoGuia.ItensGuia();
    }

    /**
     * Create an instance of {@link CtmProrrogacaoSolicitacaoGuia.DadosInternacao }
     * 
     */
    public CtmProrrogacaoSolicitacaoGuia.DadosInternacao createCtmProrrogacaoSolicitacaoGuiaDadosInternacao() {
        return new CtmProrrogacaoSolicitacaoGuia.DadosInternacao();
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia.DadosPagamentoPorData.Protocolos.DadosPagamentoGuia }
     * 
     */
    public CtoDemonstrativoOdontologia.DadosPagamentoPorData.Protocolos.DadosPagamentoGuia createCtoDemonstrativoOdontologiaDadosPagamentoPorDataProtocolosDadosPagamentoGuia() {
        return new CtoDemonstrativoOdontologia.DadosPagamentoPorData.Protocolos.DadosPagamentoGuia();
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia.DadosPrestador.CpfCNPJContratado }
     * 
     */
    public CtoDemonstrativoOdontologia.DadosPrestador.CpfCNPJContratado createCtoDemonstrativoOdontologiaDadosPrestadorCpfCNPJContratado() {
        return new CtoDemonstrativoOdontologia.DadosPrestador.CpfCNPJContratado();
    }

    /**
     * Create an instance of {@link CtRecursoGlosaRecebimento.RecursoProtocolo }
     * 
     */
    public CtRecursoGlosaRecebimento.RecursoProtocolo createCtRecursoGlosaRecebimentoRecursoProtocolo() {
        return new CtRecursoGlosaRecebimento.RecursoProtocolo();
    }

    /**
     * Create an instance of {@link CtGlosaReciboOdonto.OpcaoRecurso.RecursoGuia }
     * 
     */
    public CtGlosaReciboOdonto.OpcaoRecurso.RecursoGuia createCtGlosaReciboOdontoOpcaoRecursoRecursoGuia() {
        return new CtGlosaReciboOdonto.OpcaoRecurso.RecursoGuia();
    }

    /**
     * Create an instance of {@link CtAnexoRecebimento }
     * 
     */
    public CtAnexoRecebimento createCtAnexoRecebimento() {
        return new CtAnexoRecebimento();
    }

    /**
     * Create an instance of {@link CtmDemonstrativoPagamento.TotaisDemonstrativo.TotaisBrutosDemonstrativo }
     * 
     */
    public CtmDemonstrativoPagamento.TotaisDemonstrativo.TotaisBrutosDemonstrativo createCtmDemonstrativoPagamentoTotaisDemonstrativoTotaisBrutosDemonstrativo() {
        return new CtmDemonstrativoPagamento.TotaisDemonstrativo.TotaisBrutosDemonstrativo();
    }

    /**
     * Create an instance of {@link CtGlosaRecibo.OpcaoRecurso.RecursoGuia }
     * 
     */
    public CtGlosaRecibo.OpcaoRecurso.RecursoGuia createCtGlosaReciboOpcaoRecursoRecursoGuia() {
        return new CtGlosaRecibo.OpcaoRecurso.RecursoGuia();
    }

    /**
     * Create an instance of {@link CtGuiaDadosAnexo.ProcedimentosSolicitados }
     * 
     */
    public CtGuiaDadosAnexo.ProcedimentosSolicitados createCtGuiaDadosAnexoProcedimentosSolicitados() {
        return new CtGuiaDadosAnexo.ProcedimentosSolicitados();
    }

    /**
     * Create an instance of {@link TransformType }
     * 
     */
    public TransformType createTransformType() {
        return new TransformType();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoRadio.DiagnosticoOncologicoRadio }
     * 
     */
    public CtmAutorizacaoRadio.DiagnosticoOncologicoRadio createCtmAutorizacaoRadioDiagnosticoOncologicoRadio() {
        return new CtmAutorizacaoRadio.DiagnosticoOncologicoRadio();
    }

    /**
     * Create an instance of {@link CtRespostaElegibilidade }
     * 
     */
    public CtRespostaElegibilidade createCtRespostaElegibilidade() {
        return new CtRespostaElegibilidade();
    }

    /**
     * Create an instance of {@link CtmSpSadtGuia.DadosSolicitante }
     * 
     */
    public CtmSpSadtGuia.DadosSolicitante createCtmSpSadtGuiaDadosSolicitante() {
        return new CtmSpSadtGuia.DadosSolicitante();
    }

    /**
     * Create an instance of {@link CtDiagnostico }
     * 
     */
    public CtDiagnostico createCtDiagnostico() {
        return new CtDiagnostico();
    }

    /**
     * Create an instance of {@link CtmSpSadtGuia }
     * 
     */
    public CtmSpSadtGuia createCtmSpSadtGuia() {
        return new CtmSpSadtGuia();
    }

    /**
     * Create an instance of {@link CtSituacaoClinica.Dentes }
     * 
     */
    public CtSituacaoClinica.Dentes createCtSituacaoClinicaDentes() {
        return new CtSituacaoClinica.Dentes();
    }

    /**
     * Create an instance of {@link CtGuiaValorTotalSADT }
     * 
     */
    public CtGuiaValorTotalSADT createCtGuiaValorTotalSADT() {
        return new CtGuiaValorTotalSADT();
    }

    /**
     * Create an instance of {@link CanonicalizationMethodType }
     * 
     */
    public CanonicalizationMethodType createCanonicalizationMethodType() {
        return new CanonicalizationMethodType();
    }

    /**
     * Create an instance of {@link CtoOdontoSolicitacaoGuia.DadosProfissionaisResponsaveis }
     * 
     */
    public CtoOdontoSolicitacaoGuia.DadosProfissionaisResponsaveis createCtoOdontoSolicitacaoGuiaDadosProfissionaisResponsaveis() {
        return new CtoOdontoSolicitacaoGuia.DadosProfissionaisResponsaveis();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoServico.ServicosAutorizados }
     * 
     */
    public CtmAutorizacaoServico.ServicosAutorizados createCtmAutorizacaoServicoServicosAutorizados() {
        return new CtmAutorizacaoServico.ServicosAutorizados();
    }

    /**
     * Create an instance of {@link CtmAnexoSolicitacaoRadio.DiagnosticoOncologicoRadio }
     * 
     */
    public CtmAnexoSolicitacaoRadio.DiagnosticoOncologicoRadio createCtmAnexoSolicitacaoRadioDiagnosticoOncologicoRadio() {
        return new CtmAnexoSolicitacaoRadio.DiagnosticoOncologicoRadio();
    }

    /**
     * Create an instance of {@link CtGuiaDados }
     * 
     */
    public CtGuiaDados createCtGuiaDados() {
        return new CtGuiaDados();
    }

    /**
     * Create an instance of {@link CtGuiaCabecalho }
     * 
     */
    public CtGuiaCabecalho createCtGuiaCabecalho() {
        return new CtGuiaCabecalho();
    }

    /**
     * Create an instance of {@link CtIdentEquipe }
     * 
     */
    public CtIdentEquipe createCtIdentEquipe() {
        return new CtIdentEquipe();
    }

    /**
     * Create an instance of {@link CtGlosaReciboOdonto }
     * 
     */
    public CtGlosaReciboOdonto createCtGlosaReciboOdonto() {
        return new CtGlosaReciboOdonto();
    }

    /**
     * Create an instance of {@link CtmInternacaoSolicitacaoGuia.HipotesesDiagnosticas }
     * 
     */
    public CtmInternacaoSolicitacaoGuia.HipotesesDiagnosticas createCtmInternacaoSolicitacaoGuiaHipotesesDiagnosticas() {
        return new CtmInternacaoSolicitacaoGuia.HipotesesDiagnosticas();
    }

    /**
     * Create an instance of {@link CtValorTotal }
     * 
     */
    public CtValorTotal createCtValorTotal() {
        return new CtValorTotal();
    }

    /**
     * Create an instance of {@link CtLoginSenha }
     * 
     */
    public CtLoginSenha createCtLoginSenha() {
        return new CtLoginSenha();
    }

    /**
     * Create an instance of {@link CtmHonorarioIndividualGuia.LocalContratado.CodigoContratado }
     * 
     */
    public CtmHonorarioIndividualGuia.LocalContratado.CodigoContratado createCtmHonorarioIndividualGuiaLocalContratadoCodigoContratado() {
        return new CtmHonorarioIndividualGuia.LocalContratado.CodigoContratado();
    }

    /**
     * Create an instance of {@link CtIdentEquipeSADT.CodProfissional }
     * 
     */
    public CtIdentEquipeSADT.CodProfissional createCtIdentEquipeSADTCodProfissional() {
        return new CtIdentEquipeSADT.CodProfissional();
    }

    /**
     * Create an instance of {@link CtDemonstrativoRetorno }
     * 
     */
    public CtDemonstrativoRetorno createCtDemonstrativoRetorno() {
        return new CtDemonstrativoRetorno();
    }

    /**
     * Create an instance of {@link CtmInternacaoDados.Declaracoes }
     * 
     */
    public CtmInternacaoDados.Declaracoes createCtmInternacaoDadosDeclaracoes() {
        return new CtmInternacaoDados.Declaracoes();
    }

    /**
     * Create an instance of {@link CtoGuiaOdontologia }
     * 
     */
    public CtoGuiaOdontologia createCtoGuiaOdontologia() {
        return new CtoGuiaOdontologia();
    }

    /**
     * Create an instance of {@link CtDiagnosticoOncologico }
     * 
     */
    public CtDiagnosticoOncologico createCtDiagnosticoOncologico() {
        return new CtDiagnosticoOncologico();
    }

    /**
     * Create an instance of {@link CtmAnexoSolicitacaoOPME }
     * 
     */
    public CtmAnexoSolicitacaoOPME createCtmAnexoSolicitacaoOPME() {
        return new CtmAnexoSolicitacaoOPME();
    }

    /**
     * Create an instance of {@link CtmAnexoSolicitacaoQuimio.DrogasSolicitadas }
     * 
     */
    public CtmAnexoSolicitacaoQuimio.DrogasSolicitadas createCtmAnexoSolicitacaoQuimioDrogasSolicitadas() {
        return new CtmAnexoSolicitacaoQuimio.DrogasSolicitadas();
    }

    /**
     * Create an instance of {@link CtGlosaReciboOdonto.OpcaoRecurso.RecursoProtocolo }
     * 
     */
    public CtGlosaReciboOdonto.OpcaoRecurso.RecursoProtocolo createCtGlosaReciboOdontoOpcaoRecursoRecursoProtocolo() {
        return new CtGlosaReciboOdonto.OpcaoRecurso.RecursoProtocolo();
    }

    /**
     * Create an instance of {@link CtProtocoloAnexoStatus }
     * 
     */
    public CtProtocoloAnexoStatus createCtProtocoloAnexoStatus() {
        return new CtProtocoloAnexoStatus();
    }

    /**
     * Create an instance of {@link CtmDemonstrativoPagamento }
     * 
     */
    public CtmDemonstrativoPagamento createCtmDemonstrativoPagamento() {
        return new CtmDemonstrativoPagamento();
    }

    /**
     * Create an instance of {@link CtDrogasSolicitadas }
     * 
     */
    public CtDrogasSolicitadas createCtDrogasSolicitadas() {
        return new CtDrogasSolicitadas();
    }

    /**
     * Create an instance of {@link CtProtocoloStatus.Lote }
     * 
     */
    public CtProtocoloStatus.Lote createCtProtocoloStatusLote() {
        return new CtProtocoloStatus.Lote();
    }

    /**
     * Create an instance of {@link CtAutorizacaoSolicitaStatus }
     * 
     */
    public CtAutorizacaoSolicitaStatus createCtAutorizacaoSolicitaStatus() {
        return new CtAutorizacaoSolicitaStatus();
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia.DadosPagamentoPorData.TotalLiquidoPorData }
     * 
     */
    public CtoDemonstrativoOdontologia.DadosPagamentoPorData.TotalLiquidoPorData createCtoDemonstrativoOdontologiaDadosPagamentoPorDataTotalLiquidoPorData() {
        return new CtoDemonstrativoOdontologia.DadosPagamentoPorData.TotalLiquidoPorData();
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia.CabecalhoDemonstrativoOdonto.PeriodoProc }
     * 
     */
    public CtoDemonstrativoOdontologia.CabecalhoDemonstrativoOdonto.PeriodoProc createCtoDemonstrativoOdontologiaCabecalhoDemonstrativoOdontoPeriodoProc() {
        return new CtoDemonstrativoOdontologia.CabecalhoDemonstrativoOdonto.PeriodoProc();
    }

    /**
     * Create an instance of {@link RetrievalMethodType }
     * 
     */
    public RetrievalMethodType createRetrievalMethodType() {
        return new RetrievalMethodType();
    }

    /**
     * Create an instance of {@link CtReciboCancelaGuia }
     * 
     */
    public CtReciboCancelaGuia createCtReciboCancelaGuia() {
        return new CtReciboCancelaGuia();
    }

    /**
     * Create an instance of {@link CtGuiaDadosAnexo.ProcedimentosSolicitados.ProcedimentoSolicitado.GlosasProcedimento }
     * 
     */
    public CtGuiaDadosAnexo.ProcedimentosSolicitados.ProcedimentoSolicitado.GlosasProcedimento createCtGuiaDadosAnexoProcedimentosSolicitadosProcedimentoSolicitadoGlosasProcedimento() {
        return new CtGuiaDadosAnexo.ProcedimentosSolicitados.ProcedimentoSolicitado.GlosasProcedimento();
    }

    /**
     * Create an instance of {@link CtDemonstrativoSolicitacao.DemonstrativoPagamento }
     * 
     */
    public CtDemonstrativoSolicitacao.DemonstrativoPagamento createCtDemonstrativoSolicitacaoDemonstrativoPagamento() {
        return new CtDemonstrativoSolicitacao.DemonstrativoPagamento();
    }

    /**
     * Create an instance of {@link CtmRecursoGlosa }
     * 
     */
    public CtmRecursoGlosa createCtmRecursoGlosa() {
        return new CtmRecursoGlosa();
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia.DadosPagamentoPorData }
     * 
     */
    public CtoDemonstrativoOdontologia.DadosPagamentoPorData createCtoDemonstrativoOdontologiaDadosPagamentoPorData() {
        return new CtoDemonstrativoOdontologia.DadosPagamentoPorData();
    }

    /**
     * Create an instance of {@link CtProcedimentoAutorizado.MotivosNegativa }
     * 
     */
    public CtProcedimentoAutorizado.MotivosNegativa createCtProcedimentoAutorizadoMotivosNegativa() {
        return new CtProcedimentoAutorizado.MotivosNegativa();
    }

    /**
     * Create an instance of {@link CtGuiaCancelamentoRecibo.GuiasCanceladas }
     * 
     */
    public CtGuiaCancelamentoRecibo.GuiasCanceladas createCtGuiaCancelamentoReciboGuiasCanceladas() {
        return new CtGuiaCancelamentoRecibo.GuiasCanceladas();
    }

    /**
     * Create an instance of {@link CtGuiaDados.ProcedimentosRealizados.ProcedimentoRealizado }
     * 
     */
    public CtGuiaDados.ProcedimentosRealizados.ProcedimentoRealizado createCtGuiaDadosProcedimentosRealizadosProcedimentoRealizado() {
        return new CtGuiaDados.ProcedimentosRealizados.ProcedimentoRealizado();
    }

    /**
     * Create an instance of {@link CtmDemonstrativoPagamento.Pagamentos.PagamentosPorData.DebitosCreditosPorData }
     * 
     */
    public CtmDemonstrativoPagamento.Pagamentos.PagamentosPorData.DebitosCreditosPorData createCtmDemonstrativoPagamentoPagamentosPagamentosPorDataDebitosCreditosPorData() {
        return new CtmDemonstrativoPagamento.Pagamentos.PagamentosPorData.DebitosCreditosPorData();
    }

    /**
     * Create an instance of {@link CtDemonstrativoRetorno.SituacaoDemonstrativoRetorno }
     * 
     */
    public CtDemonstrativoRetorno.SituacaoDemonstrativoRetorno createCtDemonstrativoRetornoSituacaoDemonstrativoRetorno() {
        return new CtDemonstrativoRetorno.SituacaoDemonstrativoRetorno();
    }

    /**
     * Create an instance of {@link CtRespostaRecursoGuiaOdonto }
     * 
     */
    public CtRespostaRecursoGuiaOdonto createCtRespostaRecursoGuiaOdonto() {
        return new CtRespostaRecursoGuiaOdonto();
    }

    /**
     * Create an instance of {@link CtmHonorarioIndividualGuia.DadosContratadoExecutante }
     * 
     */
    public CtmHonorarioIndividualGuia.DadosContratadoExecutante createCtmHonorarioIndividualGuiaDadosContratadoExecutante() {
        return new CtmHonorarioIndividualGuia.DadosContratadoExecutante();
    }

    /**
     * Create an instance of {@link SignaturePropertyType }
     * 
     */
    public SignaturePropertyType createSignaturePropertyType() {
        return new SignaturePropertyType();
    }

    /**
     * Create an instance of {@link CtAutorizacaoSADT }
     * 
     */
    public CtAutorizacaoSADT createCtAutorizacaoSADT() {
        return new CtAutorizacaoSADT();
    }

    /**
     * Create an instance of {@link CtoRecursoGlosaOdonto.OpcaoRecurso.RecursoProtocolo }
     * 
     */
    public CtoRecursoGlosaOdonto.OpcaoRecurso.RecursoProtocolo createCtoRecursoGlosaOdontoOpcaoRecursoRecursoProtocolo() {
        return new CtoRecursoGlosaOdonto.OpcaoRecurso.RecursoProtocolo();
    }

    /**
     * Create an instance of {@link CtDemonstrativoCabecalho }
     * 
     */
    public CtDemonstrativoCabecalho createCtDemonstrativoCabecalho() {
        return new CtDemonstrativoCabecalho();
    }

    /**
     * Create an instance of {@link CtGuiaDadosOdonto.GlosaGuia }
     * 
     */
    public CtGuiaDadosOdonto.GlosaGuia createCtGuiaDadosOdontoGlosaGuia() {
        return new CtGuiaDadosOdonto.GlosaGuia();
    }

    /**
     * Create an instance of {@link SignatureValueType }
     * 
     */
    public SignatureValueType createSignatureValueType() {
        return new SignatureValueType();
    }

    /**
     * Create an instance of {@link CtLoteStatus.GuiasTISS.GuiasMedicas }
     * 
     */
    public CtLoteStatus.GuiasTISS.GuiasMedicas createCtLoteStatusGuiasTISSGuiasMedicas() {
        return new CtLoteStatus.GuiasTISS.GuiasMedicas();
    }

    /**
     * Create an instance of {@link CtProcedimentoSolicitado }
     * 
     */
    public CtProcedimentoSolicitado createCtProcedimentoSolicitado() {
        return new CtProcedimentoSolicitado();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoRadio.MotivosNegativa }
     * 
     */
    public CtmAutorizacaoRadio.MotivosNegativa createCtmAutorizacaoRadioMotivosNegativa() {
        return new CtmAutorizacaoRadio.MotivosNegativa();
    }

    /**
     * Create an instance of {@link PGPDataType }
     * 
     */
    public PGPDataType createPGPDataType() {
        return new PGPDataType();
    }

    /**
     * Create an instance of {@link CtmInternacaoSolicitacaoGuia.ProcedimentosSolicitados }
     * 
     */
    public CtmInternacaoSolicitacaoGuia.ProcedimentosSolicitados createCtmInternacaoSolicitacaoGuiaProcedimentosSolicitados() {
        return new CtmInternacaoSolicitacaoGuia.ProcedimentosSolicitados();
    }

    /**
     * Create an instance of {@link CtmAnexoSolicitacaoRadio.TratamentosAnteriores }
     * 
     */
    public CtmAnexoSolicitacaoRadio.TratamentosAnteriores createCtmAnexoSolicitacaoRadioTratamentosAnteriores() {
        return new CtmAnexoSolicitacaoRadio.TratamentosAnteriores();
    }

    /**
     * Create an instance of {@link CtmDemonstrativoPagamento.Pagamentos.PagamentosPorData }
     * 
     */
    public CtmDemonstrativoPagamento.Pagamentos.PagamentosPorData createCtmDemonstrativoPagamentoPagamentosPagamentosPorData() {
        return new CtmDemonstrativoPagamento.Pagamentos.PagamentosPorData();
    }

    /**
     * Create an instance of {@link CtmConsultaGuia }
     * 
     */
    public CtmConsultaGuia createCtmConsultaGuia() {
        return new CtmConsultaGuia();
    }

    /**
     * Create an instance of {@link CtRecursoGlosaRecebimento.GuiasRecurso.OpcaoRecursoGuia.RecursoGuia }
     * 
     */
    public CtRecursoGlosaRecebimento.GuiasRecurso.OpcaoRecursoGuia.RecursoGuia createCtRecursoGlosaRecebimentoGuiasRecursoOpcaoRecursoGuiaRecursoGuia() {
        return new CtRecursoGlosaRecebimento.GuiasRecurso.OpcaoRecursoGuia.RecursoGuia();
    }

    /**
     * Create an instance of {@link CtRecursoGlosaRecebimento.GuiasRecurso }
     * 
     */
    public CtRecursoGlosaRecebimento.GuiasRecurso createCtRecursoGlosaRecebimentoGuiasRecurso() {
        return new CtRecursoGlosaRecebimento.GuiasRecurso();
    }

    /**
     * Create an instance of {@link CtGuiaDadosOdonto.ProcedimentosRealizados }
     * 
     */
    public CtGuiaDadosOdonto.ProcedimentosRealizados createCtGuiaDadosOdontoProcedimentosRealizados() {
        return new CtGuiaDadosOdonto.ProcedimentosRealizados();
    }

    /**
     * Create an instance of {@link CtAnexoLote.AnexosGuiasTISS }
     * 
     */
    public CtAnexoLote.AnexosGuiasTISS createCtAnexoLoteAnexosGuiasTISS() {
        return new CtAnexoLote.AnexosGuiasTISS();
    }

    /**
     * Create an instance of {@link CtmRecursoGlosa.OpcaoRecurso.RecursoGuia.OpcaoRecursoGuia }
     * 
     */
    public CtmRecursoGlosa.OpcaoRecurso.RecursoGuia.OpcaoRecursoGuia createCtmRecursoGlosaOpcaoRecursoRecursoGuiaOpcaoRecursoGuia() {
        return new CtmRecursoGlosa.OpcaoRecurso.RecursoGuia.OpcaoRecursoGuia();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoRadio }
     * 
     */
    public CtmAutorizacaoRadio createCtmAutorizacaoRadio() {
        return new CtmAutorizacaoRadio();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoOPME.ServicosAutorizadosOPME }
     * 
     */
    public CtmAutorizacaoOPME.ServicosAutorizadosOPME createCtmAutorizacaoOPMEServicosAutorizadosOPME() {
        return new CtmAutorizacaoOPME.ServicosAutorizadosOPME();
    }

    /**
     * Create an instance of {@link CtoDemonstrativoOdontologia.DadosPagamentoPorData.Protocolos.TotaisPorProtocolo }
     * 
     */
    public CtoDemonstrativoOdontologia.DadosPagamentoPorData.Protocolos.TotaisPorProtocolo createCtoDemonstrativoOdontologiaDadosPagamentoPorDataProtocolosTotaisPorProtocolo() {
        return new CtoDemonstrativoOdontologia.DadosPagamentoPorData.Protocolos.TotaisPorProtocolo();
    }

    /**
     * Create an instance of {@link CtoGuiaOdontologia.ProcedimentosExecutados }
     * 
     */
    public CtoGuiaOdontologia.ProcedimentosExecutados createCtoGuiaOdontologiaProcedimentosExecutados() {
        return new CtoGuiaOdontologia.ProcedimentosExecutados();
    }

    /**
     * Create an instance of {@link CtOpmUtilizada.OPM.IdentificacaoOPM }
     * 
     */
    public CtOpmUtilizada.OPM.IdentificacaoOPM createCtOpmUtilizadaOPMIdentificacaoOPM() {
        return new CtOpmUtilizada.OPM.IdentificacaoOPM();
    }

    /**
     * Create an instance of {@link CtRespostaRecursoItemOdonto }
     * 
     */
    public CtRespostaRecursoItemOdonto createCtRespostaRecursoItemOdonto() {
        return new CtRespostaRecursoItemOdonto();
    }

    /**
     * Create an instance of {@link CtmInternacaoDadosSaida }
     * 
     */
    public CtmInternacaoDadosSaida createCtmInternacaoDadosSaida() {
        return new CtmInternacaoDadosSaida();
    }

    /**
     * Create an instance of {@link CtProcedimentoExecutadoInt.IdentEquipe }
     * 
     */
    public CtProcedimentoExecutadoInt.IdentEquipe createCtProcedimentoExecutadoIntIdentEquipe() {
        return new CtProcedimentoExecutadoInt.IdentEquipe();
    }

    /**
     * Create an instance of {@link CtmRecursoGlosa.OpcaoRecurso.RecursoGuia }
     * 
     */
    public CtmRecursoGlosa.OpcaoRecurso.RecursoGuia createCtmRecursoGlosaOpcaoRecursoRecursoGuia() {
        return new CtmRecursoGlosa.OpcaoRecurso.RecursoGuia();
    }

    /**
     * Create an instance of {@link CtContratadoProfissionalDados }
     * 
     */
    public CtContratadoProfissionalDados createCtContratadoProfissionalDados() {
        return new CtContratadoProfissionalDados();
    }

    /**
     * Create an instance of {@link X509DataType }
     * 
     */
    public X509DataType createX509DataType() {
        return new X509DataType();
    }

    /**
     * Create an instance of {@link CtDadosResumoDemonstrativo }
     * 
     */
    public CtDadosResumoDemonstrativo createCtDadosResumoDemonstrativo() {
        return new CtDadosResumoDemonstrativo();
    }

    /**
     * Create an instance of {@link CtRecursoGlosaRecebimento.GuiasRecurso.OpcaoRecursoGuia.ItensGuia }
     * 
     */
    public CtRecursoGlosaRecebimento.GuiasRecurso.OpcaoRecursoGuia.ItensGuia createCtRecursoGlosaRecebimentoGuiasRecursoOpcaoRecursoGuiaItensGuia() {
        return new CtRecursoGlosaRecebimento.GuiasRecurso.OpcaoRecursoGuia.ItensGuia();
    }

    /**
     * Create an instance of {@link ReferenceType }
     * 
     */
    public ReferenceType createReferenceType() {
        return new ReferenceType();
    }

    /**
     * Create an instance of {@link SPKIDataType }
     * 
     */
    public SPKIDataType createSPKIDataType() {
        return new SPKIDataType();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoServico.PrestadorAutorizado }
     * 
     */
    public CtmAutorizacaoServico.PrestadorAutorizado createCtmAutorizacaoServicoPrestadorAutorizado() {
        return new CtmAutorizacaoServico.PrestadorAutorizado();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoRadio.TratamentosAnteriores }
     * 
     */
    public CtmAutorizacaoRadio.TratamentosAnteriores createCtmAutorizacaoRadioTratamentosAnteriores() {
        return new CtmAutorizacaoRadio.TratamentosAnteriores();
    }

    /**
     * Create an instance of {@link CtmInternacaoSolicitacaoGuia.AnexoClinico }
     * 
     */
    public CtmInternacaoSolicitacaoGuia.AnexoClinico createCtmInternacaoSolicitacaoGuiaAnexoClinico() {
        return new CtmInternacaoSolicitacaoGuia.AnexoClinico();
    }

    /**
     * Create an instance of {@link CtGlosaRecibo.OpcaoRecurso }
     * 
     */
    public CtGlosaRecibo.OpcaoRecurso createCtGlosaReciboOpcaoRecurso() {
        return new CtGlosaRecibo.OpcaoRecurso();
    }

    /**
     * Create an instance of {@link CtProtocoloRecebimentoAnexo }
     * 
     */
    public CtProtocoloRecebimentoAnexo createCtProtocoloRecebimentoAnexo() {
        return new CtProtocoloRecebimentoAnexo();
    }

    /**
     * Create an instance of {@link CtProcedimentoExecutado }
     * 
     */
    public CtProcedimentoExecutado createCtProcedimentoExecutado() {
        return new CtProcedimentoExecutado();
    }

    /**
     * Create an instance of {@link Epilogo }
     * 
     */
    public Epilogo createEpilogo() {
        return new Epilogo();
    }

    /**
     * Create an instance of {@link CtmAnexoSolicitacaoQuimio }
     * 
     */
    public CtmAnexoSolicitacaoQuimio createCtmAnexoSolicitacaoQuimio() {
        return new CtmAnexoSolicitacaoQuimio();
    }

    /**
     * Create an instance of {@link CtmSpSadtSolicitacaoGuia.AnexoClinico }
     * 
     */
    public CtmSpSadtSolicitacaoGuia.AnexoClinico createCtmSpSadtSolicitacaoGuiaAnexoClinico() {
        return new CtmSpSadtSolicitacaoGuia.AnexoClinico();
    }

    /**
     * Create an instance of {@link CtmSpSadtSolicitacaoGuia.ProcedimentosSolicitados }
     * 
     */
    public CtmSpSadtSolicitacaoGuia.ProcedimentosSolicitados createCtmSpSadtSolicitacaoGuiaProcedimentosSolicitados() {
        return new CtmSpSadtSolicitacaoGuia.ProcedimentosSolicitados();
    }

    /**
     * Create an instance of {@link CtmInternacaoSolicitacaoGuia.DadosHospitalSolicitado }
     * 
     */
    public CtmInternacaoSolicitacaoGuia.DadosHospitalSolicitado createCtmInternacaoSolicitacaoGuiaDadosHospitalSolicitado() {
        return new CtmInternacaoSolicitacaoGuia.DadosHospitalSolicitado();
    }

    /**
     * Create an instance of {@link CtmInternacaoSolicitacaoGuia }
     * 
     */
    public CtmInternacaoSolicitacaoGuia createCtmInternacaoSolicitacaoGuia() {
        return new CtmInternacaoSolicitacaoGuia();
    }

    /**
     * Create an instance of {@link CtmDemonstrativoPagamento.Pagamentos }
     * 
     */
    public CtmDemonstrativoPagamento.Pagamentos createCtmDemonstrativoPagamentoPagamentos() {
        return new CtmDemonstrativoPagamento.Pagamentos();
    }

    /**
     * Create an instance of {@link CtmProrrogacaoSolicitacaoGuia.ProcedimentosAdicionais }
     * 
     */
    public CtmProrrogacaoSolicitacaoGuia.ProcedimentosAdicionais createCtmProrrogacaoSolicitacaoGuiaProcedimentosAdicionais() {
        return new CtmProrrogacaoSolicitacaoGuia.ProcedimentosAdicionais();
    }

    /**
     * Create an instance of {@link CtGlosaRecibo }
     * 
     */
    public CtGlosaRecibo createCtGlosaRecibo() {
        return new CtGlosaRecibo();
    }

    /**
     * Create an instance of {@link CtContaMedicaResumo.RelacaoGuias.DetalhesGuia.RelacaoGlosa }
     * 
     */
    public CtContaMedicaResumo.RelacaoGuias.DetalhesGuia.RelacaoGlosa createCtContaMedicaResumoRelacaoGuiasDetalhesGuiaRelacaoGlosa() {
        return new CtContaMedicaResumo.RelacaoGuias.DetalhesGuia.RelacaoGlosa();
    }

    /**
     * Create an instance of {@link CtmDemonstrativoPagamento.Pagamentos.PagamentosPorData.TotaisLiquidosPorData }
     * 
     */
    public CtmDemonstrativoPagamento.Pagamentos.PagamentosPorData.TotaisLiquidosPorData createCtmDemonstrativoPagamentoPagamentosPagamentosPorDataTotaisLiquidosPorData() {
        return new CtmDemonstrativoPagamento.Pagamentos.PagamentosPorData.TotaisLiquidosPorData();
    }

    /**
     * Create an instance of {@link CtmDemonstrativoPagamento.Pagamentos.PagamentosPorData.DadosResumo }
     * 
     */
    public CtmDemonstrativoPagamento.Pagamentos.PagamentosPorData.DadosResumo createCtmDemonstrativoPagamentoPagamentosPagamentosPorDataDadosResumo() {
        return new CtmDemonstrativoPagamento.Pagamentos.PagamentosPorData.DadosResumo();
    }

    /**
     * Create an instance of {@link CtoRecursoGlosaOdonto }
     * 
     */
    public CtoRecursoGlosaOdonto createCtoRecursoGlosaOdonto() {
        return new CtoRecursoGlosaOdonto();
    }

    /**
     * Create an instance of {@link CtGlosaRecibo.DadosContratado }
     * 
     */
    public CtGlosaRecibo.DadosContratado createCtGlosaReciboDadosContratado() {
        return new CtGlosaRecibo.DadosContratado();
    }

    /**
     * Create an instance of {@link CtMotivoGlosa }
     * 
     */
    public CtMotivoGlosa createCtMotivoGlosa() {
        return new CtMotivoGlosa();
    }

    /**
     * Create an instance of {@link CtmSolicitacaoLote }
     * 
     */
    public CtmSolicitacaoLote createCtmSolicitacaoLote() {
        return new CtmSolicitacaoLote();
    }

    /**
     * Create an instance of {@link CtmProrrogacaoSolicitacaoGuia }
     * 
     */
    public CtmProrrogacaoSolicitacaoGuia createCtmProrrogacaoSolicitacaoGuia() {
        return new CtmProrrogacaoSolicitacaoGuia();
    }

    /**
     * Create an instance of {@link CtmDemonstrativoAnaliseConta.DadosConta.DadosProtocolo }
     * 
     */
    public CtmDemonstrativoAnaliseConta.DadosConta.DadosProtocolo createCtmDemonstrativoAnaliseContaDadosContaDadosProtocolo() {
        return new CtmDemonstrativoAnaliseConta.DadosConta.DadosProtocolo();
    }

    /**
     * Create an instance of {@link CtmRecursoGlosa.OpcaoRecurso.RecursoGuia.OpcaoRecursoGuia.RecursoGuiaCompleta }
     * 
     */
    public CtmRecursoGlosa.OpcaoRecurso.RecursoGuia.OpcaoRecursoGuia.RecursoGuiaCompleta createCtmRecursoGlosaOpcaoRecursoRecursoGuiaOpcaoRecursoGuiaRecursoGuiaCompleta() {
        return new CtmRecursoGlosa.OpcaoRecurso.RecursoGuia.OpcaoRecursoGuia.RecursoGuiaCompleta();
    }

    /**
     * Create an instance of {@link CtGuiaDadosOdonto.ProcedimentosRealizados.ProcedimentoRealizado.GlosasProcedimento }
     * 
     */
    public CtGuiaDadosOdonto.ProcedimentosRealizados.ProcedimentoRealizado.GlosasProcedimento createCtGuiaDadosOdontoProcedimentosRealizadosProcedimentoRealizadoGlosasProcedimento() {
        return new CtGuiaDadosOdonto.ProcedimentosRealizados.ProcedimentoRealizado.GlosasProcedimento();
    }

    /**
     * Create an instance of {@link CtSituacaoClinica }
     * 
     */
    public CtSituacaoClinica createCtSituacaoClinica() {
        return new CtSituacaoClinica();
    }

    /**
     * Create an instance of {@link CtmDemonstrativoAnaliseConta }
     * 
     */
    public CtmDemonstrativoAnaliseConta createCtmDemonstrativoAnaliseConta() {
        return new CtmDemonstrativoAnaliseConta();
    }

    /**
     * Create an instance of {@link CtmAnexoSolicitacaoRadio }
     * 
     */
    public CtmAnexoSolicitacaoRadio createCtmAnexoSolicitacaoRadio() {
        return new CtmAnexoSolicitacaoRadio();
    }

    /**
     * Create an instance of {@link X509IssuerSerialType }
     * 
     */
    public X509IssuerSerialType createX509IssuerSerialType() {
        return new X509IssuerSerialType();
    }

    /**
     * Create an instance of {@link CtmSpSadtSolicitacaoGuia.DadosExecutante }
     * 
     */
    public CtmSpSadtSolicitacaoGuia.DadosExecutante createCtmSpSadtSolicitacaoGuiaDadosExecutante() {
        return new CtmSpSadtSolicitacaoGuia.DadosExecutante();
    }

    /**
     * Create an instance of {@link CtmHonorarioIndividualGuia.ProcedimentosRealizados }
     * 
     */
    public CtmHonorarioIndividualGuia.ProcedimentosRealizados createCtmHonorarioIndividualGuiaProcedimentosRealizados() {
        return new CtmHonorarioIndividualGuia.ProcedimentosRealizados();
    }

    /**
     * Create an instance of {@link CtDescontos }
     * 
     */
    public CtDescontos createCtDescontos() {
        return new CtDescontos();
    }

    /**
     * Create an instance of {@link CtOpmUtilizada.OPM }
     * 
     */
    public CtOpmUtilizada.OPM createCtOpmUtilizadaOPM() {
        return new CtOpmUtilizada.OPM();
    }

    /**
     * Create an instance of {@link CtProtocoloRecebimento }
     * 
     */
    public CtProtocoloRecebimento createCtProtocoloRecebimento() {
        return new CtProtocoloRecebimento();
    }

    /**
     * Create an instance of {@link CtmAutorizacaoQuimio.MotivosNegativa }
     * 
     */
    public CtmAutorizacaoQuimio.MotivosNegativa createCtmAutorizacaoQuimioMotivosNegativa() {
        return new CtmAutorizacaoQuimio.MotivosNegativa();
    }

    /**
     * Create an instance of {@link ManifestType }
     * 
     */
    public ManifestType createManifestType() {
        return new ManifestType();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SPKIDataType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "SPKIData")
    public JAXBElement<SPKIDataType> createSPKIData(SPKIDataType value) {
        return new JAXBElement<SPKIDataType>(_SPKIData_QNAME, SPKIDataType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "KeyName")
    public JAXBElement<String> createKeyName(String value) {
        return new JAXBElement<String>(_KeyName_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RSAKeyValueType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "RSAKeyValue")
    public JAXBElement<RSAKeyValueType> createRSAKeyValue(RSAKeyValueType value) {
        return new JAXBElement<RSAKeyValueType>(_RSAKeyValue_QNAME, RSAKeyValueType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SignatureType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "Signature")
    public JAXBElement<SignatureType> createSignature(SignatureType value) {
        return new JAXBElement<SignatureType>(_Signature_QNAME, SignatureType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link KeyInfoType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "KeyInfo")
    public JAXBElement<KeyInfoType> createKeyInfo(KeyInfoType value) {
        return new JAXBElement<KeyInfoType>(_KeyInfo_QNAME, KeyInfoType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SignatureValueType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "SignatureValue")
    public JAXBElement<SignatureValueType> createSignatureValue(SignatureValueType value) {
        return new JAXBElement<SignatureValueType>(_SignatureValue_QNAME, SignatureValueType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "MgmtData")
    public JAXBElement<String> createMgmtData(String value) {
        return new JAXBElement<String>(_MgmtData_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SignatureMethodType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "SignatureMethod")
    public JAXBElement<SignatureMethodType> createSignatureMethod(SignatureMethodType value) {
        return new JAXBElement<SignatureMethodType>(_SignatureMethod_QNAME, SignatureMethodType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObjectType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "Object")
    public JAXBElement<ObjectType> createObject(ObjectType value) {
        return new JAXBElement<ObjectType>(_Object_QNAME, ObjectType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SignaturePropertiesType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "SignatureProperties")
    public JAXBElement<SignaturePropertiesType> createSignatureProperties(SignaturePropertiesType value) {
        return new JAXBElement<SignaturePropertiesType>(_SignatureProperties_QNAME, SignaturePropertiesType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link TransformType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "Transform")
    public JAXBElement<TransformType> createTransform(TransformType value) {
        return new JAXBElement<TransformType>(_Transform_QNAME, TransformType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link KeyValueType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "KeyValue")
    public JAXBElement<KeyValueType> createKeyValue(KeyValueType value) {
        return new JAXBElement<KeyValueType>(_KeyValue_QNAME, KeyValueType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PGPDataType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "PGPData")
    public JAXBElement<PGPDataType> createPGPData(PGPDataType value) {
        return new JAXBElement<PGPDataType>(_PGPData_QNAME, PGPDataType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link TransformsType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "Transforms")
    public JAXBElement<TransformsType> createTransforms(TransformsType value) {
        return new JAXBElement<TransformsType>(_Transforms_QNAME, TransformsType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ReferenceType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "Reference")
    public JAXBElement<ReferenceType> createReference(ReferenceType value) {
        return new JAXBElement<ReferenceType>(_Reference_QNAME, ReferenceType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RetrievalMethodType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "RetrievalMethod")
    public JAXBElement<RetrievalMethodType> createRetrievalMethod(RetrievalMethodType value) {
        return new JAXBElement<RetrievalMethodType>(_RetrievalMethod_QNAME, RetrievalMethodType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DSAKeyValueType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "DSAKeyValue")
    public JAXBElement<DSAKeyValueType> createDSAKeyValue(DSAKeyValueType value) {
        return new JAXBElement<DSAKeyValueType>(_DSAKeyValue_QNAME, DSAKeyValueType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DigestMethodType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "DigestMethod")
    public JAXBElement<DigestMethodType> createDigestMethod(DigestMethodType value) {
        return new JAXBElement<DigestMethodType>(_DigestMethod_QNAME, DigestMethodType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link byte[]}{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "DigestValue")
    public JAXBElement<byte[]> createDigestValue(byte[] value) {
        return new JAXBElement<byte[]>(_DigestValue_QNAME, byte[].class, null, ((byte[]) value));
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link CanonicalizationMethodType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "CanonicalizationMethod")
    public JAXBElement<CanonicalizationMethodType> createCanonicalizationMethod(CanonicalizationMethodType value) {
        return new JAXBElement<CanonicalizationMethodType>(_CanonicalizationMethod_QNAME, CanonicalizationMethodType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SignedInfoType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "SignedInfo")
    public JAXBElement<SignedInfoType> createSignedInfo(SignedInfoType value) {
        return new JAXBElement<SignedInfoType>(_SignedInfo_QNAME, SignedInfoType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link X509DataType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "X509Data")
    public JAXBElement<X509DataType> createX509Data(X509DataType value) {
        return new JAXBElement<X509DataType>(_X509Data_QNAME, X509DataType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ManifestType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "Manifest")
    public JAXBElement<ManifestType> createManifest(ManifestType value) {
        return new JAXBElement<ManifestType>(_Manifest_QNAME, ManifestType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SignaturePropertyType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "SignatureProperty")
    public JAXBElement<SignaturePropertyType> createSignatureProperty(SignaturePropertyType value) {
        return new JAXBElement<SignaturePropertyType>(_SignatureProperty_QNAME, SignaturePropertyType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link byte[]}{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "SPKISexp", scope = SPKIDataType.class)
    public JAXBElement<byte[]> createSPKIDataTypeSPKISexp(byte[] value) {
        return new JAXBElement<byte[]>(_SPKIDataTypeSPKISexp_QNAME, byte[].class, SPKIDataType.class, ((byte[]) value));
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "XPath", scope = TransformType.class)
    public JAXBElement<String> createTransformTypeXPath(String value) {
        return new JAXBElement<String>(_TransformTypeXPath_QNAME, String.class, TransformType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link X509IssuerSerialType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "X509IssuerSerial", scope = X509DataType.class)
    public JAXBElement<X509IssuerSerialType> createX509DataTypeX509IssuerSerial(X509IssuerSerialType value) {
        return new JAXBElement<X509IssuerSerialType>(_X509DataTypeX509IssuerSerial_QNAME, X509IssuerSerialType.class, X509DataType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link byte[]}{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "X509CRL", scope = X509DataType.class)
    public JAXBElement<byte[]> createX509DataTypeX509CRL(byte[] value) {
        return new JAXBElement<byte[]>(_X509DataTypeX509CRL_QNAME, byte[].class, X509DataType.class, ((byte[]) value));
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "X509SubjectName", scope = X509DataType.class)
    public JAXBElement<String> createX509DataTypeX509SubjectName(String value) {
        return new JAXBElement<String>(_X509DataTypeX509SubjectName_QNAME, String.class, X509DataType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link byte[]}{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "X509SKI", scope = X509DataType.class)
    public JAXBElement<byte[]> createX509DataTypeX509SKI(byte[] value) {
        return new JAXBElement<byte[]>(_X509DataTypeX509SKI_QNAME, byte[].class, X509DataType.class, ((byte[]) value));
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link byte[]}{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "X509Certificate", scope = X509DataType.class)
    public JAXBElement<byte[]> createX509DataTypeX509Certificate(byte[] value) {
        return new JAXBElement<byte[]>(_X509DataTypeX509Certificate_QNAME, byte[].class, X509DataType.class, ((byte[]) value));
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link byte[]}{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "PGPKeyID", scope = PGPDataType.class)
    public JAXBElement<byte[]> createPGPDataTypePGPKeyID(byte[] value) {
        return new JAXBElement<byte[]>(_PGPDataTypePGPKeyID_QNAME, byte[].class, PGPDataType.class, ((byte[]) value));
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link byte[]}{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "PGPKeyPacket", scope = PGPDataType.class)
    public JAXBElement<byte[]> createPGPDataTypePGPKeyPacket(byte[] value) {
        return new JAXBElement<byte[]>(_PGPDataTypePGPKeyPacket_QNAME, byte[].class, PGPDataType.class, ((byte[]) value));
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BigInteger }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.w3.org/2000/09/xmldsig#", name = "HMACOutputLength", scope = SignatureMethodType.class)
    public JAXBElement<BigInteger> createSignatureMethodTypeHMACOutputLength(BigInteger value) {
        return new JAXBElement<BigInteger>(_SignatureMethodTypeHMACOutputLength_QNAME, BigInteger.class, SignatureMethodType.class, value);
    }

}
