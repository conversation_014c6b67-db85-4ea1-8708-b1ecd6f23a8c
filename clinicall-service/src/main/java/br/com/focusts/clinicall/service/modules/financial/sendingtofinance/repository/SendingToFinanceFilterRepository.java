package br.com.focusts.clinicall.service.modules.financial.sendingtofinance.repository;

import br.com.focusts.clinicall.service.modules.financial.sendingtofinance.po.SendingToFinanceFilterPO;
import org.springframework.stereotype.Repository;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

@Repository
public interface SendingToFinanceFilterRepository
        extends CrudRepository<SendingToFinanceFilterPO, java.lang.Long>,
            QuerydslPredicateExecutor<SendingToFinanceFilterPO>{
}
