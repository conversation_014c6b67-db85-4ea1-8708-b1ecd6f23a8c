
package br.com.focusts.clinicall.service.modules.tables.tiss.facade;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.TypeHospitalizationPO;

public interface TypeHospitalizationFacade extends CrudFacade<TypeHospitalizationPO,java.lang.Long>{

    public Page<TypeHospitalizationPO> findByCodeContainingOrNameContaining(PageSearchTO pageSearchTO);

}

