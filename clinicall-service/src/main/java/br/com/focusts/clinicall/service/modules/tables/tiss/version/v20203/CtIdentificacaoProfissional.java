//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2022.06.08 at 12:20:52 PM BRT 
//


package br.com.focusts.clinicall.service.modules.tables.tiss.version.v20203;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ct_identificacaoProfissional complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_identificacaoProfissional">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="nomeProfissional" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_nome" minOccurs="0"/>
 *         &lt;element name="conselhoProfissional" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_conselhoProfissional"/>
 *         &lt;element name="cbos" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_CBOS" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_identificacaoProfissional", propOrder = {
    "nomeProfissional",
    "conselhoProfissional",
    "cbos"
})
public class CtIdentificacaoProfissional {

    protected String nomeProfissional;
    @XmlElement(required = true)
    protected CtConselhoProfissional conselhoProfissional;
    protected String cbos;

    /**
     * Gets the value of the nomeProfissional property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeProfissional() {
        return nomeProfissional;
    }

    /**
     * Sets the value of the nomeProfissional property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeProfissional(String value) {
        this.nomeProfissional = value;
    }

    /**
     * Gets the value of the conselhoProfissional property.
     * 
     * @return
     *     possible object is
     *     {@link CtConselhoProfissional }
     *     
     */
    public CtConselhoProfissional getConselhoProfissional() {
        return conselhoProfissional;
    }

    /**
     * Sets the value of the conselhoProfissional property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtConselhoProfissional }
     *     
     */
    public void setConselhoProfissional(CtConselhoProfissional value) {
        this.conselhoProfissional = value;
    }

    /**
     * Gets the value of the cbos property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCbos() {
        return cbos;
    }

    /**
     * Sets the value of the cbos property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCbos(String value) {
        this.cbos = value;
    }

}
