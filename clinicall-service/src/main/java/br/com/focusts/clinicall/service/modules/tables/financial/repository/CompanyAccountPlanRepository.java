
package br.com.focusts.clinicall.service.modules.tables.financial.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.tables.financial.po.CompanyAccountPlanPO;
import br.com.focusts.clinicall.service.modules.tables.financial.to.CompanyAccountPlanTO;

@Repository
public interface CompanyAccountPlanRepository extends CrudRepository<CompanyAccountPlanPO,java.lang.Long>{

    public Page<CompanyAccountPlanPO> findByCompany_nameContainingOrAccountPlan_nameContaining(String companyName ,String accountPlanName , Pageable pageable);

    @Query("""
           SELECT
           new br.com.focusts.clinicall.service.modules.tables.financial.to.CompanyAccountPlanTO(
           companyAccountPlan.id as id,
           accountPlan.id as accountPlanId,
           accountPlan.name as accountPlanName,
           company.id as companyId,
           company.name as companyName,
           companyAccountPlan.active as active
           )
           FROM
           CompanyAccountPlanPO companyAccountPlan
           LEFT JOIN companyAccountPlan.accountPlan accountPlan
           LEFT JOIN companyAccountPlan.company company
           WHERE(
           accountPlan.name LIKE :argument% OR accountPlan.alias LIKE :argument%)
           """)
    public Page<CompanyAccountPlanTO> findByName(String argument, Pageable pageable);

    @Query("""
           SELECT
           new br.com.focusts.clinicall.service.modules.tables.financial.to.CompanyAccountPlanTO(
           companyAccountPlan.id as id,
           accountPlan.id as accountPlanId,
           accountPlan.name as accountPlanName,
           company.id as companyId,
           company.name as companyName,
           companyAccountPlan.active as active
           )
           FROM
           CompanyAccountPlanPO companyAccountPlan
           LEFT JOIN companyAccountPlan.accountPlan accountPlan
           LEFT JOIN companyAccountPlan.company company
           WHERE(
           company.name LIKE :argument% OR company.alias LIKE :argument%)
           """)
    public Page<CompanyAccountPlanTO> findByCompanyName(String argument, Pageable pageable);

    @Query("""
           SELECT
           new br.com.focusts.clinicall.service.modules.tables.financial.to.CompanyAccountPlanTO(
           companyAccountPlan.id as id,
           accountPlan.id as accountPlanId,
           accountPlan.name as accountPlanName,
           company.id as companyId,
           company.name as companyName,
           companyAccountPlan.active as active
           )
           FROM
           CompanyAccountPlanPO companyAccountPlan
           LEFT JOIN companyAccountPlan.accountPlan accountPlan
           LEFT JOIN companyAccountPlan.company company
           WHERE (
           company.name LIKE :argument% OR company.alias LIKE :argument%) AND accountPlan.id = :accountPlanId 
           ORDER BY 5
           """)
    public Page<CompanyAccountPlanTO> findByCompanyNameAndAccountPlanId(String argument, Pageable pageable, Long accountPlanId);

    @Query("""
                   SELECT
           new br.com.focusts.clinicall.service.modules.tables.financial.to.CompanyAccountPlanTO(
           companyAccountPlan.id as id,
           accountPlan.id as accountPlanId,
           accountPlan.name as accountPlanName,
           company.id as companyId,
           company.name as companyName,
           companyAccountPlan.active as active
           )
           FROM
           CompanyAccountPlanPO companyAccountPlan
           LEFT JOIN companyAccountPlan.accountPlan accountPlan
           LEFT JOIN companyAccountPlan.company company
           WHERE (
           accountPlan.name LIKE :argument%)
               AND company.id = :companyId 
               AND accountPlan.type = :type
               AND accountPlan.parent.id IS NOT NULL
               AND companyAccountPlan.active = true
           ORDER BY 5 
                  """)
    public Page<CompanyAccountPlanTO> findByCompanyAndTtype(Long companyId, String type, String argument, Pageable pageable);
}



