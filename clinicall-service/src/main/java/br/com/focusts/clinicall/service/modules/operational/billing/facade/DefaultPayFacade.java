
package br.com.focusts.clinicall.service.modules.operational.billing.facade;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.xml.bind.JAXBException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.exception.ApplicationException;
import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.financial.bond.facade.BondFacade;
import br.com.focusts.clinicall.service.modules.financial.bond.service.BondPayService;
import br.com.focusts.clinicall.service.modules.financial.bond.to.BondPayTO;
import br.com.focusts.clinicall.service.modules.financial.sendingtofinance.facade.SendingToFinanceFacade;
import br.com.focusts.clinicall.service.modules.financial.sendingtofinance.facade.SendingToFinanceItemFacade;
import br.com.focusts.clinicall.service.modules.operational.billing.po.BillingOrderItemPO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.BillingPayOrderItemPO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.GlossPO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.GlossTypeItem;
import br.com.focusts.clinicall.service.modules.operational.billing.po.InvoicePO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.PayEartefactPO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.PayInvoicePO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.PayPO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.PayXmlPO;
import br.com.focusts.clinicall.service.modules.operational.billing.repository.BillingPayOrderItemRepository;
import br.com.focusts.clinicall.service.modules.operational.billing.repository.GlossRepository;
import br.com.focusts.clinicall.service.modules.operational.billing.service.BillingPayOrderItemService;
import br.com.focusts.clinicall.service.modules.operational.billing.service.GlossService;
import br.com.focusts.clinicall.service.modules.operational.billing.service.InvoiceService;
import br.com.focusts.clinicall.service.modules.operational.billing.service.PayEartefactService;
import br.com.focusts.clinicall.service.modules.operational.billing.service.PayInvoiceService;
import br.com.focusts.clinicall.service.modules.operational.billing.service.PayService;
import br.com.focusts.clinicall.service.modules.operational.billing.service.PayXmlService;
import br.com.focusts.clinicall.service.modules.operational.billing.to.PayBondPayTO;
import br.com.focusts.clinicall.service.modules.operational.billing.to.PayInfoGlossTO;
import br.com.focusts.clinicall.service.modules.operational.billing.to.ValuePaidRequestTO;
import br.com.focusts.clinicall.service.modules.register.accreditation.facade.InsuranceFacade;
import br.com.focusts.clinicall.service.modules.tables.financial.facade.AccountFacade;
import br.com.focusts.clinicall.service.modules.tables.financial.to.AccountTO;

@Component
@Transactional
public class DefaultPayFacade extends AbstractCrudFacade<PayPO, java.lang.Long> implements PayFacade {

  @Autowired
  private PayService payService;

  @Autowired
  private PayInvoiceService payInvoiceService;

  @Autowired
  private GlossService glossService;

  @Autowired
  private InvoiceService invoiceService;

  @Autowired
  private GlossRepository glossRepository;

  @Autowired
  private BillingPayOrderItemRepository billingPayOrderItemRepository;

  @Autowired
  private BillingPayOrderItemService billingPayOrderItemService;

  @Autowired
  private PayEartefactService payEartefactService;

  @Autowired
  private PayXmlService payXmlService;

  @Autowired
  private AccountFacade accountFacade;

  @Autowired
  private BondFacade bondFacade;

  @Autowired
  private SendingToFinanceItemFacade sendingToFinanceItemFacade;

  @Autowired
  private SendingToFinanceFacade sendingToFinanceFacade;

  @Autowired
  private BondPayService bondPayService;

  @Autowired
  private InsuranceFacade insuranceFacade;


  @Override
  public CrudService<PayPO, java.lang.Long> getCrudService() {
    return payService;
  }

  @Override
  public Page<PayPO> find(PageSearchTO pageSearchTO) {
    return payService.find(pageSearchTO);
  }

  @Override
  public GlossPO insertGloss(GlossPO glossPO) {
    glossPO = glossService.save(glossPO);
    return glossPO;
  }

  @Override
  public BillingPayOrderItemPO finByBillingPayOrderItemId(Long billingPayOrderItemId) {
    Optional<BillingPayOrderItemPO> billingPayOrderItem = billingPayOrderItemRepository
        .findById(billingPayOrderItemId);
    if (billingPayOrderItem.isEmpty()) {
      throw new ApplicationException("Guia não encontrada");
    }
    return billingPayOrderItem.get();
  }

  @Override
  public PayInfoGlossTO findByInfoGloss(Long payId) {

    return payService.findByInfoGloss(payId);
  }

  @Override
  public Void closedPay(Long payId) {
    var pay = payService.findById(payId);
    if (pay == null) {
      throw new ApplicationException("Baixa não encontrada");
    }
    payService.closedPay(payId);
    return null;
  }

  @Override
  @Transactional
  public void deleteGloss(Long glossId) {
    var gloss = glossService.findById(glossId);
    var glossDad = glossRepository.findByParent_id(glossId);
    if (glossDad.isPresent()) {

      gloss = resetGloss(gloss, glossDad.get());

      glossRepository.save(gloss);
    } else {

      var billingPayOrderItem = billingPayOrderItemRepository.findById(gloss.getBillingPayOrderItem().getId());
      var valueGloss = gloss.getValue() + gloss.getValueAuxiliary() + gloss.getValueFee()
          + gloss.getValueMedicament() + gloss.getValueMaterial();
      billingPayOrderItem.get().setValue(billingPayOrderItem.get().getValue() + valueGloss);
      billingPayOrderItem.get().setGloss(null);
      glossRepository.deleteById(glossId);
    }

  }

  private GlossPO resetGloss(GlossPO gloss, GlossPO glossDad) {

    gloss.setPayday(LocalDate.now());
    // gloss.setBillingPayOrderItem();
    gloss.setAppealable(true);
    gloss.setValue(glossDad.getValue());
    gloss.setValueAuxiliary(glossDad.getValueAuxiliary());
    gloss.setValueFee(glossDad.getValueFee());
    gloss.setValueMaterial(glossDad.getValueMaterial());
    gloss.setValueMedicament(glossDad.getValueMedicament());
    gloss.getGlossTypeList().clear();
    glossDad.getGlossTypeList().forEach(gtl -> {
      var glossTypeItem = new GlossTypeItem();
      glossTypeItem.setGlossType(gtl.getGlossType());
      glossTypeItem.setGloss(gloss);
      gloss.getGlossTypeList().add(glossTypeItem);
    });
    // gloss.setGlossTypeList(glossDad.getGlossTypeList());
    gloss.setNote(glossDad.getNote());
    gloss.setAmountPaid(0.0);
    gloss.setAmountPaidAuxiliary(0.0);
    gloss.setAmountPaidFee(0.0);
    gloss.setAmountPaidMaterial(0.0);
    gloss.setAmountPaidMedicament(0.0);

    if (glossDad.getGlossOrderItemAuxiliaryList().size() > 0) {
      for (var itemDad : glossDad.getGlossOrderItemAuxiliaryList()) {
        for (var glossDetailsChild : gloss.getGlossOrderItemAuxiliaryList()) {
          if (glossDetailsChild.getOrderItemAuxiliary().getId()
              .equals(itemDad.getOrderItemAuxiliary().getId())) {
            // glossDetailsChild.setGloss(item.getGloss());
            glossDetailsChild.setOrderItemAuxiliary(itemDad.getOrderItemAuxiliary());
            glossDetailsChild.setValue(
                itemDad.getValuePaid() == null ? itemDad.getValue()
                    : Double.compare(itemDad.getValuePaid(), itemDad.getValue()) != 0
                        ? itemDad.getValue() - itemDad.getValuePaid()
                        : itemDad.getValue());
            glossDetailsChild.setValuePaid(null);
            glossDetailsChild.setDatePaid(null);
          }
        }
      }
    }

    if (glossDad.getGlossOrderItemFeeList().size() > 0) {
      for (var itemDad : glossDad.getGlossOrderItemFeeList()) {
        for (var glossDetailsChild : gloss.getGlossOrderItemFeeList()) {
          if (glossDetailsChild.getOrderItemFee().getId().equals(itemDad.getOrderItemFee().getId())) {
            // glossDetailsChild.setGloss(item.getGloss());
            glossDetailsChild.setOrderItemFee(itemDad.getOrderItemFee());
            glossDetailsChild.setValue(
                itemDad.getValuePaid() == null ? itemDad.getValue()
                    : Double.compare(itemDad.getValuePaid(), itemDad.getValue()) != 0
                        ? itemDad.getValue() - itemDad.getValuePaid()
                        : itemDad.getValue());
            glossDetailsChild.setValuePaid(null);
            glossDetailsChild.setDatePaid(null);
          }
        }
      }
    }

    if (glossDad.getGlossOrderItemProductList().size() > 0) {
      for (var itemDad : glossDad.getGlossOrderItemProductList()) {
        for (var glossDetailsChild : gloss.getGlossOrderItemProductList()) {
          if (glossDetailsChild.getOrderItemProduct().getId().equals(itemDad.getOrderItemProduct().getId())) {
            // glossDetailsChild.setGloss(item.getGloss());
            glossDetailsChild.setOrderItemProduct(itemDad.getOrderItemProduct());
            glossDetailsChild.setValue(
                itemDad.getValuePaid() == null ? itemDad.getValue()
                    : Double.compare(itemDad.getValuePaid(), itemDad.getValue()) != 0
                        ? itemDad.getValue() - itemDad.getValuePaid()
                        : itemDad.getValue());
            glossDetailsChild.setValuePaid(null);
            glossDetailsChild.setDatePaid(null);
          }
        }
      }
    }

    return gloss;
  }

  public PayInvoicePO insertPayInvoice(PayInvoicePO payInvoice) {

    return payInvoiceService.save(payInvoice);
  }

  @Override
  public void deletePayInvoice(PayInvoicePO payInvoice) {
    payInvoiceService.delete(payInvoice.getId());

  }

  @Override
  public InvoicePO calcImpostos(Long payId) {
    return invoiceService.calcImpostosPay(payId);
  }

  @Override
  public BillingPayOrderItemPO updateValuePaid(Long billingPayOrderItemId, ValuePaidRequestTO valuePaid) {
    var billingPayOrderItem = billingPayOrderItemService.updateValuePaid(billingPayOrderItemId, valuePaid);
    return billingPayOrderItem;
  }

  @Override
  public void deleteBillingPayOrderItem(Long id) {
    var billingPayOrderItem = billingPayOrderItemService.findById(id);
    if (billingPayOrderItem != null) {
      billingPayOrderItemService.delete(id);
    } else {
      throw new ApplicationException("Item já retirado do pagamento");
    }

  }

  @Override
  public List<BillingOrderItemPO> getRecoveryBillingPayorderItem(ArrayList<Long> billingIdList) {
    return payService.getRecoveryBillingPayorderItem(billingIdList);
  }

  @Override
  public List<BillingPayOrderItemPO> recoveryBillingPayorderItem(Long payId,
      ArrayList<BillingOrderItemPO> billingOrderItemList) {
    return payService.recoveryBillingPayorderItem(payId, billingOrderItemList);
  }

  @Override
  public Boolean checkPayGloss(Long glossId) {
    return glossService.checkPayGloss(glossId);
  }

  @Override
  public Double calcCreditValue(Long payId) {
    return payService.calcCreditValue(payId);
  }

  @Override
  public Page<BillingPayOrderItemPO> searchBillingPayOrderItem(PageSearchTO pageSearchTO, Long payId) {
    return billingPayOrderItemService.searchBillingPayOrderItem(pageSearchTO, payId);
  }

  @Override
  public PayPO saveAutomatic(PayPO billingPayPO, String xmlBase64, String xmlName) throws JAXBException {
    return payService.saveAutomatic(billingPayPO, xmlBase64, xmlName);
  }

  @Override
  public List<PayEartefactPO> findEartefact(Long payId) {
    return payEartefactService.findByPay_id(payId);
  }

  @Override
  public Page<PayXmlPO> findPayXMlByPayId(Long payId, String type, PageSearchTO pageSearchTO) {
    return payService.findPayXMlByPayId(payId, type, pageSearchTO);
  }

  @Override
  public List<String> findProtocolByPaiId(Long payId) {
    return payXmlService.findProtocolByPaiId(payId);
  }

  @Override
  public Page<AccountTO> findByAccount(PageSearchTO pageSearchTO) {
    return accountFacade.searchAccount(pageSearchTO);
  }

  @Override
  public List<PayBondPayTO> findBondByPayId(Long payid) {
    return bondFacade.findBondByPayId(payid);
  }

  @Transactional
  @Override
  public void processBondPay(List<PayBondPayTO> payBondPay) {
    List<BondPayTO> bondPayToList = new ArrayList<>();
    for (PayBondPayTO pay : payBondPay) {

      if (pay.getType().equals("C")) {
        sendingToFinanceFacade.sendBillingByPayFinancial(pay);
      } else {
        bondPayToList.add(BondPayTO.builder()
            .value(pay.getValueCredited())
            .account(new AccountTO(pay.getAccountId()))
            .dated(pay.getDateCredited())
            .discount(0.0)
            .interest(0.0)
            .penalty(0.0)
            .bondId(pay.getBondId())
            .note("Recebimento gerado pela baixa de fatura")
            .build());
        sendingToFinanceItemFacade.setPayInSendToitem(pay.getBillingId(), pay.getPayId());
      }
    };
    
    for (BondPayTO bondPay : bondPayToList) {
      bondFacade.insertBondPay(bondPay);
    }
  }

  @Override
  public AccountTO getInsuranceAccount(Long insuranceId) {
    return insuranceFacade.findAccountByInsuranceId(insuranceId);
  }
}
