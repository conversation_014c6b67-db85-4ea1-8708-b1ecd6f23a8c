
package br.com.focusts.clinicall.service.modules.tables.general.facade;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.general.po.ContactListPO;
import br.com.focusts.clinicall.service.modules.tables.general.po.PersonPO;
import br.com.focusts.clinicall.service.modules.tables.general.to.PersonTO;

public interface PersonFacade extends CrudFacade<PersonPO,java.lang.Long>{

    public Page<PersonPO> findByNameContainingOrAliasContainingOrSocialNameContainingOrCpfContainingOrCnsContainingOrNisContaining(PageSearchTO pageSearchTO);

	public PersonPO findByCpf(String cpf);

	public PersonPO findByCpfType(String cpf, String type);

    public ContactListPO findContactByPerson(Long personId);

     Page<PersonTO> findByName(PageSearchTO pageSearchTO);
}

