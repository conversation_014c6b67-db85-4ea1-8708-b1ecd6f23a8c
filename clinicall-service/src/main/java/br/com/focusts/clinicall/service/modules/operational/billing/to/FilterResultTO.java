package br.com.focusts.clinicall.service.modules.operational.billing.to;

import java.util.List;

import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPO;
import br.com.focusts.clinicall.service.modules.operational.reception.to.OrderBillingTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.domain.Page;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FilterResultTO {

    private Page<OrderBillingTO> orderList;
    private Page<OrderPO> orderList1;
    private List<OrderBillingTO> checkBillingPOList;
    private Double total;
    private Long quantityOrderItem;
    
}
