package br.com.focusts.clinicall.service.modules.operational.scheduling.repository;

import br.com.focusts.clinicall.fw.exception.ApplicationException;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.medical.po.QDocumentPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderPayPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderPayerPO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.enums.ScheduleCustomKeyEnum;
import br.com.focusts.clinicall.service.modules.operational.scheduling.enums.ScheduleStatusEnum;
import br.com.focusts.clinicall.service.modules.operational.scheduling.po.*;
import br.com.focusts.clinicall.service.modules.operational.scheduling.to.*;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.*;
import br.com.focusts.clinicall.service.modules.register.operational.po.QEventPO;
import br.com.focusts.clinicall.service.modules.register.operational.po.QPatientPO;
import br.com.focusts.clinicall.service.modules.register.organizational.enums.CompanyPerformerCustomEnum;
import br.com.focusts.clinicall.service.modules.register.organizational.enums.ShiftTypeEnum;
import br.com.focusts.clinicall.service.modules.register.organizational.po.*;
import br.com.focusts.clinicall.service.modules.register.organizational.repository.PerformerRepository;
import br.com.focusts.clinicall.service.modules.register.organizational.service.CompanyPerformerCustomService;
import br.com.focusts.clinicall.service.modules.register.organizational.to.PerformerTO;
import br.com.focusts.clinicall.service.modules.register.security.comparator.ScheduleComparator;
import br.com.focusts.clinicall.service.modules.register.security.comparator.ScheduleTOComparator;
import br.com.focusts.clinicall.service.modules.register.security.po.QUserPO;
import br.com.focusts.clinicall.service.modules.system.constants.GlobalKeyParameterConstants;
import br.com.focusts.clinicall.service.modules.system.po.ParameterPO;
import br.com.focusts.clinicall.service.modules.system.po.QParameterPO;
import br.com.focusts.clinicall.service.modules.system.po.QParameterValuePO;
import br.com.focusts.clinicall.service.modules.system.service.ParameterService;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.QGroupPO;
import br.com.focusts.clinicall.service.modules.tables.general.po.QContactListPO;
import br.com.focusts.clinicall.service.modules.tables.general.po.QOccupationPO;
import br.com.focusts.clinicall.service.modules.tables.general.po.QPersonPO;
import br.com.focusts.clinicall.service.modules.tables.general.repository.PersonRepository;
import br.com.focusts.clinicall.service.modules.tables.tiss.to.HeaderTransactionXmlTO;
import br.com.focusts.clinicall.util.DateUtil;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.group.GroupBy;
import com.querydsl.core.types.*;
import com.querydsl.core.types.dsl.*;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.jdbc.core.JdbcOperationsExtensionsKt.query;

@Repository
public class DefaultScheduleQueryDslRepository implements ScheduleQueryDslRepository {
    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private ParameterService parameterService;
    @Autowired
    private PersonRepository personRepository;
    @Autowired
    private PerformerRepository performerRepository;
    @Autowired
    private CompanyPerformerCustomService companyPerformerCustomService;

    @Override
    public List<SchedulePO> buscarBySchedulePatientOrPerson(ScheduleTO scheduleTO) {

        QSchedulePO qSchedulePO = QSchedulePO.schedulePO;
        QPatientPO qPatientPO = QPatientPO.patientPO;
        QPersonPO qPersonPO = new QPersonPO("p2");
        QPersonPO qPersonPO1 = new QPersonPO("p");

        JPAQuery<SchedulePO> query = new JPAQuery<>(entityManager);
        String filter = scheduleTO.getPatientName();
        String filter2 = scheduleTO.getPatientName();

        query.select(qSchedulePO)
                .from(qSchedulePO)
                .leftJoin(qPatientPO).on(qPatientPO.id.eq(qSchedulePO.patient.id).and(qSchedulePO.person.isNull()))
                .leftJoin(qPersonPO).on(qPersonPO.id.eq(qPatientPO.person.id))
                .leftJoin(qPersonPO1).on(qPersonPO1.id.eq(qSchedulePO.person.id).and(qSchedulePO.patient.isNull()))
                .where(qPersonPO.name.contains(filter).or(qPersonPO1.name.contains(filter2))
                        .and(qSchedulePO.date.between(scheduleTO.getStarted(), scheduleTO.getEnded())));

        var result = query.fetch();
        return result;
    }

    @Override
    public PerformerScheduleDateTO findPerformerSchedulesDate(ScheduleTO scheduleTO) {
        PerformerScheduleDateTO performerScheduleDateTO = new PerformerScheduleDateTO();
        performerScheduleDateTO.setSchedulesDate(new ArrayList<>());
        QSchedulePO qSchedulePO = QSchedulePO.schedulePO;
        JPAQuery<ScheduleDateStatusTO> query = new JPAQuery<>(entityManager);

        BooleanExpression availableExpression = Expressions
                .booleanTemplate("case when {0} >= current_date() then true else false end", qSchedulePO.date);

        BooleanExpression pastExpression = Expressions
                .booleanTemplate("case when (count({0}) + count({1})) = count({2}) then true else false end",
                        qSchedulePO.patient.id, qSchedulePO.person.id, qSchedulePO.id);

        query.select(
                Projections.bean(
                        ScheduleDateStatusTO.class,
                        qSchedulePO.date.as("date"),
                        availableExpression.as("available"),
                        pastExpression.as("past")

                ))
                .from(qSchedulePO)
                .groupBy(qSchedulePO.date)
                .orderBy(qSchedulePO.date.asc());

        if (scheduleTO.getPerformerId() != null) {
            query.where(qSchedulePO.performer.id.eq(scheduleTO.getPerformerId()));
        }
        if (scheduleTO.getCompanyId() != null) {
            query.where(qSchedulePO.company.id.eq(scheduleTO.getCompanyId()));
        }

        if (scheduleTO.getStarted() != null && scheduleTO.getEnded() != null) {
            query.where(qSchedulePO.date.between(scheduleTO.getStarted(), scheduleTO.getEnded()));
        }

        performerScheduleDateTO.getSchedulesDate().addAll(query.fetch());
        return performerScheduleDateTO;

    }

    @Override
    public List<PerformerTO> findPerformerBySchedule(ScheduleTO scheduleTO) {
        QSchedulePO schedule = QSchedulePO.schedulePO;
        QPerformerPO performer = QPerformerPO.performerPO;
        QProfessionalPO professional = QProfessionalPO.professionalPO;
        QPersonPO person = QPersonPO.personPO;
        QSpecialityPO speciality = QSpecialityPO.specialityPO;

        JPAQuery<PerformerTO> query = new JPAQuery<>(entityManager);

        query
                .select(Projections.bean(
                        PerformerTO.class,
                        person.name.as("performerName"),
                        performer.id.as("performerId"),
                        professional.id.as("professionalId"),
                        speciality.name.as("specialityName"),
                        speciality.id.as("specialityId")))
                .from(schedule)
                .join(performer).on(performer.id.eq(schedule.performer.id))
                .join(professional).on(professional.id.eq(performer.professional.id))
                .leftJoin(speciality).on(speciality.id.eq(professional.speciality.id))
                .join(person).on(person.id.eq(professional.person.id))
                .where(schedule.company.id.eq(scheduleTO.getCompanyId())
                        .and(schedule.date.between(scheduleTO.getStarted(), scheduleTO.getEnded()))
                        .and(performer.active.isTrue()))
                .groupBy(performer.id)
                .orderBy(person.name.asc());

        return query.fetch();
    }

    public List<ScheduleResponseTO> findByScheduleList(ScheduleTO scheduleTO) {

        List<String> parameterNameList = new ArrayList<>();
        parameterNameList.add(GlobalKeyParameterConstants.PATIENT_FINISH_SCHEDULE);
        ParameterPO parameterFinishSchedule = null;
        var result = parameterService.findListByName(parameterNameList);
        for (ParameterPO parameterPO : result) {
            if (parameterPO.getName().equals(GlobalKeyParameterConstants.PATIENT_FINISH_SCHEDULE)) {
                parameterFinishSchedule = parameterPO;
            }
        }

        QSchedulePO schedule = QSchedulePO.schedulePO;
        QSchedulePO scheduleParent = new QSchedulePO("scheduleParent");
        QCompanyPO company = QCompanyPO.companyPO;
        QProcedurePO procedure = QProcedurePO.procedurePO;
        QHoraryPO horary = QHoraryPO.horaryPO;
        QGroupPO groupHorary = QGroupPO.groupPO;
        QGroupPO groupSchedule = new QGroupPO("groupSchedule");
        QInsurancePO insurance = QInsurancePO.insurancePO;
        QPatientPO patient = QPatientPO.patientPO;
        QUserPO user = QUserPO.userPO;
        QSpecialityPO specialityHorary = new QSpecialityPO("specialityHorary");
        QPersonPO personPatient = new QPersonPO("personPatient");
        QPersonPO personSchedule = new QPersonPO("personSchedule");
        QPersonPO personUser = new QPersonPO("personUser");
        QPersonPO personPerformer = new QPersonPO("personPerformer");
        QInsurancePO insurancePatient = new QInsurancePO("insurancePatient");
        QPerformerPO performer = QPerformerPO.performerPO;
        QProfessionalPO professional = QProfessionalPO.professionalPO;
        QEventPO event = QEventPO.eventPO;
        QContactListPO contactListPatient = new QContactListPO("contactListPatient");
        QContactListPO contactListPerson = new QContactListPO("contactListPerson");
        QScheduleCustomPO scheduleCustom = new QScheduleCustomPO("scheduleCustom");
        QScheduleCustomPO scheduleCustomPriority = new QScheduleCustomPO("scheduleCustomPriority");
        QScheduleNotePO scheduleNote = new QScheduleNotePO("note");
        QOccupationPO occupation = new QOccupationPO("occupation");
        QCompanyPO eventCompany = new QCompanyPO("eventCompany");
        QPerformerPO eventPerformer = new QPerformerPO("eventPerformer");
        QProfessionalPO eventProfessional = new QProfessionalPO("eventProfessional");
        QPersonPO eventPerson = new QPersonPO("eventPerson");
        QShiftPO shift = QShiftPO.shiftPO;
        QDocumentPO document = QDocumentPO.documentPO;
        QScheduleOrderPO scheduleOrder = QScheduleOrderPO.scheduleOrderPO;
        QOrderPO order = QOrderPO.orderPO;
        QDocumentPO subDocument = new QDocumentPO("subDocument");
        QParameterValuePO parametervalueFinishList = new QParameterValuePO("parametervalueFinishList");
        QOrderPayPO orderPay = new QOrderPayPO("orderPay");

        QSchedulePO subSchedule = new QSchedulePO("subSchedule");
        // QOrderPayPO orderPay = new QOrderPayPO("orderPay");

        SubQueryExpression<Long> maxIdSubQueryPatient = JPAExpressions
                .select(contactListPatient.id.max())
                .from(contactListPatient)
                .where(contactListPatient.type.eq("S")
                        .and(contactListPatient.parentId.eq(personPatient.contactList.id)));

        SubQueryExpression<Long> maxIdSubQueryPerson = JPAExpressions
                .select(contactListPerson.id.max())
                .from(contactListPerson)
                .where(contactListPerson.type.eq("S")
                        .and(contactListPerson.parentId.eq(personSchedule.contactList.id)));

        SubQueryExpression<Long> maxDocumentId = JPAExpressions
                .select(subDocument.id.max())
                .from(subDocument)
                .where(subDocument.dateAt.eq(schedule.date)
                        .and(subDocument.type.eq(4))
                        .and(subDocument.performer.id.eq(schedule.performer.id))
                        .and(subDocument.patient.id.eq(schedule.patient.id))
                        .and(schedule.patient.id.isNotNull().or(schedule.person.id.isNotNull())));

        JPAQuery<ScheduleResponseTO> querySchedule = new JPAQuery<>(entityManager);
        JPAQuery<ScheduleResponseTO> queryEvent = new JPAQuery<>(entityManager);

        querySchedule.select(Projections.bean(
                ScheduleResponseTO.class,
                schedule.id.as("id"),
                schedule.date.as("date"),
                schedule.hour.as("hour"),
                schedule.arrivalTime.as("arrivalTime"),
                schedule.scheduleStatus.as("scheduleStatus"),
                schedule.patientStatus.as("patientStatus"),
                schedule.dateStatus.as("dateStatus"),
                schedule.hourStatus.as("hourStatus"),
                scheduleNote.note.as("note"),
                scheduleCustom.value.as("statusCustom"),
                scheduleCustomPriority.value.as("priority"),
                groupHorary.name.coalesce(groupSchedule.name).as("horaryGroupName"),
                company.id.as("companyId"),
                company.name.as("companyName"),
                company.alias.as("companyAlias"),
                patient.id.as("patientId"),
                personPatient.id.as("patientPersonId"),
                personPatient.name.as("patientPersonName"),
                personPatient.birthday.as("patientPersonBirthday"),
                personPatient.cpf.as("patientPersonCpf"),
                personPatient.cns.as("patientPersonCns"),
                contactListPatient.phone.as("patientPersonPhoneStandart"),
                patient.enrollment.as("patientEnrollment"),
                patient.holder.as("patientHolder"),
                patient.medicalRecord.as("patientMedicalRecord"),
                patient.active.as("patientActive"),
                patient.dated.as("patientDated"),
                occupation.name.as("patientOcupation"),
                insurancePatient.id.as("patientInsuranceId"),
                insurancePatient.name.as("patientInsuranceName"),
                personSchedule.id.as("personId"),
                personSchedule.name.as("personName"),
                personSchedule.birthday.as("personBirthday"),
                personSchedule.cpf.as("personCpf"),
                personSchedule.cns.as("personCns"),
                contactListPerson.phone.as("personPhoneStandart"),
                insurance.id.as("insuranceId"),
                insurance.name.as("insuranceName"),
                insurance.paymentModel.as("insurancePaymentModel"),
                procedure.id.as("procedureId"),
                procedure.name.as("procedureName"),
                procedure.alias.as("procedureAlias"),
                horary.id.as("horaryId"),
                specialityHorary.name.as("horarySpecialityName"),
                performer.id.as("performerId"),
                professional.id.as("performerProfessionalId"),
                personPerformer.id.as("performerProfessionalPersonId"),
                personPerformer.name.as("performerName"),
                personUser.name.as("userName"),
                schedule.session.id.as("sessionId"),
                user.id.as("userId"),
                document.id.as("documentId"),
                scheduleOrder.order.id.as("orderId"),
                order.session.id.as("orderSessionId"),
                new CaseBuilder()
                        .when(
                                JPAExpressions.selectOne()
                                        .from(scheduleParent)
                                        .where(scheduleParent.parent.id.eq(schedule.id))
                                        .exists())
                        .then(true)
                        .otherwise(false)
                        .as("whithChilden"),
                new CaseBuilder()
                        .when(order.id.isNotNull())
                        .then(
                                (Predicate) new CaseBuilder()
                                        .when(
                                                JPAExpressions
                                                        .select(orderPay.value.sum().coalesce(0.0))
                                                        .from(orderPay)
                                                        .leftJoin(order).on(order.eq(orderPay.order))
                                                        .leftJoin(scheduleOrder).on(scheduleOrder.order.eq(order))
                                                        .leftJoin(subSchedule)
                                                        .on(subSchedule.id.eq(scheduleOrder.schedule.id))
                                                        .where(
                                                                scheduleOrder.schedule.id.eq(schedule.id)
                                                                        .or(scheduleOrder.schedule.id
                                                                                .eq(schedule.parent.id)))
                                                        .lt(
                                                                JPAExpressions
                                                                        .select(order.value.sum().coalesce(0.0))
                                                                        .from(order)
                                                                        .leftJoin(scheduleOrder)
                                                                        .on(scheduleOrder.order.eq(order))
                                                                        .where(
                                                                                scheduleOrder.schedule.id
                                                                                        .eq(schedule.id)
                                                                                        .or(scheduleOrder.schedule.id
                                                                                                .eq(schedule.parent.id)))))
                                        .then(true)
                                        .otherwise(false))
                        .otherwise((Boolean) null)
                        .as("hasOverpayment")

        ))
                .from(schedule)
                .innerJoin(company).on(company.id.eq(schedule.company.id))
                .leftJoin(procedure).on(procedure.id.eq(schedule.procedure.id))
                .leftJoin(horary).on(horary.id.eq(schedule.horary.id))
                .leftJoin(groupSchedule).on(groupSchedule.id.eq(horary.group.id))
                .leftJoin(groupHorary).on(groupHorary.id.eq(schedule.group.id))
                .leftJoin(insurance).on(insurance.id.eq(schedule.insurance.id))
                .leftJoin(patient).on(patient.id.eq(schedule.patient.id))
                .leftJoin(personPatient).on(personPatient.id.eq(patient.person.id))
                .leftJoin(personSchedule).on(personSchedule.id.eq(schedule.person.id))
                .leftJoin(user).on(user.id.eq(schedule.user.id))
                .leftJoin(personUser).on(personUser.id.eq(user.person.id))
                .leftJoin(specialityHorary).on(specialityHorary.id.eq(schedule.horary.id))
                .leftJoin(insurancePatient).on(insurancePatient.id.eq(patient.insurance.id))
                .leftJoin(performer).on(performer.id.eq(schedule.performer.id))
                .leftJoin(professional).on(professional.id.eq(performer.professional.id))
                .leftJoin(personPerformer).on(personPerformer.id.eq(professional.person.id))
                .leftJoin(contactListPatient).on(contactListPatient.id.eq(maxIdSubQueryPatient))
                .leftJoin(contactListPerson).on(contactListPerson.id.eq(maxIdSubQueryPerson))
                .leftJoin(occupation).on(occupation.id.eq(personPatient.occupation.id))
                .leftJoin(scheduleNote).on(scheduleNote.schedule.id.eq(schedule.id))
                .leftJoin(scheduleOrder).on(scheduleOrder.schedule.id.eq(schedule.id))
                .leftJoin(order).on(order.id.eq(scheduleOrder.order.id))
                .leftJoin(document).on(document.id.eq(maxDocumentId))
                .leftJoin(scheduleCustom)
                .on(scheduleCustom.schedule.id.eq(schedule.id)
                        .and(scheduleCustom.key.eq(ScheduleCustomKeyEnum.CUSTOM.getValue())))
                .leftJoin(scheduleCustomPriority)
                .on(scheduleCustomPriority.schedule.id.eq(schedule.id)
                        .and(scheduleCustomPriority.key.eq(ScheduleCustomKeyEnum.PRIORITY.getValue())))
                .where(schedule.parent.isNull())
                .orderBy(schedule.hour.asc());

        List<String> shifts = new ArrayList<>();

        if (scheduleTO.getMorning() != null && scheduleTO.getMorning()) {
            shifts.add(ShiftTypeEnum.MORNING.getValue());
        }

        if (scheduleTO.getEvening() != null && scheduleTO.getEvening()) {
            shifts.add(ShiftTypeEnum.EVENING.getValue());
        }

        if (scheduleTO.getNight() != null && scheduleTO.getNight()) {
            shifts.add(ShiftTypeEnum.NIGHT.getValue());
        }

        if (!shifts.isEmpty()) {
            querySchedule.leftJoin(shift).on(shift.company.id.eq(company.id));
            querySchedule.where(shift.type.in(shifts));
            querySchedule.where(schedule.hour.between(shift.started, shift.ended));
        }

        if (scheduleTO.getPerformerProcedureList() != null && !scheduleTO.getPerformerProcedureList().isEmpty()) {
            List<Long> performerIdList = scheduleTO.getPerformerProcedureList().stream()
                    .map(PerformerProcedureTO::getPerformerId).toList();
            querySchedule.where(schedule.performer.id.in(performerIdList));
        }

        BooleanBuilder whereClauseSchedule = new BooleanBuilder();

        if (scheduleTO.getCompanyId() != null) {
            whereClauseSchedule.and(company.id.eq(scheduleTO.getCompanyId()));
        }
        if (scheduleTO.getPerformerId() != null) {
            whereClauseSchedule.and(performer.id.eq(scheduleTO.getPerformerId()));
        }
        if (scheduleTO.getStarted() != null) {
            whereClauseSchedule.and(schedule.date.between(scheduleTO.getStarted(), scheduleTO.getEnded()));
        }
        if (scheduleTO.getStartedTime() != null && scheduleTO.getEndedTime() != null) {
            whereClauseSchedule.and(schedule.hour.between(scheduleTO.getStartedTime(), scheduleTO.getEndedTime()));
        }
        if (scheduleTO.getPatientId() != null) {
            whereClauseSchedule.and(patient.id.eq(scheduleTO.getPatientId()));
        }
        if (scheduleTO.getType() != null && scheduleTO.getType().getValue().equals("C")) {
            whereClauseSchedule.and(patient.isNull().and(personSchedule.isNull()));
            // querySchedule.limit(scheduleTO.getLimit());
            if (scheduleTO.getNotInSchedule() != null && !scheduleTO.getNotInSchedule().isEmpty()) {
                querySchedule.where(schedule.id.notIn(scheduleTO.getNotInSchedule()));
            }
        }

        if (scheduleTO.getStatus() != null) {
            if (scheduleTO.getStatus().equals("NL")) {
                whereClauseSchedule.and(schedule.scheduleStatus.in("L", "B", "A", "C", "X", "E"));
            } else {
                whereClauseSchedule.and(schedule.scheduleStatus.eq(scheduleTO.getStatus()));
            }
        }

        if (scheduleTO.getPatientStatus() != null) {
            whereClauseSchedule.and(schedule.patientStatus.eq(scheduleTO.getPatientStatus()));
        }
        querySchedule.where(whereClauseSchedule);
        List<ScheduleResponseTO> schedulePOList = querySchedule.fetch();

        LocalDate startedDate = scheduleTO.getStarted();

        SimpleExpression<LocalDate> datedExpression = new CaseBuilder()
                .when(event.id.lt(0L)).then(startedDate)
                .otherwise(startedDate);

        queryEvent.select(Projections.bean(
                ScheduleResponseTO.class,
                ExpressionUtils.as(Expressions.constant(0L), "id"),
                ExpressionUtils.as(Expressions.constant(scheduleTO.getStarted()), "dated"),
                event.type.as("scheduleStatus"),
                event.type.as("patientStatus"),
                event.hour.as("hour"),
                event.name.as("message"),
                event.dated.as("date"),
                event.color.as("corMessage"),
                event.id.as("eventId")))
                .from(event)
                .leftJoin(eventCompany).on(eventCompany.id.eq(event.company.id))
                .leftJoin(eventPerformer).on(eventPerformer.id.eq(event.performer.id))
                .leftJoin(eventProfessional).on(eventProfessional.id.eq(eventPerformer.professional.id))
                .leftJoin(eventPerson).on(eventPerson.id.eq(eventProfessional.person.id));

        BooleanBuilder whereClauseEvent = new BooleanBuilder();

        if (scheduleTO.getCompanyId() != null) {
            whereClauseEvent.and(eventCompany.id.eq(scheduleTO.getCompanyId()).or(eventCompany.isNull()));
        }
        if (scheduleTO.getPerformerId() != null) {
            whereClauseEvent.and(eventPerformer.id.eq(scheduleTO.getPerformerId()).or(eventPerformer.isNull()));
        }
        if (scheduleTO.getStarted() != null) {
            whereClauseEvent
                    .and(event.dated.between(scheduleTO.getStarted(), scheduleTO.getEnded()).or(event.dated.isNull()));
        }
        queryEvent.where(whereClauseEvent);
        List<ScheduleResponseTO> eventPOList = queryEvent.fetch();

        if (!eventPOList.isEmpty() && scheduleTO.getPatientId() == null) {
            schedulePOList.addAll(eventPOList);
            schedulePOList.sort(Comparator.comparing(ScheduleResponseTO::getHour));
        }

        if (scheduleTO.getPerformerId() != null && scheduleTO.getPatientId() == null) {
            Optional<PerformerPO> performerPO = performerRepository.findById(scheduleTO.getPerformerId());
            if (performerPO.isPresent()) {
                String parameterOrderOfArrival = parameterService.findValueByParameterOrParameterValue(
                        GlobalKeyParameterConstants.ORDER_OF_ARRIVAL,
                        performerPO.get().getProfessional().getId().toString());
                Optional<CompanyPerformerCustomPO> performerCustomPO = companyPerformerCustomService
                        .findByPerformer_idAndCompany_idAndKey(performerPO.get().getId(), scheduleTO.getCompanyId(),
                                CompanyPerformerCustomEnum.ARRIVAL_ORDER_BY_COMPANY.getValue());

                if ((performerCustomPO.isPresent() && "S".equals(performerCustomPO.get().getValue()))
                        || "1".equals(parameterOrderOfArrival)) {
                    Collections.sort(schedulePOList, new ScheduleTOComparator());
                }
            }
        }
        if (scheduleTO.getPerformerId() != null) {
            PerformerPO performerP = performerRepository.findById(scheduleTO.getPerformerId()).get();
            String valueFinishSchedule = parameterService.findValueByParameterOrParameterValue(
                    GlobalKeyParameterConstants.PATIENT_FINISH_SCHEDULE,
                    performerP.getProfessional().getId().toString());

            if (valueFinishSchedule.equals("1") && scheduleTO.getScheduleProfessional() == true) {
                schedulePOList
                        .sort(Comparator.comparing((ScheduleResponseTO s) -> "X".equals(s.getPatientStatus()) ? 1 : 0)
                                .thenComparing(ScheduleResponseTO::getHour));
            }
        }
        return schedulePOList;
    }

    public List<ScheduleResponseCalendarTO> findScheduleModuleCalendar(ScheduleTO scheduleTO) {
        QSchedulePO schedule = QSchedulePO.schedulePO;
        QSchedulePO scheduleParent = new QSchedulePO("scheduleParent");
        QCompanyPO company = QCompanyPO.companyPO;
        QProcedurePO procedure = QProcedurePO.procedurePO;
        QHoraryPO horary = QHoraryPO.horaryPO;
        QGroupPO groupHorary = QGroupPO.groupPO;
        QGroupPO groupSchedule = new QGroupPO("groupSchedule");
        QInsurancePO insurance = QInsurancePO.insurancePO;
        QPatientPO patient = QPatientPO.patientPO;
        QUserPO user = QUserPO.userPO;
        QSpecialityPO specialityHorary = new QSpecialityPO("specialityHorary");
        QPersonPO personPatient = new QPersonPO("personPatient");
        QPersonPO personSchedule = new QPersonPO("personSchedule");
        QPersonPO personUser = new QPersonPO("personUser");
        QPersonPO personPerformer = new QPersonPO("personPerformer");
        QInsurancePO insurancePatient = new QInsurancePO("insurancePatient");
        QPerformerPO performer = QPerformerPO.performerPO;
        QProfessionalPO professional = QProfessionalPO.professionalPO;
        QEventPO event = QEventPO.eventPO;
        QContactListPO contactListPatient = new QContactListPO("contactListPatient");
        QContactListPO contactListPerson = new QContactListPO("contactListPerson");
        QScheduleCustomPO scheduleCustom = new QScheduleCustomPO("scheduleCustom");
        QScheduleCustomPO scheduleCustomPriority = new QScheduleCustomPO("scheduleCustomPriority");
        QScheduleNotePO scheduleNote = new QScheduleNotePO("note");

        QCompanyPO eventCompany = new QCompanyPO("eventCompany");
        QPerformerPO eventPerformer = new QPerformerPO("eventPerformer");
        QProfessionalPO eventProfessional = new QProfessionalPO("eventProfessional");
        QPersonPO eventPerson = new QPersonPO("eventPerson");

        SubQueryExpression<Long> maxIdSubQueryPatient = JPAExpressions
                .select(contactListPatient.id.max())
                .from(contactListPatient)
                .where(contactListPatient.type.eq("S")
                        .and(contactListPatient.parentId.eq(personPatient.contactList.id)));

        SubQueryExpression<Long> maxIdSubQueryPerson = JPAExpressions
                .select(contactListPerson.id.max())
                .from(contactListPerson)
                .where(contactListPerson.type.eq("S")
                        .and(contactListPerson.parentId.eq(personSchedule.contactList.id)));

        JPAQuery<ScheduleResponseCalendarTO> querySchedule = new JPAQuery<>(entityManager);
        JPAQuery<ScheduleResponseCalendarTO> queryEvent = new JPAQuery<>(entityManager);

        var titleCondition = Expressions.stringTemplate(
                "CASE " +
                        "WHEN {0} IS NOT NULL THEN {0} " + // Se personPatient.name existir, use ele
                        "WHEN {1} IS NOT NULL THEN {1} " + // Senão, use personSchedule.name
                        "WHEN {2} = 'B' THEN 'Bloqueado' " + // Senão, se schedule.scheduleStatus for 'B', use
                                                             // 'Bloqueado'
                        "WHEN {2} = 'X' THEN 'Extra' " + // Senão, se schedule.scheduleStatus for 'X', use 'Extra'
                        "ELSE 'Livre' END", // Caso contrário, use 'Livre'
                personPatient.name, // {0}
                personSchedule.name, // {1}
                schedule.scheduleStatus // {2}
        );

        querySchedule.select(Projections.bean(
                ScheduleResponseCalendarTO.class,
                schedule.id.as("id"),
                titleCondition.as("title"),
                personPatient.name.coalesce(personSchedule.name).as("personName"),
                procedure.name.as("procedureName"),
                performer.id.as("performerId"),
                personPerformer.name.as("performerName"),
                schedule.patientStatus.as("patientStatus"),
                schedule.scheduleStatus.as("scheduleStatus"),
                schedule.patient.id.as("patientId"),
                schedule.person.id.as("personId"),
                scheduleNote.note.as("note"),
                company.id.as("companyId"),
                company.alias.as("companyName"),
                insurance.alias.as("insuranceName"),
                schedule.session.id.as("sessionId"),
                contactListPatient.phone.coalesce(contactListPerson.phone).as("contact"),
                schedule.hour.as("hour"),
                Expressions.stringTemplate(
                        "CONCAT({0}, ' ', {1})",
                        schedule.date,
                        schedule.hour).as("start"),
                Expressions.stringTemplate(
                        "CONCAT({0}, ' ', {1})",
                        schedule.date,
                        schedule.hourFinal).as("end"),
                Expressions.stringTemplate(
                        "CASE " +
                                "WHEN {1} = 'Livre' THEN '#b1ffb4' " +
                                "WHEN {1} = 'Extra' THEN '#b1ffb4' " +
                                "WHEN {1} = 'Bloqueado' THEN '#ea8f85' " +
                                "ELSE " +
                                "CASE {0} " +
                                "WHEN 'Q' THEN '#757575' " +
                                "WHEN 'B' THEN '#ffffff' " +
                                "WHEN 'T' THEN '#ef9a9a' " +
                                "WHEN 'F' THEN '#81c784' " +
                                "WHEN 'L' THEN '#ce93d8' " +
                                "WHEN 'Z' THEN '#b0bec5' " +
                                "WHEN 'W' THEN '#ffab91' " +
                                "WHEN 'A' THEN '#b2ebf2' " +
                                "WHEN 'C' THEN '#ffe0b2' " +
                                "WHEN 'X' THEN '#ffee58' " +
                                "WHEN 'P' THEN '#e1bee7' " +
                                "WHEN 'E' THEN '#c8e6c9' " +
                                "WHEN 'U' THEN '#e57373' " +
                                "WHEN 'R' THEN '#ffcdd2' " +
                                "WHEN 'I' THEN '#f44336' " +
                                "WHEN 'S' THEN '#90a4ae' " +
                                "WHEN 'S' THEN '#90a4ae' " +
                                // "WHEN 'N' THEN '#1' " +
                                "ELSE '' END " + // Cor padrão para patientStatus
                                "END",
                        schedule.patientStatus, // {0}
                        titleCondition)
                        .as("backgroundColor"),
                ExpressionUtils.as(Expressions.constant(false), "allDay"),
                ExpressionUtils.as(Expressions.constant("#000000"), "textColor"),
                new CaseBuilder()
                        .when(
                                JPAExpressions.selectOne()
                                        .from(scheduleParent)
                                        .where(scheduleParent.parent.id.eq(schedule.id))
                                        .exists())
                        .then(true)
                        .otherwise(false)
                        .as("whithChilden")));

        querySchedule.from(schedule)
                .innerJoin(company).on(company.id.eq(schedule.company.id))
                .leftJoin(procedure).on(procedure.id.eq(schedule.procedure.id))
                .leftJoin(horary).on(horary.id.eq(schedule.horary.id))
                .leftJoin(groupSchedule).on(groupSchedule.id.eq(horary.group.id))
                .leftJoin(groupHorary).on(groupHorary.id.eq(schedule.group.id))
                .leftJoin(insurance).on(insurance.id.eq(schedule.insurance.id))
                .leftJoin(patient).on(patient.id.eq(schedule.patient.id))
                .leftJoin(personPatient).on(personPatient.id.eq(patient.person.id))
                .leftJoin(personSchedule).on(personSchedule.id.eq(schedule.person.id))
                .leftJoin(user).on(user.id.eq(schedule.user.id))
                .leftJoin(personUser).on(personUser.id.eq(user.person.id))
                .leftJoin(specialityHorary).on(specialityHorary.id.eq(schedule.horary.id))
                .leftJoin(insurancePatient).on(insurancePatient.id.eq(patient.insurance.id))
                .leftJoin(performer).on(performer.id.eq(schedule.performer.id))
                .leftJoin(professional).on(professional.id.eq(performer.professional.id))
                .leftJoin(personPerformer).on(personPerformer.id.eq(professional.person.id))
                .leftJoin(contactListPatient).on(contactListPatient.id.eq(maxIdSubQueryPatient))
                .leftJoin(contactListPerson).on(contactListPerson.id.eq(maxIdSubQueryPerson))
                .leftJoin(scheduleNote).on(scheduleNote.schedule.id.eq(schedule.id))
                .leftJoin(scheduleCustom)
                .on(scheduleCustom.schedule.id.eq(schedule.id)
                        .and(scheduleCustom.key.eq(ScheduleCustomKeyEnum.CUSTOM.getValue())))
                .leftJoin(scheduleCustomPriority)
                .on(scheduleCustomPriority.schedule.id.eq(schedule.id)
                        .and(scheduleCustomPriority.key.eq(ScheduleCustomKeyEnum.PRIORITY.getValue())))
                .where(schedule.parent.isNull())
                .orderBy(schedule.hour.asc());

        BooleanBuilder whereClauseSchedule = new BooleanBuilder();

        if (scheduleTO.getCompanyId() != null) {
            whereClauseSchedule.and(company.id.eq(scheduleTO.getCompanyId()));
        }
        if (scheduleTO.getPerformerId() != null) {
            whereClauseSchedule.and(performer.id.eq(scheduleTO.getPerformerId()));
        }
        if (scheduleTO.getStarted() != null) {
            whereClauseSchedule.and(schedule.date.between(scheduleTO.getStarted(), scheduleTO.getEnded()));
        }
        if (scheduleTO.getStartedTime() != null && scheduleTO.getEndedTime() != null) {
            whereClauseSchedule.and(schedule.hour.between(scheduleTO.getStartedTime(), scheduleTO.getEndedTime()));
        }
        if (scheduleTO.getPatientId() != null) {
            whereClauseSchedule.and(patient.id.eq(scheduleTO.getPatientId()));
        }
        if (scheduleTO.getType() != null && scheduleTO.getType().getValue().equals("C")) {
            whereClauseSchedule.and(patient.isNull().and(personSchedule.isNull()));
        }

        if (scheduleTO.getStatus() != null) {
            if (scheduleTO.getStatus().equals("NL")) {
                whereClauseSchedule.and(schedule.scheduleStatus.in("L", "B", "A", "C", "X", "E"));
            } else {
                whereClauseSchedule.and(schedule.scheduleStatus.eq(scheduleTO.getStatus()));
            }
        }

        if (scheduleTO.getPatientStatus() != null) {
            whereClauseSchedule.and(schedule.patientStatus.eq(scheduleTO.getPatientStatus()));
        }

        if (scheduleTO.getScheduleId() != null) {
            whereClauseSchedule.and(schedule.id.eq(scheduleTO.getScheduleId()));
        }
        querySchedule.where(whereClauseSchedule);
        List<ScheduleResponseCalendarTO> schedulePOList = querySchedule.fetch();

        List<ScheduleResponseCalendarTO> eventPOList = new ArrayList<>();
        if (scheduleTO.getScheduleId() == null) {
            LocalDate startedDate = scheduleTO.getStarted();

            SimpleExpression<LocalDate> datedExpression = new CaseBuilder()
                    .when(event.id.lt(0L)).then(startedDate)
                    .otherwise(startedDate);

            queryEvent.select(Projections.bean(
                    ScheduleResponseCalendarTO.class,
                    ExpressionUtils.as(Expressions.constant(0L), "id"),
                    event.name.as("title"),
                    event.color.as("color"),
                    event.id.as("eventId"),
                    Expressions.stringTemplate(
                            "CONCAT({0})",
                            event.dated).as("start"),
                    ExpressionUtils.as(Expressions.constant(true), "allDay")

            ))
                    .from(event)
                    .leftJoin(eventCompany).on(eventCompany.id.eq(event.company.id))
                    .leftJoin(eventPerformer).on(eventPerformer.id.eq(event.performer.id))
                    .leftJoin(eventProfessional).on(eventProfessional.id.eq(eventPerformer.professional.id))
                    .leftJoin(eventPerson).on(eventPerson.id.eq(eventProfessional.person.id));

            BooleanBuilder whereClauseEvent = new BooleanBuilder();

            if (scheduleTO.getCompanyId() != null) {
                whereClauseEvent.and(eventCompany.id.eq(scheduleTO.getCompanyId()).or(eventCompany.isNull()));
            }
            if (scheduleTO.getPerformerId() != null) {
                whereClauseEvent.and(eventPerformer.id.eq(scheduleTO.getPerformerId()).or(eventPerformer.isNull()));
            }
            if (scheduleTO.getStarted() != null) {
                whereClauseEvent.and(
                        event.dated.between(scheduleTO.getStarted(), scheduleTO.getEnded()).or(event.dated.isNull()));
            }
            queryEvent.where(whereClauseEvent);
            eventPOList = queryEvent.fetch();
        }
        if (!eventPOList.isEmpty() && scheduleTO.getPatientId() == null) {
            schedulePOList.addAll(eventPOList);
            schedulePOList.sort(Comparator.comparing(ScheduleResponseCalendarTO::getStart));
        }

        return schedulePOList;
    }

    public List<SchedulePartnersTO> findByScheduleListPartners(ScheduleTO scheduleTO) {
        List<String> parameterNameList = new ArrayList<>();
        parameterNameList.add(GlobalKeyParameterConstants.ORDER_OF_ARRIVAL);
        parameterNameList.add(GlobalKeyParameterConstants.SEND_WHATSAPP_PATIENT);
        parameterNameList.add(GlobalKeyParameterConstants.SEND_WHATSAPP_INSURANCE);
        parameterNameList.add(GlobalKeyParameterConstants.SEND_WHATSAPP_COMPANY);
        parameterNameList.add(GlobalKeyParameterConstants.SEND_WHATSAPP_PROCEDURE);
        parameterNameList.add(GlobalKeyParameterConstants.SEND_WHATSAPP_PERFORMER);
        ParameterPO parameterOrderOfArrival = null;
        ParameterPO parameterSendPatient = null;
        ParameterPO parameterSendInsurance = null;
        ParameterPO parameterSendCompany = null;
        ParameterPO parameterSendProcedure = null;
        ParameterPO parameterSendPerformer = null;
        var result = parameterService.findListByName(parameterNameList);
        for (ParameterPO parameterPO : result) {
            if (parameterPO.getName().equals(GlobalKeyParameterConstants.ORDER_OF_ARRIVAL)) {
                parameterOrderOfArrival = parameterPO;
            } else if (parameterPO.getName().equals(GlobalKeyParameterConstants.SEND_WHATSAPP_PATIENT)) {
                parameterSendPatient = parameterPO;
            } else if (parameterPO.getName().equals(GlobalKeyParameterConstants.SEND_WHATSAPP_INSURANCE)) {
                parameterSendInsurance = parameterPO;
            } else if (parameterPO.getName().equals(GlobalKeyParameterConstants.SEND_WHATSAPP_COMPANY)) {
                parameterSendCompany = parameterPO;
            } else if (parameterPO.getName().equals(GlobalKeyParameterConstants.SEND_WHATSAPP_PROCEDURE)) {
                parameterSendProcedure = parameterPO;
            } else if (parameterPO.getName().equals(GlobalKeyParameterConstants.SEND_WHATSAPP_PERFORMER)) {
                parameterSendPerformer = parameterPO;
            }

        }

        QSchedulePO schedule = QSchedulePO.schedulePO;
        QCompanyPO company = QCompanyPO.companyPO;
        QParameterValuePO parametervalueOrderOfArrival = QParameterValuePO.parameterValuePO;
        QParameterValuePO parametervalueSendPatient = new QParameterValuePO("parametervalueSendPatient");
        QParameterValuePO parametervalueSendInsurance = new QParameterValuePO("parametervalueSendInsurance");
        QParameterValuePO parametervalueSendCompany = new QParameterValuePO("parametervalueSendCompany");
        QParameterValuePO parametervalueSendProcedure = new QParameterValuePO("parametervalueSendProcedure");
        QParameterValuePO parametervalueSendPerformer = new QParameterValuePO("parametervalueSendPerformer");
        QProcedurePO procedure = QProcedurePO.procedurePO;
        QProcedureSpecsPO procedureSpec = QProcedureSpecsPO.procedureSpecsPO;
        QHoraryPO horary = QHoraryPO.horaryPO;
        QGroupPO groupHorary = QGroupPO.groupPO;
        QGroupPO groupSchedule = new QGroupPO("groupSchedule");
        QInsurancePO insurance = QInsurancePO.insurancePO;
        QPatientPO patient = QPatientPO.patientPO;
        QUserPO user = QUserPO.userPO;
        QSpecialityPO specialityHorary = new QSpecialityPO("specialityHorary");
        QPersonPO personPatient = new QPersonPO("personPatient");
        QPersonPO personSchedule = new QPersonPO("personSchedule");
        QPersonPO personUser = new QPersonPO("personUser");
        QPersonPO personPerformer = new QPersonPO("personPerformer");
        QInsurancePO insurancePatient = new QInsurancePO("insurancePatient");
        QPerformerPO performer = QPerformerPO.performerPO;
        QProfessionalPO professional = QProfessionalPO.professionalPO;
        QEventPO event = QEventPO.eventPO;
        QContactListPO contactListPatient = new QContactListPO("contactListPatient");
        QContactListPO contactListPerson = new QContactListPO("contactListPerson");
        QScheduleCustomPO scheduleCustom = new QScheduleCustomPO("scheduleCustom");
        QScheduleCustomPO scheduleCustomPriority = new QScheduleCustomPO("scheduleCustomPriority");
        QScheduleNotePO scheduleNote = new QScheduleNotePO("note");

        QScheduleConfirmPO scheduleConfirm = QScheduleConfirmPO.scheduleConfirmPO;

        SubQueryExpression<Long> maxIdSubQueryPatient = JPAExpressions
                .select(contactListPatient.id.max())
                .from(contactListPatient)
                .where(contactListPatient.type.eq("S")
                        .and(contactListPatient.parentId.eq(personPatient.contactList.id)));

        SubQueryExpression<Long> maxIdSubQueryPerson = JPAExpressions
                .select(contactListPerson.id.max())
                .from(contactListPerson)
                .where(contactListPerson.type.eq("S")
                        .and(contactListPerson.parentId.eq(personSchedule.contactList.id)));

        JPAQuery<SchedulePartnersTO> querySchedule = new JPAQuery<>(entityManager);

        querySchedule.select(Projections.bean(
                SchedulePartnersTO.class,
                schedule.id.as("id"),
                schedule.date.as("date"),
                schedule.hour.as("hour"),
                schedule.arrivalTime.as("arrivalTime"),
                schedule.scheduleStatus.as("scheduleStatus"),
                schedule.patientStatus.as("patientStatus"),
                schedule.dateStatus.as("dateStatus"),
                schedule.hourStatus.as("hourStatus"),
                scheduleNote.note.as("note"),
                scheduleCustom.value.as("statusCustom"),
                scheduleCustomPriority.value.as("priority"),
                groupHorary.name.coalesce(groupSchedule.name).as("horaryGroupName"),
                company.id.as("companyId"),
                company.name.as("companyName"),
                company.alias.as("companyAlias"),
                patient.id.as("patientId"),
                personSchedule.id.coalesce(personPatient.id).as("personId"),
                personSchedule.name.coalesce(personPatient.name).as("patientName"),
                personSchedule.socialName.coalesce(personPatient.socialName).as("patientSocialName"),
                personSchedule.birthday.coalesce(personPatient.birthday).as("patientBirthday"),
                personSchedule.cpf.coalesce(personPatient.cpf).as("patientCpf"),
                personSchedule.cns.coalesce(personPatient.cns).as("patientCns"),
                contactListPerson.phone.coalesce(contactListPatient.phone).as("patientPhoneStandart"),
                patient.enrollment.as("patientEnrollment"),
                patient.holder.as("patientHolder"),
                patient.medicalRecord.as("patientMedicalRecord"),
                patient.active.as("patientActive"),
                patient.dated.as("patientDated"),
                insurance.id.as("insuranceId"),
                insurance.name.as("insuranceName"),
                procedure.id.as("procedureId"),
                procedure.name.as("procedureName"),
                procedure.alias.as("procedureAlias"),
                procedureSpec.preparation.as("preparation"),
                horary.id.as("horaryId"),
                specialityHorary.name.as("horarySpecialityName"),
                performer.id.as("performerId"),
                professional.id.as("performerProfessionalId"),
                personPerformer.id.as("performerProfessionalPersonId"),
                personPerformer.name.as("performerName"),
                personUser.name.as("userName"),
                schedule.session.id.as("sessionId"),
                user.id.as("userId"),
                new CaseBuilder().when(parametervalueOrderOfArrival.value.eq("1")).then(true).otherwise(false)
                        .as("orderOfArrival")))
                .from(schedule)
                .innerJoin(company).on(company.id.eq(schedule.company.id))
                .leftJoin(parametervalueSendCompany)
                .on(parametervalueSendCompany.parameter.id.eq(parameterSendCompany.getId())
                        .and(parametervalueSendCompany.key.eq(company.id.stringValue())))
                .leftJoin(procedure).on(procedure.id.eq(schedule.procedure.id))
                .leftJoin(parametervalueSendProcedure)
                .on(parametervalueSendProcedure.parameter.id.eq(parameterSendProcedure.getId())
                        .and(parametervalueSendProcedure.key.eq(procedure.id.stringValue())))
                .leftJoin(procedureSpec).on(procedureSpec.procedure.id.eq(procedure.id))
                .leftJoin(horary).on(horary.id.eq(schedule.horary.id))
                .leftJoin(groupSchedule).on(groupSchedule.id.eq(horary.group.id))
                .leftJoin(groupHorary).on(groupHorary.id.eq(schedule.group.id))
                .leftJoin(insurance).on(insurance.id.eq(schedule.insurance.id))
                .leftJoin(parametervalueSendInsurance)
                .on(parametervalueSendInsurance.parameter.id.eq(parameterSendInsurance.getId())
                        .and(parametervalueSendInsurance.key.eq(insurance.id.stringValue())))
                .leftJoin(patient).on(patient.id.eq(schedule.patient.id))
                .leftJoin(parametervalueSendPatient)
                .on(parametervalueSendPatient.parameter.id.eq(parameterSendPatient.getId())
                        .and(parametervalueSendPatient.key.eq(patient.id.stringValue())))
                .leftJoin(personPatient).on(personPatient.id.eq(patient.person.id))
                .leftJoin(personSchedule).on(personSchedule.id.eq(schedule.person.id))
                .leftJoin(user).on(user.id.eq(schedule.user.id))
                .leftJoin(personUser).on(personUser.id.eq(user.person.id))
                .leftJoin(specialityHorary).on(specialityHorary.id.eq(horary.speciality.id))
                .leftJoin(insurancePatient).on(insurancePatient.id.eq(patient.insurance.id))
                .leftJoin(performer).on(performer.id.eq(schedule.performer.id))
                .leftJoin(professional).on(professional.id.eq(performer.professional.id))
                .leftJoin(parametervalueSendPerformer)
                .on(parametervalueSendPerformer.parameter.id.eq(parameterSendPerformer.getId())
                        .and(parametervalueSendPerformer.key.eq(professional.id.stringValue())))
                .leftJoin(personPerformer).on(personPerformer.id.eq(professional.person.id))
                .leftJoin(parametervalueOrderOfArrival)
                .on(parametervalueOrderOfArrival.parameter.id.eq(parameterOrderOfArrival.getId())
                        .and(parametervalueOrderOfArrival.key.eq(professional.id.stringValue())))
                .leftJoin(contactListPatient).on(contactListPatient.id.eq(maxIdSubQueryPatient))
                .leftJoin(contactListPerson).on(contactListPerson.id.eq(maxIdSubQueryPerson))
                .leftJoin(scheduleNote).on(scheduleNote.schedule.id.eq(schedule.id))
                .leftJoin(scheduleCustom)
                .on(scheduleCustom.schedule.id.eq(schedule.id)
                        .and(scheduleCustom.key.eq(ScheduleCustomKeyEnum.CUSTOM.getValue())))
                .leftJoin(scheduleCustomPriority)
                .on(scheduleCustomPriority.schedule.id.eq(schedule.id)
                        .and(scheduleCustomPriority.key.eq(ScheduleCustomKeyEnum.PRIORITY.getValue())))
                .orderBy(schedule.hour.asc());

        BooleanBuilder whereClauseSchedule = new BooleanBuilder();

        if (scheduleTO.getCompanyId() != null) {
            whereClauseSchedule.and(company.id.eq(scheduleTO.getCompanyId()));
        }
        if (scheduleTO.getPerformerId() != null) {
            whereClauseSchedule.and(performer.id.eq(scheduleTO.getPerformerId()));
        }
        if (scheduleTO.getStarted() != null) {
            whereClauseSchedule.and(schedule.date.between(scheduleTO.getStarted(), scheduleTO.getEnded()));
        }
        if (scheduleTO.getStartedTime() != null && scheduleTO.getEndedTime() != null) {
            whereClauseSchedule.and(schedule.hour.between(scheduleTO.getStartedTime(), scheduleTO.getEndedTime()));
        }
        if (scheduleTO.getPatientId() != null) {
            whereClauseSchedule.and(patient.id.eq(scheduleTO.getPatientId()));
        }

        if (scheduleTO.getScheduledOnly() != null) {
            if (scheduleTO.getScheduledOnly()) {
                whereClauseSchedule.and(patient.isNotNull().or(personSchedule.isNotNull()));
            } else {
                whereClauseSchedule.and(patient.isNull().and(personSchedule.isNull()));
            }
        }

        if (scheduleTO.getType() != null && scheduleTO.getType().getValue().equals("C")) {
            whereClauseSchedule.and(patient.isNull().and(personSchedule.isNull()));
        }

        whereClauseSchedule.and(parametervalueSendCompany.value.coalesce(parameterSendCompany.getValue()).eq("1"));
        whereClauseSchedule.and(parametervalueSendPatient.value.coalesce(parameterSendPatient.getValue()).eq("1"));
        whereClauseSchedule.and(parametervalueSendInsurance.value.coalesce(parameterSendCompany.getValue()).eq("1"));
        whereClauseSchedule.and(parametervalueSendProcedure.value.coalesce(parameterSendCompany.getValue()).eq("1"));
        whereClauseSchedule.and(parametervalueSendPerformer.value.coalesce(parameterSendPatient.getValue()).eq("1"));

        if (scheduleTO.getConfirm() != null) {
            if (scheduleTO.getConfirm() == true) {
                querySchedule.join(scheduleConfirm).on(scheduleConfirm.schedule.id.eq(schedule.id));
            } else if (scheduleTO.getConfirm() == false) {
                querySchedule.where(JPAExpressions.selectOne().from(scheduleConfirm)
                        .where(scheduleConfirm.schedule.id.eq(schedule.id)).notExists()
                        .and(personSchedule.isNotNull().or(patient.isNotNull())));
            }
        }

        if (scheduleTO.getStatus() != null) {
            if (scheduleTO.getStatus().equals("NL")) {
                whereClauseSchedule.and(schedule.scheduleStatus.in("L", "B", "A", "C", "X", "E"));
            } else {
                whereClauseSchedule.and(schedule.scheduleStatus.eq(scheduleTO.getStatus()));
            }
        } else {
            whereClauseSchedule.and(schedule.scheduleStatus.ne(ScheduleStatusEnum.BLOCKED.getValue()));
        }

        if (scheduleTO.getPatientStatus() != null) {
            whereClauseSchedule.and(schedule.patientStatus.eq(scheduleTO.getPatientStatus()));
        }
        querySchedule.where(whereClauseSchedule);
        List<SchedulePartnersTO> schedulePOList = querySchedule.fetch();

        return schedulePOList;
    }

    public Map<String, List<ScheduleResponseTO>> findByScheduleListByHoraryPerformer(ScheduleTO scheduleTO) {
        QSchedulePO schedule = QSchedulePO.schedulePO;
        QSchedulePO scheduleParent = new QSchedulePO("scheduleParent");
        QCompanyPO company = QCompanyPO.companyPO;
        QProcedurePO procedure = QProcedurePO.procedurePO;
        QHoraryPO horary = QHoraryPO.horaryPO;
        QInsurancePO insurance = QInsurancePO.insurancePO;
        QPatientPO patient = QPatientPO.patientPO;
        QUserPO user = QUserPO.userPO;

        QEnvironmentPO environment = QEnvironmentPO.environmentPO;

        QShiftPO shiftMat = new QShiftPO("shiftMat");
        QShiftPO shiftVes = new QShiftPO("shiftVes");
        QShiftPO shiftNot = new QShiftPO("shiftNot");

        QSpecialityPO specialityHorary = new QSpecialityPO("specialityHorary");
        QPersonPO personPatient = new QPersonPO("personPatient");
        QPersonPO personSchedule = new QPersonPO("personSchedule");
        QPersonPO personUser = new QPersonPO("personUser");
        QPersonPO personPerformer = new QPersonPO("personPerformer");
        QInsurancePO insurancePatient = new QInsurancePO("insurancePatient");
        QPerformerPO performer = QPerformerPO.performerPO;
        QProfessionalPO professional = QProfessionalPO.professionalPO;
        QEventPO event = QEventPO.eventPO;
        QContactListPO contactListPatient = new QContactListPO("contactListPatient");
        QContactListPO contactListPerson = new QContactListPO("contactListPerson");
        QContactListPO contactListStandardPerson = new QContactListPO("contactListStandardPerson");
        QContactListPO contactListStandardPatient = new QContactListPO("contactListStandardPatient");
        QScheduleCustomPO scheduleCustom = new QScheduleCustomPO("scheduleCustom");
        QScheduleCustomPO scheduleCustomPriority = new QScheduleCustomPO("scheduleCustomPriority");
        QScheduleNotePO scheduleNote = new QScheduleNotePO("note");

        QCompanyPO eventCompany = new QCompanyPO("eventCompany");
        QPerformerPO eventPerformer = new QPerformerPO("eventPerformer");
        QProfessionalPO eventProfessional = new QProfessionalPO("eventProfessional");
        QPersonPO eventPerson = new QPersonPO("eventPerson");

        JPAQuery<ScheduleResponseTO> querySchedule = new JPAQuery<>(entityManager);
        JPAQuery<ScheduleResponseTO> queryEvent = new JPAQuery<>(entityManager);

        querySchedule.select(Projections.bean(
                ScheduleResponseTO.class,
                schedule.id.as("id"),
                schedule.date.as("date"),
                schedule.hour.as("hour"),
                schedule.arrivalTime.as("arrivalTime"),
                schedule.scheduleStatus.as("scheduleStatus"),
                schedule.patientStatus.as("patientStatus"),
                schedule.dateStatus.as("dateStatus"),
                schedule.hourStatus.as("hourStatus"),
                scheduleNote.note.as("note"),
                scheduleCustom.value.as("statusCustom"),
                scheduleCustomPriority.value.as("priority"),
                company.id.as("companyId"),
                company.name.as("companyName"),
                company.alias.as("companyAlias"),
                patient.id.as("patientId"),
                personPatient.id.as("patientPersonId"),
                personPatient.name.as("patientPersonName"),
                personPatient.birthday.as("patientPersonBirthday"),
                personPatient.cpf.as("patientPersonCpf"),
                personPatient.cns.as("patientPersonCns"),
                contactListStandardPatient.phone.as("patientPersonPhoneStandart"),
                patient.enrollment.as("patientEnrollment"),
                patient.holder.as("patientHolder"),
                patient.medicalRecord.as("patientMedicalRecord"),
                patient.active.as("patientActive"),
                patient.dated.as("patientDated"),
                insurancePatient.id.as("patientInsuranceId"),
                insurancePatient.name.as("patientInsuranceName"),
                personSchedule.id.as("personId"),
                personSchedule.name.as("personName"),
                personSchedule.birthday.as("personBirthday"),
                personSchedule.cpf.as("personCpf"),
                personSchedule.cns.as("personCns"),
                contactListStandardPerson.phone.as("personPhoneStandart"),
                insurance.id.as("insuranceId"),
                insurance.name.as("insuranceName"),
                procedure.id.as("procedureId"),
                procedure.name.as("procedureName"),
                procedure.alias.as("procedureAlias"),
                horary.id.as("horaryId"),
                specialityHorary.name.as("horarySpecialityName"),
                performer.id.as("performerId"),
                professional.id.as("performerProfessionalId"),
                personPerformer.id.as("performerProfessionalPersonId"),
                schedule.session.id.as("sessionId"),
                personPerformer.name.as("performerName"),
                environment.name.as("environmentName"),
                environment.color.as("environmentColor"),
                new CaseBuilder()
                        .when(
                                JPAExpressions.selectOne()
                                        .from(scheduleParent)
                                        .where(scheduleParent.parent.id.eq(schedule.id))
                                        .exists())
                        .then(true)
                        .otherwise(false)
                        .as("whithChilden")))
                .from(schedule)
                .leftJoin(environment).on(environment.id.eq(schedule.environment.id))
                .innerJoin(company).on(company.id.eq(schedule.company.id))
                .leftJoin(procedure).on(procedure.id.eq(schedule.procedure.id))
                .leftJoin(horary).on(horary.id.eq(schedule.horary.id))
                .leftJoin(insurance).on(insurance.id.eq(schedule.insurance.id))
                .leftJoin(patient).on(patient.id.eq(schedule.patient.id))
                .leftJoin(personPatient).on(personPatient.id.eq(patient.person.id))
                .leftJoin(personSchedule).on(personSchedule.id.eq(schedule.person.id))
                .leftJoin(user).on(user.id.eq(schedule.user.id))
                .leftJoin(personUser).on(personUser.id.eq(user.person.id))
                .leftJoin(specialityHorary).on(specialityHorary.id.eq(schedule.horary.id))
                .leftJoin(insurancePatient).on(insurancePatient.id.eq(patient.insurance.id))
                .leftJoin(performer).on(performer.id.eq(schedule.performer.id))
                .leftJoin(professional).on(professional.id.eq(performer.professional.id))
                .leftJoin(personPerformer).on(personPerformer.id.eq(professional.person.id))
                .leftJoin(contactListPerson).on(contactListPerson.id.eq(personSchedule.contactList.id))
                .leftJoin(contactListStandardPerson)
                .on(contactListStandardPerson.parentId.eq(contactListPerson.id)
                        .and(contactListStandardPerson.type.eq("S")))
                .leftJoin(contactListPatient).on(contactListPatient.id.eq(personPatient.contactList.id))
                .leftJoin(contactListStandardPatient)
                .on(contactListStandardPatient.parentId.eq(contactListPatient.id)
                        .and(contactListStandardPatient.type.eq("S")))
                .leftJoin(scheduleNote).on(scheduleNote.schedule.id.eq(schedule.id))
                .leftJoin(scheduleCustom)
                .on(scheduleCustom.schedule.id.eq(schedule.id)
                        .and(scheduleCustom.key.eq(ScheduleCustomKeyEnum.CUSTOM.getValue())))
                .leftJoin(scheduleCustomPriority)
                .on(scheduleCustomPriority.schedule.id.eq(schedule.id)
                        .and(scheduleCustomPriority.key.eq(ScheduleCustomKeyEnum.PRIORITY.getValue())))
                .leftJoin(shiftMat).on(shiftMat.company.id.eq(company.id).and(shiftMat.type.eq("1")))
                .leftJoin(shiftVes).on(shiftVes.company.id.eq(company.id).and(shiftVes.type.eq("2")))
                .leftJoin(shiftNot).on(shiftNot.company.id.eq(company.id).and(shiftNot.type.eq("3")))
                .where(schedule.parent.isNull());

        BooleanBuilder whereClauseSchedule = new BooleanBuilder();

        if (Boolean.TRUE.equals(scheduleTO.getMorning()) && Boolean.TRUE.equals(scheduleTO.getEvening())
                && Boolean.TRUE.equals(scheduleTO.getNight())) {
            whereClauseSchedule.and(schedule.hour.between(shiftMat.started, shiftNot.ended));
        } else if (Boolean.TRUE.equals(scheduleTO.getMorning()) && Boolean.TRUE.equals(scheduleTO.getEvening())) {
            whereClauseSchedule.and(schedule.hour.between(shiftMat.started, shiftVes.ended));
        } else if (Boolean.TRUE.equals(scheduleTO.getMorning())) {
            whereClauseSchedule.and(schedule.hour.between(shiftMat.started, shiftMat.ended));
        } else if (Boolean.TRUE.equals(scheduleTO.getEvening())) {
            whereClauseSchedule.and(schedule.hour.between(shiftVes.started, shiftVes.ended));
        } else if (Boolean.TRUE.equals(scheduleTO.getNight())) {
            whereClauseSchedule.and(schedule.hour.between(shiftNot.started, shiftNot.ended));
        }

        whereClauseSchedule.and(performer.active.isTrue());

        if (scheduleTO.getCompanyId() != null) {
            whereClauseSchedule.and(company.id.eq(scheduleTO.getCompanyId()));
        }
        if (scheduleTO.getPerformerId() != null) {
            whereClauseSchedule.and(performer.id.eq(scheduleTO.getPerformerId()));
        }
        if (scheduleTO.getStarted() != null) {
            whereClauseSchedule.and(schedule.date.between(scheduleTO.getStarted(), scheduleTO.getEnded()));
        }
        if (scheduleTO.getPatientId() != null) {
            whereClauseSchedule.and(patient.id.eq(scheduleTO.getPatientId()));
        }

        if (scheduleTO.getStatus() != null) {
            if (scheduleTO.getStatus().equals("NL")) {
                whereClauseSchedule.and(schedule.scheduleStatus.in("L", "B", "A", "C", "X", "E"));
            } else {
                whereClauseSchedule.and(schedule.scheduleStatus.eq(scheduleTO.getStatus()));
            }
        }

        if (scheduleTO.getPatientStatus() != null) {
            whereClauseSchedule.and(schedule.patientStatus.eq(scheduleTO.getPatientStatus()));
        }
        querySchedule.where(whereClauseSchedule);
        List<ScheduleResponseTO> schedulePOList = querySchedule.fetch();

        if (scheduleTO.getPerformerId() != null && scheduleTO.getPatientId() == null) {
            Optional<PerformerPO> performerPO = performerRepository.findById(scheduleTO.getPerformerId());
            if (performerPO.isPresent()) {
                String parameterOrderOfArrival = parameterService.findValueByParameterOrParameterValue(
                        GlobalKeyParameterConstants.ORDER_OF_ARRIVAL,
                        performerPO.get().getProfessional().getId().toString());
                Optional<CompanyPerformerCustomPO> performerCustomPO = companyPerformerCustomService
                        .findByPerformer_idAndCompany_idAndKey(performerPO.get().getId(), scheduleTO.getCompanyId(),
                                CompanyPerformerCustomEnum.ARRIVAL_ORDER_BY_COMPANY.getValue());

                if ((performerCustomPO.isPresent() && "S".equals(performerCustomPO.get().getValue()))
                        || "1".equals(parameterOrderOfArrival)) {
                    Collections.sort(schedulePOList, new ScheduleTOComparator());
                }
            }
        }
        LocalDate startedDate = scheduleTO.getStarted();

        SimpleExpression<LocalDate> datedExpression = new CaseBuilder()
                .when(event.id.lt(0L)).then(startedDate)
                .otherwise(startedDate);

        queryEvent.select(Projections.bean(
                ScheduleResponseTO.class,
                ExpressionUtils.as(Expressions.constant(0L), "id"),
                ExpressionUtils.as(Expressions.constant(scheduleTO.getStarted()), "dated"),
                event.type.as("scheduleStatus"),
                event.type.as("patientStatus"),
                event.hour.as("hour"),
                event.name.as("message"),
                event.color.as("corMessage")))
                .from(event)
                .leftJoin(eventCompany).on(eventCompany.id.eq(event.company.id))
                .leftJoin(eventPerformer).on(eventPerformer.id.eq(event.performer.id))
                .leftJoin(eventProfessional).on(eventProfessional.id.eq(eventPerformer.professional.id))
                .leftJoin(eventPerson).on(eventPerson.id.eq(eventProfessional.person.id));

        BooleanBuilder whereClauseEvent = new BooleanBuilder();

        if (scheduleTO.getCompanyId() != null) {
            whereClauseEvent.and(eventCompany.id.eq(scheduleTO.getCompanyId()).or(eventCompany.isNull()));
        }
        if (scheduleTO.getPerformerId() != null) {
            whereClauseEvent.and(eventPerformer.id.eq(scheduleTO.getPerformerId()).or(eventPerformer.isNull()));
        }
        if (scheduleTO.getStarted() != null) {
            whereClauseEvent.and(event.dated.eq(scheduleTO.getStarted()).or(event.dated.isNull()));
        }
        queryEvent.where(whereClauseEvent);
        List<ScheduleResponseTO> eventPOList = queryEvent.fetch();
        /*
         * if (!eventPOList.isEmpty() && scheduleTO.getPatientId() == null){
         * schedulePOList.addAll(eventPOList);
         * }
         */
        Map<String, List<ScheduleResponseTO>> groupedByProfessional = schedulePOList.stream()
                .collect(Collectors.groupingBy(item -> item.getPerformerName()));

        return groupedByProfessional;
    }

    public List<ScheduleResponseTO> findByScheduleListPatient(ScheduleTO scheduleTO) {
        QSchedulePO schedule = QSchedulePO.schedulePO;
        QCompanyPO company = QCompanyPO.companyPO;
        QProcedurePO procedure = QProcedurePO.procedurePO;
        QHoraryPO horary = QHoraryPO.horaryPO;
        QInsurancePO insurance = QInsurancePO.insurancePO;
        QPatientPO patient = QPatientPO.patientPO;
        QUserPO user = QUserPO.userPO;
        QSpecialityPO specialityHorary = new QSpecialityPO("specialityHorary");
        QPersonPO personPatient = new QPersonPO("personPatient");
        QPersonPO personSchedule = new QPersonPO("personSchedule");
        QPersonPO personUser = new QPersonPO("personUser");
        QPersonPO personPerformer = new QPersonPO("personPerformer");
        QInsurancePO insurancePatient = new QInsurancePO("insurancePatient");
        QPerformerPO performer = QPerformerPO.performerPO;
        QProfessionalPO professional = QProfessionalPO.professionalPO;
        QEventPO event = QEventPO.eventPO;
        QContactListPO contactListPatient = new QContactListPO("contactListPatient");
        QContactListPO contactListPerson = new QContactListPO("contactListPerson");
        QScheduleCustomPO scheduleCustom = new QScheduleCustomPO("scheduleCustom");
        QScheduleCustomPO scheduleCustomPriority = new QScheduleCustomPO("scheduleCustomPriority");
        QScheduleNotePO scheduleNote = new QScheduleNotePO("note");

        SubQueryExpression<Long> maxIdSubQueryPatient = JPAExpressions
                .select(contactListPatient.id.max())
                .from(contactListPatient)
                .where(contactListPatient.type.eq("S")
                        .and(contactListPatient.parentId.eq(personPatient.contactList.id)));

        SubQueryExpression<Long> maxIdSubQueryPerson = JPAExpressions
                .select(contactListPerson.id.max())
                .from(contactListPerson)
                .where(contactListPerson.type.eq("S")
                        .and(contactListPerson.parentId.eq(personSchedule.contactList.id)));

        JPAQuery<ScheduleResponseTO> querySchedule = new JPAQuery<>(entityManager);

        querySchedule.select(Projections.bean(
                ScheduleResponseTO.class,
                schedule.id.as("id"),
                schedule.date.as("date"),
                schedule.hour.as("hour"),
                schedule.arrivalTime.as("arrivalTime"),
                schedule.scheduleStatus.as("scheduleStatus"),
                schedule.patientStatus.as("patientStatus"),
                schedule.dateStatus.as("dateStatus"),
                schedule.hourStatus.as("hourStatus"),
                scheduleNote.note.as("note"),
                scheduleCustom.value.as("statusCustom"),
                scheduleCustomPriority.value.as("priority"),
                company.id.as("companyId"),
                company.name.as("companyName"),
                company.alias.as("companyAlias"),
                patient.id.as("patientId"),
                personPatient.id.as("patientPersonId"),
                personPatient.name.as("patientPersonName"),
                personPatient.birthday.as("patientPersonBirthday"),
                personPatient.cpf.as("patientPersonCpf"),
                personPatient.cns.as("patientPersonCns"),
                contactListPatient.phone.as("patientPersonPhoneStandart"),
                patient.enrollment.as("patientEnrollment"),
                patient.holder.as("patientHolder"),
                patient.medicalRecord.as("patientMedicalRecord"),
                patient.active.as("patientActive"),
                patient.dated.as("patientDated"),
                insurancePatient.id.as("patientInsuranceId"),
                insurancePatient.name.as("patientInsuranceName"),
                personSchedule.id.as("personId"),
                personSchedule.name.as("personName"),
                personSchedule.birthday.as("personBirthday"),
                personSchedule.cpf.as("personCpf"),
                personSchedule.cns.as("personCns"),
                contactListPerson.phone.as("personPhoneStandart"),
                insurance.id.as("insuranceId"),
                insurance.name.as("insuranceName"),
                procedure.id.as("procedureId"),
                procedure.name.as("procedureName"),
                procedure.alias.as("procedureAlias"),
                horary.id.as("horaryId"),
                specialityHorary.name.as("horarySpecialityName"),
                performer.id.as("performerId"),
                professional.id.as("performerProfessionalId"),
                personPerformer.id.as("performerProfessionalPersonId"),
                personPerformer.name.as("performerName"),
                personUser.name.as("userName"),
                schedule.session.id.as("sessionId"),
                user.id.as("userId")))
                .from(schedule)
                .innerJoin(company).on(company.id.eq(schedule.company.id))
                .leftJoin(procedure).on(procedure.id.eq(schedule.procedure.id))
                .leftJoin(horary).on(horary.id.eq(schedule.horary.id))
                .leftJoin(insurance).on(insurance.id.eq(schedule.insurance.id))
                .leftJoin(patient).on(patient.id.eq(schedule.patient.id))
                .leftJoin(personPatient).on(personPatient.id.eq(patient.person.id))
                .leftJoin(personSchedule).on(personSchedule.id.eq(schedule.person.id))
                .leftJoin(user).on(user.id.eq(schedule.user.id))
                .leftJoin(personUser).on(personUser.id.eq(user.person.id))
                .leftJoin(specialityHorary).on(specialityHorary.id.eq(schedule.horary.id))
                .leftJoin(insurancePatient).on(insurancePatient.id.eq(patient.insurance.id))
                .leftJoin(performer).on(performer.id.eq(schedule.performer.id))
                .leftJoin(professional).on(professional.id.eq(performer.professional.id))
                .leftJoin(personPerformer).on(personPerformer.id.eq(professional.person.id))
                .leftJoin(contactListPatient).on(contactListPatient.id.eq(maxIdSubQueryPatient))
                .leftJoin(contactListPerson).on(contactListPerson.id.eq(maxIdSubQueryPerson))
                .leftJoin(scheduleNote).on(scheduleNote.schedule.id.eq(schedule.id))
                .leftJoin(scheduleCustom)
                .on(scheduleCustom.schedule.id.eq(schedule.id)
                        .and(scheduleCustom.key.eq(ScheduleCustomKeyEnum.CUSTOM.getValue())))
                .leftJoin(scheduleCustomPriority).on(scheduleCustomPriority.schedule.id.eq(schedule.id)
                        .and(scheduleCustomPriority.key.eq(ScheduleCustomKeyEnum.PRIORITY.getValue())));

        BooleanBuilder whereClauseSchedule = new BooleanBuilder();

        if (scheduleTO.getCompanyId() != null) {
            whereClauseSchedule.and(company.id.eq(scheduleTO.getCompanyId()));
        }
        if (scheduleTO.getPerformerId() != null) {
            whereClauseSchedule.and(performer.id.eq(scheduleTO.getPerformerId()));
        }
        if (scheduleTO.getStarted() != null && scheduleTO.getEnded() != null) {
            whereClauseSchedule.and(schedule.date.between(scheduleTO.getStarted(), scheduleTO.getEnded()));
        }
        if (scheduleTO.getStarted() != null && scheduleTO.getEnded() == null) {
            whereClauseSchedule.and(schedule.date.goe(scheduleTO.getStarted()));
        }
        if (scheduleTO.getPatientId() != null) {
            whereClauseSchedule.and(patient.id.eq(scheduleTO.getPatientId()));
        }

        if (scheduleTO.getPatientName() != null) {
            whereClauseSchedule.and(personPatient.name.startsWithIgnoreCase(scheduleTO.getPatientName())
                    .or(personSchedule.name.startsWithIgnoreCase(scheduleTO.getPatientName())));
        }

        if (scheduleTO.getStatus() != null) {
            if (scheduleTO.getStatus().equals("NL")) {
                whereClauseSchedule.and(schedule.scheduleStatus.in("L", "B", "A", "C", "X", "E"));
            } else {
                whereClauseSchedule.and(schedule.scheduleStatus.eq(scheduleTO.getStatus()));
            }
        }

        if (scheduleTO.getPatientStatus() != null) {
            whereClauseSchedule.and(schedule.patientStatus.eq(scheduleTO.getPatientStatus()));
        }
        querySchedule.where(whereClauseSchedule)
                .orderBy(schedule.date.asc());
        List<ScheduleResponseTO> schedulePOList = querySchedule.fetch();

        return schedulePOList;
    }

    public List<ScheduleResponseTO> findByScheduleListBySession(LocalDate dateSchedule, Long sessionId) {

        QSchedulePO schedule = QSchedulePO.schedulePO;
        QCompanyPO company = QCompanyPO.companyPO;
        QProcedurePO procedure = QProcedurePO.procedurePO;
        QHoraryPO horary = QHoraryPO.horaryPO;
        QInsurancePO insurance = QInsurancePO.insurancePO;
        QPatientPO patient = QPatientPO.patientPO;
        QUserPO user = QUserPO.userPO;
        QSpecialityPO specialityHorary = new QSpecialityPO("specialityHorary");
        QPersonPO personPatient = new QPersonPO("personPatient");
        QPersonPO personSchedule = new QPersonPO("personSchedule");
        QPersonPO personUser = new QPersonPO("personUser");
        QPersonPO personPerformer = new QPersonPO("personPerformer");
        QInsurancePO insurancePatient = new QInsurancePO("insurancePatient");
        QPerformerPO performer = QPerformerPO.performerPO;
        QProfessionalPO professional = QProfessionalPO.professionalPO;
        QEventPO event = QEventPO.eventPO;
        QContactListPO contactListPatient = new QContactListPO("contactListPatient");
        QContactListPO contactListPerson = new QContactListPO("contactListPerson");
        QScheduleCustomPO scheduleCustom = new QScheduleCustomPO("scheduleCustom");
        QScheduleCustomPO scheduleCustomPriority = new QScheduleCustomPO("scheduleCustomPriority");
        QScheduleNotePO scheduleNote = new QScheduleNotePO("note");

        SubQueryExpression<Long> maxIdSubQueryPatient = JPAExpressions
                .select(contactListPatient.id.max())
                .from(contactListPatient)
                .where(contactListPatient.type.eq("S")
                        .and(contactListPatient.parentId.eq(personPatient.contactList.id)));

        SubQueryExpression<Long> maxIdSubQueryPerson = JPAExpressions
                .select(contactListPerson.id.max())
                .from(contactListPerson)
                .where(contactListPerson.type.eq("S")
                        .and(contactListPerson.parentId.eq(personSchedule.contactList.id)));

        JPAQuery<ScheduleResponseTO> querySchedule = new JPAQuery<>(entityManager);

        querySchedule.select(Projections.bean(
                ScheduleResponseTO.class,
                schedule.id.as("id"),
                schedule.date.as("date"),
                schedule.hour.as("hour"),
                schedule.arrivalTime.as("arrivalTime"),
                schedule.scheduleStatus.as("scheduleStatus"),
                schedule.patientStatus.as("patientStatus"),
                schedule.dateStatus.as("dateStatus"),
                schedule.hourStatus.as("hourStatus"),
                scheduleNote.note.as("note"),
                scheduleCustom.value.as("statusCustom"),
                scheduleCustomPriority.value.as("priority"),
                company.id.as("companyId"),
                company.name.as("companyName"),
                company.alias.as("companyAlias"),
                patient.id.as("patientId"),
                personPatient.id.as("patientPersonId"),
                personPatient.name.as("patientPersonName"),
                personPatient.birthday.as("patientPersonBirthday"),
                personPatient.cpf.as("patientPersonCpf"),
                personPatient.cns.as("patientPersonCns"),
                contactListPatient.phone.as("patientPersonPhoneStandart"),
                patient.enrollment.as("patientEnrollment"),
                patient.holder.as("patientHolder"),
                patient.medicalRecord.as("patientMedicalRecord"),
                patient.active.as("patientActive"),
                patient.dated.as("patientDated"),
                insurancePatient.id.as("patientInsuranceId"),
                insurancePatient.name.as("patientInsuranceName"),
                personSchedule.id.as("personId"),
                personSchedule.name.as("personName"),
                personSchedule.birthday.as("personBirthday"),
                personSchedule.cpf.as("personCpf"),
                personSchedule.cns.as("personCns"),
                contactListPerson.phone.as("personPhoneStandart"),
                insurance.id.as("insuranceId"),
                insurance.name.as("insuranceName"),
                procedure.id.as("procedureId"),
                procedure.name.as("procedureName"),
                procedure.alias.as("procedureAlias"),
                horary.id.as("horaryId"),
                specialityHorary.name.as("horarySpecialityName"),
                performer.id.as("performerId"),
                professional.id.as("performerProfessionalId"),
                personPerformer.id.as("performerProfessionalPersonId"),
                personPerformer.name.as("performerName"),
                personUser.name.as("userName"),
                user.id.as("userId")))
                .from(schedule)
                .innerJoin(company).on(company.id.eq(schedule.company.id))
                .leftJoin(procedure).on(procedure.id.eq(schedule.procedure.id))
                .leftJoin(horary).on(horary.id.eq(schedule.horary.id))
                .leftJoin(insurance).on(insurance.id.eq(schedule.insurance.id))
                .leftJoin(patient).on(patient.id.eq(schedule.patient.id))
                .leftJoin(personPatient).on(personPatient.id.eq(patient.person.id))
                .leftJoin(personSchedule).on(personSchedule.id.eq(schedule.person.id))
                .leftJoin(user).on(user.id.eq(schedule.user.id))
                .leftJoin(personUser).on(personUser.id.eq(user.person.id))
                .leftJoin(specialityHorary).on(specialityHorary.id.eq(schedule.horary.id))
                .leftJoin(insurancePatient).on(insurancePatient.id.eq(patient.insurance.id))
                .leftJoin(performer).on(performer.id.eq(schedule.performer.id))
                .leftJoin(professional).on(professional.id.eq(performer.professional.id))
                .leftJoin(personPerformer).on(personPerformer.id.eq(professional.person.id))
                .leftJoin(contactListPatient).on(contactListPatient.id.eq(maxIdSubQueryPatient))
                .leftJoin(contactListPerson).on(contactListPerson.id.eq(maxIdSubQueryPerson))
                .leftJoin(scheduleNote).on(scheduleNote.schedule.id.eq(schedule.id))
                .leftJoin(scheduleCustom)
                .on(scheduleCustom.schedule.id.eq(schedule.id)
                        .and(scheduleCustom.key.eq(ScheduleCustomKeyEnum.CUSTOM.getValue())))
                .leftJoin(scheduleCustomPriority).on(scheduleCustomPriority.schedule.id.eq(schedule.id)
                        .and(scheduleCustomPriority.key.eq(ScheduleCustomKeyEnum.PRIORITY.getValue())));

        BooleanBuilder whereClauseSchedule = new BooleanBuilder();

        whereClauseSchedule.and(schedule.session.id.eq(sessionId));
        whereClauseSchedule.and(schedule.date.goe(dateSchedule));

        querySchedule.where(whereClauseSchedule)
                .orderBy(schedule.date.asc());
        List<ScheduleResponseTO> schedulePOList = querySchedule.fetch();

        return schedulePOList;
    }

    public ScheduleResponseTO findScheduleResponseById(Long scheduleId) {

        QSchedulePO schedule = QSchedulePO.schedulePO;
        QSchedulePO scheduleParent = new QSchedulePO("scheduleParent");
        QCompanyPO company = QCompanyPO.companyPO;
        QProcedurePO procedure = QProcedurePO.procedurePO;
        QHoraryPO horary = QHoraryPO.horaryPO;
        QGroupPO groupHorary = QGroupPO.groupPO;
        QGroupPO groupSchedule = new QGroupPO("groupSchedule");
        QInsurancePO insurance = QInsurancePO.insurancePO;
        QPatientPO patient = QPatientPO.patientPO;
        QUserPO user = QUserPO.userPO;
        QSpecialityPO specialityHorary = new QSpecialityPO("specialityHorary");
        QPersonPO personPatient = new QPersonPO("personPatient");
        QPersonPO personSchedule = new QPersonPO("personSchedule");
        QPersonPO personUser = new QPersonPO("personUser");
        QPersonPO personPerformer = new QPersonPO("personPerformer");
        QInsurancePO insurancePatient = new QInsurancePO("insurancePatient");
        QPerformerPO performer = QPerformerPO.performerPO;
        QProfessionalPO professional = QProfessionalPO.professionalPO;
        QEventPO event = QEventPO.eventPO;
        QContactListPO contactListPatient = new QContactListPO("contactListPatient");
        QContactListPO contactListPerson = new QContactListPO("contactListPerson");
        QScheduleCustomPO scheduleCustom = new QScheduleCustomPO("scheduleCustom");
        QScheduleCustomPO scheduleCustomPriority = new QScheduleCustomPO("scheduleCustomPriority");
        QScheduleNotePO scheduleNote = new QScheduleNotePO("note");
        QScheduleOrderPO scheduleOrder = QScheduleOrderPO.scheduleOrderPO;
        QOrderPO order = QOrderPO.orderPO;

        SubQueryExpression<Long> maxIdSubQueryPatient = JPAExpressions
                .select(contactListPatient.id.max())
                .from(contactListPatient)
                .where(contactListPatient.type.eq("S")
                        .and(contactListPatient.parentId.eq(personPatient.contactList.id)));

        SubQueryExpression<Long> maxIdSubQueryPerson = JPAExpressions
                .select(contactListPerson.id.max())
                .from(contactListPerson)
                .where(contactListPerson.type.eq("S")
                        .and(contactListPerson.parentId.eq(personSchedule.contactList.id)));

        JPAQuery<ScheduleResponseTO> querySchedule = new JPAQuery<>(entityManager);

        querySchedule.select(Projections.bean(
                ScheduleResponseTO.class,
                schedule.id.as("id"),
                schedule.date.as("date"),
                schedule.hour.as("hour"),
                schedule.arrivalTime.as("arrivalTime"),
                schedule.scheduleStatus.as("scheduleStatus"),
                schedule.patientStatus.as("patientStatus"),
                schedule.dateStatus.as("dateStatus"),
                schedule.hourStatus.as("hourStatus"),
                scheduleNote.note.as("note"),
                scheduleCustom.value.as("statusCustom"),
                scheduleCustomPriority.value.as("priority"),
                groupHorary.name.coalesce(groupSchedule.name).as("horaryGroupName"),
                company.id.as("companyId"),
                company.name.as("companyName"),
                company.alias.as("companyAlias"),
                patient.id.as("patientId"),
                personPatient.id.as("patientPersonId"),
                personPatient.name.as("patientPersonName"),
                personPatient.birthday.as("patientPersonBirthday"),
                personPatient.cpf.as("patientPersonCpf"),
                personPatient.cns.as("patientPersonCns"),
                contactListPatient.phone.as("patientPersonPhoneStandart"),
                patient.enrollment.as("patientEnrollment"),
                patient.holder.as("patientHolder"),
                patient.medicalRecord.as("patientMedicalRecord"),
                patient.active.as("patientActive"),
                patient.dated.as("patientDated"),
                insurancePatient.id.as("patientInsuranceId"),
                insurancePatient.name.as("patientInsuranceName"),
                personSchedule.id.as("personId"),
                personSchedule.name.as("personName"),
                personSchedule.birthday.as("personBirthday"),
                personSchedule.cpf.as("personCpf"),
                personSchedule.cns.as("personCns"),
                contactListPerson.phone.as("personPhoneStandart"),
                insurance.id.as("insuranceId"),
                insurance.name.as("insuranceName"),
                procedure.id.as("procedureId"),
                procedure.name.as("procedureName"),
                procedure.alias.as("procedureAlias"),
                horary.id.as("horaryId"),
                specialityHorary.name.as("horarySpecialityName"),
                performer.id.as("performerId"),
                professional.id.as("performerProfessionalId"),
                personPerformer.id.as("performerProfessionalPersonId"),
                personPerformer.name.as("performerName"),
                personUser.name.as("userName"),
                scheduleOrder.order.id.as("orderId"),
                order.session.id.as("orderSessionId"),
                user.id.as("userId"),
                new CaseBuilder()
                        .when(
                                JPAExpressions.selectOne()
                                        .from(scheduleParent)
                                        .where(scheduleParent.parent.id.eq(schedule.id))
                                        .exists())
                        .then(true)
                        .otherwise(false)
                        .as("whithChilden")))
                .from(schedule)
                .innerJoin(company).on(company.id.eq(schedule.company.id))
                .leftJoin(procedure).on(procedure.id.eq(schedule.procedure.id))
                .leftJoin(horary).on(horary.id.eq(schedule.horary.id))
                .leftJoin(groupSchedule).on(groupSchedule.id.eq(horary.group.id))
                .leftJoin(groupHorary).on(groupHorary.id.eq(schedule.group.id))
                .leftJoin(insurance).on(insurance.id.eq(schedule.insurance.id))
                .leftJoin(patient).on(patient.id.eq(schedule.patient.id))
                .leftJoin(personPatient).on(personPatient.id.eq(patient.person.id))
                .leftJoin(personSchedule).on(personSchedule.id.eq(schedule.person.id))
                .leftJoin(user).on(user.id.eq(schedule.user.id))
                .leftJoin(personUser).on(personUser.id.eq(user.person.id))
                .leftJoin(specialityHorary).on(specialityHorary.id.eq(schedule.horary.id))
                .leftJoin(insurancePatient).on(insurancePatient.id.eq(patient.insurance.id))
                .leftJoin(performer).on(performer.id.eq(schedule.performer.id))
                .leftJoin(professional).on(professional.id.eq(performer.professional.id))
                .leftJoin(personPerformer).on(personPerformer.id.eq(professional.person.id))
                .leftJoin(scheduleOrder).on(scheduleOrder.schedule.id.eq(scheduleId))
                .leftJoin(order).on(order.id.eq(scheduleOrder.order.id))
                .leftJoin(contactListPatient).on(contactListPatient.id.eq(maxIdSubQueryPatient))
                .leftJoin(contactListPerson).on(contactListPerson.id.eq(maxIdSubQueryPerson))
                .leftJoin(scheduleNote).on(scheduleNote.schedule.id.eq(schedule.id))
                .leftJoin(scheduleCustom)
                .on(scheduleCustom.schedule.id.eq(schedule.id)
                        .and(scheduleCustom.key.eq(ScheduleCustomKeyEnum.CUSTOM.getValue())))
                .leftJoin(scheduleCustomPriority)
                .on(scheduleCustomPriority.schedule.id.eq(schedule.id)
                        .and(scheduleCustomPriority.key.eq(ScheduleCustomKeyEnum.PRIORITY.getValue())))
                .where(schedule.parent.isNull())
                .where(schedule.id.eq(scheduleId));

        ScheduleResponseTO scheduleResponse = querySchedule.fetchOne();

        return scheduleResponse;
    }

    @Override
    public List<AvailabilitySchedulingTO> findAvailabilitySchedulingGroup(ScheduleTO scheduleTO) {

        QSchedulePO schedule = QSchedulePO.schedulePO;
        QCompanyPO company = QCompanyPO.companyPO;
        QShiftPO shift = QShiftPO.shiftPO;

        JPAQuery<AvailabilitySchedulingTO> query = new JPAQuery<>(entityManager);

        NumberTemplate<Integer> numberTemplate = Expressions.numberTemplate(Integer.class, "{0},{1}", 1, 2);

        query.select(
                Projections.bean(AvailabilitySchedulingTO.class,
                        schedule.date.as("date"),
                        Expressions.stringTemplate(
                                "CASE DAYOFWEEK({0}) " +
                                        "WHEN 1 THEN 'Domingo' " +
                                        "WHEN 2 THEN 'Segunda-feira' " +
                                        "WHEN 3 THEN 'Terça-feira' " +
                                        "WHEN 4 THEN 'Quarta-feira' " +
                                        "WHEN 5 THEN 'Quinta-feira' " +
                                        "WHEN 6 THEN 'Sexta-feira' " +
                                        "WHEN 7 THEN 'Sábado' " +
                                        "END",
                                schedule.date).as("day")))
                .from(schedule)
                .join(company).on(company.id.eq(schedule.company.id))
                .join(shift).on(shift.company.id.eq(company.id))
                .where(schedule.date.between(scheduleTO.getStarted(), scheduleTO.getEnded())
                        .and(schedule.scheduleStatus.eq("L")));

        if (scheduleTO.getCompanyId() != null) {
            query.where(company.id.eq(scheduleTO.getCompanyId()));
        }

        List<String> shifts = new ArrayList<>();

        if (scheduleTO.getMorning()) {
            shifts.add(ShiftTypeEnum.MORNING.getValue());
        }

        if (scheduleTO.getEvening()) {
            shifts.add(ShiftTypeEnum.EVENING.getValue());
        }

        if (scheduleTO.getNight()) {
            shifts.add(ShiftTypeEnum.NIGHT.getValue());
        }

        if (!shifts.isEmpty()) {
            query.where(shift.type.in(shifts));
            query.where(schedule.hour.between(shift.started, shift.ended));
        }

        if (!scheduleTO.getPerformerProcedureList().isEmpty()) {
            List<Long> performerIdList = scheduleTO.getPerformerProcedureList().stream()
                    .map(PerformerProcedureTO::getPerformerId).toList();
            query.where(schedule.performer.id.in(performerIdList));
        }
        query.groupBy(numberTemplate);

        var result = query.fetch();
        return result;
    }

    @Override
    public List<AvailabilitySchedulingTO> findAvailabilitySchedulingNoGroup(ScheduleTO scheduleTO) {

        QSchedulePO schedule = QSchedulePO.schedulePO;
        QCompanyPO company = QCompanyPO.companyPO;
        QShiftPO shift = QShiftPO.shiftPO;
        QGroupPO group = QGroupPO.groupPO;
        QPerformerPO performer = QPerformerPO.performerPO;
        QProfessionalPO professional = QProfessionalPO.professionalPO;
        QPersonPO person = QPersonPO.personPO;

        JPAQuery<AvailabilitySchedulingTO> query = new JPAQuery<>(entityManager);

        NumberTemplate<Integer> numberTemplate = Expressions.numberTemplate(Integer.class, "{0},{1},{2},{3},{4}", 1, 2,
                3, 4, 5);

        query.select(
                Projections.bean(AvailabilitySchedulingTO.class,
                        schedule.date.as("date"),
                        Expressions.stringTemplate(
                                "CASE DAYOFWEEK({0}) " +
                                        "WHEN 1 THEN 'Domingo' " +
                                        "WHEN 2 THEN 'Segunda-feira' " +
                                        "WHEN 3 THEN 'Terça-feira' " +
                                        "WHEN 4 THEN 'Quarta-feira' " +
                                        "WHEN 5 THEN 'Quinta-feira' " +
                                        "WHEN 6 THEN 'Sexta-feira' " +
                                        "WHEN 7 THEN 'Sábado' " +
                                        "END",
                                schedule.date).as("day"),
                        shift.type.as("shift"),
                        person.name.as("performer"),
                        group.name.as("group")))
                .from(schedule)
                .join(company).on(company.id.eq(schedule.company.id))
                .join(shift).on(shift.company.id.eq(company.id).and(schedule.hour.between(shift.started, shift.ended)))
                .leftJoin(group).on(group.id.eq(schedule.group.id))
                .join(performer).on(performer.id.eq(schedule.performer.id))
                .join(professional).on(professional.id.eq(performer.professional.id))
                .join(person).on(person.id.eq(professional.person.id))
                .where(schedule.date.between(scheduleTO.getStarted(), scheduleTO.getEnded())
                        .and(schedule.scheduleStatus.eq("L")));

        if (scheduleTO.getCompanyId() != null) {
            query.where(company.id.eq(scheduleTO.getCompanyId()));
        }

        List<String> shifts = new ArrayList<>();

        if (scheduleTO.getMorning()) {
            shifts.add(ShiftTypeEnum.MORNING.getValue());
        }

        if (scheduleTO.getEvening()) {
            shifts.add(ShiftTypeEnum.EVENING.getValue());
        }

        if (scheduleTO.getNight()) {
            shifts.add(ShiftTypeEnum.NIGHT.getValue());
        }

        if (!shifts.isEmpty()) {
            query.where(shift.type.in(shifts));
        }

        if (!scheduleTO.getPerformerProcedureList().isEmpty()) {
            List<Long> performerIdList = scheduleTO.getPerformerProcedureList().stream()
                    .map(PerformerProcedureTO::getPerformerId).toList();
            query.where(schedule.performer.id.in(performerIdList));
        }
        query.groupBy(numberTemplate);

        var result = query.fetch();
        return result;
    }

    @Override
    public List<Long> findByScheduleCancel(ScheduleTO scheduleTO) {
        JPAQuery<Long> querySchedule = new JPAQuery<>(entityManager);

        QSchedulePO schedule = QSchedulePO.schedulePO;
        querySchedule
                .from(schedule)
                .where(schedule.patient.isNull().and(schedule.person.isNull()));

        if (scheduleTO.getCompanyId() != null) {
            querySchedule.where(schedule.company.id.eq(scheduleTO.getCompanyId()));
        }

        if (scheduleTO.getPerformerId() != null) {
            querySchedule.where(schedule.performer.id.eq(scheduleTO.getPerformerId()));
        }

        if (scheduleTO.getStarted() != null) {
            querySchedule.where(schedule.date.between(scheduleTO.getStarted(), scheduleTO.getEnded()));
        }

        if (scheduleTO.getListWeekAvalited().isEmpty()) {
            throw new ApplicationException("É necessário escolher um dia da semana válido.");
        } else {
            if (scheduleTO.getListWeekAvalited().size() != 7) {
                querySchedule.where(Expressions.numberTemplate(Integer.class, "DAYOFWEEK({0})", schedule.date)
                        .in(scheduleTO.getListWeekAvalited()));
            }
        }

        if (scheduleTO.getStartedTime() != null) {
            querySchedule.where(schedule.hour.between(scheduleTO.getStartedTime(), scheduleTO.getEndedTime()));
        }

        // querySchedule.limit(scheduleTO.getLimit());
        // querySchedule.where(schedule.id.notIn(scheduleTO.getNotInSchedule()));

        var result = querySchedule.select(schedule.id).fetch();
        return result;
    }

    @Override
    public Page<ScheduleResponseTO> findScheduleListByPatientId(Long patientId, PageSearchTO pageSearchTO) {

        var pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage());
        var arguments = pageSearchTO.getArgument();

        QSchedulePO schedule = QSchedulePO.schedulePO;
        QCompanyPO company = QCompanyPO.companyPO;
        QProcedurePO procedure = QProcedurePO.procedurePO;
        QHoraryPO horary = QHoraryPO.horaryPO;
        QInsurancePO insurance = QInsurancePO.insurancePO;
        QPatientPO patient = QPatientPO.patientPO;
        QUserPO user = QUserPO.userPO;
        QSpecialityPO specialityHorary = new QSpecialityPO("specialityHorary");
        QPersonPO personPatient = new QPersonPO("personPatient");
        QPersonPO personSchedule = new QPersonPO("personSchedule");
        QPersonPO personUser = new QPersonPO("personUser");
        QPersonPO personPerformer = new QPersonPO("personPerformer");
        QInsurancePO insurancePatient = new QInsurancePO("insurancePatient");
        QPerformerPO performer = QPerformerPO.performerPO;
        QProfessionalPO professional = QProfessionalPO.professionalPO;
        QEventPO event = QEventPO.eventPO;
        QContactListPO contactListPatient = new QContactListPO("contactListPatient");
        QContactListPO contactListPerson = new QContactListPO("contactListPerson");
        QScheduleCustomPO scheduleCustom = new QScheduleCustomPO("scheduleCustom");
        QScheduleCustomPO scheduleCustomPriority = new QScheduleCustomPO("scheduleCustomPriority");
        QScheduleNotePO scheduleNote = new QScheduleNotePO("note");

        SubQueryExpression<Long> maxIdSubQueryPatient = JPAExpressions
                .select(contactListPatient.id.max())
                .from(contactListPatient)
                .where(contactListPatient.type.eq("S")
                        .and(contactListPatient.parentId.eq(personPatient.contactList.id)));

        SubQueryExpression<Long> maxIdSubQueryPerson = JPAExpressions
                .select(contactListPerson.id.max())
                .from(contactListPerson)
                .where(contactListPerson.type.eq("S")
                        .and(contactListPerson.parentId.eq(personSchedule.contactList.id)));

        JPAQuery<ScheduleResponseTO> querySchedule = new JPAQuery<>(entityManager);

        querySchedule.select(Projections.bean(
                ScheduleResponseTO.class,
                schedule.id.as("id"),
                schedule.date.as("date"),
                schedule.hour.as("hour"),
                schedule.arrivalTime.as("arrivalTime"),
                schedule.scheduleStatus.as("scheduleStatus"),
                schedule.patientStatus.as("patientStatus"),
                schedule.dateStatus.as("dateStatus"),
                schedule.hourStatus.as("hourStatus"),
                scheduleNote.note.as("note"),
                scheduleCustom.value.as("statusCustom"),
                scheduleCustomPriority.value.as("priority"),
                company.id.as("companyId"),
                company.name.as("companyName"),
                company.alias.as("companyAlias"),
                patient.id.as("patientId"),
                personPatient.id.as("patientPersonId"),
                personPatient.name.as("patientPersonName"),
                personPatient.birthday.as("patientPersonBirthday"),
                personPatient.cpf.as("patientPersonCpf"),
                personPatient.cns.as("patientPersonCns"),
                contactListPatient.phone.as("patientPersonPhoneStandart"),
                patient.enrollment.as("patientEnrollment"),
                patient.holder.as("patientHolder"),
                patient.medicalRecord.as("patientMedicalRecord"),
                patient.active.as("patientActive"),
                patient.dated.as("patientDated"),
                insurancePatient.id.as("patientInsuranceId"),
                insurancePatient.name.as("patientInsuranceName"),
                personSchedule.id.as("personId"),
                personSchedule.name.as("personName"),
                personSchedule.birthday.as("personBirthday"),
                personSchedule.cpf.as("personCpf"),
                personSchedule.cns.as("personCns"),
                contactListPerson.phone.as("personPhoneStandart"),
                insurance.id.as("insuranceId"),
                insurance.name.as("insuranceName"),
                procedure.id.as("procedureId"),
                procedure.name.as("procedureName"),
                procedure.alias.as("procedureAlias"),
                horary.id.as("horaryId"),
                specialityHorary.name.as("horarySpecialityName"),
                performer.id.as("performerId"),
                professional.id.as("performerProfessionalId"),
                personPerformer.id.as("performerProfessionalPersonId"),
                personPerformer.name.as("performerName"),
                personUser.name.as("userName"),
                user.id.as("userId")))
                .from(schedule)
                .innerJoin(company).on(company.id.eq(schedule.company.id))
                .leftJoin(procedure).on(procedure.id.eq(schedule.procedure.id))
                .leftJoin(horary).on(horary.id.eq(schedule.horary.id))
                .leftJoin(insurance).on(insurance.id.eq(schedule.insurance.id))
                .leftJoin(patient).on(patient.id.eq(schedule.patient.id))
                .leftJoin(personPatient).on(personPatient.id.eq(patient.person.id))
                .leftJoin(personSchedule).on(personSchedule.id.eq(schedule.person.id))
                .leftJoin(user).on(user.id.eq(schedule.user.id))
                .leftJoin(personUser).on(personUser.id.eq(user.person.id))
                .leftJoin(specialityHorary).on(specialityHorary.id.eq(schedule.horary.id))
                .leftJoin(insurancePatient).on(insurancePatient.id.eq(patient.insurance.id))
                .leftJoin(performer).on(performer.id.eq(schedule.performer.id))
                .leftJoin(professional).on(professional.id.eq(performer.professional.id))
                .leftJoin(personPerformer).on(personPerformer.id.eq(professional.person.id))
                .leftJoin(contactListPatient).on(contactListPatient.id.eq(maxIdSubQueryPatient))
                .leftJoin(contactListPerson).on(contactListPerson.id.eq(maxIdSubQueryPerson))
                .leftJoin(scheduleNote).on(scheduleNote.schedule.id.eq(schedule.id))
                .leftJoin(scheduleCustom)
                .on(scheduleCustom.schedule.id.eq(schedule.id)
                        .and(scheduleCustom.key.eq(ScheduleCustomKeyEnum.CUSTOM.getValue())))
                .leftJoin(scheduleCustomPriority).on(scheduleCustomPriority.schedule.id.eq(schedule.id)
                        .and(scheduleCustomPriority.key.eq(ScheduleCustomKeyEnum.PRIORITY.getValue())));

        BooleanBuilder whereClauseSchedule = new BooleanBuilder();

        whereClauseSchedule.and(schedule.patient.id.eq(patientId));

        querySchedule.where(whereClauseSchedule).orderBy(schedule.date.desc());
        querySchedule.limit(pageRequest.getPageSize()).offset(pageRequest.getOffset());
        List<ScheduleResponseTO> schedulePOList = querySchedule.fetch();
        long total = querySchedule.fetchCount();
        var page = new PageImpl<>(schedulePOList, pageRequest, total);
        return page;
    }

    @Override
    public List<ScheduleResponseTO> findScheduleListByPatientUnregistered(String cpf, LocalDate birthday) {
        QSchedulePO schedule = QSchedulePO.schedulePO;
        QCompanyPO company = QCompanyPO.companyPO;
        QProcedurePO procedure = QProcedurePO.procedurePO;
        QHoraryPO horary = QHoraryPO.horaryPO;
        QInsurancePO insurance = QInsurancePO.insurancePO;
        QPatientPO patient = QPatientPO.patientPO;
        QUserPO user = QUserPO.userPO;
        QSpecialityPO specialityHorary = new QSpecialityPO("specialityHorary");
        QPersonPO personPatient = new QPersonPO("personPatient");
        QPersonPO personSchedule = new QPersonPO("personSchedule");
        QPersonPO personUser = new QPersonPO("personUser");
        QPersonPO personPerformer = new QPersonPO("personPerformer");
        QInsurancePO insurancePatient = new QInsurancePO("insurancePatient");
        QPerformerPO performer = QPerformerPO.performerPO;
        QProfessionalPO professional = QProfessionalPO.professionalPO;
        QEventPO event = QEventPO.eventPO;
        QContactListPO contactListPatient = new QContactListPO("contactListPatient");
        QContactListPO contactListPerson = new QContactListPO("contactListPerson");
        QScheduleCustomPO scheduleCustom = new QScheduleCustomPO("scheduleCustom");
        QScheduleCustomPO scheduleCustomPriority = new QScheduleCustomPO("scheduleCustomPriority");
        QScheduleNotePO scheduleNote = new QScheduleNotePO("note");

        SubQueryExpression<Long> maxIdSubQueryPatient = JPAExpressions
                .select(contactListPatient.id.max())
                .from(contactListPatient)
                .where(contactListPatient.type.eq("S")
                        .and(contactListPatient.parentId.eq(personPatient.contactList.id)));

        SubQueryExpression<Long> maxIdSubQueryPerson = JPAExpressions
                .select(contactListPerson.id.max())
                .from(contactListPerson)
                .where(contactListPerson.type.eq("S")
                        .and(contactListPerson.parentId.eq(personSchedule.contactList.id)));

        JPAQuery<ScheduleResponseTO> querySchedule = new JPAQuery<>(entityManager);

        querySchedule.select(Projections.bean(
                ScheduleResponseTO.class,
                schedule.id.as("id"),
                schedule.date.as("date"),
                schedule.hour.as("hour"),
                schedule.arrivalTime.as("arrivalTime"),
                schedule.scheduleStatus.as("scheduleStatus"),
                schedule.patientStatus.as("patientStatus"),
                schedule.dateStatus.as("dateStatus"),
                schedule.hourStatus.as("hourStatus"),
                scheduleNote.note.as("note"),
                scheduleCustom.value.as("statusCustom"),
                scheduleCustomPriority.value.as("priority"),
                company.id.as("companyId"),
                company.name.as("companyName"),
                company.alias.as("companyAlias"),
                patient.id.as("patientId"),
                personPatient.id.as("patientPersonId"),
                personPatient.name.as("patientPersonName"),
                personPatient.birthday.as("patientPersonBirthday"),
                personPatient.cpf.as("patientPersonCpf"),
                personPatient.cns.as("patientPersonCns"),
                contactListPatient.phone.as("patientPersonPhoneStandart"),
                patient.enrollment.as("patientEnrollment"),
                patient.holder.as("patientHolder"),
                patient.medicalRecord.as("patientMedicalRecord"),
                patient.active.as("patientActive"),
                patient.dated.as("patientDated"),
                insurancePatient.id.as("patientInsuranceId"),
                insurancePatient.name.as("patientInsuranceName"),
                personSchedule.id.as("personId"),
                personSchedule.name.as("personName"),
                personSchedule.birthday.as("personBirthday"),
                personSchedule.cpf.as("personCpf"),
                personSchedule.cns.as("personCns"),
                contactListPerson.phone.as("personPhoneStandart"),
                insurance.id.as("insuranceId"),
                insurance.name.as("insuranceName"),
                procedure.id.as("procedureId"),
                procedure.name.as("procedureName"),
                procedure.alias.as("procedureAlias"),
                horary.id.as("horaryId"),
                specialityHorary.name.as("horarySpecialityName"),
                performer.id.as("performerId"),
                professional.id.as("performerProfessionalId"),
                personPerformer.id.as("performerProfessionalPersonId"),
                personPerformer.name.as("performerName"),
                personUser.name.as("userName"),
                user.id.as("userId")))
                .from(schedule)
                .innerJoin(company).on(company.id.eq(schedule.company.id))
                .leftJoin(procedure).on(procedure.id.eq(schedule.procedure.id))
                .leftJoin(horary).on(horary.id.eq(schedule.horary.id))
                .leftJoin(insurance).on(insurance.id.eq(schedule.insurance.id))
                .leftJoin(patient).on(patient.id.eq(schedule.patient.id))
                .leftJoin(personPatient).on(personPatient.id.eq(patient.person.id))
                .leftJoin(personSchedule).on(personSchedule.id.eq(schedule.person.id))
                .leftJoin(user).on(user.id.eq(schedule.user.id))
                .leftJoin(personUser).on(personUser.id.eq(user.person.id))
                .leftJoin(specialityHorary).on(specialityHorary.id.eq(schedule.horary.id))
                .leftJoin(insurancePatient).on(insurancePatient.id.eq(patient.insurance.id))
                .leftJoin(performer).on(performer.id.eq(schedule.performer.id))
                .leftJoin(professional).on(professional.id.eq(performer.professional.id))
                .leftJoin(personPerformer).on(personPerformer.id.eq(professional.person.id))
                .leftJoin(contactListPatient).on(contactListPatient.id.eq(maxIdSubQueryPatient))
                .leftJoin(contactListPerson).on(contactListPerson.id.eq(maxIdSubQueryPerson))
                .leftJoin(scheduleNote).on(scheduleNote.schedule.id.eq(schedule.id))
                .leftJoin(scheduleCustom)
                .on(scheduleCustom.schedule.id.eq(schedule.id)
                        .and(scheduleCustom.key.eq(ScheduleCustomKeyEnum.CUSTOM.getValue())))
                .leftJoin(scheduleCustomPriority).on(scheduleCustomPriority.schedule.id.eq(schedule.id)
                        .and(scheduleCustomPriority.key.eq(ScheduleCustomKeyEnum.PRIORITY.getValue())));

        BooleanBuilder whereClauseSchedule = new BooleanBuilder();

        whereClauseSchedule.and(schedule.patient.id.isNull());
        whereClauseSchedule.and(schedule.person.cpf.eq(cpf));
        whereClauseSchedule.and(schedule.person.birthday.eq(birthday));

        querySchedule.where(whereClauseSchedule)
                .orderBy(schedule.date.desc());
        List<ScheduleResponseTO> schedulePOList = querySchedule.fetch();

        return schedulePOList;
    }

    @Override
    public ScheduleQtdStatusTO findStatusQtdByPatientId(Long patientId) {
        QSchedulePO schedule = QSchedulePO.schedulePO;
        QParameterPO parameter = QParameterPO.parameterPO;

        JPAQuery<Long> query = new JPAQuery<>(entityManager);

        // Buscar o valor de 'QTD_SCHEDULE_APP' e converter para Integer
        Integer maxSchedules = query.select(parameter.value.castToNum(Integer.class))
                .from(parameter)
                .where(parameter.name.eq("QTD_SCHEDULE_APP"))
                .fetchOne();

        if (maxSchedules == null) {
            throw new IllegalStateException("Parâmetro 'QTD_SCHEDULE_APP' não encontrado.");
        }

        // Contar agendamentos para o paciente a partir de hoje
        Long scheduleCount = query.select(schedule.id.count())
                .from(schedule)
                .where(
                        schedule.patient.id.eq(patientId)
                                .and(schedule.date.goe(LocalDate.now())))
                .fetchOne();

        ScheduleQtdStatusTO scheduleQtdStatusTO = new ScheduleQtdStatusTO();
        scheduleQtdStatusTO.setStatus(scheduleCount < maxSchedules);
        scheduleQtdStatusTO.setLimit(maxSchedules);

        // Retornar status baseado na comparação
        return scheduleQtdStatusTO;
    }

    @Override
    public List<ScheduleDateAppTO> findScheduleDateListToApp(Long performerId, Long companyId) {
        QSchedulePO schedule = QSchedulePO.schedulePO;

        JPAQuery<ScheduleDateAppTO> querySchedule = new JPAQuery<>(entityManager);

        querySchedule.select(Projections.bean(
                ScheduleDateAppTO.class,
                schedule.date.as("date")))
                .from(schedule);

        BooleanBuilder whereClauseSchedule = new BooleanBuilder();

        whereClauseSchedule.and(schedule.performer.id.eq(performerId));
        whereClauseSchedule.and(schedule.company.id.eq(companyId));
        whereClauseSchedule.and(schedule.date.goe(LocalDate.now()));
        whereClauseSchedule.and(schedule.patient.isNull().and(schedule.person.isNull()));
        querySchedule.where(whereClauseSchedule)
                .orderBy(schedule.date.asc());
        querySchedule.groupBy(schedule.date);

        List<ScheduleDateAppTO> schedulePOList = querySchedule.fetch();

        return schedulePOList;
    }

    @Override
    public List<ScheduleDateAppTO> findScheduleHourListToApp(Long performerId, Long companyId, LocalDate date) {
        QSchedulePO schedule = QSchedulePO.schedulePO;

        JPAQuery<ScheduleDateAppTO> querySchedule = new JPAQuery<>(entityManager);

        querySchedule.select(Projections.bean(
                ScheduleDateAppTO.class,
                schedule.id.as("id"),
                schedule.hour.as("hour")))
                .from(schedule);

        BooleanBuilder whereClauseSchedule = new BooleanBuilder();

        whereClauseSchedule.and(schedule.performer.id.eq(performerId));
        whereClauseSchedule.and(schedule.company.id.eq(companyId));
        whereClauseSchedule.and(schedule.date.eq(date));
        whereClauseSchedule.and(schedule.patient.isNull().and(schedule.person.isNull()));
        querySchedule.where(whereClauseSchedule)
                .orderBy(schedule.date.asc());

        List<ScheduleDateAppTO> schedulePOList = querySchedule.fetch();

        return schedulePOList;
    }
}

class Schedule {
    String professiosssnal;
    String appointment;

    Schedule(String professional, String appointment) {

        this.professiosssnal = professional;
        this.appointment = appointment;
    }

    @Override
    public String toString() {
        return "{professional='" + professiosssnal + "', appointment='" + appointment + "'}";
    }
}
