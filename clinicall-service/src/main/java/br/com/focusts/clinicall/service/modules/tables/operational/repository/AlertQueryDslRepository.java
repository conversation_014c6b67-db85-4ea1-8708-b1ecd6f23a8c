package br.com.focusts.clinicall.service.modules.tables.operational.repository;

import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.operational.to.AlertListTO;
import br.com.focusts.clinicall.service.modules.tables.operational.to.AlertTO;
import org.springframework.data.domain.Page;


public interface AlertQueryDslRepository {

    public Page<AlertListTO> findByContext(String context, Long id, PageSearchTO pageSearchTO);
}
