
package br.com.focusts.clinicall.service.modules.tables.general.service;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.general.po.RegistryTypePO;

public interface RegistryTypeService extends CrudService<RegistryTypePO,java.lang.Long>{

    public Page<RegistryTypePO> findByNameContaining(PageSearchTO pageSearchTO);

}
