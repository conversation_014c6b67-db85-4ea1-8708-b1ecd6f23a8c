package br.com.focusts.clinicall.service.modules.operational.general.service;

import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.general.po.DocumentModelPO;
import br.com.focusts.clinicall.service.modules.operational.general.repository.DocumentModelRepository;
import br.com.focusts.clinicall.service.modules.system.constants.GlobalKeyParameterConstants;
import br.com.focusts.clinicall.service.modules.system.entity.BlobExportPO;
import br.com.focusts.clinicall.service.modules.system.repository.BlobExportRepository;
import br.com.focusts.clinicall.service.modules.system.service.ParameterService;
import br.com.focusts.clinicall.service.modules.system.service.StorageService;
import br.com.focusts.clinicall.util.TenantContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
@Slf4j
public class DefaultDocumentModelService extends AbstractCrudService<DocumentModelPO, Long> implements DocumentModelService {

	private static final String DOCUMENT_MODEL_TABLE = "document_model";
	private static final String DOCUMENT_FOLDER = "document";
	private static final String TYPE_FIELD = "type";

	private final DocumentModelRepository documentModelRepository;
	private final BlobExportRepository blobExportRepository;
	private StorageService storageService;
	private ParameterService parameterService;


	@Value("${storage.type}")
	private String storageType;

	@Value("${gcp.storage.clinicall-files.bucketName}")
	private String bucketNameClinicallFiles;

	@Autowired
	public DefaultDocumentModelService(
			DocumentModelRepository documentModelRepository,
			BlobExportRepository blobExportRepository,
			ParameterService parameterService) {
		this.documentModelRepository = documentModelRepository;
		this.blobExportRepository = blobExportRepository;
		this.parameterService = parameterService;
	}

	@Autowired
	public void setStorageService(ApplicationContext context) {
		this.storageService = (StorageService) context.getBean(storageType);
	}

	@Override
	public CrudRepository<DocumentModelPO, Long> getCrudRepository() {
		return documentModelRepository;
	}

	@Override
	public Page<DocumentModelPO> findByTitleContaining(PageSearchTO pageSearchTO) {
		PageRequest pageRequest = createPageRequest(pageSearchTO);

		if (TYPE_FIELD.equals(pageSearchTO.getFieldSort())) {
			return documentModelRepository.findByTypeContaining(pageRequest);
		}
		return documentModelRepository.findByTitleContaining(pageSearchTO.getArgument(), pageRequest);
	}

	@Override
	public List<DocumentModelPO> findByType(Integer type) {
		return documentModelRepository.findByType(type);
	}

	@Override
	public DocumentModelPO findById(Long id) {
		DocumentModelPO documentModel = super.findById(id);

		if (isDocumentModelSavedInTheStorage()) {
			setDocumentFileFromStorage(documentModel);
		}

		return documentModel;
	}


	@Override
	public Page<DocumentModelPO> findByTypeContaining(PageSearchTO pageSearchTO, Integer type) {
		PageRequest pageRequest = createPageRequest(pageSearchTO);
		return documentModelRepository.findByTypeInAndTitleStartingWithAndActiveTrue(
				List.of(type),
				pageSearchTO.getArgument(),
				pageRequest
		);
	}

	@Override
	public void saveDocumentFileStorage(DocumentModelPO documentModel, byte[] file) {
		if (documentModel.getId() != null) {
			String targetPath = getPathFileDocumentInStorage(documentModel.getId());
			storageService.uploadFile(bucketNameClinicallFiles, targetPath, file);
		}
	}

	public void deleteFileInStorage(Long documentModelId) {
		if (documentModelId != null) {
			try {
				String targetPath = getPathFileDocumentInStorage(documentModelId);
				storageService.deleteFile(bucketNameClinicallFiles, targetPath);
			} catch (Exception ex) {
				log.error("Error deleting file for document model id: {}", documentModelId, ex);
				throw new RuntimeException("Failed to delete document file", ex);
			}
		}
	}

	private void setDocumentFileFromStorage(DocumentModelPO documentModel) {
		if (documentModel != null && documentModel.getId() != null) {
			String pathFile = getPathFileDocumentInStorage(documentModel.getId());
			documentModel.setDocument(storageService.getFile(bucketNameClinicallFiles, pathFile));
		}
	}

	private String getFileNameDocumentInStorage(Long documentModelId) {
		return blobExportRepository
				.findByTableNameAndDocumentId(DOCUMENT_MODEL_TABLE, documentModelId)
				.map(BlobExportPO::getFileNameEncoded)
				.orElse(null);
	}

	private String getPathFileDocumentInStorage(Long documentModelId) {
		return String.format("%s/%s/%s/%s",
				TenantContext.getCurrentTenant(),
				DOCUMENT_MODEL_TABLE,
				DOCUMENT_FOLDER,
				getFileNameDocumentInStorage(documentModelId));
	}

	private PageRequest createPageRequest(PageSearchTO pageSearchTO) {
		return PageRequest.of(
				pageSearchTO.getPage(),
				pageSearchTO.getSizePage(),
				Sort.Direction.fromString(pageSearchTO.getSortDirection()),
				pageSearchTO.getFieldSort()
		);
	}

	private boolean isDocumentModelSavedInTheStorage() {
		var target = parameterService.findByName(GlobalKeyParameterConstants.DOCUMENT_MODEL_STORAGE_TARGET);
		return target != null && target.getValue().equals(GlobalKeyParameterConstants.TARGET_STORAGE);
	}
}