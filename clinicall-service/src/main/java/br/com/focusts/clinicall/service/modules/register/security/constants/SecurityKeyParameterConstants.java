package br.com.focusts.clinicall.service.modules.register.security.constants;

public class SecurityKeyParameterConstants {
	
	public static final String MAX_NUMBER_CONSECUTIE_UNSECESSFUL_ATTEMPTS_LOGIN = "MAX_NUMBER_CONSECUTIE_UNSECESSFUL_ATTEMPTS_LOGIN";
	public static final String PREVIOUS_PASSWORD_NUMBER_NOT_ALLOWED = "PREVIOUS_PASSWORD_NUMBER_NOT_ALLOWED";
	public static final String LIMIT_HOURS_REQUEST_RECOVERY_PASSWORD = "LIMIT_HOURS_REQUEST_RECOVERY_PASSWORD";
	public static final String PERIOD_EXPIRED_PASSWORD = "PERIOD_EXPIRED_PASSWORD";
	public static final String TENANT_ID = "TENANT_ID";
	public static final String IP_CLINICALL = "IP_CLINICALL";


	
	


}
