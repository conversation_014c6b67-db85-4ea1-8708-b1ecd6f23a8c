
package br.com.focusts.clinicall.service.modules.operational.billing.service;

import br.com.focusts.clinicall.service.modules.operational.billing.po.AppealItemGlossTypePO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.AppealPO;
import br.com.focusts.clinicall.service.modules.operational.billing.repository.AppealItemGlossTypeRepository;
import br.com.focusts.clinicall.service.modules.operational.billing.repository.AppealQueryDslRepository;
import br.com.focusts.clinicall.service.modules.operational.billing.to.AppealItemTO;
import br.com.focusts.clinicall.service.modules.operational.billing.to.ExtractedDataAppealTO;
import br.com.focusts.clinicall.service.modules.operational.billing.to.GlossAppealItem;
import br.com.focusts.clinicall.service.modules.tables.tiss.version.v40100.CtContaMedicaResumo;
import br.com.focusts.clinicall.service.modules.tables.tiss.version.v40100.CtDemonstrativoRetorno;
import br.com.focusts.clinicall.service.modules.tables.tiss.version.v40100.CtmDemonstrativoAnaliseConta;
import br.com.focusts.clinicall.service.modules.tables.tiss.version.v40100.MensagemTISS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.AppealItemPO;
import br.com.focusts.clinicall.service.modules.operational.billing.repository.AppealItemRepository;
import org.springframework.web.multipart.MultipartFile;

import javax.xml.bind.*;
import javax.xml.transform.stream.StreamSource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class DefaultAppealItemService extends AbstractCrudService<AppealItemPO,java.lang.Long> implements AppealItemService {

	@Autowired
	private AppealItemRepository appealItemRepository;

	@Autowired
	private AppealQueryDslRepository appealQueryDslRepository;

	@Autowired
	private AppealItemGlossTypeRepository appealItemGlossTypeRepository;

	@Override
	public CrudRepository<AppealItemPO,java.lang.Long> getCrudRepository() {
	    return appealItemRepository;
	}

	@Override
	public List<AppealItemTO> findByAppeal_id(Long appealId) {

		List<AppealItemTO> appealItemTOS = appealQueryDslRepository.findAppealItemByAppealId(appealId);

		return appealItemTOS;
	}

	@Override
	public List<AppealItemPO> saveGlossAppealItemAll(GlossAppealItem glossAppealItem) {

		List<AppealItemPO> appealItemList = appealItemRepository.findByAppeal_id(glossAppealItem.getAppeal().getId());

		for(var appealItem : appealItemList){
			var glossAppealItemData = glossAppealItem.getAppealItem();

			if (glossAppealItemData.getDate_appeal() != null) {
				appealItem.setDate_appeal(glossAppealItemData.getDate_appeal());
			}

			if (glossAppealItemData.getGuide_number() != null && glossAppealItemData.getGuide_number() != "") {
				appealItem.setGuide_number(glossAppealItemData.getGuide_number());
			}

			if (glossAppealItemData.getLot_number() != null && glossAppealItemData.getLot_number() != "") {
				appealItem.setLot_number(glossAppealItemData.getLot_number());
			}

			if (glossAppealItemData.getPassword() != null && glossAppealItemData.getPassword() != "") {
				appealItem.setPassword(glossAppealItemData.getPassword());
			}

			if (glossAppealItemData.getValue_appeal() != null) {
				appealItem.setValue_appeal(glossAppealItemData.getValue_appeal());
			}

			if (glossAppealItemData.getJustify() != null && glossAppealItemData.getJustify() != "") {
				List<AppealItemGlossTypePO> appealItemGlossTypeList = appealItemGlossTypeRepository.findByAppeal_id(glossAppealItem.getAppeal().getId());
				List<AppealItemGlossTypePO> appealItemGlossTypeListPersist = new ArrayList<AppealItemGlossTypePO>();

				for(var appealItemGlossType : appealItemGlossTypeList){
					appealItemGlossType.setJustify(glossAppealItemData.getJustify());
				}
				appealItemGlossTypeRepository.saveAll(appealItemGlossTypeListPersist);
			}

			if (glossAppealItemData.getProtocol_number() != null && glossAppealItemData.getProtocol_number() != "") {
				appealItem.setProtocol_number(glossAppealItemData.getProtocol_number());
			}

			if (glossAppealItemData.getLot_number() != null && glossAppealItemData.getLot_number() != "") {
				appealItem.setLot_number(glossAppealItemData.getLot_number());
			}

			if (glossAppealItemData.getPassword() != null && glossAppealItemData.getPassword() != "") {
				appealItem.setPassword(glossAppealItemData.getPassword());
			}
		}

		appealItemRepository.saveAll(appealItemList);

		return appealItemList;
	}

	@Override
	public AppealItemPO save(AppealItemPO appealItem) {

		if (appealItem.getJustify() != null && !appealItem.getJustify().isEmpty()) {
			List<AppealItemGlossTypePO> appealItemGlossTypeList = appealItemGlossTypeRepository.findByAppealItem_id(appealItem.getId());

			if(!appealItemGlossTypeList.isEmpty()){
				for(var appealItemGlossType : appealItemGlossTypeList){
					appealItemGlossType.setJustify(appealItem.getJustify());
					appealItemGlossTypeRepository.save(appealItemGlossType);
				}
			}
		}

		return super.save(appealItem);
	}

	@Override
	public void deleteAppealItem(long id) {

		List<AppealItemGlossTypePO> appealItemGlossTypeList = appealItemGlossTypeRepository.findByAppealItem_id(id);

		if (!appealItemGlossTypeList.isEmpty()){
			appealItemGlossTypeRepository.deleteAll(appealItemGlossTypeList);
		}

		super.delete(id);
	}

	@Override
	public List<ExtractedDataAppealTO> processarXmlTiss(MultipartFile file) throws IOException {
		if (file == null || file.isEmpty()) {
			throw new IllegalArgumentException("Arquivo não pode ser vazio");
		}

		try (InputStream xmlStream = file.getInputStream()) {
			MensagemTISS mensagemTISS = parseTissXml(xmlStream);
			List<ExtractedDataAppealTO> extractedDataAppealTO = extrairDados(mensagemTISS);
			return extractedDataAppealTO;
		} catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

	private MensagemTISS parseTissXml(InputStream xmlStream) throws Exception {
		try {

			JAXBContext context = JAXBContext.newInstance(MensagemTISS.class);
			Unmarshaller unmarshaller = context.createUnmarshaller();
			unmarshaller.setEventHandler(new ValidationEventHandler() {
				@Override
				public boolean handleEvent(ValidationEvent event) {
					System.out.println("Evento de validação: " + event.getMessage());
					return true;
				}
			});

			JAXBElement<MensagemTISS> jaxbElement =
					unmarshaller.unmarshal(new StreamSource(xmlStream), MensagemTISS.class);

			return jaxbElement.getValue();
		} finally {
			if (xmlStream != null) {
				xmlStream.close();
			}
		}
	}

	public List<ExtractedDataAppealTO> extrairDados(MensagemTISS mensagem) {
		List<ExtractedDataAppealTO> resultados = new ArrayList<>();

		if (mensagem.getOperadoraParaPrestador() == null) return resultados;

		CtDemonstrativoRetorno retorno = mensagem.getOperadoraParaPrestador().getDemonstrativosRetorno();
		if (retorno == null || retorno.getDemonstrativoAnaliseConta() == null) return resultados;

		Map<String, ExtractedDataAppealTO> guiaMap = new HashMap<>();

		for (CtmDemonstrativoAnaliseConta conta : retorno.getDemonstrativoAnaliseConta()) {

			String registroANS = conta.getCabecalhoDemonstrativo().getRegistroANS();
			String nomeOperadora = conta.getCabecalhoDemonstrativo().getNomeOperadora();
			String codigoPrestadorNaOperadora = conta.getDadosPrestador().getDadosContratado().getCodigoPrestadorNaOperadora();
			String cnes = conta.getDadosPrestador().getCNES();
			// String numeroLotePrestador = conta.getDadosConta().getNumeroLote(); // opcional

			for (CtmDemonstrativoAnaliseConta.DadosConta.DadosProtocolo protocolo : conta.getDadosConta().getDadosProtocolo()) {
				String numeroProtocolo = protocolo.getNumeroProtocolo();

				for (CtContaMedicaResumo.RelacaoGuias dadosGuia : protocolo.getRelacaoGuias()) {

					String numeroGuiaPrestador = dadosGuia.getNumeroGuiaPrestador();
					String numeroGuiaOperadora = dadosGuia.getNumeroGuiaOperadora();
					String senha = dadosGuia.getSenha();
					String numeroCarteira = dadosGuia.getNumeroCarteira();

					String key = numeroGuiaPrestador;

					ExtractedDataAppealTO dto = guiaMap.getOrDefault(key, new ExtractedDataAppealTO());

					dto.setRegistroANS(registroANS);
					dto.setNomeOperadora(nomeOperadora);
					dto.setCodigoPrestadorNaOperadora(codigoPrestadorNaOperadora);
					dto.setCnes(cnes);
					dto.setNumeroProtocolo(numeroProtocolo);
					dto.setNumeroGuiaPrestador(numeroGuiaPrestador);
					dto.setNumeroGuiaOperadora(numeroGuiaOperadora);
					dto.setSenha(senha);
					dto.setNumeroCarteira(numeroCarteira);

					BigDecimal valorInformadoTotal = dto.getValorInformado() != null ? dto.getValorInformado() : BigDecimal.ZERO;
					BigDecimal valorProcessadoTotal = dto.getValorProcessado() != null ? dto.getValorProcessado() : BigDecimal.ZERO;
					BigDecimal valorLiberadoTotal = dto.getValorLiberado() != null ? dto.getValorLiberado() : BigDecimal.ZERO;
					BigDecimal valorGlosaTotal = dto.getValorGlosa() != null ? dto.getValorGlosa() : BigDecimal.ZERO;
					StringBuilder tipoGlosaConcat = new StringBuilder(dto.getTipoGlosa() != null ? dto.getTipoGlosa() : "");

					if (dadosGuia.getDetalhesGuia() != null && !dadosGuia.getDetalhesGuia().isEmpty()) {
						for (CtContaMedicaResumo.RelacaoGuias.DetalhesGuia detalhe : dadosGuia.getDetalhesGuia()) {

							if (detalhe.getProcedimento() != null) {
								// Se quiser concatenar códigos ou descrições de procedimentos, pode adaptar aqui.
								dto.setCodigoProcedimento(detalhe.getProcedimento().getCodigoProcedimento());
								dto.setDescricaoProcedimento(detalhe.getProcedimento().getDescricaoProcedimento());
							}

							if (detalhe.getValorInformado() != null) {
								valorInformadoTotal = valorInformadoTotal.add(detalhe.getValorInformado());
							}
							if (detalhe.getValorProcessado() != null) {
								valorProcessadoTotal = valorProcessadoTotal.add(detalhe.getValorProcessado());
							}
							if (detalhe.getValorLiberado() != null) {
								valorLiberadoTotal = valorLiberadoTotal.add(detalhe.getValorLiberado());
							}

							for (CtContaMedicaResumo.RelacaoGuias.DetalhesGuia.RelacaoGlosa glosa : detalhe.getRelacaoGlosa()) {
								if (glosa.getValorGlosa() != null) {
									valorGlosaTotal = valorGlosaTotal.add(glosa.getValorGlosa());
								}
								if (glosa.getTipoGlosa() != null) {
									tipoGlosaConcat.append(glosa.getTipoGlosa()).append("; ");
								}
							}
						}
					}

					dto.setValorInformado(valorInformadoTotal);
					dto.setValorProcessado(valorProcessadoTotal);
					dto.setValorLiberado(valorLiberadoTotal);
					dto.setValorGlosa(valorGlosaTotal);
					dto.setTipoGlosa(tipoGlosaConcat.toString().trim());

					guiaMap.put(key, dto);
				}
			}
		}

		resultados.addAll(guiaMap.values());
		return resultados;
	}



}
