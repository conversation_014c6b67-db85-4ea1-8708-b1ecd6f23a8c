
package br.com.focusts.clinicall.service.modules.financial.bankmoviment.repository;

import br.com.focusts.clinicall.service.modules.register.organizational.po.PerformerPO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.financial.bankmoviment.po.BankMovimentPO;

@Repository
public interface BankMovimentRepository extends CrudRepository<BankMovimentPO,java.lang.Long>{

    public Page<BankMovimentPO> findByHistoricContaining(java.lang.String historic, Pageable pageable);

    @Query(nativeQuery = true, value = "SELECT * FROM bank_moviment WHERE bank_moviment.parent_id =:id")
    public BankMovimentPO findParentById(Long id);

    @Query(nativeQuery = true, value = "SELECT * FROM bank_moviment WHERE parent_id = :parentId")
    BankMovimentPO findChildByParentId(Long parentId);


}



