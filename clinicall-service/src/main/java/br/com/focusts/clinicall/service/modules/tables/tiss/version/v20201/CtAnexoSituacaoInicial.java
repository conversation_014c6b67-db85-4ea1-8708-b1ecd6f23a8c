//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2023.03.30 at 09:43:38 PM BRT 
//


package br.com.focusts.clinicall.service.modules.tables.tiss.version.v20201;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ct_anexoSituacaoInicial complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_anexoSituacaoInicial">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="cabecalhoAnexoGuia" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_cabecalhoAnexoOdonto"/>
 *         &lt;element name="dadosBeneficiario">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_beneficiario">
 *                 &lt;sequence>
 *                   &lt;element name="nomeEmpresa" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_descricao40" minOccurs="0"/>
 *                   &lt;element name="numeroTelefone" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_numeroTel" minOccurs="0"/>
 *                   &lt;element name="nomeTitular" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_nome" minOccurs="0"/>
 *                 &lt;/sequence>
 *               &lt;/extension>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="dadosContratadoExecutante">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="dadosContratado">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_contratado">
 *                           &lt;sequence>
 *                             &lt;element name="conselhoProfissional" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_conselhoProfissional"/>
 *                           &lt;/sequence>
 *                         &lt;/extension>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="dadosExecutante" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_profissionalExecutante" minOccurs="0"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="situacaoInicial" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_situacaoInicial"/>
 *         &lt;element name="observacao" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_observacao" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_anexoSituacaoInicial", propOrder = {
    "cabecalhoAnexoGuia",
    "dadosBeneficiario",
    "dadosContratadoExecutante",
    "situacaoInicial",
    "observacao"
})
public class CtAnexoSituacaoInicial {

    @XmlElement(required = true)
    protected CtCabecalhoAnexoOdonto cabecalhoAnexoGuia;
    @XmlElement(required = true)
    protected CtAnexoSituacaoInicial.DadosBeneficiario dadosBeneficiario;
    @XmlElement(required = true)
    protected CtAnexoSituacaoInicial.DadosContratadoExecutante dadosContratadoExecutante;
    @XmlElement(required = true)
    protected CtSituacaoInicial situacaoInicial;
    protected String observacao;

    /**
     * Gets the value of the cabecalhoAnexoGuia property.
     * 
     * @return
     *     possible object is
     *     {@link CtCabecalhoAnexoOdonto }
     *     
     */
    public CtCabecalhoAnexoOdonto getCabecalhoAnexoGuia() {
        return cabecalhoAnexoGuia;
    }

    /**
     * Sets the value of the cabecalhoAnexoGuia property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtCabecalhoAnexoOdonto }
     *     
     */
    public void setCabecalhoAnexoGuia(CtCabecalhoAnexoOdonto value) {
        this.cabecalhoAnexoGuia = value;
    }

    /**
     * Gets the value of the dadosBeneficiario property.
     * 
     * @return
     *     possible object is
     *     {@link CtAnexoSituacaoInicial.DadosBeneficiario }
     *     
     */
    public CtAnexoSituacaoInicial.DadosBeneficiario getDadosBeneficiario() {
        return dadosBeneficiario;
    }

    /**
     * Sets the value of the dadosBeneficiario property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtAnexoSituacaoInicial.DadosBeneficiario }
     *     
     */
    public void setDadosBeneficiario(CtAnexoSituacaoInicial.DadosBeneficiario value) {
        this.dadosBeneficiario = value;
    }

    /**
     * Gets the value of the dadosContratadoExecutante property.
     * 
     * @return
     *     possible object is
     *     {@link CtAnexoSituacaoInicial.DadosContratadoExecutante }
     *     
     */
    public CtAnexoSituacaoInicial.DadosContratadoExecutante getDadosContratadoExecutante() {
        return dadosContratadoExecutante;
    }

    /**
     * Sets the value of the dadosContratadoExecutante property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtAnexoSituacaoInicial.DadosContratadoExecutante }
     *     
     */
    public void setDadosContratadoExecutante(CtAnexoSituacaoInicial.DadosContratadoExecutante value) {
        this.dadosContratadoExecutante = value;
    }

    /**
     * Gets the value of the situacaoInicial property.
     * 
     * @return
     *     possible object is
     *     {@link CtSituacaoInicial }
     *     
     */
    public CtSituacaoInicial getSituacaoInicial() {
        return situacaoInicial;
    }

    /**
     * Sets the value of the situacaoInicial property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtSituacaoInicial }
     *     
     */
    public void setSituacaoInicial(CtSituacaoInicial value) {
        this.situacaoInicial = value;
    }

    /**
     * Gets the value of the observacao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getObservacao() {
        return observacao;
    }

    /**
     * Sets the value of the observacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setObservacao(String value) {
        this.observacao = value;
    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_beneficiario">
     *       &lt;sequence>
     *         &lt;element name="nomeEmpresa" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_descricao40" minOccurs="0"/>
     *         &lt;element name="numeroTelefone" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_numeroTel" minOccurs="0"/>
     *         &lt;element name="nomeTitular" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_nome" minOccurs="0"/>
     *       &lt;/sequence>
     *     &lt;/extension>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "nomeEmpresa",
        "numeroTelefone",
        "nomeTitular"
    })
    public static class DadosBeneficiario
        extends CtBeneficiario
    {

        protected String nomeEmpresa;
        protected String numeroTelefone;
        protected String nomeTitular;

        /**
         * Gets the value of the nomeEmpresa property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getNomeEmpresa() {
            return nomeEmpresa;
        }

        /**
         * Sets the value of the nomeEmpresa property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setNomeEmpresa(String value) {
            this.nomeEmpresa = value;
        }

        /**
         * Gets the value of the numeroTelefone property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getNumeroTelefone() {
            return numeroTelefone;
        }

        /**
         * Sets the value of the numeroTelefone property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setNumeroTelefone(String value) {
            this.numeroTelefone = value;
        }

        /**
         * Gets the value of the nomeTitular property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getNomeTitular() {
            return nomeTitular;
        }

        /**
         * Sets the value of the nomeTitular property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setNomeTitular(String value) {
            this.nomeTitular = value;
        }

    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="dadosContratado">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_contratado">
     *                 &lt;sequence>
     *                   &lt;element name="conselhoProfissional" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_conselhoProfissional"/>
     *                 &lt;/sequence>
     *               &lt;/extension>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="dadosExecutante" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_profissionalExecutante" minOccurs="0"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "dadosContratado",
        "dadosExecutante"
    })
    public static class DadosContratadoExecutante {

        @XmlElement(required = true)
        protected CtAnexoSituacaoInicial.DadosContratadoExecutante.DadosContratado dadosContratado;
        protected CtProfissionalExecutante dadosExecutante;

        /**
         * Gets the value of the dadosContratado property.
         * 
         * @return
         *     possible object is
         *     {@link CtAnexoSituacaoInicial.DadosContratadoExecutante.DadosContratado }
         *     
         */
        public CtAnexoSituacaoInicial.DadosContratadoExecutante.DadosContratado getDadosContratado() {
            return dadosContratado;
        }

        /**
         * Sets the value of the dadosContratado property.
         * 
         * @param value
         *     allowed object is
         *     {@link CtAnexoSituacaoInicial.DadosContratadoExecutante.DadosContratado }
         *     
         */
        public void setDadosContratado(CtAnexoSituacaoInicial.DadosContratadoExecutante.DadosContratado value) {
            this.dadosContratado = value;
        }

        /**
         * Gets the value of the dadosExecutante property.
         * 
         * @return
         *     possible object is
         *     {@link CtProfissionalExecutante }
         *     
         */
        public CtProfissionalExecutante getDadosExecutante() {
            return dadosExecutante;
        }

        /**
         * Sets the value of the dadosExecutante property.
         * 
         * @param value
         *     allowed object is
         *     {@link CtProfissionalExecutante }
         *     
         */
        public void setDadosExecutante(CtProfissionalExecutante value) {
            this.dadosExecutante = value;
        }


        /**
         * <p>Java class for anonymous complex type.
         * 
         * <p>The following schema fragment specifies the expected content contained within this class.
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_contratado">
         *       &lt;sequence>
         *         &lt;element name="conselhoProfissional" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_conselhoProfissional"/>
         *       &lt;/sequence>
         *     &lt;/extension>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "conselhoProfissional"
        })
        public static class DadosContratado
            extends CtContratado
        {

            @XmlElement(required = true)
            protected CtConselhoProfissional conselhoProfissional;

            /**
             * Gets the value of the conselhoProfissional property.
             * 
             * @return
             *     possible object is
             *     {@link CtConselhoProfissional }
             *     
             */
            public CtConselhoProfissional getConselhoProfissional() {
                return conselhoProfissional;
            }

            /**
             * Sets the value of the conselhoProfissional property.
             * 
             * @param value
             *     allowed object is
             *     {@link CtConselhoProfissional }
             *     
             */
            public void setConselhoProfissional(CtConselhoProfissional value) {
                this.conselhoProfissional = value;
            }

        }

    }

}
