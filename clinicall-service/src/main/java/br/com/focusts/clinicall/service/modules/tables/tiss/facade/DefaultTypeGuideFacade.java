
package br.com.focusts.clinicall.service.modules.tables.tiss.facade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.TypeGuidePO;
import br.com.focusts.clinicall.service.modules.tables.tiss.service.TypeGuideService;

@Component
@Transactional
public class DefaultTypeGuideFacade extends AbstractCrudFacade <TypeGuidePO, java.lang.Long> implements TypeGuideFacade  {

	@Autowired
	private TypeGuideService typeGuideService;

	@Override
	public CrudService<TypeGuidePO,java.lang.Long> getCrudService() {
            return typeGuideService;
	}

	@Override
	public Page<TypeGuidePO> findByCodeContainingOrNameContaining(PageSearchTO pageSearchTO) {
            return typeGuideService.findByCodeContainingOrNameContaining(pageSearchTO);
	}        
      
}
