package br.com.focusts.clinicall.service.modules.operational.reception.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.AuxiliaryPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.CurrencyPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.PerformerPO;

@Entity
@Table(name = "order_item_auxiliary")
public class OrderItemAuxiliaryPO extends AbstractPO<Long> {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "order_item_auxiliary_id")
	private Long id;
	
	@JoinColumn(name = "order_item_id", referencedColumnName = "order_item_id")
	@NotNull
	@ManyToOne
	private OrderItemPO orderItem;
	
	@JoinColumn(name = "auxiliary_id", referencedColumnName = "auxiliary_id")
	@ManyToOne
	@NotNull
	private AuxiliaryPO auxiliary;
	
	@JoinColumn(name = "performer_id", referencedColumnName = "performer_id")
	@ManyToOne
	@NotNull
	private PerformerPO performer;

	@JoinColumn(name = "performer_id_accredited", referencedColumnName = "performer_id")
	@ManyToOne
	@NotNull
	private PerformerPO performerAccredited;
	
	@JoinColumn(name = "currency_id", referencedColumnName = "currency_id")
	@ManyToOne
	@NotNull
	private CurrencyPO currency;

	@Size(max = 25)
	private String number;

	@NotNull
	private Double value;

	private Double amount;
	@Size(max = 1)
	@Column(name = "participation_type")
	private String participationType;

	@Column(name = "participation_value")
	private Double participationValue;

	@Size(max = 1)
	@Column(name = "tax_type")
	private String taxType;

	@Column(name = "tax_value")
	private Double taxValue;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public OrderItemPO getOrderItem() {
		return orderItem;
	}

	public void setOrderItem(OrderItemPO orderItem) {
		this.orderItem = orderItem;
	}


	public AuxiliaryPO getAuxiliary() {
		return auxiliary;
	}

	public void setAuxiliary(AuxiliaryPO auxiliary) {
		this.auxiliary = auxiliary;
	}

	public PerformerPO getPerformer() {
		return performer;
	}

	public void setPerformer(PerformerPO performer) {
		this.performer = performer;
	}

	public CurrencyPO getCurrency() {
		return currency;
	}

	public void setCurrency(CurrencyPO currency) {
		this.currency = currency;
	}

	public Double getValue() {
		return value;
	}

	public void setValue(Double value) {
		this.value = value;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public String getParticipationType() {
		return participationType;
	}

	public void setParticipationType(String participationType) {
		this.participationType = participationType;
	}

	public Double getParticipationValue() {
		return participationValue;
	}

	public void setParticipationValue(Double participationValue) {
		this.participationValue = participationValue;
	}

	public @Size(max = 25) String getNumber() {
		return number;
	}

	public void setNumber(@Size(max = 25) String number) {
		this.number = number;
	}

	public @Size(max = 1) String getTaxType() {
		return taxType;
	}

	public void setTaxType(@Size(max = 1) String taxType) {
		this.taxType = taxType;
	}

	public Double getTaxValue() {
		return taxValue;
	}

	public void setTaxValue(Double taxValue) {
		this.taxValue = taxValue;
	}

	public PerformerPO getPerformerAccredited() {
		return performerAccredited;
	}

	public void setPerformerAccredited(PerformerPO performerAccredited) {
		this.performerAccredited = performerAccredited;
	}
}
