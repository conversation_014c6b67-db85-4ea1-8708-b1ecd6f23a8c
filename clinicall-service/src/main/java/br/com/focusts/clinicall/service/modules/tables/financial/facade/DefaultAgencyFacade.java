
package br.com.focusts.clinicall.service.modules.tables.financial.facade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.financial.po.AgencyPO;
import br.com.focusts.clinicall.service.modules.tables.financial.service.AgencyService;

@Component
@Transactional
public class DefaultAgencyFacade extends AbstractCrudFacade <AgencyPO, java.lang.Long> implements AgencyFacade  {

	@Autowired
	private AgencyService agencyService;

	@Override
	public CrudService<AgencyPO,java.lang.Long> getCrudService() {
            return agencyService;
	}

	@Override
	public Page<AgencyPO> findByCodeContainingOrNameContainingOrBank_nameContaining(PageSearchTO pageSearchTO) {
            return agencyService.findByCodeContainingOrNameContainingOrBank_nameContaining(pageSearchTO);
	}        
      
}
