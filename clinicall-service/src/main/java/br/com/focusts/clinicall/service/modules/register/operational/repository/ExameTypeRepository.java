
package br.com.focusts.clinicall.service.modules.register.operational.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.register.operational.po.ExameTypePO;

import java.util.Optional;

@Repository
public interface ExameTypeRepository extends CrudRepository<ExameTypePO,java.lang.Long>{


     Optional<ExameTypePO> findByCode(String code);

     Page<ExameTypePO> findByCodeStartingWithOrNameStartingWith(String code, String name, Pageable pageable);
 
}



