package br.com.focusts.clinicall.service.modules.register.security.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.security.po.ModulePO;
import br.com.focusts.clinicall.service.modules.register.security.repository.ModuleRepository;

@Service
public class DefaultModuleService extends AbstractCrudService<ModulePO, Long> implements ModuleService {

	@Autowired
	private ModuleRepository moduleRepository;

	@Override
	public CrudRepository<ModulePO, Long> getCrudRepository() {
		return moduleRepository;
	}

	@Override
	public Page<ModulePO> findByNameContaining(PageSearchTO pageSearchTO) {

		PageRequest pageRequest =  PageRequest.of(pageSearchTO.getPage(),pageSearchTO.getSizePage(), Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());

		return moduleRepository.findByNameContaining(pageSearchTO.getArgument(), pageRequest);
	}

	@Override
	public List<ModulePO> findSubModulesByModuleId(Long moduleId) {
		return moduleRepository.findByParent_idOrderByName(moduleId);
	}


	@Override
	public List<ModulePO> findModules() {
		return moduleRepository.findByParentIsNull();
	}

	@Override
	public List<ModulePO> findAll() {
		return findModules();
	}
	
}
