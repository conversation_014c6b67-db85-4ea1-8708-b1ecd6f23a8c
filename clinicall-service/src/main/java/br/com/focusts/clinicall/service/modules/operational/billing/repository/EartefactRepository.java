package br.com.focusts.clinicall.service.modules.operational.billing.repository;

import java.util.List;

import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPayerPO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.operational.billing.po.EartefactPO;

@Repository 
public interface EartefactRepository extends CrudRepository<EartefactPO,Long>{

    public List<EartefactPO> findByTypeContaining(String type);
    
}
