package br.com.focusts.clinicall.service.modules.partners.to.nuclearisTO;

import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateDeserializer;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateSerializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
public class PatherCampaignWhatsAppResponseTO {

    private Long CDPACIENTE;
    private Long cdgrupo;
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate dtatendimento;
    private String dsgrupo;
    private String NMPACIENTE;
    private String nomeSocial;
    private String NUCELULAR;
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate dtnascimento;
    private Long cdsexo;
    private Long cdestadocivil;
    private Long cdestado;
    private String nmcidade;
    private Long cdservico;
    private String nmservico;
    private Long cdmedico;
    private String nmmedico;
    private String nmconvenio;
    private String nmespecialidade;
    private String DSAVISO;


}
