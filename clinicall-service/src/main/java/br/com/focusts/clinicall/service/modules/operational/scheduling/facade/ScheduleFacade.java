package br.com.focusts.clinicall.service.modules.operational.scheduling.facade;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.reception.to.OrderTO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.po.SchedulePO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.to.*;
import br.com.focusts.clinicall.service.modules.register.organizational.to.PerformerTO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

public interface ScheduleFacade extends CrudFacade<SchedulePO, Long> {

	public List<SchedulePO> findByDate(LocalDate date);

	public List<SchedulePO> findByPerformerAndDate(Long performerId, LocalDate startDate);

	public List<PerformerScheduleDateTO> findPerformerSchedulesDateList(ScheduleTO scheduleTO);
	
	public PerformerScheduleDateTO findPerformerSchedulesDate(ScheduleTO ScheduleTO);

	public List<SchedulePO> findSchedule(ScheduleTO ScheduleTO);

	public List<ScheduleResponseTO> findScheduleModule(ScheduleTO ScheduleTO);

	public List<ScheduleResponseTO> findScheduleModulePatient(ScheduleTO ScheduleTO);

	List<ScheduleResponseCalendarTO> findScheduleModuleCalendar(ScheduleTO scheduleTO);

	public Map<String, List<ScheduleResponseTO>> findScheduleModuleHorary(ScheduleTO ScheduleTO);

	public ScheduleResponseTO findScheduleResponseById(Long scheduleId);

	public List<SchedulePO> findBySchedulePatientOrPerson(ScheduleTO scheduleTO);

	public List<PerformerScheduleTO> findPerformerSchedules(ScheduleTO scheduleTO);

	public void registerScheduleUsage(Long scheduleId);

	public void updateRegisterScheduleUsage(Long scheduleId);

	public void unregisterScheduleUsage(Long scheduleId);
	
	public void unregisterScheduleUsagebyId(Long userId);
	
	public List<Long> unregisterScheduleExpired();

	public List<SchedulePO> futureSchedule(Long patientId);

	public SchedulePO copySchedule(Long scheduleIdSouce, Long scheduleIdTarget);

	public SchedulePO markdownSchedule(Long scheduleIdSouce, Long scheduleIdTarget);

	public Long copyScheduleOrMarkdown(Long scheduleIdSouce, ScheduleResponseCalendarTO scheduleInfo, String type );

	public List<AvailabilitySchedulingTO> findAvailabilitySchedulingNoGroup(ScheduleTO ScheduleTO);

	public List<AvailabilitySchedulingTO> findAvailabilitySchedulingGroup(ScheduleTO ScheduleTO);

	public List<AvailabilitySchedulingTO> findAvailabilityScheduling(ScheduleTO scheduleTO);

	public void unregisterAllScheduleUsage();

	public SchedulePO updateStatus(Long scheduleId, String type, String status);

	public SchedulePO schedule(SchedulePO schedule);
	
	public ScheduleTransferResponseTO transferSchedule(ScheduleTransferRequestTO request);

    public AvailabilityScheduleSpecialityTO availabilitySchedulerBySpeciality(ScheduleTO scheduleTO);

	public Long checkScheduleOrder(Long scheduleId);

	public List<SchedulePO> findScheduleToWhatsApp(ScheduleTO scheduleTO);

	public void prepareTalk(ScheduleFilterTO scheduleFilterTO);

	List<PerformerTO> findPerformerBySchedule(ScheduleTO scheduleTO);

	List<ScheduleResponseTO> findByScheduleListBySession(Long scheduleId, Long sessionId);

    String findByParameterProfessional(Long performerId);

    void saveScheduleMultiples(List<AvailabilitySchedulingTO> availabilitySchedulingList);

    void associanteSchedulePatient(Long personId, Long patientId);

	List<String> findProceduresBySchedule(Long scheduleId);

	List<SchedulePO> findByParentId(Long scheduleId);
	Page<ScheduleResponseTO> findScheduleListByPatientId(Long patientId, PageSearchTO pageSearchTO);
	ScheduleQtdStatusTO findStatusQtdByPatientId(Long patientId);
	List<ScheduleResponseTO> findScheduleListByPatientUnregistered(String cpf, LocalDate birthday);
	List<ScheduleDateAppTO> findScheduleDateListToApp(Long performerId, Long companyId);
	List<ScheduleDateAppTO> findScheduleHourListToApp(Long performerId, Long companyId, LocalDate date);
	SchedulePO insertScheduleApp(ScheduleAppTO schedule);

    public Page<OrderTO> findOrderByPatientId(Long patientId, PageSearchTO pageSearchTO);
}
