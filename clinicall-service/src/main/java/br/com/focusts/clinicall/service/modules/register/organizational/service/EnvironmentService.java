
package br.com.focusts.clinicall.service.modules.register.organizational.service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.EnvironmentPO;

public interface EnvironmentService extends CrudService<EnvironmentPO,java.lang.Long>{

    public Page<EnvironmentPO> findByNameContainingOrDescriptionContaining(PageSearchTO pageSearchTO);
    public Page<EnvironmentPO> findByNameContainingOrDescriptionContainingAndCompanyCostCenter_Company_idOrAndCompanyCostCenterIsNull(Long companyId,PageSearchTO pageSearchTO);
	public List<EnvironmentPO> findByParentIsNull();

    List<EnvironmentPO> findEnvironmentFree(Long companyId, LocalDate scheduleDate, LocalTime hour);
}
