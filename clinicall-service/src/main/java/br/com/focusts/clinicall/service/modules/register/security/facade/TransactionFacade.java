package br.com.focusts.clinicall.service.modules.register.security.facade;

import java.util.List;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.security.po.TransactionPO;
import br.com.focusts.clinicall.service.modules.system.po.TransactionFilterPO;

public interface TransactionFacade extends CrudFacade<TransactionPO, Long>{

    public Page<TransactionPO> findByNameContainingOrCodeContainingOrViewNameContaining(PageSearchTO pageSearchTO);

    public List<TransactionFilterPO> findByTransaction_id(Long id);

}
