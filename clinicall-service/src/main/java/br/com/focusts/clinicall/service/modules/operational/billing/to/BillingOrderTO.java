package br.com.focusts.clinicall.service.modules.operational.billing.to;

import br.com.focusts.clinicall.service.modules.operational.billing.po.BillingPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPO;
import br.com.focusts.clinicall.service.modules.operational.reception.to.OrderBillingTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BillingOrderTO {

    private Long id;
    private BillingPO billing;
    private OrderPO order;
    private Boolean release;
    private String advice;

}
