
package br.com.focusts.clinicall.service.modules.register.organizational.service;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.ParticipationElementPO;

import java.util.List;

public interface ParticipationElementService extends CrudService<ParticipationElementPO,java.lang.Long>{

    public Page<ParticipationElementPO> findByParticipation_id(Long participationId, PageSearchTO pageSearchTO);
    public List<ParticipationElementPO> findByParticipation_id(Long participationId);
}
