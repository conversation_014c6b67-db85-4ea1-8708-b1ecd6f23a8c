
package br.com.focusts.clinicall.service.modules.operational.reception.service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import br.com.focusts.clinicall.service.modules.register.accreditation.enums.InsurancePaymentModelTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import br.com.focusts.clinicall.fw.exception.ApplicationException;
import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.service.modules.operational.reception.enums.OrderStateEnum;
import br.com.focusts.clinicall.service.modules.operational.reception.enums.OrderStatusEnum;
import br.com.focusts.clinicall.service.modules.operational.reception.po.CashierOsPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderItemPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPayPO;
import br.com.focusts.clinicall.service.modules.operational.reception.repository.OrderItemRepository;
import br.com.focusts.clinicall.service.modules.operational.reception.repository.OrderPayRepository;
import br.com.focusts.clinicall.service.modules.operational.reception.to.ApplyDiscountTO;
import br.com.focusts.clinicall.service.modules.operational.reception.to.OrderPayTO;
import br.com.focusts.clinicall.service.modules.operational.util.CalcOperational;
import br.com.focusts.clinicall.service.modules.tables.financial.po.PayTypePO;
import br.com.focusts.clinicall.service.modules.tables.financial.service.PayTypeService;

@Service
public class DefaultOrderPayService extends AbstractCrudService<OrderPayPO, java.lang.Long> implements OrderPayService {

	@Autowired
	private OrderPayRepository orderPayRepository;

	@Autowired
	private PayTypeService payTypeService;

	@Autowired
	@Lazy
	private OrderService orderService;

	@Autowired
	private OrderItemRepository orderItemRepository;

	@Autowired
	private CashierOsService cashierOsService;

	@Override
	public CrudRepository<OrderPayPO, java.lang.Long> getCrudRepository() {
		return orderPayRepository;
	}

	@Override
	public List<OrderPayPO> findByOrderId(Long orderId) {
		return orderPayRepository.findByOrder_id(orderId);
	}

	@Override
	public void generateOrderPay(OrderPayTO orderPayTO) {

		PayTypePO payTypePO = payTypeService.findById(orderPayTO.getPayTypeId());

		Optional<OrderPO> orderPOOptional = Optional.ofNullable(orderService.findById(orderPayTO.getOrderId()));
		if (orderPOOptional.isEmpty()) {
			throw new ApplicationException("Order não encontrada");
		}

		OrderPO orderPO = orderPOOptional.get();
		CashierOsPO cashierOsPO = new CashierOsPO();

		if (orderPayTO.getCashierID() != null) {
			cashierOsPO = cashierOsService.findById(orderPayTO.getCashierID());
		}

		if (orderPayTO.getValue() > orderPO.getValue()) {
			throw new ApplicationException("O valor do pagamento utrapassa o valor do Ordem"); // TODO colocar no
			// properties
		}

		// valor parcela
		Double valueParcel = CalcOperational.calcParcel(orderPayTO.getValue(), orderPayTO.getAmountParcel());
		Double valueDif = CalcOperational.calcLastParcel(valueParcel, orderPayTO.getValue(),
				orderPayTO.getAmountParcel());
		LocalDate dateParcel = orderPayTO.getDate();

		List<OrderPayPO> orderPayList = new ArrayList<>();
		if (orderPayTO.getAmountParcel() > payTypePO.getSplitMax()) {
			throw new ApplicationException("Número de parcelas ultrapassa a quantidade permitida");
		}

		if (!payTypePO.getSplit() && orderPayTO.getAmountParcel() > 0) {
			throw new ApplicationException("Forma de pagameno não aceita parcelamento");
		}

		if (payTypePO.getSplit() && orderPayTO.getAmountParcel() > payTypePO.getSplitMax()) {
			throw new ApplicationException("Forma de pagameno ultrapassa a quantidade de parcelas permitidas");
		}

		var intFor = orderPayTO.getAmountParcel() == 0 ? 1 : orderPayTO.getAmountParcel();

		for (int parcel = 1; parcel <= intFor; parcel++) {

			OrderPayPO orderPayPO = new OrderPayPO();
			orderPayPO.setAccount(payTypePO.getAccount());
			orderPayPO.setPayType(payTypePO);
			if (parcel == 1 && payTypePO.getApproximationRule().equals("P")) {
				orderPayPO.setValue(valueDif);
			} else if (parcel == orderPayTO.getAmountParcel() && payTypePO.getApproximationRule().equals("U")) {
				orderPayPO.setValue(valueDif);
			} else {
				orderPayPO.setValue(valueParcel);
			}
			orderPayPO.setOrder(orderPO);
			if (orderPayTO.getCashierID() != null) {
				orderPayPO.setCashier(cashierOsPO);
			}
			orderPayPO.setBrand(payTypePO.getBrand());
			orderPayPO.setParcel(parcel);
			orderPayPO.setDate(orderPayTO.getDate());
			orderPayPO.setReceptDate(dateParcel);

			if (payTypePO.getFee() != null) {
				orderPayPO.setFee(payTypePO.getFee());
			} else {
				orderPayPO.setFee(0.0);
			}

			if (payTypePO.getTax() != null) {
				orderPayPO.setTax(payTypePO.getTax());
			} else {
				orderPayPO.setTax(0.0);
			}

			// payTypePO.get
			// Pega o interval de date de pay_Type
			dateParcel = dateParcel.plusDays(payTypePO.getInterval());

			while (dateParcel.getDayOfWeek().equals(DayOfWeek.SATURDAY)
					|| dateParcel.getDayOfWeek().equals(DayOfWeek.SUNDAY)) {

				if (payTypePO.getWeekendRule().equals("A")) {
					dateParcel = dateParcel.minusDays(1);
				} else {
					dateParcel = dateParcel.plusDays(1);
				}
			}

			orderPayList.add(orderPayPO);
		}

		if (!orderPayList.isEmpty()) {
			super.save(orderPayList);
		}

		var saldo = getMissingValueOrderPayInOrder(orderPO.getId());
		if (saldo == 0 && orderPO.getStatus().equals(OrderStatusEnum.ABERTO.getValue())
				&& orderPO.getState().equals(OrderStateEnum.NORMAL.getValue())) {
			orderService.closeOrderById(orderPO.getId());
		}
	}

	@Override
	public void applyDiscount(ApplyDiscountTO applyDiscount) {

		var order = orderService.findByOrderId(applyDiscount.getOrderId());

		if (applyDiscount.getForceDiscount() == null) {
			applyDiscount.setForceDiscount(false);
		}

		if ((!order.getInsurance().getPaymentModel().toString()
				.equals(InsurancePaymentModelTypeEnum.PARTICULAR.getValue())
				&& !order.getInsurance().getPaymentModel().toString()
						.equals(InsurancePaymentModelTypeEnum.COPARTICIPATION.getValue()))
				&& (applyDiscount.getForceDiscount() == false)) {
			throw new ApplicationException("Desconto só é permitido para particular");
		}
		List<OrderItemPO> orderItemList = new ArrayList<>();
		var orderPayList = orderPayRepository.findByOrder_id(applyDiscount.getOrderId());
		if (!orderPayList.isEmpty()) {
			orderPayRepository.deleteAll(orderPayList);
		}
		var total = 0.0;

		if (applyDiscount.getOrderItemId() == null) {
			orderItemList = orderItemRepository.findByOrder_id(applyDiscount.getOrderId());
		} else {
			var orderItem = orderItemRepository.findById(applyDiscount.getOrderItemId());
			if (orderItem.isPresent()) {
				orderItemList.add(orderItem.get());
			}
		}
		total = orderItemList
				.stream()
				.mapToDouble(orderItem -> orderItem.getTotal_origin())
				.sum();

		if (orderItemList.isEmpty()) {
			throw new ApplicationException("OS sem itens");
		}

		Double discount = 0.0;

		if (applyDiscount.getValueDiscount() > total) {
			throw new ApplicationException("Valor do desconto maior que o valor cobrado!");
		}

		if (applyDiscount.getValueDiscount() > 0) {
			if (applyDiscount.getType().equals("1")) {
				discount = applyDiscount.getValueDiscount();
			} else {
				discount = CalcOperational.calcValueDiscount(applyDiscount.getValueDiscount(), total);
			}
		}

		if (discount > 0) {
			double valorTotalPedido = order.getValue();
			double percentValuePay = CalcOperational.calcPercentDefinitvParcicpation(valorTotalPedido, discount);

			double somaDescontos = 0.0;
			List<Double> descontosAplicados = new ArrayList<>();

			// 1. Calcular descontos proporcionais e arredondar
			for (var orderItem : orderItemList) {
				double desconto = CalcOperational.calcValueDefinitvParcicpation(percentValuePay,
						orderItem.getTotal_origin());
				desconto = CalcOperational.roudingDouble(desconto); // Arredondar para 2 casas
				descontosAplicados.add(desconto);
				somaDescontos += desconto;
			}

			// 2. Corrigir diferença no item com maior valor, para evitar valores negativos
			double diferenca = CalcOperational.roudingDouble(discount - somaDescontos);

			if (Math.abs(diferenca) >= 0.01) {
				// Achar índice do item com maior total_origin
				int indexMaiorItem = 0;
				double maiorValor = 0.0;
				for (int i = 0; i < orderItemList.size(); i++) {
					if (orderItemList.get(i).getTotal_origin() > maiorValor) {
						maiorValor = orderItemList.get(i).getTotal_origin();
						indexMaiorItem = i;
					}
				}

				double novoDesconto = descontosAplicados.get(indexMaiorItem) + diferenca;

				// Evita desconto negativo
				novoDesconto = Math.max(0.0, CalcOperational.roudingDouble(novoDesconto));
				descontosAplicados.set(indexMaiorItem, novoDesconto);
			}

			// 3. Aplicar descontos nos itens
			for (int i = 0; i < orderItemList.size(); i++) {
				double desconto = descontosAplicados.get(i);
				double totalComDesconto = CalcOperational
						.roudingDouble(orderItemList.get(i).getTotal_origin() - desconto);

				orderItemList.get(i).setDiscount(desconto);
				orderItemList.get(i).setTotal(totalComDesconto);
			}
		} else {
			for (var orderItem : orderItemList) {
				orderItem.setDiscount(0.0);
				orderItem.setTotal(orderItem.getTotal_origin());
				orderItemRepository.save(orderItem);
			}
		}
		orderItemRepository.saveAll(orderItemList);
		orderService.updateOrderValue(applyDiscount.getOrderId());
	}

	@Override
	public OrderPayPO save(OrderPayPO orderPayPO) {
		Optional<OrderPO> orderPOOptional = Optional.ofNullable(orderService.findById(orderPayPO.getOrder().getId()));
		if (orderPOOptional.isEmpty()) {
			throw new ApplicationException("Order não encontrada");
		}

		OrderPO orderPO = orderPOOptional.get();
		Double totalOrderPay = getTotalOrderPay(orderPO.getOrderPayList());

		if ((totalOrderPay + orderPayPO.getValue() > orderPO.getValue())) {
			throw new ApplicationException("O valor do pagamento utrapassa o valor do Ordem"); // TODO colocar no
			// properties
		}
		PayTypePO payTypePO = payTypeService.findById(orderPayPO.getPayType().getId());
		orderPayPO.setAccount(payTypePO.getAccount());

		return super.save(orderPayPO);
	}

	@Override
	public OrderPayPO update(OrderPayPO orderPayUpdate) {
		Optional<OrderPO> orderPOOptional = Optional
				.ofNullable(orderService.findById(orderPayUpdate.getOrder().getId()));
		if (orderPOOptional.isEmpty()) {
			throw new ApplicationException("Order não encontrada");
		}

		OrderPO orderPO = orderPOOptional.get();

		Double totalOrderPay = getTotalOrderPayWithoutConsideringSpecificPayOrder(orderPO.getOrderPayList(),
				orderPayUpdate);

		if ((totalOrderPay + orderPayUpdate.getValue() > orderPO.getValue())) {
			throw new ApplicationException("O valor do pagamento utrapassa o valor do Ordem"); // TODO colocar no
			// properties
		}
		PayTypePO payTypePO = payTypeService.findById(orderPayUpdate.getPayType().getId());
		orderPayUpdate.setAccount(payTypePO.getAccount());

		return super.save(orderPayUpdate);
	}

	public Double getMissingValueOrderPayInOrder(Long orderId) {
		Optional<OrderPO> orderPOOptional = Optional.ofNullable(orderService.findById(orderId));
		if (orderPOOptional.isEmpty()) {
			throw new ApplicationException("Order não encontrada");
		}

		OrderPO orderPO = orderPOOptional.get();

		double total = 0;

		if (!CollectionUtils.isEmpty(orderPO.getOrderPayList())) {
			for (OrderPayPO orderPayPO : orderPO.getOrderPayList()) {
				total += orderPayPO.getValue();
			}
		}

		var result = CalcOperational.roudingDouble(orderPO.getValue()) - total;

		return result;
	}

	private Double getTotalOrderPay(List<OrderPayPO> orderPayList) {
		double total = 0;

		if (!CollectionUtils.isEmpty(orderPayList)) {
			for (OrderPayPO orderPayPO : orderPayList) {
				total += orderPayPO.getValue();
			}
		}

		return total;
	}

	private Double getTotalOrderPayWithoutConsideringSpecificPayOrder(List<OrderPayPO> orderPayList,
			OrderPayPO orderPay) {
		double total = 0;

		if (!CollectionUtils.isEmpty(orderPayList)) {
			for (OrderPayPO orderPayPO : orderPayList) {
				if (orderPayPO.getId().equals(orderPay.getId())) {
					continue;
				}
				total += orderPayPO.getValue();
			}
		}

		return total;
	}
}
