package br.com.focusts.clinicall.service.modules.operational.reception.po;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.FeePO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.InsurancePO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.ProcedurePO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.ProductPO;
import br.com.focusts.clinicall.service.modules.register.operational.po.PatientPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.CompanyPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.PerformerPO;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateDeserializer;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateSerializer;
import br.com.focusts.clinicall.service.modules.tables.general.po.PersonPO;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.querydsl.core.annotations.QueryInit;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;

@Entity
@Table(name = "budget_item")
public class BudgetItemPO extends AbstractPO<Long>{

    private static final long serialVersionUID = 1L;

    @Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "budget_item_id")
	private Long id;


    @JoinColumn(name = "procedure_id", referencedColumnName = "procedure_id")
    @ManyToOne
    private ProcedurePO procedure;

    @JoinColumn(name = "product_id", referencedColumnName = "product_id")
    @ManyToOne
    private ProductPO product;

    @JoinColumn(name = "fee_id", referencedColumnName = "fee_id")
    @ManyToOne
    private FeePO fee;

    @Size(max = 10)
    @NotBlank
    private String code;

    @NotBlank
    @Size(max = 250)
    private String name;

    private Long quantity;

    @NotNull
    private Double value;

    @JoinColumn(name = "budget_id", referencedColumnName = "budget_id")
    @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.DETACH, CascadeType.MERGE})
    @NotNull
    private BudgetPO budget;

    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ProcedurePO getProcedure() {
        return procedure;
    }

    public void setProcedure(ProcedurePO procedure) {
        this.procedure = procedure;
    }

    public ProductPO getProduct() {
        return product;
    }

    public void setProduct(ProductPO product) {
        this.product = product;
    }

    public FeePO getFee() {
        return fee;
    }

    public void setFee(FeePO fee) {
        this.fee = fee;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getQuantity() {
        return quantity;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public BudgetPO getBudget() {
        return budget;
    }

    public void setBudget(BudgetPO budget) {
        this.budget = budget;
    }
}
