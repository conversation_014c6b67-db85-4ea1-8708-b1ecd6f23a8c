//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2023.03.30 at 09:43:38 PM BRT 
//


package br.com.focusts.clinicall.service.modules.tables.tiss.version.v20201;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ct_situacaoAutorizacao complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_situacaoAutorizacao">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="dadosAutorizacao" maxOccurs="unbounded">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_autorizacaoProcedimento">
 *                 &lt;sequence>
 *                   &lt;element name="totalOdonto" minOccurs="0">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="totalvalor" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_quantidade" minOccurs="0"/>
 *                             &lt;element name="totalFranquiaCoparticipacao" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_quantidade" minOccurs="0"/>
 *                             &lt;element name="totalquantidadeUS" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_quantidade" minOccurs="0"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="ct_diariaProrrogacaoAutorizada" minOccurs="0">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_diariaProrrogacao">
 *                           &lt;sequence>
 *                             &lt;element name="tipoAcomodacaoAutorizada" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_tipoAcomodacao" minOccurs="0"/>
 *                             &lt;element name="quantidadeAutorizada" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_quantidade" minOccurs="0"/>
 *                             &lt;element name="motivoGlosa" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_motivoGlosa" minOccurs="0"/>
 *                           &lt;/sequence>
 *                         &lt;/extension>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                 &lt;/sequence>
 *               &lt;/extension>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_situacaoAutorizacao", propOrder = {
    "dadosAutorizacao"
})
public class CtSituacaoAutorizacao {

    @XmlElement(required = true)
    protected List<CtSituacaoAutorizacao.DadosAutorizacao> dadosAutorizacao;

    /**
     * Gets the value of the dadosAutorizacao property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the dadosAutorizacao property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDadosAutorizacao().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link CtSituacaoAutorizacao.DadosAutorizacao }
     * 
     * 
     */
    public List<CtSituacaoAutorizacao.DadosAutorizacao> getDadosAutorizacao() {
        if (dadosAutorizacao == null) {
            dadosAutorizacao = new ArrayList<CtSituacaoAutorizacao.DadosAutorizacao>();
        }
        return this.dadosAutorizacao;
    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_autorizacaoProcedimento">
     *       &lt;sequence>
     *         &lt;element name="totalOdonto" minOccurs="0">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="totalvalor" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_quantidade" minOccurs="0"/>
     *                   &lt;element name="totalFranquiaCoparticipacao" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_quantidade" minOccurs="0"/>
     *                   &lt;element name="totalquantidadeUS" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_quantidade" minOccurs="0"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="ct_diariaProrrogacaoAutorizada" minOccurs="0">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_diariaProrrogacao">
     *                 &lt;sequence>
     *                   &lt;element name="tipoAcomodacaoAutorizada" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_tipoAcomodacao" minOccurs="0"/>
     *                   &lt;element name="quantidadeAutorizada" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_quantidade" minOccurs="0"/>
     *                   &lt;element name="motivoGlosa" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_motivoGlosa" minOccurs="0"/>
     *                 &lt;/sequence>
     *               &lt;/extension>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *       &lt;/sequence>
     *     &lt;/extension>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "totalOdonto",
        "ctDiariaProrrogacaoAutorizada"
    })
    public static class DadosAutorizacao
        extends CtAutorizacaoProcedimento
    {

        protected CtSituacaoAutorizacao.DadosAutorizacao.TotalOdonto totalOdonto;
        @XmlElement(name = "ct_diariaProrrogacaoAutorizada")
        protected CtSituacaoAutorizacao.DadosAutorizacao.CtDiariaProrrogacaoAutorizada ctDiariaProrrogacaoAutorizada;

        /**
         * Gets the value of the totalOdonto property.
         * 
         * @return
         *     possible object is
         *     {@link CtSituacaoAutorizacao.DadosAutorizacao.TotalOdonto }
         *     
         */
        public CtSituacaoAutorizacao.DadosAutorizacao.TotalOdonto getTotalOdonto() {
            return totalOdonto;
        }

        /**
         * Sets the value of the totalOdonto property.
         * 
         * @param value
         *     allowed object is
         *     {@link CtSituacaoAutorizacao.DadosAutorizacao.TotalOdonto }
         *     
         */
        public void setTotalOdonto(CtSituacaoAutorizacao.DadosAutorizacao.TotalOdonto value) {
            this.totalOdonto = value;
        }

        /**
         * Gets the value of the ctDiariaProrrogacaoAutorizada property.
         * 
         * @return
         *     possible object is
         *     {@link CtSituacaoAutorizacao.DadosAutorizacao.CtDiariaProrrogacaoAutorizada }
         *     
         */
        public CtSituacaoAutorizacao.DadosAutorizacao.CtDiariaProrrogacaoAutorizada getCtDiariaProrrogacaoAutorizada() {
            return ctDiariaProrrogacaoAutorizada;
        }

        /**
         * Sets the value of the ctDiariaProrrogacaoAutorizada property.
         * 
         * @param value
         *     allowed object is
         *     {@link CtSituacaoAutorizacao.DadosAutorizacao.CtDiariaProrrogacaoAutorizada }
         *     
         */
        public void setCtDiariaProrrogacaoAutorizada(CtSituacaoAutorizacao.DadosAutorizacao.CtDiariaProrrogacaoAutorizada value) {
            this.ctDiariaProrrogacaoAutorizada = value;
        }


        /**
         * <p>Java class for anonymous complex type.
         * 
         * <p>The following schema fragment specifies the expected content contained within this class.
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_diariaProrrogacao">
         *       &lt;sequence>
         *         &lt;element name="tipoAcomodacaoAutorizada" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_tipoAcomodacao" minOccurs="0"/>
         *         &lt;element name="quantidadeAutorizada" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_quantidade" minOccurs="0"/>
         *         &lt;element name="motivoGlosa" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_motivoGlosa" minOccurs="0"/>
         *       &lt;/sequence>
         *     &lt;/extension>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "tipoAcomodacaoAutorizada",
            "quantidadeAutorizada",
            "motivoGlosa"
        })
        public static class CtDiariaProrrogacaoAutorizada
            extends CtDiariaProrrogacao
        {

            protected String tipoAcomodacaoAutorizada;
            protected BigDecimal quantidadeAutorizada;
            protected CtMotivoGlosa motivoGlosa;

            /**
             * Gets the value of the tipoAcomodacaoAutorizada property.
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getTipoAcomodacaoAutorizada() {
                return tipoAcomodacaoAutorizada;
            }

            /**
             * Sets the value of the tipoAcomodacaoAutorizada property.
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setTipoAcomodacaoAutorizada(String value) {
                this.tipoAcomodacaoAutorizada = value;
            }

            /**
             * Gets the value of the quantidadeAutorizada property.
             * 
             * @return
             *     possible object is
             *     {@link BigDecimal }
             *     
             */
            public BigDecimal getQuantidadeAutorizada() {
                return quantidadeAutorizada;
            }

            /**
             * Sets the value of the quantidadeAutorizada property.
             * 
             * @param value
             *     allowed object is
             *     {@link BigDecimal }
             *     
             */
            public void setQuantidadeAutorizada(BigDecimal value) {
                this.quantidadeAutorizada = value;
            }

            /**
             * Gets the value of the motivoGlosa property.
             * 
             * @return
             *     possible object is
             *     {@link CtMotivoGlosa }
             *     
             */
            public CtMotivoGlosa getMotivoGlosa() {
                return motivoGlosa;
            }

            /**
             * Sets the value of the motivoGlosa property.
             * 
             * @param value
             *     allowed object is
             *     {@link CtMotivoGlosa }
             *     
             */
            public void setMotivoGlosa(CtMotivoGlosa value) {
                this.motivoGlosa = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         * 
         * <p>The following schema fragment specifies the expected content contained within this class.
         * 
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="totalvalor" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_quantidade" minOccurs="0"/>
         *         &lt;element name="totalFranquiaCoparticipacao" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_quantidade" minOccurs="0"/>
         *         &lt;element name="totalquantidadeUS" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_quantidade" minOccurs="0"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "totalvalor",
            "totalFranquiaCoparticipacao",
            "totalquantidadeUS"
        })
        public static class TotalOdonto {

            protected BigDecimal totalvalor;
            protected BigDecimal totalFranquiaCoparticipacao;
            protected BigDecimal totalquantidadeUS;

            /**
             * Gets the value of the totalvalor property.
             * 
             * @return
             *     possible object is
             *     {@link BigDecimal }
             *     
             */
            public BigDecimal getTotalvalor() {
                return totalvalor;
            }

            /**
             * Sets the value of the totalvalor property.
             * 
             * @param value
             *     allowed object is
             *     {@link BigDecimal }
             *     
             */
            public void setTotalvalor(BigDecimal value) {
                this.totalvalor = value;
            }

            /**
             * Gets the value of the totalFranquiaCoparticipacao property.
             * 
             * @return
             *     possible object is
             *     {@link BigDecimal }
             *     
             */
            public BigDecimal getTotalFranquiaCoparticipacao() {
                return totalFranquiaCoparticipacao;
            }

            /**
             * Sets the value of the totalFranquiaCoparticipacao property.
             * 
             * @param value
             *     allowed object is
             *     {@link BigDecimal }
             *     
             */
            public void setTotalFranquiaCoparticipacao(BigDecimal value) {
                this.totalFranquiaCoparticipacao = value;
            }

            /**
             * Gets the value of the totalquantidadeUS property.
             * 
             * @return
             *     possible object is
             *     {@link BigDecimal }
             *     
             */
            public BigDecimal getTotalquantidadeUS() {
                return totalquantidadeUS;
            }

            /**
             * Sets the value of the totalquantidadeUS property.
             * 
             * @param value
             *     allowed object is
             *     {@link BigDecimal }
             *     
             */
            public void setTotalquantidadeUS(BigDecimal value) {
                this.totalquantidadeUS = value;
            }

        }

    }

}
