
package br.com.focusts.clinicall.service.modules.tables.tiss.facade;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.TypeAttendancePO;

public interface TypeAttendanceFacade extends CrudFacade<TypeAttendancePO,java.lang.Long>{

    public Page<TypeAttendancePO> findByCodeContainingOrNameContaining(PageSearchTO pageSearchTO);

}

