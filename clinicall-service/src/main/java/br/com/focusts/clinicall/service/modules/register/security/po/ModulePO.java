package br.com.focusts.clinicall.service.modules.register.security.po;

import java.util.List;

import javax.persistence.*;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import br.com.focusts.clinicall.fw.po.AbstractPO;

@Entity
@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
@Table(name = "module")
public class ModulePO extends AbstractPO<Long>  {
	

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "module_id")
	private Long id;

	private String name;
	
	private String code;

	@OrderBy("name ASC")
	@OneToMany(mappedBy = "module", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
	private List<ViewPO> views;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "parent_id")
	private ModulePO parent;


	public ModulePO() {
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public List<ViewPO> getViews() {
		return this.views;
	}

	public void setViews(List<ViewPO> viewPOs) {
		this.views = viewPOs;
	}

	public ModulePO getParent() {
		return parent;
	}

	public void setParent(ModulePO parent) {
		this.parent = parent;
	}

	public ViewPO addView(ViewPO viewPO) {
		getViews().add(viewPO);
		viewPO.setModule(this);

		return viewPO;
	}

	public ViewPO removeView(ViewPO viewPO) {
		getViews().remove(viewPO);
		viewPO.setModule(null);

		return viewPO;
	}

}