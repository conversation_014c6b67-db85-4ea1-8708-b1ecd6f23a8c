package br.com.focusts.clinicall.service.modules.register.accreditation.po;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;

import org.hibernate.envers.Audited;

import br.com.focusts.clinicall.fw.po.AbstractPO;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "procedure_code")
@Audited
public class ProcedureCodePO extends AbstractPO<Long> {
	
    private static final long serialVersionUID = 1L;
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "procedure_code_id")
    private Long id;
    
	@NotBlank
	private String code;
    
	@NotBlank
	@Column(name = "table_name")
    private String tableName;
    
    @JoinColumn(name = "procedure_id", referencedColumnName = "procedure_id")
    @ManyToOne(optional = false)
    private ProcedurePO procedure;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public ProcedurePO getProcedure() {
		return procedure;
	}

	public void setProcedure(ProcedurePO procedure) {
		this.procedure = procedure;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = super.hashCode();
		result = prime * result + ((code == null) ? 0 : code.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (!super.equals(obj))
			return false;
		if (getClass() != obj.getClass())
			return false;
		ProcedureCodePO other = (ProcedureCodePO) obj;
		if (code == null) {
			if (other.code != null)
				return false;
		} else if (!code.equals(other.code))
			return false;
		return true;
	}

	

}
