package br.com.focusts.clinicall.service.modules.operational.reception.service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

import br.com.focusts.clinicall.service.modules.operational.reception.to.OrderHistoricPatientTO;
import br.com.focusts.clinicall.service.modules.operational.reception.to.OrderItemTO;
import br.com.focusts.clinicall.service.modules.operational.reception.to.OrderRecalculationTO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.ProcedurePO;
import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.ExclusionOrderItemPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderItemPO;

public interface OrderItemService extends CrudService<OrderItemPO, Long>{

	public Page<OrderItemPO> search(PageSearchTO pageSearchTO);


	public OrderItemPO findMainOrderItem(Long orderId);
	
	public List<OrderItemPO> findByOrder_idOrderByDateAsc(Long orderId);

	public void processValues(OrderItemPO orderItemPO);

	public void delete(ExclusionOrderItemPO exclusionOrderItemPO);

	public String recalculation(OrderRecalculationTO orderRecalculation);

	OrderItemPO setInfoParticipations(OrderItemPO orderItemPO);

    List<OrderHistoricPatientTO> findByHistoricOrder(Long ordeId);

	public void duplicateOrderItem(Long orderitemId, LocalDate date, LocalTime hour);

	public List<OrderHistoricPatientTO> findOrderItemByOrderId(Long orderId);

	OrderItemPO duplicateOrderItemReturn(Long orderitemId, ProcedurePO procedure);
}