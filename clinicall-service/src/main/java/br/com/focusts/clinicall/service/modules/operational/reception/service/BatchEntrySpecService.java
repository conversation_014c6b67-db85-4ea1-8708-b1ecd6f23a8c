
package br.com.focusts.clinicall.service.modules.operational.reception.service;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.BatchEntrySpecPO;

public interface BatchEntrySpecService extends CrudService<BatchEntrySpecPO,java.lang.Long>{

    public Page<BatchEntrySpecPO> find(PageSearchTO pageSearchTO);

}
