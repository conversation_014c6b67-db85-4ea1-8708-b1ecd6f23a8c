
package br.com.focusts.clinicall.service.modules.register.accreditation.service;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.AgreementPO;

import java.util.List;

public interface AgreementService extends CrudService<AgreementPO,java.lang.Long>{

    public Page<AgreementPO> findActives(PageSearchTO pageSearchTO);

	public Page<AgreementPO> findActivesWithoutAccreditationForProcedure(Long procedureId, PageSearchTO pageSearchTO);

	public Page<AgreementPO> findByInsuranceId(Long insuranceId, PageSearchTO pageSearchTO);
	
	public AgreementPO  findByCompanyIdAndInsuranceId(Long companyId,Long insuranceId);
	
	public AgreementPO findByInsuranceId(Long insuranceId);

	public AgreementPO findByInsuranceId(Long insuranceId, Long companyId);

	public Page<AgreementPO> findByInsurance_idAndCompany_idIsActive(Long insuranceId, Long companyId, PageSearchTO pageSearchTO);

	public List<AgreementPO> findByInsurance_idAndActiveTrue(Long insuranceId, Long notInAgreementId);

}
