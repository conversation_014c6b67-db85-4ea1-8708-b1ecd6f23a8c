
package br.com.focusts.clinicall.service.modules.tables.general.facade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.general.po.OccupationTissPO;
import br.com.focusts.clinicall.service.modules.tables.general.service.OccupationTissService;

@Component
@Transactional
public class DefaultOccupationTissFacade extends AbstractCrudFacade <OccupationTissPO, java.lang.Long> implements OccupationTissFacade  {

	@Autowired
	private OccupationTissService occupationTissService;

	@Override
	public CrudService<OccupationTissPO,java.lang.Long> getCrudService() {
            return occupationTissService;
	}

	@Override
	public Page<OccupationTissPO> findByCodeContainingOrOccupation_nameContainingOrTiss_versionContaining(PageSearchTO pageSearchTO) {
            return occupationTissService.findByCodeContainingOrOccupation_nameContainingOrTiss_versionContaining(pageSearchTO);
	}
        
    
	
}
