package br.com.focusts.clinicall.service.modules.tables.general.po;

import java.util.List;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import br.com.focusts.clinicall.fw.po.AbstractPO;

/**
 *
 * <AUTHOR>
 */
@Entity
@Audited
@Table(name = "country")
public class CountryPO extends AbstractPO<Long> {
	private static final long serialVersionUID = 1L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "country_id")
	private Long id;
	
	@Basic(optional = false)
	@NotBlank 
	@Size(max = 3)
	@Pattern(regexp = "^[0-9]*$" , message = "onlyNumber")
	private String code;
	
	@Basic(optional = false)
	@NotBlank 
	@Size(max = 250)
	private String name;
	
	@Basic(optional = false)
	@NotBlank 
	@Size(max = 3)
	private String initials;
	
	@Size(max = 250)
	private String gentile;
	
	@Size(max = 250)
	private String officialName;
	
	public CountryPO() {
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getInitials() {
		return initials;
	}

	public void setInitials(String initials) {
		this.initials = initials;
	}

	public String getGentile() {
		return gentile;
	}

	public void setGentile(String gentile) {
		this.gentile = gentile;
	}

	public String getOfficialName() {
		return officialName;
	}

	public void setOfficialName(String officialName) {
		this.officialName = officialName;
	}

}
