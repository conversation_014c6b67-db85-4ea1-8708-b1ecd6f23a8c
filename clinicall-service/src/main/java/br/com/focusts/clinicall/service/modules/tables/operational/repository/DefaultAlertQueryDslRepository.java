package br.com.focusts.clinicall.service.modules.tables.operational.repository;

import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QInsurancePO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QInsurancePlanPO;
import br.com.focusts.clinicall.service.modules.register.operational.po.QPatientPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.QCompanyPO;
import br.com.focusts.clinicall.service.modules.register.security.po.QUserPO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.QPlanPO;
import br.com.focusts.clinicall.service.modules.tables.general.po.QPersonPO;
import br.com.focusts.clinicall.service.modules.tables.operational.to.AlertListTO;
import br.com.focusts.clinicall.service.modules.tables.operational.po.QAlertPO;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

@Repository
public class DefaultAlertQueryDslRepository implements AlertQueryDslRepository{

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Page<AlertListTO> findByContext(String context, Long id, PageSearchTO pageSearchTO) {
        PageRequest pageRequest = null;
        String arguments = "";

        if (pageSearchTO != null) {
            pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage());
            arguments = pageSearchTO.getArgument();
        }

        JPAQuery<AlertListTO> queryAlert = new JPAQuery<>(entityManager);

        QAlertPO alert = QAlertPO.alertPO;
        QCompanyPO company = QCompanyPO.companyPO;
        QPatientPO patient = QPatientPO.patientPO;
        QPersonPO person = QPersonPO.personPO;
        QInsurancePO insurance = QInsurancePO.insurancePO;
        QInsurancePlanPO insurancePlan = QInsurancePlanPO.insurancePlanPO;
        QPlanPO plan = QPlanPO.planPO;
        QUserPO user = QUserPO.userPO;

        queryAlert.select(
                        Projections.bean(
                                AlertListTO.class,
                                alert.id,
                                alert.name.as("description"),
                                alert.module.as("module"),
                                company.alias.as("company"),
                                person.name.as("patient"),
                                insurance.name.as("insurance"),
                                plan.name.as("plan"),
                                alert.active,
                                user.login.as("userCreated")
                                )
                )
                .from(alert)
                .leftJoin(company).on(company.id.eq(alert.company.id))
                .leftJoin(patient).on(patient.id.eq(alert.patient.id))
                .leftJoin(person).on(person.id.eq(patient.person.id))
                .leftJoin(insurance).on(insurance.id.eq(alert.insurance.id))
                .leftJoin(insurancePlan).on(insurancePlan.id.eq(alert.insurancePlanId))
                .leftJoin(plan).on(plan.id.eq(insurancePlan.plan.id))
                .leftJoin(user).on(user.id.eq(alert.createdBy));

        switch (context){
            case "PATIENT":
                queryAlert.where(patient.id.eq(id));
                break;
            case "INSURANCE":
                queryAlert.where(insurance.id.eq(id));
                break;
            case "COMPANY":
                queryAlert.where(company.id.eq(id));
                break;
        }

        var count = queryAlert.fetchCount();
        queryAlert.limit(pageRequest.getPageSize()).offset(pageRequest.getOffset());
        var result = queryAlert.fetch();
        return new PageImpl<AlertListTO>(result,pageRequest ,count);
    }
}
