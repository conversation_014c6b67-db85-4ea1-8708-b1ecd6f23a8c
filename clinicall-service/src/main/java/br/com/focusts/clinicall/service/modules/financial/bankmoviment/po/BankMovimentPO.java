package br.com.focusts.clinicall.service.modules.financial.bankmoviment.po;

import br.com.focusts.clinicall.fw.po.AbstractPO;

import br.com.focusts.clinicall.service.modules.financial.bond.po.BondPayPO;
import br.com.focusts.clinicall.service.modules.tables.financial.po.AccountPO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import java.time.LocalDate;

/**
 * Movimento Bancário - Lançamento de Movimentação Bancária
 */
@Entity
@Table(name = "bank_moviment")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BankMovimentPO extends AbstractPO<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * Id Sequencial
     */
    @Id
    @GeneratedValue (strategy = GenerationType.IDENTITY)
    @Column(name = "bank_moviment_id")
    private Long id;

    /**
     * Conta Bancária
     * @see br.com.focusts.clinicall.service.modules.tables.financial.po.AccountPO
     */
    @NotNull
    @JoinColumn(name = "account_id", referencedColumnName = "account_id")
    @OneToOne(fetch = FetchType.LAZY)
    private AccountPO account;

    /**
     * Conciliação Bancária
     * @see br.com.focusts.clinicall.service.modules.tables.financial.po.AccountPO
     */
    @NotNull
    @JoinColumn(name = "bank_conciliation_id", referencedColumnName = "bank_conciliation_id")
    @OneToOne(fetch = FetchType.LAZY)
    private BankConciliationPO bankConciliation;

    /**
     * Pagamento do Titulo
     * @see br.com.focusts.clinicall.service.modules.financial.bond.po.BondPayPO
     */
    @NotNull
    @JoinColumn(name = "bond_pay_id", referencedColumnName = "bond_pay_id")
    @OneToOne(fetch = FetchType.LAZY)
    private BondPayPO bondPay;

    /**
     * Tipo de Operação (Débito/Crédito)
     */
    @NotBlank
    @NotNull
    @Size(max = 1)
    private String operation;

    /**
     * Historico Descritivo
     */
    @NotNull
    @NotBlank
    @Size(max = 250)
    private String historic;

    /**
     * Data da Movimentação
     */
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @Column(name = "dated")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull
    @NotBlank
    private LocalDate dated;

    /**
     * Valor da Movimentação
     */
    @NotNull
    @NotBlank
    private Double value;

    @JoinColumn(name = "parent_id", referencedColumnName = "bank_moviment_id")
    @OneToOne(fetch = FetchType.LAZY)
    private BankMovimentPO bankMoviment;

    /**
     * Id do Movimento Bancário
     * @return Valor atual do Identificador do Titulo
     */
    @Override
    public Long getId() {
        return this.id;
    }
}
