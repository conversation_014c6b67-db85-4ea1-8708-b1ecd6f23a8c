
package br.com.focusts.clinicall.service.modules.register.organizational.service;

import java.util.List;

import javax.validation.Valid;

import br.com.focusts.clinicall.service.modules.register.organizational.to.HoraryTO;
import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.HoraryCustomPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.HoraryPO;

public interface HoraryService extends CrudService<HoraryPO,java.lang.Long>{

	public Page<HoraryTO> find(Long performerId, PageSearchTO pageSearchTO);

	public void saveAllDays(HoraryPO horaryPO);
	
	public void deleteHoraryCustom(Long id);

	public List<HoraryCustomPO> findAllHoraryCustom();

	public List<HoraryCustomPO> findAllHoraryCustomByHoraryId(Long horaryId);
	public void deleteByPerformerId(Long performerId);

	public HoraryCustomPO saveHoraryCustom(@Valid HoraryCustomPO horaryCustomPO);

}
