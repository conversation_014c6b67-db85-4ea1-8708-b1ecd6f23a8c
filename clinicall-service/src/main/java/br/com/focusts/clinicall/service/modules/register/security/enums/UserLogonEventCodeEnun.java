package br.com.focusts.clinicall.service.modules.register.security.enums;

import com.fasterxml.jackson.annotation.JsonValue;

import br.com.focusts.clinicall.fw.enums.DescriptionEnum;

public enum UserLogonEventCodeEnun implements DescriptionEnum {

	
	LOGON_SUCCESS {

		public String getName() {
			return "Logon";
		}

		public String getValue() {

			return "0";
		}

		public String getDescription() {
			return "Logon com sucesso";
		}

	},
	
	INVALID_AUTHENTICATION {

		public String getName() {
			return "Invalida";
		}

		public String getValue() {

			return "1";
		}

		public String getDescription() {
			return "Autenticação Invalida";
		}

	},
	USER_INACTIVE {
		
		public String getName() {
			return "Inativo";
		}

		public String getValue() {

			return "2";
		}

		public String getDescription() {
			return "Usuário Inativo";
		}

	},
	
	USER_LOCKED {

		public String getName() {
			return "Bloqueado";
		}

		public String getValue() {

			return "3";
		}

		public String getDescription() {
			return "Usuário Bloqueado";
		}

	},
	
	PASSWORD_EXPIRED {

		public String getName() {
			return "Senha Expirada";
		}

		public String getValue() {

			return "4";
		}

		public String getDescription() {
			return "Usuário com senha expirada";
		}

	},
	LOGOF_SUCCESS {

		public String getName() {
			return "Logof";
		}

		public String getValue() {

			return "5";
		}

		public String getDescription() {
			return "Logof com sucesso";
		}

	};
	

	@JsonValue
    public String toValue() {

        return getValue(); 
    }
	
	
}