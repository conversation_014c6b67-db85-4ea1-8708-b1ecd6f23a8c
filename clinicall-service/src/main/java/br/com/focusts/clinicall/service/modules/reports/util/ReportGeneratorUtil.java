package br.com.focusts.clinicall.service.modules.reports.util;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.Connection;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import br.com.focusts.clinicall.service.modules.operational.reception.to.OrderBillingTO;
import br.com.focusts.clinicall.service.modules.reports.enums.ReportPathEnum;
import br.com.focusts.clinicall.service.modules.reports.to.*;
import net.sf.jasperreports.engine.*;
import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.core.io.ClassPathResource;

import br.com.focusts.clinicall.service.modules.reports.enums.ReportParameterType;
import br.com.focusts.clinicall.service.modules.reports.enums.ReportTypeEnum;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.ooxml.JRXlsxExporter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import net.sf.jasperreports.export.SimplePdfExporterConfiguration;
import net.sf.jasperreports.export.SimpleXlsxReportConfiguration;

public class ReportGeneratorUtil {

	private ReportGeneratorUtil() {
	}

	private static final String PATH_REPORT = "reports/";

	private static Logger log = LogManager.getLogger(ReportGeneratorUtil.class);

	static {
		setPropertyReport();
	}

	public static InputStream generate(ReportTO report, Connection connection) throws Exception {

		InputStream generatedReport = null;
		log.info("ReportGenerator,  Processo de gereção iniciado");
		try {
			Map<String, Object> parametros = processParameter(report.getReportParamTOList(), connection);
			log.info("ReportGenerator,  Parâmetros processados!");
			log.info("ReportGenerator,  Método: generate");
			ReportTypeEnum reportTypeEnum = report.getReportTypeEnum();
			if (reportTypeEnum == ReportTypeEnum.PDF) {
				if (connection == null){
					generatedReport = gerarPDFDataSource(parametros, report);
				}else{
					generatedReport = gerarPDF(parametros, report, connection);
				}
			} else if (reportTypeEnum == ReportTypeEnum.XLSX) {
				generatedReport = gerarXLSX(parametros, report, connection);
			}

		} finally {
			if(connection!=null && ! connection.isClosed()) {
				connection.close();
			}
		}

		return generatedReport;

	}

	private static HashMap<String, Object> processParameter(List<ReportParamTO> reportParamTOList,
															Connection connection) throws Exception {

		HashMap<String, Object> mapReport = new HashMap<>();

		for (ReportParamTO reportParamTO : reportParamTOList) {
			ReportParameterType reportParameterType = ReportParameterType.valueOf(reportParamTO.getParamType());

			String paramName = reportParamTO.getParamName();
			Object paramValue = reportParamTO.getParamValue();

			if (paramValue != null) {
				setParameterValue(connection, mapReport, reportParameterType, paramName, paramValue);
			} else {
				mapReport.put(paramName, null);
			}
		}

		return mapReport;
	}

	private static void setParameterValue(Connection connection, HashMap<String, Object> mapReport,
										  ReportParameterType reportParameterType, String paramName, Object paramValue)
			throws ParseException, Exception {
		switch (reportParameterType) {
			case STRING:
				mapReport.put(paramName, paramValue.toString());
				break;
			case INTEGER:
				mapReport.put(paramName, Integer.valueOf(paramValue.toString()));
				break;
			case BOOLEAN:
				mapReport.put(paramName, Boolean.valueOf(paramValue.toString()));
				break;
			case DATE:
				mapReport.put(paramName, new SimpleDateFormat("yyyy-MM-dd").parse(paramValue.toString()));
				break;
			case IN:
				ArrayList<Integer> values = (ArrayList<Integer>) paramValue;
				mapReport.put(paramName, values);
				break;
			case LONG:
				Long longValue = (Long) paramValue;
				mapReport.put(paramName, longValue);
				break;
			case DOUBLE:
				Double doubleValue = (Double) paramValue;
				mapReport.put(paramName, doubleValue);
				break;
//		case COLLECTION:
//			Map<String, Map<String, List<ReportDetailsTransProdTO>>> paramList = (Map<String, Map<String, List<ReportDetailsTransProdTO>>>) paramValue;
//			mapReport.put(paramName, paramList);
//			break;
			case TIME:
				mapReport.put(paramName, new SimpleDateFormat("HH:mm").parse(paramValue.toString()));
				break;
			case SUBREPORTS:
				List<ReportParamTO> subReportParamList = (List<ReportParamTO>) paramValue;

				List<ReportParamTO> newsubReportParamList = new ArrayList<>();

				for (Object reportParam : subReportParamList) {

					if (reportParam instanceof LinkedHashMap) {
						LinkedHashMap reportParamMap = (LinkedHashMap) reportParam;
						ReportParamTO reportParamSubreports = new ReportParamTO();
						reportParamSubreports.setParamName(reportParamMap.get("paramName").toString());
						reportParamSubreports.setParamType(reportParamMap.get("paramType").toString());
						reportParamSubreports.setParamValue(reportParamMap.get("paramValue"));
						newsubReportParamList.add(reportParamSubreports);
					} else if (reportParam instanceof ReportParamTO) {
						newsubReportParamList.add((ReportParamTO) reportParam);
					}
				}
				HashMap<String, Object> mapSubreports = processParameter(newsubReportParamList, connection);
				ReportTO reportTO = new ReportTO();
				reportTO.setReportName(paramName);
				reportTO.setReportType(ReportTypeEnum.PDF);
				InputStream inputStreamSubReports = null;
				if (connection == null){
					inputStreamSubReports = gerarPDFDataSource(mapSubreports, reportTO);
				}else{
					inputStreamSubReports = gerarPDF(mapSubreports, reportTO, connection);
				}
				mapReport.put(paramName, inputStreamSubReports);
				break;
			default:
				break;
		}
	}

	private static InputStream gerarPDFDataSource(Map<String, Object> parametros, ReportTO report) throws JRException, IOException {
		log.info("Iniciando a geração do relatório " + report.getFileName());
		try {
			log.info("Obtendo caminho do relatório:" + PATH_REPORT + report.getJasperName());
			String path = PATH_REPORT + report.getJasperName();
			InputStream jasper = null;
			JasperPrint jasperPrint = null;

			log.info("Iniciando processamento do Jasper...");
			path = path.replace(".jasper",".jrxml");
			jasper = new ClassPathResource(path).getInputStream();


			List<ReportCustonArray> reportCuston = new ArrayList<ReportCustonArray>();
			reportCuston.add(new ReportCustonArray());

			var reportParam = report.getReportParamTOList().stream()
					.filter(rp -> rp.getParamName().equals("dataSource")).findFirst().get();

			if (report.getReportName().equals(ReportPathEnum.REPORT_DETAILED_TRANSFER_PRODUCTION.getValue())) {
				reportCuston.get(0).setReportDetailsTransProdTOList((List<ReportDetailsTransProdTO>) reportParam.getParamValue());
			}
			if (report.getReportName().equals(ReportPathEnum.REPORT_CHECK_BILLING.getValue())) {
				reportCuston.get(0).setOrderList((List<OrderBillingTO>) reportParam.getParamValue());
			}
			if (report.getReportName().equals(ReportPathEnum.HONORARY_GUIDE.getValue())){
				reportCuston.get(0).setGuia((List<ReportGuideTO>) reportParam.getParamValue());
				var jasperSup = new ClassPathResource("reports/guias/honorario/guiaHonorarioReport.jrxml").getInputStream();
				var jasperSupProcedimentos = new ClassPathResource("reports/guias/honorario/subreports/procedimentosRealizados.jrxml").getInputStream();
				var jasperSupProfissionais = new ClassPathResource("reports/guias/honorario/subreports/profissionais.jrxml").getInputStream();
				var jasperSubReport = JasperCompileManager.compileReport(jasperSup);
				var jasperSubReportProcedimentos = JasperCompileManager.compileReport(jasperSupProcedimentos);
				var jasperSubReportProfissionais = JasperCompileManager.compileReport(jasperSupProfissionais);
				parametros.put("subreportParameter", jasperSubReport);
				parametros.put("subreportParameterProcedures", jasperSubReportProcedimentos);
				parametros.put("subreportParameterProfessionals", jasperSubReportProfissionais);
			}

			if (report.getReportName().equals(ReportPathEnum.ADMISSION_GUIDE.getValue())){
				reportCuston.get(0).setGuia((List<ReportGuideTO>) reportParam.getParamValue());
				var jasperSubReport = JasperCompileManager.compileReport(new ClassPathResource("reports/guias/internamento/guiaInternacao.jrxml").getInputStream());
				var jasperSubReportProcedimentos = JasperCompileManager.compileReport(new ClassPathResource("reports/guias/internamento/subreports/procedimentosRealizados.jrxml").getInputStream());
				var jasperSupProfissionais =  JasperCompileManager.compileReport(new ClassPathResource("reports/guias/internamento/subreports/profissionais.jrxml").getInputStream());
				parametros.put("subreportParameter", jasperSubReport);
				parametros.put("subreportParameterProcedures", jasperSubReportProcedimentos);
				parametros.put("subreportParameterProfessionals", jasperSupProfissionais);
			}

			JRBeanCollectionDataSource dataSource = getDataSource(reportCuston);

			var jasperReport= JasperCompileManager.compileReport(jasper);

			jasperPrint = JasperFillManager.fillReport(jasperReport, parametros, dataSource);


			log.info("Processamento finalizado.");
			log.info("Iniciando Exportação.");
			byte[] bytes = JasperExportManager.exportReportToPdf(jasperPrint);
			log.info("Exportação finalizada.");

			return new ByteArrayInputStream(bytes);
		} catch (Exception e) {
			log.error("Erro na geração do relatório " + report.getFileName(), e.getMessage());
			throw e;
		}
	}

	private static InputStream gerarPDF(Map<String, Object> parametros, ReportTO report, Connection connection)
			throws Exception {
		log.info("Iniciando a geração do relatório " + report.getFileName());
		try {
			log.info("Obtendo caminho do relatório:" + PATH_REPORT + report.getJasperName());
			String path = PATH_REPORT + report.getJasperName();
			InputStream jasper = null;
			JasperPrint jasperPrint = null;

			log.info("Iniciando processamento do Jasper...");

			if (connection == null) {

				List<ReportCustonArray> reportCuston = new ArrayList<ReportCustonArray>();
				reportCuston.add(new ReportCustonArray());



				JRBeanCollectionDataSource dataSource = getDataSource(reportCuston);
				path = path.replace(".jasper",".jrxml");
				jasper = new ClassPathResource(path).getInputStream();

				var jasperSup = new ClassPathResource("reports/guias/honorario/guiaHonorarioReport.jrxml").getInputStream();
				var jasperSupProcedimentos = new ClassPathResource("reports/guias/honorario/subreports/procedimentosRealizados.jrxml").getInputStream();
				var jasperSupProfissionais = new ClassPathResource("reports/guias/honorario/subreports/profissionais.jrxml").getInputStream();

				var jasperReport= JasperCompileManager.compileReport(jasper);
				var jasperSubReport = JasperCompileManager.compileReport(jasperSup);
				var jasperSubReportProcedimentos = JasperCompileManager.compileReport(jasperSupProcedimentos);
				var jasperSubReportProfissionais = JasperCompileManager.compileReport(jasperSupProfissionais);


				parametros.put("subreportParameter", jasperSubReport);
				parametros.put("subreportParameterProcedures", jasperSubReportProcedimentos);
				parametros.put("subreportParameterProfessionals", jasperSubReportProfissionais);
				jasperPrint = JasperFillManager.fillReport(jasperReport, parametros, dataSource);
			} else {
				jasper = new ClassPathResource(path).getInputStream();
				jasperPrint = JasperFillManager.fillReport(jasper, parametros, connection);
			}

			log.info("Processamento finalizado.");
			log.info("Iniciando Exportação.");
			byte[] bytes = JasperExportManager.exportReportToPdf(jasperPrint);
			log.info("Exportação finalizada.");

			return new ByteArrayInputStream(bytes);
		} catch (Exception e) {
			log.error("Erro na geração do relatório " + report.getFileName(), e.getMessage());
			throw e;
		}

	}



	public static JasperPrint gerarJasperPrint(List<?> dados, Map<String, Object> parametros, ReportTO report)
			throws Exception {
		log.info("Iniciando a geração do relatório " + report.getFileName());
		try {
			JRDataSource dataSource = getDataSource(dados);
			log.info("Obtendo caminho do relatório:" + PATH_REPORT + report.getJasperName());
			InputStream jasper = new ClassPathResource(PATH_REPORT + report.getJasperName()).getInputStream();

			log.info("Iniciando processamento do Jasper...");
			JasperPrint jasperPrint = JasperFillManager.fillReport(jasper, parametros, dataSource);

			for (Iterator<JRPrintPage> iterator = jasperPrint.getPages().iterator(); iterator.hasNext();) {
				JRPrintPage page = iterator.next();
				if (!iterator.hasNext() && page.getElements().isEmpty()) {
					iterator.remove();
				}
			}

			return jasperPrint;
		} catch (Exception e) {
			log.error("Erro na geração do relatório " + report.getFileName(), e.getMessage());
			throw e;
		}

	}

	public static InputStream gerarPDF2(List<JasperPrint> jasperPrintList) throws Exception {
		try {
			ByteArrayOutputStream byteArrayOuputStream = new ByteArrayOutputStream();

			File pdf = new File("teste.pdf");
			// Generating report using List<JasperPrint>
			JRPdfExporter exporter = new JRPdfExporter();
			exporter.setExporterInput(SimpleExporterInput.getInstance(jasperPrintList));
			exporter.setExporterOutput(new SimpleOutputStreamExporterOutput(pdf));

			SimplePdfExporterConfiguration configuration = new SimplePdfExporterConfiguration();
			configuration.setCreatingBatchModeBookmarks(true);
			exporter.setConfiguration(configuration);
			exporter.exportReport();

			byte[] byteArray = IOUtils.toByteArray(new FileInputStream(pdf));
			byteArrayOuputStream.write(byteArray);

			pdf.delete();

			return new ByteArrayInputStream(byteArrayOuputStream.toByteArray());
		} catch (Exception e) {
			log.error("Erro na geração do relatório ");
			throw e;
		}

	}

	private static InputStream gerarXLSX(Map<String, Object> parametros, ReportTO report, Connection connection)
			throws JRException, IOException {
		log.info("Iniciando a geração do relatório " + report.getFileName());
		SimpleXlsxReportConfiguration configuration = new SimpleXlsxReportConfiguration();
		configuration.setOnePagePerSheet(false);
		configuration.setDetectCellType(false);
		configuration.setCollapseRowSpan(false);
		configuration.setWhitePageBackground(false);
		configuration.setAutoFitPageHeight(false);
		configuration.setCellHidden(false);
		configuration.setCellLocked(false);
		configuration.setForcePageBreaks(false);
		configuration.setIgnoreGraphics(false);

		// Remover espaços em branco entre as linhas
		configuration.setRemoveEmptySpaceBetweenRows(true);

		log.info("Obtendo caminho do relatório:" + PATH_REPORT + report.getJasperName());
		InputStream jasper = ReportGeneratorUtil.class.getClassLoader()
				.getResourceAsStream(PATH_REPORT + report.getJasperName());
		log.info("Iniciando processamento do Jasper...");
		JasperPrint jasperPrint = null;
		if (connection == null){
			List<ReportCustonArray> reportCuston = new ArrayList<ReportCustonArray>();
			reportCuston.add(new ReportCustonArray());
			var reportParam = report.getReportParamTOList().stream()
					.filter(rp -> rp.getParamName().equals("dataSource")).findFirst().get();

			JRBeanCollectionDataSource dataSource = getDataSource(reportCuston);
			reportCuston.get(0).setOrderList((List<OrderBillingTO>) reportParam.getParamValue());
			jasperPrint = JasperFillManager.fillReport(jasper, parametros, dataSource);
		}else {
			jasperPrint = JasperFillManager.fillReport(jasper, parametros, connection);
		}
		log.info("Finalizado processamento do Jasper.");
		JRXlsxExporter exporterXLSX = new JRXlsxExporter();
		exporterXLSX.setExporterInput(new SimpleExporterInput(jasperPrint));
		ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
		exporterXLSX.setExporterOutput(new SimpleOutputStreamExporterOutput(byteArrayOutputStream));
		exporterXLSX.setConfiguration(configuration);
		log.info("Iniciando exportação do Relatório.");
		exporterXLSX.exportReport();
		log.info("Finalizado exportação do Relatório.");
		byte[] byteArray = byteArrayOutputStream.toByteArray();
		byteArrayOutputStream.close();
		return new ByteArrayInputStream(byteArray);
	}


	private static InputStream gerarXLSX(List<?> dados, Map<String, Object> parametros, ReportTO report)
			throws JRException, IOException {
		log.info("Iniciando a geração do relatório " + report.getFileName());
		SimpleXlsxReportConfiguration configuration = new SimpleXlsxReportConfiguration();
		configuration.setOnePagePerSheet(false);
		configuration.setDetectCellType(false);
		configuration.setCollapseRowSpan(false);
		configuration.setWhitePageBackground(false);
		configuration.setAutoFitPageHeight(false);
		configuration.setCellHidden(false);
		configuration.setCellLocked(false);
		configuration.setForcePageBreaks(false);
		configuration.setIgnoreGraphics(false);
		JRDataSource dataSource = getDataSource(dados);
		log.info("Obtendo caminho do relatório:" + PATH_REPORT + report.getJasperName());
		InputStream jasper = ReportGeneratorUtil.class.getClassLoader()
				.getResourceAsStream(PATH_REPORT + report.getJasperName());
		log.info("Iniciando processamento do Jasper...");
		JasperPrint jasperPrint = JasperFillManager.fillReport(jasper, parametros, dataSource);
		log.info("Finalizado processamento do Jasper.");
		JRXlsxExporter exporterXLSX = new JRXlsxExporter();
		exporterXLSX.setExporterInput(new SimpleExporterInput(jasperPrint));
		ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
		exporterXLSX.setExporterOutput(new SimpleOutputStreamExporterOutput(byteArrayOutputStream));
		exporterXLSX.setConfiguration(configuration);
		log.info("Iniciando exportação do Relatório.");
		exporterXLSX.exportReport();
		log.info("Finalizado exportação do Relatório.");
		byte[] byteArray = byteArrayOutputStream.toByteArray();
		byteArrayOutputStream.close();
		return new ByteArrayInputStream(byteArray);
	}

	private static void setPropertyReport() {
		DefaultJasperReportsContext context = DefaultJasperReportsContext.getInstance();
		JRPropertiesUtil.getInstance(context).setProperty("net.sf.jasperreports.xpath.executer.factory",
				"net.sf.jasperreports.engine.util.xml.JaxenXPathExecuterFactory");
	}

	private static JRBeanCollectionDataSource getDataSource(List<?> dados) {
		// Conversao do Formato Jasper para PDF.
		return new JRBeanCollectionDataSource(dados);
		// if (!CollectionUtils.isEmpty(dados)) {

		// } else {
		// return new JREmptyDataSource();
		// }
	}

	public static void publish(HttpServletResponse response, InputStream generatedReport, ReportTO report)
			throws IOException {
		log.info(String.format("Preparando relatório %s para enviar para o frontend ", report.getFileName()));
		response.setContentType(report.getReportTypeEnum().getContentType()); // tipo do conteúdo na resposta
		response.setContentLength(generatedReport.available()); // opcional. ajuda na barra de progresso
		response.setHeader("Content-Disposition", String.format("attachment; filename=%s", report.getFileName()));
		response.setHeader("Access-Control-Allow-Credentials", "true");
		response.setHeader("Access-Control-Allow-Origin", "*");
		OutputStream outputStream = response.getOutputStream();
		IOUtils.copy(generatedReport, outputStream);
		log.info(String.format("Relatório %s enviado no response para o frontend ", report.getFileName()));
	}

}
