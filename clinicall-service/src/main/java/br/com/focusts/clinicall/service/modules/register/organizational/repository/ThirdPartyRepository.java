
package br.com.focusts.clinicall.service.modules.register.organizational.repository;

import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.to.PatientTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.register.organizational.po.ThirdPartyPO;

import java.util.List;
import java.util.Optional;

@Repository
public interface ThirdPartyRepository extends CrudRepository<ThirdPartyPO,java.lang.Long>{

    @Query(nativeQuery = true, value = """
            SELECT oitp.order_item_third_party_id FROM order_item_third_party oitp
            JOIN order_item orderItem ON orderItem.order_item_id = oitp.order_item_id 
            JOIN `order` o ON o.order_id = orderItem.order_id 
            WHERE o.order_id = :orderId
            """)
    public List<Long> findByOrderItem_order_id(Long orderId);

    @Query(nativeQuery = true, value = """
            SELECT oitp.third_party_id FROM order_item_third_party oitp
            JOIN order_item orderItem ON orderItem.order_item_id = oitp.order_item_id
            WHERE orderItem.order_item_id = :orderItemId
            AND oitp.third_party_id = :thirdPartyId
            """)
    public Optional<Long> findByOrderItem_order_idAntThird(Long orderItemId, Long thirdPartyId);

}



