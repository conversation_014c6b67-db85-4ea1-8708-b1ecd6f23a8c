package br.com.focusts.clinicall.service.modules.register.security.repository;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.register.security.po.UserMetaPO;

@Repository
public interface UserMetaRepository extends CrudRepository<UserMetaPO, Long> {

	@Query("SELECT MAX(u) FROM UserMetaPO u WHERE u.user.id = :userId AND u.meta.key = :metaKey")
	public UserMetaPO findByUser_idAndMeta_key(Long userId, String metaKey);

	public List<UserMetaPO> findByUserId(Long userId);

}
