
package br.com.focusts.clinicall.service.modules.operational.reception.facade;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderTissDeclarationPO;

import java.util.List;

public interface OrderTissDeclarationFacade extends CrudFacade<OrderTissDeclarationPO, Long>{
	public List<OrderTissDeclarationPO> findByOrderTissId(Long orderId);

}

