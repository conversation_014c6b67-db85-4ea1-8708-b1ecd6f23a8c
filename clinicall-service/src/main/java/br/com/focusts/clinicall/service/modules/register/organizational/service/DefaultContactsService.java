
package br.com.focusts.clinicall.service.modules.register.organizational.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.ContactsPO;
import br.com.focusts.clinicall.service.modules.register.organizational.repository.ContactsRepository;


@Service
public class DefaultContactsService extends AbstractCrudService<ContactsPO,java.lang.Long> implements ContactsService {

	@Autowired
	private ContactsRepository contactsRepository;

	@Override
	public CrudRepository<ContactsPO,java.lang.Long> getCrudRepository() {
	    return contactsRepository;
	}

	@Override
	public Page<ContactsPO> findByNameContaining(PageSearchTO pageSearchTO) {

            PageRequest pageRequest =  PageRequest.of(pageSearchTO.getPage(),pageSearchTO.getSizePage(), Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());

	    return contactsRepository.findByNameContaining(pageSearchTO.getArgument(),  pageRequest);
	}
	
}
