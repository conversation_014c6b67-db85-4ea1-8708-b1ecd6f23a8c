package br.com.focusts.clinicall.service.modules.operational.reception.to;


public interface CashierOsTO {

    
    public abstract Long getOs();

    public abstract void setOs(Long os);

    public abstract String getName();

    public abstract void setName(String name);

    public abstract String getOrigem();

    public abstract void setOrigem(String origem);

    public abstract String getInsurance();

    public abstract void setInsurance(String insurance);

    public abstract Double getValue();

    public abstract void setValue(Double value);

   	

	
}
