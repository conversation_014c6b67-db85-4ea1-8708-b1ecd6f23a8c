
package br.com.focusts.clinicall.service.modules.register.operational.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import br.com.focusts.clinicall.service.modules.register.operational.to.PatientResponseTO;
import br.com.focusts.clinicall.service.modules.register.security.service.UserService;
import br.com.focusts.clinicall.service.modules.system.constants.GlobalKeyParameterConstants;
import br.com.focusts.clinicall.service.modules.system.repository.ParameterRepository;
import br.com.focusts.clinicall.service.modules.tables.general.po.PersonPO;
import br.com.focusts.clinicall.service.modules.tables.general.repository.PersonRepository;
import br.com.focusts.clinicall.util.StringUtil;
import br.com.focusts.clinicall.util.ValidateUtil;
import io.lettuce.core.ScriptOutputType;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.querydsl.core.types.dsl.BooleanExpression;

import br.com.focusts.clinicall.fw.exception.ApplicationException;
import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.to.PatientTO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.InsurancePO;
import br.com.focusts.clinicall.service.modules.register.accreditation.service.InsuranceService;
import br.com.focusts.clinicall.service.modules.register.operational.po.PatientPO;
import br.com.focusts.clinicall.service.modules.register.operational.po.QPatientPO;
import br.com.focusts.clinicall.service.modules.register.operational.to.PatientAttendanceTO;
import br.com.focusts.clinicall.service.modules.register.organizational.repository.PatientQueryDslRepository;
import br.com.focusts.clinicall.service.modules.register.organizational.repository.PatientRepository;
import br.com.focusts.clinicall.service.modules.register.organizational.service.PatientService;
import br.com.focusts.clinicall.service.modules.system.repository.PersonImageRepository;

@Service
public class DefaultPatientService extends AbstractCrudService<PatientPO, java.lang.Long> implements PatientService {

	@Autowired
	private PatientRepository patientRepository;

	@Autowired
	private PatientQueryDslRepository patientQueryDslRepository;

	@Autowired
	private PersonImageRepository personImageRepository;

	@Autowired
	private ParameterRepository parameterRepository;

	@Autowired
	private InsuranceService insuranceService;

	@Autowired
	private PersonRepository personRepository;

	@Autowired
	private UserService userService;
	@Override
	public CrudRepository<PatientPO, java.lang.Long> getCrudRepository() {
		return patientRepository;
	}


	@Override
	public Page<PatientPO> search(PageSearchTO pageSearchTO) {

		PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage(),
				Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());

		return patientRepository
				.findByPerson_cpfOrPerson_nameStartingWithOrPerson_SocialNameStartingWithOrMedicalRecordStartingWith(pageSearchTO.getArgument(), pageSearchTO.getArgument(),pageSearchTO.getArgument(),pageSearchTO.getArgument(), pageRequest);
	}

	@Override
	public Page<PatientResponseTO> searchResponse(PageSearchTO pageSearchTO) {
		return patientQueryDslRepository.searchPatientResponse(pageSearchTO);
	}

	@Override
	public Page<PatientTO> searchPatient(PageSearchTO pageSearchTO) {
		return patientQueryDslRepository.searchPatient(pageSearchTO);
	}

	public Page<PatientPO> searchActive(PageSearchTO pageSearchTO) {

		PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage(),
				Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());

		return patientRepository
				.findByPerson_cpfOrPerson_nameStartingWithOrPerson_SocialNameStartingWithOrMedicalRecordStartingWithAndActiveTrue(pageSearchTO.getArgument(), pageSearchTO.getArgument(),pageSearchTO.getArgument(),pageSearchTO.getArgument(), pageRequest);
	}

	public Page<PatientAttendanceTO> searchForAttendance(PageSearchTO pageSearchTO, String type) {

//		PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage(),
//				Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());
		PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage());

		String arg = pageSearchTO.getArgument();

		Page<PatientAttendanceTO> page = null;

		if (StringUtils.isEmpty(arg)) {
			page = patientQueryDslRepository.searchPatientForAttendanceOfTheDay(pageSearchTO);
		} else {
			page = patientQueryDslRepository.searchPatientForAttendance(pageSearchTO, type);
			/*Long number = null;
			if (NumberUtils.isDigits(arg)) {
				number = Long.parseLong(arg);
			}

			page = patientRepository.find(arg,arg, arg, arg, arg, arg, arg, pageRequest);*/

		}

		return page;

	}

	public List<PatientPO> findPatient(PatientTO patientTO) {

		BooleanExpression booleanExpression = QPatientPO.patientPO.id.isNotNull();

		if (!StringUtils.isEmpty(patientTO.getName())) {
			booleanExpression = booleanExpression.and(QPatientPO.patientPO.person.name.startsWith(patientTO.getName()));
		}

		if (!StringUtils.isEmpty(patientTO.getCpf())) {
			booleanExpression = booleanExpression.and(QPatientPO.patientPO.person.cpf.eq(patientTO.getCpf()));
		}

		if (patientTO.getBirthday() != null) {
			booleanExpression = booleanExpression.and(QPatientPO.patientPO.person.birthday.eq(patientTO.getBirthday()));
		}

		Iterable<PatientPO> patientIterable = patientRepository.findAll(booleanExpression);
		List<PatientPO> patientList = new ArrayList<>();
		patientIterable.forEach(patientList::add);

		return patientList;
	}

	@Override
	public PatientAttendanceTO findByIdForAttendance(Long patientId) {
		return patientRepository.find(patientId);
	}

	@Override
	public PatientPO findByIdSimpleId(Long id) {
		return patientQueryDslRepository.findById(id);
	}

	@Override
	public String nexValueMedicalRecord() {
		return patientRepository.findMaxMedicalRecord();
	}

	@Override
	public String findFileById(Long fileId) {
		var file =  personImageRepository.findById(fileId);
		if (file.isEmpty()){
			throw new ApplicationException("Arquivo não encontrado");
		}
		return file.get().getImage().getContent();
	}

	@Override
	public PatientPO save(PatientPO patientPO) {


		if (patientPO.getId() == null){
			var parameterValue = parameterRepository.findByValueParameterSystem(GlobalKeyParameterConstants.UPPERCASE_REGISTRATION);
			if (parameterValue.equals("1")){
				patientPO.getPerson().setName(patientPO.getPerson().getName().toUpperCase());
				if (!StringUtil.isNullOrBlankOrEmpty(patientPO.getPerson().getSocialName())){
					patientPO.getPerson().setSocialName(patientPO.getPerson().getSocialName().toUpperCase());
				}
			}
		}
		if (patientPO.getInsurance() != null) {
			InsurancePO insurance = insuranceService.findById(patientPO.getInsurance().getId());
			if (insurance.getPaymentModel().equals('S')) {
				if (patientPO.getPerson().getCns() == null || patientPO.getPerson().getCns().isBlank()) {
					throw new ApplicationException("APPLICATION_EXCEPTION", "Numero do CNS invalido ou não preenchido!", "cns");
				}

				if (patientPO.getPerson().getAddress() == null){
					throw new ApplicationException("APPLICATION_EXCEPTION","Endereço é obrigatório para o convênio SUS!","address");
				}

				if (patientPO.getPerson().getMother() == null || patientPO.getPerson().getMother().isBlank()) {
					throw new ApplicationException("APPLICATION_EXCEPTION", "Campo NOME DA MÃE obrigatório para o convênio SUS!", "mother");
				}

				if (patientPO.getPerson().getBreed() == null || patientPO.getPerson().getBreed().getName().isBlank()) {
					throw new ApplicationException("APPLICATION_EXCEPTION", "Campo RAÇA obrigatório para o convênio SUS!", "breed");
				}

				if (patientPO.getPerson().getGender() == null || patientPO.getPerson().getGender().getName().isBlank()) {
					throw new ApplicationException("APPLICATION_EXCEPTION", "Campo GENERO obrigatório para o convênio SUS!", "gender");
				}

				if (patientPO.getPerson().getBreed().getCode().equals("99")) {
					throw new ApplicationException("APPLICATION_EXCEPTION", "Campo RAÇA é obrigatório, opção 'SEM INFORMAÇÃO' não é válido para o SUS!", "breed");
				}
			}
		}

		if (patientPO.getPerson() != null && !StringUtil.isNullOrBlankOrEmpty(patientPO.getPerson().getCpf())) {
			var validCpf = ValidateUtil.isCpfValid(patientPO.getPerson().getCpf());
			if (!validCpf) {
				throw new ApplicationException("CPF informado inválido.");
			}
		}

		if (StringUtil.isNullOrBlankOrEmpty(patientPO.getMedicalRecord()) ){
			String valueStr = nexValueMedicalRecord();
			if(!StringUtils.isEmpty(valueStr)) {
				Long value = Long.parseLong(valueStr) + 1;
				patientPO.setMedicalRecord(value.toString());
			}else {
				patientPO.setMedicalRecord("1");
			}
		}

		return super.save(patientPO);
	}

	@Override
	public PatientPO AssociateDoctorAsPatient(Long personId){
		PersonPO person = personRepository.findBy_id(personId);
		PatientPO patient = new PatientPO();
		patient.setPerson(person);
		patient.setActive(true);
		patient.setDated(LocalDateTime.now());
		String valueStr = patientRepository.findMaxMedicalRecord();
		if(!StringUtils.isEmpty(valueStr)) {
			Long value = Long.parseLong(valueStr) + 1;
			patient.setMedicalRecord(value.toString());
		}else{
			patient.setMedicalRecord("1");
		}
		super.save(patient);
		return patient;
	}

	@Override
	public PatientPO findById(Long aLong) {
		var patient = super.findById(aLong);
		if (patient.getCreatedBy() != null){
			var user = userService.findById(patient.getCreatedBy());
			if (user == null){
				patient.setUserCreated("Não encontrado");
			}else{
				patient.setUserCreated(user.getName());

			}
		}
		return patient;
	}

	@Override
	public List<PatientTO> findPatientDuplicateNameBirthdayMother(PatientTO patientTo) {
		return patientQueryDslRepository.findPatientDuplicateNameBirthdayMother(patientTo);
	}

	@Override
	public Page<PatientTO> searchPatientByParameterPatientActived(PageSearchTO pageSearchTO, Long performerId) {
		return patientQueryDslRepository.searchPatientByParameterPatientActived(pageSearchTO, performerId);
	}

	@Override
	public PatientTO findByCpfAndBirthday(String cpf, LocalDate birthday) {
		return patientQueryDslRepository.findByCpfAndBirthday(cpf, birthday);
	}

	@Override
	public PatientTO findPatientTOById(Long patientId) {
		return patientQueryDslRepository.findPatientTOById(patientId);
	}


	@Override
	public Page<PatientTO> findByName(PageSearchTO pageSearchTO) {
			PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage(),
				Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());

		return patientRepository.findByName(pageSearchTO.getArgument(), pageRequest);
	}

	
}
