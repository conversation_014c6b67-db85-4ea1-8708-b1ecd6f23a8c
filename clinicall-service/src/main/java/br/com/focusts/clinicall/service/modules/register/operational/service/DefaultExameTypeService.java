
package br.com.focusts.clinicall.service.modules.register.operational.service;

import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.tiss.repository.TypeAttendanceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.service.modules.register.operational.po.ExameTypePO;
import br.com.focusts.clinicall.service.modules.register.operational.repository.ExameTypeRepository;

@Service
public class DefaultExameTypeService extends AbstractCrudService<ExameTypePO, Long>
		implements ExameTypeService {

	@Autowired
	private ExameTypeRepository exameTypeRepository;

	@Autowired
	private TypeAttendanceRepository typeAttendanceRepository;

	@Override
	public CrudRepository<ExameTypePO, Long> getCrudRepository() {
		return exameTypeRepository;
	}

	@Override
	public ExameTypePO save(ExameTypePO po) {
		if (po.getTypeAttendance() != null){
			po.setTypeAttendance(typeAttendanceRepository.findById(po.getTypeAttendance().getId()).get());
		}
		return super.save(po);
	}

	@Override
	public Page<ExameTypePO> findByCodeStartingWithOrNameStartingWith(PageSearchTO pageSearchTO) {
		PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage(),
				Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());
		String args = pageSearchTO.getArgument() != null ? pageSearchTO.getArgument() : "";
		return exameTypeRepository.findByCodeStartingWithOrNameStartingWith(args, args, pageRequest);
	}


}
