
package br.com.focusts.clinicall.service.modules.tables.financial.facade;

import br.com.focusts.clinicall.service.modules.tables.financial.to.CompanyAccountPlanTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.financial.po.CompanyAccountPlanPO;
import br.com.focusts.clinicall.service.modules.tables.financial.service.CompanyAccountPlanService;

@Component
@Transactional
public class DefaultCompanyAccountPlanFacade extends AbstractCrudFacade<CompanyAccountPlanPO, java.lang.Long>
		implements CompanyAccountPlanFacade {

	@Autowired
	private CompanyAccountPlanService companyAccountPlanService;

	@Override
	public CrudService<CompanyAccountPlanPO, java.lang.Long> getCrudService() {
		return companyAccountPlanService;
	}

	@Override
	public Page<CompanyAccountPlanPO> findByCompany_nameContainingOrAccountPlan_nameContaining(
			PageSearchTO pageSearchTO) {
		return companyAccountPlanService.findByCompany_nameContainingOrAccountPlan_nameContaining(pageSearchTO);
	}

	@Override
	public Page<CompanyAccountPlanTO> searchCompanyAccountPlan(PageSearchTO pageSearchTO) {
		return companyAccountPlanService.searchCompanyAccountPlan(pageSearchTO);
	}

	@Override
	public Page<CompanyAccountPlanTO> searchCompanyAccountPlanByCompanyName(PageSearchTO pageSearchTO) {
		return companyAccountPlanService.searchCompanyAccountPlanByCompanyName(pageSearchTO);
	}

	@Override
	public Page<CompanyAccountPlanTO> searchByCompanyNameAndAccountPlanId(PageSearchTO pageSearchTO,
			Long accountPlanId) {
		return companyAccountPlanService.searchByCompanyNameAndAccountPlanId(pageSearchTO, accountPlanId);
	}

	@Override
	public CompanyAccountPlanTO updateAccountPlan(CompanyAccountPlanTO companyAccountPlanTO) {
		return companyAccountPlanService.updateAccountPlan(companyAccountPlanTO);
	}

	@Override
	public Page<CompanyAccountPlanTO> findByCompanyAndTtype(Long companyId, String type, PageSearchTO pageSearchTO) {
		return companyAccountPlanService.findByCompanyAndTtype(companyId, type, pageSearchTO);
	}

}
