package br.com.focusts.clinicall.service.modules.tables.general.po;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import br.com.focusts.clinicall.fw.po.AbstractPO;

/**
 *
 * <AUTHOR>
 */
@Entity
@Audited
@Table(name = "state")
public class StatePO extends AbstractPO<Long> {
	private static final long serialVersionUID = 1L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "state_id")
	private Long id;
	
	@Basic(optional = false)
	@NotNull
	private Integer code;
	
	@Basic(optional = false)
	@NotBlank 
	@Size(max = 2)
	private String initials;
	
	@Basic(optional = false)
	@NotBlank 
	@Size(max = 250)
	private String name;
	
	@Size(max = 250)
	private String gentile;

	public StatePO() {
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getInitials() {
		return initials;
	}

	public void setInitials(String initials) {
		this.initials = initials;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getGentile() {
		return gentile;
	}

	public void setGentile(String gentile) {
		this.gentile = gentile;
	}

}
