package br.com.focusts.clinicall.service.modules.operational.reception.facade;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.CashierOperationPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.CashierOsPO;
import br.com.focusts.clinicall.service.modules.operational.reception.service.CashierOperationService;
import br.com.focusts.clinicall.service.modules.operational.reception.service.CashierOsService;
import br.com.focusts.clinicall.service.modules.operational.reception.to.CashierOsTO;
import br.com.focusts.clinicall.service.modules.operational.reception.to.CashierValuesTO;


@Component
@Transactional
public class DefaultCashierOsFacade extends AbstractCrudFacade<CashierOsPO, Long> implements CashierOsFacade {

    @Autowired
	private CashierOsService cashierOsService;
    
    @Autowired
	private CashierOperationService cashierOperationService;

    @Override
	public CrudService<CashierOsPO,java.lang.Long> getCrudService() {
            return cashierOsService;
	}

    @Override
	public Page<CashierOsPO> findByUserContaining(PageSearchTO pageSearchTO) {
            return cashierOsService.findByUserContaining(pageSearchTO);
	}

    @Override
	public List<CashierOsTO> listCashierOS(String date, String type, Long cashierId) {
            return cashierOsService.listCashierOS(date, type, cashierId);
	}

    @Override
	public List<CashierValuesTO> findValuesCashier(Long cashierId) {
            return cashierOsService.findValuesCashier(cashierId);
	}

    public CashierOperationPO saveCashierOperation(CashierOperationPO cashierOperationPO) {
		return cashierOperationService.save(cashierOperationPO);
	}

    public CashierOperationPO editCashierOperation(CashierOperationPO cashierOperationPO) {
		return cashierOperationService.save(cashierOperationPO);
	}

    public CashierOperationPO findCashierOperationById(Long id) {
		return cashierOperationService.findById(id);
	}

    public void deleteCashierOperation(Long id) {
		 cashierOperationService.delete(id);		
	}
    
}
