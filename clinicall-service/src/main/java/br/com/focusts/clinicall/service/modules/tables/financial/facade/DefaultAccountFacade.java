
package br.com.focusts.clinicall.service.modules.tables.financial.facade;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.financial.po.AccountPO;
import br.com.focusts.clinicall.service.modules.tables.financial.service.AccountService;
import br.com.focusts.clinicall.service.modules.tables.financial.to.AccountTO;

@Component
@Transactional
public class DefaultAccountFacade extends AbstractCrudFacade<AccountPO, java.lang.Long> implements AccountFacade {

  @Autowired
  private AccountService accountService;

  @Override
  public CrudService<AccountPO, java.lang.Long> getCrudService() {
    return accountService;
  }

  @Override
  public Page<AccountPO> findByCodeContainingOrAgency_nameContaining(PageSearchTO pageSearchTO) {
    return accountService.findByCodeContainingOrAgency_nameContaining(pageSearchTO);
  }

  public List<AccountPO> findByAgencyId(Long agencyId) {
    return accountService.findByAgencyId(agencyId);
  }

  @Override
  public Page<AccountTO> searchAccountByCompanyId(Long companyId, PageSearchTO pageSearchTO) {
    return accountService.searchAccountByCompanyId(companyId, pageSearchTO);
  }

  @Override
  public Page<AccountTO> searchAccount(PageSearchTO pageSearchTO) {
    return accountService.searchAccount(pageSearchTO);
  }

}
