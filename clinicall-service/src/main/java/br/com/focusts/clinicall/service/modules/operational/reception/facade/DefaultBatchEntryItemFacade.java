
package br.com.focusts.clinicall.service.modules.operational.reception.facade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.BatchEntryItemPO;
import br.com.focusts.clinicall.service.modules.operational.reception.service.BatchEntryItemService;

@Component
@Transactional
public class DefaultBatchEntryItemFacade extends AbstractCrudFacade <BatchEntryItemPO, java.lang.Long> implements BatchEntryItemFacade  {

	@Autowired
	private BatchEntryItemService batchEntryItemService;

	@Override
	public CrudService<BatchEntryItemPO,java.lang.Long> getCrudService() {
            return batchEntryItemService;
	}

	@Override
	public Page<BatchEntryItemPO> find(PageSearchTO pageSearchTO) {
            return batchEntryItemService.find(pageSearchTO);
	}        
      
}
