package br.com.focusts.clinicall.service.modules.partners.to.nuclearisTO;

import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateDeserializer;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateSerializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
public class NuclearisSolicitanteTO {
    private Long id;
    private Long cdPrestadorNuclearis;
    private String nome;
    private String email;
    private String ativo;
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate dataNascimento;
    private String numeroConselho;
    private String siglaConselho;
    private String estadoConselho;
    private String especialidade;
    private String telefone;
    private InstituicaoTO instituicao;
}