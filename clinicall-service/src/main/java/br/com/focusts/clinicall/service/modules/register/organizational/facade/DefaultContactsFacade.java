
package br.com.focusts.clinicall.service.modules.register.organizational.facade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.ContactsPO;
import br.com.focusts.clinicall.service.modules.register.organizational.service.ContactsService;

@Component
@Transactional
public class DefaultContactsFacade extends AbstractCrudFacade <ContactsPO, java.lang.Long> implements ContactsFacade  {

	@Autowired
	private ContactsService contactsService;

	@Override
	public CrudService<ContactsPO,java.lang.Long> getCrudService() {
            return contactsService;
	}

	@Override
	public Page<ContactsPO> findByNameContaining(PageSearchTO pageSearchTO) {
            return contactsService.findByNameContaining(pageSearchTO);
	}        
      
}
