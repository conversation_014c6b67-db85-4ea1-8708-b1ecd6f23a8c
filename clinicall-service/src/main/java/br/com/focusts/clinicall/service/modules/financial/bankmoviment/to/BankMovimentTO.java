package br.com.focusts.clinicall.service.modules.financial.bankmoviment.to;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BankMovimentTO {
    private Long id;
    private String name;
    private String code;
    private String alias;
    private String type;
    private Boolean fixed;
    private Boolean financial;
    private Boolean operational;

    public BankMovimentTO() {
    }

    public BankMovimentTO(Long id, String name, String code, String alias, String type, Boolean fixed, Boolean financial, Boolean operational) {
        this.id = id;
        this.name = name;
        this.code = code;
        this.alias = alias;
        this.type = type;
        this.fixed = fixed;
        this.financial = financial;
        this.operational = operational;
    }
}
