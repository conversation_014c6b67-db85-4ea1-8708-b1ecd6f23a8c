
package br.com.focusts.clinicall.service.modules.register.security.service;

import br.com.focusts.clinicall.service.modules.register.security.to.ProfileChatTO;
import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.security.po.ProfilePO;

import java.util.List;

public interface ProfileService extends CrudService<ProfilePO, java.lang.Long> {

    public Page<ProfilePO> find(PageSearchTO pageSearchTO);

    ProfilePO findByUserId(Long userId);

    List<ProfileChatTO> findByUserChat(List<Long> userOnlineId);

    public ProfileChatTO findByUserChatProfile(ProfilePO profile);

    public ProfileChatTO findProfileChatTOByUserId(Long userId);
}
