package br.com.focusts.clinicall.service.modules.operational.scheduling.enums;

import br.com.focusts.clinicall.fw.enums.DescriptionEnum;

public enum ScheduleStatusEnum implements DescriptionEnum {
	
	NOT_APPLICABLE {

		public String getName() {
			return "Não se Aplica";
		}

		public String getValue() {
			return "N";
		}

		public String getDescription() {
			return "Não se Aplica";
		}

	},
	
	AVAILABLE {

		public String getName() {
			return "Livre";
		}

		public String getValue() {

			return "L";
		}

		public String getDescription() {
			return "Livre";
		}

	},
	
	SCHEDULED {

		public String getName() {
			return "Agendado";
		}

		public String getValue() {
			return "A";
		}

		public String getDescription() {
			return "Agendado";
		}

	},
	
	BLOCKED {

		public String getName() {
			return "Bloqueado";
		}

		public String getValue() {

			return "B";
		}

		public String getDescription() {
			return "Bloqueado";
		}

	},
	
	FIRST_TIME{

		public String getName() {
			return "1º Vez";
		}

		public String getValue() {
			return "V";
		}

		public String getDescription() {
			return "1º Vez";
		}

	},
	
	EXTRA {

		public String getName() {
			return "Extra";
		}

		public String getValue() {
			return "X";
		}

		public String getDescription() {
			return "Extra";
		}

	},
	
	RESULT {

		public String getName() {
			return "Resultado";
		}

		public String getValue() {
			return "R";
		}

		public String getDescription() {
			return "Resultado";
		}

	},
	
	PREVENTIVE {

		public String getName() {
			return "Preventivo";
		}

		public String getValue() {
			return "P";
		}

		public String getDescription() {
			return "Preventivo";
		}

	},
	
	EXAM {

		public String getName() {
			return "Exame";
		}

		public String getValue() {
			return "E";
		}

		public String getDescription() {
			return "Exame";
		}

	},
	
	URGENT {

		public String getName() {
			return "Urgente";
		}

		public String getValue() {
			return "U";
		}

		public String getDescription() {
			return "Urgente";
		}
	},
	
	
	
}