
package br.com.focusts.clinicall.service.modules.tables.general.facade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.general.po.RegistryTypePO;
import br.com.focusts.clinicall.service.modules.tables.general.service.RegistryTypeService;

@Component
@Transactional
public class DefaultRegistryTypeFacade extends AbstractCrudFacade <RegistryTypePO, java.lang.Long> implements RegistryTypeFacade  {

	@Autowired
	private RegistryTypeService registryTypeService;

	@Override
	public CrudService<RegistryTypePO,java.lang.Long> getCrudService() {
            return registryTypeService;
	}

	@Override
	public Page<RegistryTypePO> findByNameContaining(PageSearchTO pageSearchTO) {
            return registryTypeService.findByNameContaining(pageSearchTO);
	}        
      
}
