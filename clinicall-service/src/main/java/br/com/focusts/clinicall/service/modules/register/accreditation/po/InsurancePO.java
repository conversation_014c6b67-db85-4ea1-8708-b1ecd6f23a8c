package br.com.focusts.clinicall.service.modules.register.accreditation.po;

import java.util.List;
import java.util.Set;

import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.PerformerPO;
import br.com.focusts.clinicall.service.modules.system.po.ImagePO;
import br.com.focusts.clinicall.service.modules.tables.financial.po.AccountPO;
import br.com.focusts.clinicall.service.modules.tables.general.po.AddressPO;
import br.com.focusts.clinicall.service.modules.tables.general.po.ContactListPO;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "insurance")
@Audited
public class InsurancePO extends AbstractPO<Long> {

  private static final long serialVersionUID = 1L;
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Basic(optional = false)
  @Column(name = "insurance_id")
  private Long id;

  public InsurancePO(Long id) {
    this.id = id;
  }

  public InsurancePO() {
  }

  @JoinColumn(name = "parent_id", referencedColumnName = "insurance_id")
  @ManyToOne
  private InsurancePO insuranceParent;

  @NotBlank
  private String name;

  @NotBlank
  private String alias;

  @Basic(optional = false)
  private Character type;

  private String cnpj;

  @Column(name = "ans_registry")
  private String ansRegistry;

  @Column(name = "municipal_registry")
  private String municipalRegistry;

  @Column(name = "state_registry")
  private String stateRegistry;

  @Column(name = "provider_code")
  private String providerCode;

  @Size(max = 7)
  private String cnes;

  @JoinColumn(name = "contact_list_id")
  @ManyToOne(cascade = CascadeType.ALL)
  private ContactListPO contactList;

  @OneToOne(cascade = CascadeType.ALL, mappedBy = "insurance")
  @Valid
  private InsuranceTaxPO insuranceTax;

  @OneToOne(cascade = CascadeType.ALL, mappedBy = "insurance")
  @Valid
  private InsuranceTissPO insuranceTiss;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "insurance")
  private List<AgreementPO> agreementList;

  @OneToMany(mappedBy = "insurance", fetch = FetchType.LAZY)
  private List<QuotationPO> quotationList;

  @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  @JoinTable(name = "insurance_address", joinColumns = @JoinColumn(name = "insurance_id"), inverseJoinColumns = @JoinColumn(name = "address_id"))
  private Set<AddressPO> addressList;

  @JoinColumn(name = "insurance_coparticipation_id", referencedColumnName = "insurance_id")
  @ManyToOne
  private InsurancePO insuranceCoparticipation;

  @JoinColumn(name = "performer_id", referencedColumnName = "performer_id")
  @ManyToOne
  private PerformerPO performer;

  @JoinColumn(name = "account_id", referencedColumnName = "account_id")
  @ManyToOne
  private AccountPO account;

  @Basic(optional = false)
  @Column(name = "payment_model")
  private Character paymentModel;

  @JoinColumn(name = "image_id", referencedColumnName = "image_id")
  @ManyToOne(cascade = CascadeType.PERSIST)
  @NotAudited
  private ImagePO image;

  @NotNull
  private Boolean active;

  @NotNull
  @Column(name = "days_left_to_billing")
  private Integer daysLeftToBilling;

  @NotNull
  @Column(name = "days_left_to_gloss")
  private Integer daysLeftToGloss;

  // @ManyToMany(fetch = FetchType.LAZY)
  // @JoinTable(name = "insurance_plan", joinColumns = @JoinColumn(name =
  // "insurance_id"), inverseJoinColumns = @JoinColumn(name = "plan_id"))
  // Set<PlanPO> planList;

  public InsuranceTissPO getInsuranceTiss() {
    return insuranceTiss;
  }

  public void setInsuranceTiss(InsuranceTissPO insuranceTiss) {
    this.insuranceTiss = insuranceTiss;

    if (this.insuranceTiss != null) {
      this.insuranceTiss.setInsurance(this);
    }
  }

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public InsurancePO getInsuranceParent() {
    return insuranceParent;
  }

  public void setInsuranceParent(InsurancePO insuranceParent) {
    this.insuranceParent = insuranceParent;
  }

  public ImagePO getImage() {
    return image;
  }

  public void setImage(ImagePO image) {
    this.image = image;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getAlias() {
    return alias;
  }

  public void setAlias(String alias) {
    this.alias = alias;
  }

  public Character getType() {
    return type;
  }

  public void setType(Character type) {
    this.type = type;
  }

  public String getCnpj() {
    return cnpj;
  }

  public void setCnpj(String cnpj) {
    this.cnpj = cnpj;
  }

  public String getAnsRegistry() {
    return ansRegistry;
  }

  public void setAnsRegistry(String ansRegistry) {
    this.ansRegistry = ansRegistry;
  }

  public String getMunicipalRegistry() {
    return municipalRegistry;
  }

  public void setMunicipalRegistry(String municipalRegistry) {
    this.municipalRegistry = municipalRegistry;
  }

  public String getStateRegistry() {
    return stateRegistry;
  }

  public void setStateRegistry(String stateRegistry) {
    this.stateRegistry = stateRegistry;
  }

  public String getProviderCode() {
    return providerCode;
  }

  public void setProviderCode(String providerCode) {
    this.providerCode = providerCode;
  }

  public String getCnes() {
    return cnes;
  }

  public void setCnes(String cnes) {
    this.cnes = cnes;
  }

  public ContactListPO getContactList() {
    return contactList;
  }

  public void setContactList(ContactListPO contactList) {
    this.contactList = contactList;
  }

  public InsuranceTaxPO getInsuranceTax() {
    return insuranceTax;
  }

  public void setInsuranceTax(InsuranceTaxPO insuranceTax) {
    this.insuranceTax = insuranceTax;

    if (this.insuranceTax != null) {
      this.insuranceTax.setInsurance(this);
    }
  }

  public List<AgreementPO> getAgreementList() {
    return agreementList;
  }

  public void setAgreementList(List<AgreementPO> agreementList) {
    this.agreementList = agreementList;
  }

  public List<QuotationPO> getQuotationList() {
    return quotationList;
  }

  public void setQuotationList(List<QuotationPO> quotationList) {
    this.quotationList = quotationList;
  }

  public Set<AddressPO> getAddressList() {
    return addressList;
  }

  public void setAddressList(Set<AddressPO> addressList) {
    this.addressList = addressList;
  }

  public InsurancePO getInsuranceCoparticipation() {
    return insuranceCoparticipation;
  }

  public void setInsuranceCoparticipation(InsurancePO insuranceCoparticipation) {
    this.insuranceCoparticipation = insuranceCoparticipation;
  }

  public Character getPaymentModel() {
    return paymentModel;
  }

  public void setPaymentModel(Character paymentModel) {
    this.paymentModel = paymentModel;
  }

  // public Set<PlanPO> getPlanList() {
  // return planList;
  // }
  //
  // public void setPlanList(Set<PlanPO> planList) {
  // this.planList = planList;
  // }

  public PerformerPO getPerformer() {
    return performer;
  }

  public void setPerformer(PerformerPO performer) {
    this.performer = performer;
  }

  public Boolean getActive() {
    return active;
  }

  public void setActive(Boolean active) {
    this.active = active;
  }

  public static long getSerialversionuid() {
    return serialVersionUID;
  }

  public Integer getDaysLeftToBilling() {
    return daysLeftToBilling;
  }

  public void setDaysLeftToBilling(Integer daysLeftToBilling) {
    this.daysLeftToBilling = daysLeftToBilling;
  }

  public Integer getDaysLeftToGloss() {
    return daysLeftToGloss;
  }

  public void setDaysLeftToGloss(Integer daysLeftToGloss) {
    this.daysLeftToGloss = daysLeftToGloss;
  }

  public AccountPO getAccount() {
    return account;
  }

  public void setAccount(AccountPO account) {
    this.account = account;
  }
}
