package br.com.focusts.clinicall.service.modules.operational.reception.po;

import java.time.LocalDate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.InsurancePO;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateDeserializer;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateSerializer;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.PlanPO;

@Entity
@Table(name = "order_patient_detail")
public class OrderPatientDetailPO extends AbstractPO<Long> {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "order_patient_detail_id")
	private Long id;

	@NotNull
	@JoinColumn(name = "order_id", referencedColumnName = "order_id")
	@ManyToOne
	private OrderPO order;

	@NotNull
	@JoinColumn(name = "insurance_id", referencedColumnName = "insurance_id")
	@ManyToOne
	private InsurancePO insurance;

	
	@JoinColumn(name = "plan_id", referencedColumnName = "plan_id")
	@ManyToOne
	private PlanPO plan;
	
	@Size(max = 50)
	private String enrollment;

	@Size(max = 50)
	private String holder;

	@Size(max = 20)
	private String product;

	
	@JsonSerialize(using = LocalDateSerializer.class)
	@JsonDeserialize(using = LocalDateDeserializer.class)
	private LocalDate validity;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public OrderPO getOrder() {
		return order;
	}

	public void setOrder(OrderPO order) {
		this.order = order;
	}

	public InsurancePO getInsurance() {
		return insurance;
	}

	public void setInsurance(InsurancePO insurance) {
		this.insurance = insurance;
	}

	public PlanPO getPlan() {
		return plan;
	}

	public void setPlan(PlanPO plan) {
		this.plan = plan;
	}

	public String getHolder() {
		return holder;
	}

	public void setHolder(String holder) {
		this.holder = holder;
	}

	public String getProduct() {
		return product;
	}

	public void setProduct(String product) {
		this.product = product;
	}

	public LocalDate getValidity() {
		return validity;
	}

	public void setValidity(LocalDate validity) {
		this.validity = validity;
	}

	public String getEnrollment() {
		return enrollment;
	}

	public void setEnrollment(String enrollment) {
		this.enrollment = enrollment;
	}

	
}
