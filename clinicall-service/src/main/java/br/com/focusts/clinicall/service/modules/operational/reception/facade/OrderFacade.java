package br.com.focusts.clinicall.service.modules.operational.reception.facade;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

import br.com.focusts.clinicall.service.modules.operational.reception.to.*;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.SpecialCoveragePO;
import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.authoriertiss.tiss.to.ResponseMessageTO;
import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.billing.to.FilterTO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.ExclusionOrderItemPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderItemPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPO;

public interface OrderFacade extends CrudFacade<OrderPO, Long> {

	public OrderItemPO saveOrderItem(OrderItemPO orderItemPO);

	public List<OrderItemPO> findAllOrderItem();

	public Page<OrderPO> search(PageSearchTO pageSearchTO);

	public Page<OrderItemPO> searchOrderItem(PageSearchTO pageSearchTO);

	public OrderItemPO findMainOrderItem(Long orderId);

	public Boolean checkProduct(Long orderId);

	public Boolean checkFee(Long orderId);

	public void deleteOrderItem(ExclusionOrderItemPO exclusionOrderItemPO);

	public void delete(ExclusionOrderItemPO exclusionOrderItemPO);

	public OrderTO newOrder(Long patientId);

	public OrderItemPO findOrderItem(Long orderItemId);

	public void reProcessValues(Long orderid);

	public OrderPO updateOrder(OrderPO orderPO);

	public void unlockOrder(Long orderId);

	void lockOrder(Long orderId, String context);

	InfoOrderBillingTO getInfoOrder(Long orderId);

	OrderHistoricTO searchOrdersByFilter(Long patientId, PageSearchTO pageSearchTO);

	void lockAllOrder(FilterTO filterTO);

	void lockAllOrderById(List<Long> orderIdList, String type);

	void closeAllOrderById(List<Long> orderIdList);

	void cancelOrderById(Long orderId);

	void unCancelOrderById(Long orderId);

	void openOrderById(Long orderId);

	void closeOrderById(Long orderId);

	void generateNewNumberOrder(Long id);

	OrderPO duplicateOrder(Long orderId, LocalDate date, LocalTime hour);

	OrderPO findByOrderId(Long id);

	Object recalculation(OrderRecalculationTO orderRecalculation);

	public void reGenereteSequenceOrderItem(Long idOrder);

	Page<AutorizationControlTO> findByOrderAuthorizerStatus(FilterTO filterTO);

	List<ResponseMessageTO> sendEmailOrDownload(FilterTO filterTO, String type);

	void checkLack(QuotaLackTO lack);

	void updateEnrollmentPatient(String enrollment, Long patientId, Long orderId);

	List<OrderHistoricPatientTO> findByHistoricOrder(Long ordeId);

	List<OrderItemDetachTO> findMemoryCalc(Long orderId);

    public void generateNewNumberOrder(Long orderId, int i);

    void duplicateOrderItem(Long orderitemId, LocalDate date, LocalTime hour);

	List<SpecialCoveragePO> getSpecialCoverage();

	void cancelOrderItemById(Long orderId);

	List<OrderHistoricPatientTO> findOrderItemByOrderId(Long orderId);

	void undoCancelOrderItem(Long orderItemId);
	public Page<OrderPayPendingTO> findOrderPayPending(ParametersOrderPayPendingTO parametersOrderPayPendingTO);
	public int checkOrderPayPending(ParametersOrderPayPendingTO parametersOrderPayPendingTO);
}
