
package br.com.focusts.clinicall.service.modules.operational.billing.facade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.PayEartefactPO;
import br.com.focusts.clinicall.service.modules.operational.billing.service.PayEartefactService;

@Component
@Transactional
public class DefaultPayEartefactFacade extends AbstractCrudFacade <PayEartefactPO, java.lang.Long> implements PayEartefactFacade  {

	@Autowired
	private PayEartefactService payEartefactService;

	@Override
	public CrudService<PayEartefactPO,java.lang.Long> getCrudService() {
            return payEartefactService;
	}


      
}
