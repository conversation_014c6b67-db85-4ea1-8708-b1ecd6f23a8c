
package br.com.focusts.clinicall.service.modules.operational.general.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.general.po.DefPO;
import br.com.focusts.clinicall.service.modules.operational.general.repository.DefRepository;


@Service
public class DefaultDefService extends AbstractCrudService<DefPO,java.lang.Long> implements DefService {

	@Autowired
	private DefRepository defRepository;

	@Override
	public CrudRepository<DefPO,java.lang.Long> getCrudRepository() {
	    return defRepository;
	}

	@Override
	public Page<DefPO> findByNameContaining(PageSearchTO pageSearchTO) {

            PageRequest pageRequest =  PageRequest.of(pageSearchTO.getPage(),pageSearchTO.getSizePage(), Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());

	    return defRepository.findByNameContaining(pageSearchTO.getArgument(),  pageRequest);
	}
	
}
