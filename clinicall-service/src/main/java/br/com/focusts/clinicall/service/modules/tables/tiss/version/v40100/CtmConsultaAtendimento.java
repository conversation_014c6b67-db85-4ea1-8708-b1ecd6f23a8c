//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2023.05.22 at 03:10:01 PM BRT 
//


package br.com.focusts.clinicall.service.modules.tables.tiss.version.v40100;

import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for ctm_consultaAtendimento complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ctm_consultaAtendimento">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="coberturaEspecial" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_cobEsp" minOccurs="0"/>
 *         &lt;element name="regimeAtendimento" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_regimeAtendimento"/>
 *         &lt;element name="saudeOcupacional" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_saudeOcupacional" minOccurs="0"/>
 *         &lt;element name="dataAtendimento" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_data"/>
 *         &lt;element name="tipoConsulta" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_tipoConsulta"/>
 *         &lt;element name="procedimento">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="codigoTabela" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_tabela"/>
 *                   &lt;element name="codigoProcedimento" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_texto10"/>
 *                   &lt;element name="valorProcedimento" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_decimal8-2"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ctm_consultaAtendimento", propOrder = {
    "coberturaEspecial",
    "regimeAtendimento",
    "saudeOcupacional",
    "dataAtendimento",
    "tipoConsulta",
    "procedimento"
})
public class CtmConsultaAtendimento {

    protected String coberturaEspecial;
    @XmlElement(required = true)
    protected String regimeAtendimento;
    protected String saudeOcupacional;
    @XmlElement(required = true)
    protected XMLGregorianCalendar dataAtendimento;
    @XmlElement(required = true)
    protected String tipoConsulta;
    @XmlElement(required = true)
    protected CtmConsultaAtendimento.Procedimento procedimento;

    /**
     * Gets the value of the coberturaEspecial property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCoberturaEspecial() {
        return coberturaEspecial;
    }

    /**
     * Sets the value of the coberturaEspecial property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCoberturaEspecial(String value) {
        this.coberturaEspecial = value;
    }

    /**
     * Gets the value of the regimeAtendimento property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRegimeAtendimento() {
        return regimeAtendimento;
    }

    /**
     * Sets the value of the regimeAtendimento property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRegimeAtendimento(String value) {
        this.regimeAtendimento = value;
    }

    /**
     * Gets the value of the saudeOcupacional property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSaudeOcupacional() {
        return saudeOcupacional;
    }

    /**
     * Sets the value of the saudeOcupacional property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSaudeOcupacional(String value) {
        this.saudeOcupacional = value;
    }

    /**
     * Gets the value of the dataAtendimento property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataAtendimento() {
        return dataAtendimento;
    }

    /**
     * Sets the value of the dataAtendimento property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataAtendimento(XMLGregorianCalendar value) {
        this.dataAtendimento = value;
    }

    /**
     * Gets the value of the tipoConsulta property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoConsulta() {
        return tipoConsulta;
    }

    /**
     * Sets the value of the tipoConsulta property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoConsulta(String value) {
        this.tipoConsulta = value;
    }

    /**
     * Gets the value of the procedimento property.
     * 
     * @return
     *     possible object is
     *     {@link CtmConsultaAtendimento.Procedimento }
     *     
     */
    public CtmConsultaAtendimento.Procedimento getProcedimento() {
        return procedimento;
    }

    /**
     * Sets the value of the procedimento property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtmConsultaAtendimento.Procedimento }
     *     
     */
    public void setProcedimento(CtmConsultaAtendimento.Procedimento value) {
        this.procedimento = value;
    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="codigoTabela" type="{http://www.ans.gov.br/padroes/tiss/schemas}dm_tabela"/>
     *         &lt;element name="codigoProcedimento" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_texto10"/>
     *         &lt;element name="valorProcedimento" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_decimal8-2"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "codigoTabela",
        "codigoProcedimento",
        "valorProcedimento"
    })
    public static class Procedimento {

        @XmlElement(required = true)
        protected String codigoTabela;
        @XmlElement(required = true)
        protected String codigoProcedimento;
        @XmlElement(required = true)
        protected BigDecimal valorProcedimento;

        /**
         * Gets the value of the codigoTabela property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCodigoTabela() {
            return codigoTabela;
        }

        /**
         * Sets the value of the codigoTabela property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCodigoTabela(String value) {
            this.codigoTabela = value;
        }

        /**
         * Gets the value of the codigoProcedimento property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCodigoProcedimento() {
            return codigoProcedimento;
        }

        /**
         * Sets the value of the codigoProcedimento property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCodigoProcedimento(String value) {
            this.codigoProcedimento = value;
        }

        /**
         * Gets the value of the valorProcedimento property.
         * 
         * @return
         *     possible object is
         *     {@link BigDecimal }
         *     
         */
        public BigDecimal getValorProcedimento() {
            return valorProcedimento;
        }

        /**
         * Sets the value of the valorProcedimento property.
         * 
         * @param value
         *     allowed object is
         *     {@link BigDecimal }
         *     
         */
        public void setValorProcedimento(BigDecimal value) {
            this.valorProcedimento = value;
        }

    }

}
