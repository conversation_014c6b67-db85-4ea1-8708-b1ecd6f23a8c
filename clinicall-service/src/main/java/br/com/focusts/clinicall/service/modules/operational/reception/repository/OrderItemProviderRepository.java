package br.com.focusts.clinicall.service.modules.operational.reception.repository;

import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderItemProviderPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPendingPO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface OrderItemProviderRepository extends CrudRepository<OrderItemProviderPO,Long> {

    @Query(nativeQuery = true,
    value = "SELECT CONCAT(\n" +
            "\tDATE_FORMAT(CURRENT_TIMESTAMP(), \"%Y%m%d%H%i%S\"),\n" +
            "\tLPAD(COALESCE(MAX(CAST(RIGHT(provider_number, 6)AS SIGNED)) + 1, '0'), 6, '0')) AS provider_number\n" +
            "\tFROM order_item_provider \n" +
            "\tWHERE provider_number LIKE CONCAT(DATE_FORMAT(CURRENT_TIMESTAMP(),\"%Y%m%d\"), '%'); "
    )
    public String findmaxProvider();
}
