package br.com.focusts.clinicall.service.modules.financial.bond.to;

import java.time.LocalDate;

import javax.persistence.Column;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;

import br.com.focusts.clinicall.fw.to.UserTO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.to.PatientTO;
import br.com.focusts.clinicall.service.modules.register.accreditation.to.InsuranceTO;
import br.com.focusts.clinicall.service.modules.register.financial.to.SupplierTO;
import br.com.focusts.clinicall.service.modules.register.organizational.to.CompanyCostCenterTO;
import br.com.focusts.clinicall.service.modules.register.organizational.to.CompanyTO;
import br.com.focusts.clinicall.service.modules.register.organizational.to.PerformerTO;
import br.com.focusts.clinicall.service.modules.register.organizational.to.ProfessionalTO;
import br.com.focusts.clinicall.service.modules.tables.financial.po.DocTypePO;
import br.com.focusts.clinicall.service.modules.tables.financial.to.CompanyAccountPlanTO;
import br.com.focusts.clinicall.service.modules.tables.financial.to.HistoricTO;
import br.com.focusts.clinicall.service.modules.tables.general.to.FirmTO;
import br.com.focusts.clinicall.service.modules.tables.general.to.PersonTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BondTO {

    private Long id;
    private String bondType;
    private String note;
    private String document;
    private Short parcelNumber;
    private Double value;
    private Double totalPay;
    private CompanyTO company;
    private CompanyTO companyTarget;
    private FirmTO firm;
    private SupplierTO supplier;
    private PatientTO patient;
    private InsuranceTO insurance;
    private ProfessionalTO professional;
     private PerformerTO performer;
    private PersonTO person;
    private UserTO user;
    private HistoricTO historic;
    private CompanyCostCenterTO companyCostCenter;
    private String typeDebtorOrCreditor;
    private BondTO bondParent;
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @Column(name = "origin_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate discountAt;
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @Column(name = "due_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate dueDate;
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @Column(name = "origin_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate originDate;
    private BondDetailTO bondDetail;
    private BondPayTO bondPay;
    private DocTypePO docType;
    private CompanyAccountPlanTO companyAccountPlan;
    private Long sendingToFinanceItemId;
    private String userCreated;
    private String originBond;
}
