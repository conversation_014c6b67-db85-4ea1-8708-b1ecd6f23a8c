package br.com.focusts.clinicall.service.modules.application.application.repository;

import br.com.focusts.clinicall.service.modules.register.operational.po.PatientPO;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FromToRepository extends CrudRepository<PatientPO, Long> {

        @Query(nativeQuery = true, value = """
                        SELECT
                        	TABLE_NAME,
                        	COLUMN_NAME
                        FROM
                        	INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                        WHERE
                        	CONSTRAINT_SCHEMA = DATABASE()
                        	AND REFERENCED_TABLE_NAME = :patient
                        """)

        List<Map<String, Object>> findReferencedTable(String patient);

        @Modifying
        @Query("""
                        DELETE
                        	FROM
                        	PatientPO patient
                        WHERE
                        	 patient.id IN (:patientList)
                        	AND NOT EXISTS (SELECT 1 FROM ProfessionalPO professional WHERE professional.person.id = patient.person.id)
                        """)
        void deletePatientNoRelationships(List<Long> patientList);

        @Modifying
        @Query("""
                        UPDATE
                        	PatientPO patient
                        	SET patient.active = false
                        WHERE
                        	 patient.id IN (:patientList)
                        	AND NOT EXISTS (SELECT 1 FROM ProfessionalPO professional WHERE professional.person.id = patient.person.id)
                        """)
        void updatePatientRelationships(List<Long> patientList);

        @Modifying
        @Query("""
                        UPDATE
                        	PerformerPO performer
                        	SET performer.active = false
                        WHERE
                        	 performer.id IN (:performerList)
                        """)
        void updatePerformerRelationships(List<Long> performerList);

        @Modifying
        @Query("""
                        DELETE
                        	FROM
                        	PerformerPO performer
                        WHERE
                        	 performer.id IN (:performerList)
                        """)
        void deletePerformerNoRelationships(List<Long> performerList);

        @Modifying
        @Query("""
                            DELETE FROM ProfessionalSpecialityPO ps
                            WHERE ps.professional.id IN (
                                SELECT performer.professional.id
                                FROM PerformerPO performer
                                WHERE performer.id IN (:performerList)
                            )
                        """)
        void deleteProfessionalSpecialityByPerformer(List<Long> performerList);

        @Modifying
        @Query("""
                        DELETE FROM ProfessionalSpecialityPO ps
                        WHERE ps.professional.id IN (:professionalList)
                                    """)

        void deleteProfessionalSpecialityByProfessional(List<Long> professionalList);

        @Modifying
        @Query("""
                        UPDATE
                        	ProfessionalPO professional
                        	SET professional.active = false
                        WHERE
                        	 professional.id IN (:professionalList)
                        """)
        void updateProfessionalRelationships(List<Long> professionalList);

        @Modifying
        @Query(nativeQuery = true, value = """
                        DELETE
                        	FROM
                        	professional
                        WHERE
                        	 professional.professional_id IN (:professionalList)
                        """)
        void deleteProfessionalNoRelationships(List<Long> professionalList);
}
