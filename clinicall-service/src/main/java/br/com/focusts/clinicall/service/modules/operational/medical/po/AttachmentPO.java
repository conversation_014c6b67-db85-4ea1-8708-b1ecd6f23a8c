package br.com.focusts.clinicall.service.modules.operational.medical.po;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.EartefactPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.InsurancePO;
import br.com.focusts.clinicall.service.modules.register.operational.po.PatientPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.PerformerPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.ProfessionalPO;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateDeserializer;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateSerializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import javax.persistence.*;
import javax.print.Doc;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@Entity
@Table(name = "attachment")
public class AttachmentPO extends AbstractPO<Long> {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "attachment_id")
    private Long id;

    @NotNull
    @Column(name = "name")
    private String name;

    @NotNull
    @JoinColumn(name = "insurance_id", referencedColumnName = "insurance_id")
    @ManyToOne
    private InsurancePO insurance;

    @NotNull
    @JoinColumn(name = "patient_id", referencedColumnName = "patient_id")
    @ManyToOne
    private PatientPO patient;

    @NotNull
    @JoinColumn(name = "performer_id", referencedColumnName = "performer_id")
    @ManyToOne
    private PerformerPO performer;

    @NotNull
    @JoinColumn(name = "eartefact_id", referencedColumnName = "eartefact_id")
    @OneToOne(cascade = { CascadeType.ALL, CascadeType.DETACH, CascadeType.PERSIST })
    private EartefactPO eartefact;

    @JoinColumn(name = "document_id", referencedColumnName = "document_id")
    @ManyToOne
    private DocumentPO document;

    @NotNull
    @Column(name = "dated")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate dated;

    @Column(name = "type")
    private Integer type;

    @Column(name = "checked")
    private Boolean checked;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public InsurancePO getInsurance() {
        return insurance;
    }

    public void setInsurance(InsurancePO insurance) {
        this.insurance = insurance;
    }

    public PatientPO getPatient() {
        return patient;
    }

    public void setPatient(PatientPO patient) {
        this.patient = patient;
    }

    public PerformerPO getPerformer() {
        return performer;
    }

    public void setPerformer(PerformerPO performer) {
        this.performer = performer;
    }

    public EartefactPO getEartefact() {
        return eartefact;
    }

    public void setEartefact(EartefactPO eartefact) {
        this.eartefact = eartefact;
    }

    public LocalDate getDated() {
        return dated;
    }

    public void setDated(LocalDate dated) {
        this.dated = dated;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public DocumentPO getDocument() {
        return document;
    }

    public void setDocument(DocumentPO document) {
        this.document = document;
    }

    public Boolean getChecked() {
        return checked;
    }

    public void setChecked(Boolean checked) {
        this.checked = checked;
    }
}
