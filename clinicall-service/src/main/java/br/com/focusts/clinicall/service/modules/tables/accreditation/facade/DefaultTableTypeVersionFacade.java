
package br.com.focusts.clinicall.service.modules.tables.accreditation.facade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.TableTypeVersionPO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.service.TableTypeVersionService;

@Component
@Transactional
public class DefaultTableTypeVersionFacade extends AbstractCrudFacade <TableTypeVersionPO, java.lang.Long> implements TableTypeVersionFacade  {

	@Autowired
	private TableTypeVersionService tableTypeVersionService;

	@Override
	public CrudService<TableTypeVersionPO,java.lang.Long> getCrudService() {
            return tableTypeVersionService;
	}

	@Override
	public Page<TableTypeVersionPO> findByVersionContainingOrTableType_nameContaining(PageSearchTO pageSearchTO) {
            return tableTypeVersionService.findByVersionContainingOrTableType_nameContaining(pageSearchTO);
	}
        
   
	
}
