package br.com.focusts.clinicall.service.modules.operational.reception.po;

import java.time.LocalDate;
import java.time.LocalTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateDeserializer;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateSerializer;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalTimeDeserializer;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalTimeSerializer;
import br.com.focusts.clinicall.service.modules.tables.tiss.po.ClosingReasonPO;

@Entity
@Table(name = "order_closure")
public class OrderClosurePO extends AbstractPO<Long> {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "order_closure_id")
	private Long id;

	@JoinColumn(name = "order_id", referencedColumnName = "order_id")
	@ManyToOne
	@NotNull
	private OrderPO order;
	
	@JoinColumn(name = "closing_reason_id", referencedColumnName = "closing_reason_id")
	@ManyToOne
	@NotNull
	private ClosingReasonPO closingReason;

	@NotBlank
	@Size(max = 255)
	private String description;

	@NotNull
	@JsonSerialize(using = LocalDateSerializer.class)
	@JsonDeserialize(using = LocalDateDeserializer.class)
	private LocalDate date;

	@NotNull
	@JsonSerialize(using = LocalTimeSerializer.class)
	@JsonDeserialize(using = LocalTimeDeserializer.class)
	private LocalTime hour;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public OrderPO getOrder() {
		return order;
	}

	public void setOrder(OrderPO order) {
		this.order = order;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public LocalDate getDate() {
		return date;
	}

	public void setDate(LocalDate date) {
		this.date = date;
	}

	public LocalTime getHour() {
		return hour;
	}

	public void setHour(LocalTime hour) {
		this.hour = hour;
	}

	public ClosingReasonPO getClosingReason() {
		return closingReason;
	}

	public void setClosingReason(ClosingReasonPO closingReason) {
		this.closingReason = closingReason;
	}


}
