package br.com.focusts.clinicall.service.modules.operational.scheduling.repository;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Repository;

import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;

import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QSpecialityPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.to.SpecialityTO;

@Repository
public class DefaultSpecialityDslRepository implements SpecialityQueryDslRepository {
    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<SpecialityTO> findSpecialistyList() {
        QSpecialityPO specialityPO = QSpecialityPO.specialityPO;

        JPAQuery<SpecialityTO> query = new JPAQuery<>(entityManager);
        query.select(Projections.bean(SpecialityTO.class,
                specialityPO.id.as("id"),
                specialityPO.name.as("name")))
                .from(specialityPO);

        var result = query.fetch();

        return result;
    }

 
    @Override
    public Page<SpecialityTO> searchSpecialiy(PageSearchTO pageSearchTO) {

        PageRequest pageRequest = null;
        String arguments = "";
        pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage());
        arguments = pageSearchTO.getArgument();

        QSpecialityPO specialityPO = QSpecialityPO.specialityPO;

        JPAQuery<SpecialityTO> query = new JPAQuery<>(entityManager);

        query.from(specialityPO);

        if (!arguments.isBlank()){
            query.where(specialityPO.name.startsWithIgnoreCase(arguments));
        }

        Long totalItens = query.select(specialityPO.id.count()).fetchOne();

        query.select(Projections.bean(SpecialityTO.class,
                specialityPO.id.as("id"),
                specialityPO.name.as("name")));

        query.limit(pageRequest.getPageSize()).offset(pageRequest.getOffset());
        var result = query.fetch();
        return new PageImpl<SpecialityTO>(result, pageRequest, totalItens);
    }
}
