package br.com.focusts.clinicall.service.modules.tables.financial.po;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.CompanyCostCenterPO;

import javax.persistence.*;

@Entity
@Table(name = "apportionment")
public class ApportionmentPO extends AbstractPO<Long> {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "apportionment_id")
    private Long id;

    private String name;
    private Double percent;

    @JoinColumn(name = "company_cost_center_id", referencedColumnName = "company_cost_center_id")
    @OneToOne(cascade = CascadeType.ALL)
    private CompanyCostCenterPO companyCostCenter;

    @JoinColumn(name = "company_account_plan_id", referencedColumnName = "company_account_plan_id")
    @OneToOne(cascade = CascadeType.ALL)
    private CompanyAccountPlanPO companyAccountPlan;

    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getPercent() {
        return percent;
    }

    public void setPercent(Double percent) {
        this.percent = percent;
    }

    public CompanyCostCenterPO getCompanyCostCenter() {
        return companyCostCenter;
    }

    public void setCompanyCostCenter(CompanyCostCenterPO companyCostCenter) {
        this.companyCostCenter = companyCostCenter;
    }

    public CompanyAccountPlanPO getCompanyAccountPlan() {
        return companyAccountPlan;
    }

    public void setCompanyAccountPlan(CompanyAccountPlanPO companyAccountPlan) {
        this.companyAccountPlan = companyAccountPlan;
    }
}
