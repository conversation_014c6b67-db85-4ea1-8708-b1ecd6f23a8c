
package br.com.focusts.clinicall.service.modules.register.operational.facade;

import java.util.List;

import br.com.focusts.clinicall.service.modules.operational.scheduling.to.ScheduleFilterTO;
import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.po.SchedulePO;
import br.com.focusts.clinicall.service.modules.register.operational.po.OperationPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.HoraryPO;

public interface OperationFacade extends CrudFacade<OperationPO,java.lang.Long>{

    public Page<OperationPO> findByCompany_nameContaining(PageSearchTO pageSearchTO);
    
	public List<SchedulePO> openExtraHours(HoraryPO horaryPO);

    List<SchedulePO> findBySchedule(ScheduleFilterTO scheduleFilterTO);

    void deleteScheduleFree(List<SchedulePO>  schedule);
}

