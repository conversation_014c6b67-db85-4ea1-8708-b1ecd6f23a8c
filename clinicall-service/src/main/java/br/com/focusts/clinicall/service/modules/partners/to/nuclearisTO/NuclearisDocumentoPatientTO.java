package br.com.focusts.clinicall.service.modules.partners.to.nuclearisTO;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
public class NuclearisDocumentoPatientTO {
    private Long idDocumento;
    private Long idPaciente;
    private String cdNuclearis;
    private String prontuario;
    private Long idProfissional;

    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate data;

    private String titulo;
    private String tipo;
    private String categoria;
    private String origem;
    private String nomeProfissional;
    private String especialidade;


}