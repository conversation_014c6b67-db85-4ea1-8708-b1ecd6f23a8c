package br.com.focusts.clinicall.service.modules.tables.financial.po;

import java.util.List;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.CompanyPO;

@Entity
@Table(name = "account_plan")
public class AccountPlanPO extends AbstractPO<Long> {


	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "account_plan_id")
	private Long id;
	
	@NotBlank 
	@Size(max = 25)
	private String code;	
	
	@NotBlank 
	@Size(max = 250)
	private String name;	
	
	@NotBlank 
	@Size(max = 250)
	private String alias;	
	
	@NotBlank 
	@Size(max = 1)
	private String type;

	private Boolean fixed;
	private Boolean financial;
	private Boolean operational;

	@JoinColumn(name = "parent_id", referencedColumnName = "account_plan_id")
    @ManyToOne
	private AccountPlanPO parent;

	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "company_account_plan", joinColumns = @JoinColumn(name = "account_plan_id"), inverseJoinColumns = @JoinColumn(name = "company_id"))
	private List<CompanyPO> companyList;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public AccountPlanPO getParent() {
		return parent;
	}

	public void setParent(AccountPlanPO parent) {
		this.parent = parent;
	}

	public List<CompanyPO> getCompanyList() {
		return companyList;
	}

	public void setCompanyList(List<CompanyPO> companyList) {
		this.companyList = companyList;
	}

	public Boolean getFixed() {
		return fixed;
	}

	public void setFixed(Boolean fixed) {
		this.fixed = fixed;
	}

	public Boolean getFinancial() {
		return financial;
	}

	public void setFinancial(Boolean financial) {
		this.financial = financial;
	}

	public Boolean getOperational() {
		return operational;
	}

	public void setOperational(Boolean operational) {
		this.operational = operational;
	}
}
