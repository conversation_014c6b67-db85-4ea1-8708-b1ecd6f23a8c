
package br.com.focusts.clinicall.service.modules.register.operational.service;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.operational.po.EventPO;

import java.time.LocalDate;
import java.util.List;

public interface EventService extends CrudService<EventPO,java.lang.Long>{

    public Page<EventPO> findAll(PageSearchTO pageSearchTO);

    public List<EventPO> findByDateAndPerformer_id(LocalDate date, Long performerId, Long companyId);

}
