
package br.com.focusts.clinicall.service.modules.register.accreditation.service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import br.com.focusts.clinicall.fw.enums.DescriptionEnumUtils;
import br.com.focusts.clinicall.fw.exception.ApplicationException;
import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.accreditation.constants.AccreditationKeyMessagesConstants;
import br.com.focusts.clinicall.service.modules.register.accreditation.enums.QuotationTypeEnum;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QuotationPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.repository.QuotationRepository;
import br.com.focusts.clinicall.service.modules.register.accreditation.to.QuotationListTO;

@Service
public class DefaultQuotationService extends AbstractCrudService<QuotationPO, java.lang.Long>
		implements QuotationService {

	@Autowired
	private QuotationRepository quotationRepository;

	@Override
	public CrudRepository<QuotationPO, java.lang.Long> getCrudRepository() {
		return quotationRepository;
	}

	@Override
	public Page<QuotationListTO> findByValueContainingOrCurrency_nameContainingOrGroup_nameContainingOrInsurance_nameContainingOrProcedure_nameContaining(
			PageSearchTO pageSearchTO) {

		PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage());
		return quotationRepository
				.findQuotationByStartat(pageRequest);

	}

	@Override
	public Page<QuotationPO> findActivesByProcedureId(Long idRelationship, PageSearchTO pageSearchTO) {

		PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage(),
				Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());

		return quotationRepository.findActivesByProcedureId(idRelationship, LocalDate.now(), pageRequest);
	}

	@Override
	public Page<QuotationPO> findActivesByGroupId(Long idRelationship, PageSearchTO pageSearchTO) {
		PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage(),
				Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());

		return quotationRepository.findActivesByGroupId(idRelationship, LocalDate.now(), pageRequest);
	}

	@Override
	public Page<QuotationPO> findActivesByInsuranceId(Long idRelationship, PageSearchTO pageSearchTO) {
		PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage(),
				Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());

		return quotationRepository.findActivesByInsuranceId(idRelationship, LocalDate.now(), pageRequest);
	}

	@Override
	public List<QuotationPO> save(List<QuotationPO> pos) {
		for (QuotationPO qt : pos) {
			if (qt.getId() != null) {
				break;
			}
			List<QuotationPO> quotations = null;
			if (qt.getInsurance() != null) {
				quotations = quotationRepository.findByInsuranceIdAndCurrencyId(qt.getInsurance().getId(),
						qt.getCurrency().getId());
			} else {
				quotations = quotationRepository.findByCurrencyIdAndInsuranceNull(qt.getCurrency().getId());
			}
			if (quotations.size() > 0) {
				validate(qt, quotations);
			}
		}

		return super.save(pos);
	}

	private void validate(QuotationPO quotation, List<QuotationPO> quotations) {

		for (QuotationPO qt : quotations) {

			if (qt.getId() == null && quotation.getId() == null) {
				continue;
			}
			if (qt.getId() != null && quotation.getId() != null && qt.getId().equals(quotation.getId())) {
				continue;
			}

			if (qt.getType() != null && quotation.getType() != null) {

				if (DescriptionEnumUtils.fromValue(QuotationTypeEnum.class, qt.getType()) == QuotationTypeEnum.PROCEDURE
						&& DescriptionEnumUtils.fromValue(QuotationTypeEnum.class,
								quotation.getType()) == QuotationTypeEnum.PROCEDURE) {

					if (qt.getProcedure().equals(quotation.getProcedure())
							&& (quotation.getEndat() == null && qt.getEndat() == null)) {

						HashMap<String, String> params = new HashMap<>();
						params.put("{0}", quotation.getProcedure().getName());

						throw new ApplicationException(
								AccreditationKeyMessagesConstants.QUOTATION_PROCEDURE_CONFLICTS_DATE, params);
					}

					if (quotation.getStartat() != null && qt.getEndat() != null
							&& (quotation.getStartat().isAfter(qt.getStartat())
									|| quotation.getStartat().isEqual(qt.getStartat()))
							&& (quotation.getStartat().isBefore(qt.getEndat())
									|| quotation.getStartat().isEqual(qt.getEndat()))) {

						HashMap<String, String> params = new HashMap<>();
						params.put("{0}", quotation.getProcedure().getName());


						throw new ApplicationException(
								AccreditationKeyMessagesConstants.QUOTATION_PROCEDURE_CONFLICTS_DATE, params);
					}

				}

				if (DescriptionEnumUtils.fromValue(QuotationTypeEnum.class, qt.getType()) == QuotationTypeEnum.GROUP
						&& DescriptionEnumUtils.fromValue(QuotationTypeEnum.class,
								quotation.getType()) == QuotationTypeEnum.GROUP) {

					if (qt.getGroup().equals(quotation.getGroup())
							&& (quotation.getEndat() == null && qt.getEndat() == null)) {

						HashMap<String, String> params = new HashMap<>();
						params.put("{0}", quotation.getGroup().getName());

						throw new ApplicationException(AccreditationKeyMessagesConstants.QUOTATION_GROUP_CONFLICTS_DATE,
								params);
					}

					if (quotation.getStartat() != null && qt.getEndat() != null
							&& (quotation.getStartat().isAfter(qt.getStartat())
									|| quotation.getStartat().isEqual(qt.getStartat()))
							&& (quotation.getStartat().isBefore(qt.getEndat())
									|| quotation.getStartat().isEqual(qt.getEndat()))) {

						HashMap<String, String> params = new HashMap<>();
						params.put("{0}", quotation.getGroup().getName());

						throw new ApplicationException(AccreditationKeyMessagesConstants.QUOTATION_GROUP_CONFLICTS_DATE,
								params);
					}

				}

				if (DescriptionEnumUtils.fromValue(QuotationTypeEnum.class, qt.getType()) == QuotationTypeEnum.INSURANCE
						&& DescriptionEnumUtils.fromValue(QuotationTypeEnum.class,
								quotation.getType()) == QuotationTypeEnum.INSURANCE) {

					if (qt.getInsurance().equals(quotation.getInsurance())
							&& (quotation.getEndat() == null && qt.getEndat() == null)) {

						HashMap<String, String> params = new HashMap<>();
						params.put("{0}", quotation.getInsurance().getName());

						throw new ApplicationException(
								AccreditationKeyMessagesConstants.QUOTATION_INSURANCE_CONFLICTS_DATE, params);
					}

					if (qt.getInsurance().equals(quotation.getInsurance()) && quotation.getStartat() != null
							&& qt.getEndat() != null
							&& (quotation.getStartat().isAfter(qt.getStartat())
									|| quotation.getStartat().isEqual(qt.getStartat()))
							&& (quotation.getStartat().isBefore(qt.getEndat())
									|| quotation.getStartat().isEqual(qt.getEndat()))) {

						HashMap<String, String> params = new HashMap<>();
						params.put("{0}", quotation.getInsurance().getName());

						throw new ApplicationException(
								AccreditationKeyMessagesConstants.QUOTATION_INSURANCE_CONFLICTS_DATE, params);
					}

				}
			}

		}
	}

	@Override
	public List<QuotationPO> findByQuotationInsurance(Long insuranceId,
			LocalDate startAt) {

		List<QuotationPO> quotationList = null;
		if (insuranceId == 0) {
			quotationList = quotationRepository.findByInsuranceNullAndStartat(startAt);
		}else{
			quotationList = quotationRepository.findByInsuranceIdAndStartat(insuranceId, startAt);
		}
		return quotationList;
	}

}
