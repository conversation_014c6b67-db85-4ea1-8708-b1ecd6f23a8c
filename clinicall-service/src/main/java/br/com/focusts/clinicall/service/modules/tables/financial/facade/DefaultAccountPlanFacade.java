
package br.com.focusts.clinicall.service.modules.tables.financial.facade;

import br.com.focusts.clinicall.service.modules.tables.financial.to.AccountPlanTO;
import br.com.focusts.clinicall.service.modules.tables.financial.to.CompanyAccountPlanTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.financial.po.AccountPlanPO;
import br.com.focusts.clinicall.service.modules.tables.financial.service.AccountPlanService;

@Component
@Transactional
public class DefaultAccountPlanFacade extends AbstractCrudFacade <AccountPlanPO, java.lang.Long> implements AccountPlanFacade  {

	@Autowired
	private AccountPlanService accountPlanService;

	@Override
	public CrudService<AccountPlanPO,java.lang.Long> getCrudService() {
            return accountPlanService;
	}

	@Override
	public Page<AccountPlanPO> findByCodeContainingOrNameContainingOrAliasContaining(PageSearchTO pageSearchTO) {
            return accountPlanService.findByCodeContainingOrNameContainingOrAliasContaining(pageSearchTO);
	}

	@Override
	public Page<AccountPlanTO> searchAccountPlan(PageSearchTO pageSearchTO) {
		return accountPlanService.searchAccountPlan(pageSearchTO);
	}

	@Override
	public Page<AccountPlanTO> searchAccountPlanByParentId(PageSearchTO pageSearchTO, Long parentId) {
		return accountPlanService.searchAccountPlanByParentId(pageSearchTO,parentId);
	}

	@Override
	public AccountPlanTO accountPlanById(Long id) {
		return accountPlanService.accountPlanById(id);
	}

}
