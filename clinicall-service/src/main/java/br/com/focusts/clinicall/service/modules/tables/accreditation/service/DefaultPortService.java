
package br.com.focusts.clinicall.service.modules.tables.accreditation.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.PortPO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.repository.PortRepository;


@Service
public class DefaultPortService extends AbstractCrudService<PortPO,java.lang.Long> implements PortService {

	@Autowired
	private PortRepository portRepository;

	@Override
	public CrudRepository<PortPO,java.lang.Long> getCrudRepository() {
	    return portRepository;
	}

	@Override
	public Page<PortPO> findByNameContainingOrValueContainingOrCurrency_nameContaining(PageSearchTO pageSearchTO) {

        PageRequest pageRequest =  PageRequest.of(pageSearchTO.getPage(),pageSearchTO.getSizePage(), Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());

	    return portRepository.findByNameContainingOrCurrency_nameContaining( pageSearchTO.getArgument(), pageSearchTO.getArgument(),  pageRequest);
	}
	
}
