package br.com.focusts.clinicall.service.modules.reports.enums;

import br.com.focusts.clinicall.fw.enums.DescriptionEnum;

public enum ReportPathEnum implements DescriptionEnum {

    CONSULTATION_GUIDE {
        public String getName() {
            return "GUIA DE CONSULTA";
        }

        public String getValue() {
            return "guias/consulta/guiaConsulta";
        }

        public String getDescription() {
            return "imprime dados da guia no tipo consulta";
        }
    },

    CONSULTATION_GUIDE_V4 {
        public String getName() {
            return "GUIA DE CONSULTA";
        }

        public String getValue() {
            return "guias/consultaV4/guiaConsulta";
        }

        public String getDescription() {
            return "imprime dados da guia no tipo consulta";
        }
    },

    SADT_GUIDE {
        public String getName() {
            return "GUIA SADT";
        }

        public String getValue() {
            return "guias/sadt/guiaSADT";
        }

        public String getDescription() {
            return "imprime dados da guia no tipo sadt";
        }
    },

    SADT_GUIDE_V4 {
        public String getName() {
            return "GUIA SADT";
        }

        public String getValue() {
            return "guias/sadtV4/guiaSADT";
        }

        public String getDescription() {
            return "imprime dados da guia no tipo sadt";
        }
    },

    ADMISSION_GUIDE {
        public String getName() {
            return "GUIA DE INTERNAMENTO";
        }

        public String getValue() {
            return "guias/internamento/orderdto";
        }

        public String getDescription() {
            return "imprime dados da guia no tipo INTERNAMENTO";

        }
    },

    HONORARY_GUIDE {
        public String getName() {
            return "GUIA DE HONORARIO";
        }

        public String getValue() {
            return "guias/honorario/orderdto";
        }

        public String getDescription() {
            return "imprime dados da guia no tipo honorario";
        }
    },

    EXPENDE {
        public String getName() {
            return "GUIA DE OUTRAS DESPESAS";
        }

        public String getValue() {
            return "guias/outrasDespesas/guideExpense";
        }

        public String getDescription() {
            return "GUIA DE OUTRAS DESPESAS";
        }
    },

    PATIENT_RECORD {

        public String getName() {
            return "FICHA DO PACIENTE";
        }

        public String getValue() {
            return "patient/patientRecord/patientRecord";
        }

        public String getDescription() {
            return "FICHA DO PACIENTE";
        }
    },

    APAC_REPORT {

        public String getName() {
            return "LAUDO APAC";
        }

        public String getValue() {
            return "sus/apac/report_apac";
        }

        public String getDescription() {
            return "LAUDO APAC";
        }
    },

    RECEIPT_OS {

        public String getName() {
            return "RECIBO";
        }

        public String getValue() {
            return "receipt-attendance/receipt-attendance";
        }

        public String getDescription() {
            return "RECIBO";
        }
    },

    BUDGET {

        public String getName() {
            return "ORÇAMENTO";
        }

        public String getValue() {
            return "budget/budget";
        }

        public String getDescription() {
            return "ORÇAMENTO";
        }
    },

    REPORT_OS {

        public String getName() {
            return "GUIA OS";
        }

        public String getValue() {
            return "guias/relatorioOS/Report_OS";
        }

        public String getDescription() {
            return "GUIA OS";
        }
    },

    REPORT_DETAILED_TRANSFER_PRODUCTION {

        public String getName() {
            return "transfer";
        }

        public String getValue() {
            return "transfer/application-report-transfer-detailed-transfer-by-production/application-report-transfer-detailed-transfer-by-production";
        }

        public String getDescription() {
            return "Repasse detalhado por produção";
        }
    },

    REPORT_DETAILED_TRANSFER {

        public String getName() {
            return "transfer detailed";
        }

        public String getValue() {
            return "transfer/application-report-transfer-detailed-transfer/application-report-transfer-detailed-transfer";
        }

        public String getDescription() {
            return "Repasse detalhado";
        }
    },

    REPORT_CHECK_BILLING {

        public String getName() {
            return "Atendimentos a faturar";
        }

        public String getValue() {
            return "billing/application-report-billing-check-billing/application-report-billing-check-billing";
        }

        public String getDescription() {
            return "Atendimentos a faturar";
        }
    },

    REPORT_CHECK_BILLING_XLS{

        public String getName() {
            return "Atendimentos a faturar";
        }

        public String getValue() {
            return "billing/application-report-billing-check-billing-xls/application-report-billing-check-billing-xls";
        }

        public String getDescription() {
            return "Atendimentos a faturar";
        }
    },

    SEARCH_SCHEDULE_PATIENT {

        public String getName() {
            return "PESQUISA DE PACIENTE NA AGENDA";
        }

        public String getValue() {
            return "schedule/search-patient/report-search-patient";
        }

        public String getDescription() {
            return "PESQUISA DE PACIENTE NA AGENDA";
        }
    },

    SEARCH_SCHEDULE_CANCEL {

        public String getName() {
            return "PESQUISA DE PACIENTES CANCELADOS NA AGENDA";
        }

        public String getValue() {
            return "schedule/search-patient-cancel/report-search-patient-cancel";
        }

        public String getDescription() {
            return "PESQUISA DE PACIENTES CANCELADOS NA AGENDA";
        }
    },

    REPORT_SHEET {

        public String getName() {
            return "FOLHA DE LAUDO";
        }

        public String getValue() {
            return "guias/report-sheet/report-sheet";
        }

        public String getDescription() {
            return "FOLHA DE LAUDO";
        }
    },

    ATTENDANCE_PROFESSIONAL_REQUESTER {

        public String getName() {
            return "Atendimento por Solicitante";
        }

        public String getValue() {
            return "order/application-report-order-attendance-professional-requester/application-report-order-attendance-professional-requester";
        }

        public String getDescription() {
            return "Atendimento por Solicitante";
        }
    },

    CONTROL_SHEET {

        public String getName() {
            return "FOLHA DE CONTROLE";
        }

        public String getValue() {
            return "control-sheet/control-sheet-patient-active";
        }

        public String getDescription() {
            return "FOLHA DE CONTROLE";
        }
    },

    CONTROL_SHEET_SCHEDULE {

        public String getName() {
            return "FOLHA DE CONTROLE";
        }

        public String getValue() {
            return "control-sheet/control-sheet-schedule";
        }

        public String getDescription() {
            return "FOLHA DE CONTROLE";
        }
    },

    CAPTURE {

        public String getName() {
            return "Captura";
        }

        public String getValue() {
            return "capture/capture";
        }

        public String getDescription() {
            return "Captura";
        }
    },


    }
