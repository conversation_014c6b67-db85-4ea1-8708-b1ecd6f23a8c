
package br.com.focusts.clinicall.service.modules.tables.general.facade;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.general.po.OccupationTissPO;

public interface OccupationTissFacade extends CrudFacade<OccupationTissPO,java.lang.Long>{

    public Page<OccupationTissPO> findByCodeContainingOrOccupation_nameContainingOrTiss_versionContaining(PageSearchTO pageSearchTO);

}

