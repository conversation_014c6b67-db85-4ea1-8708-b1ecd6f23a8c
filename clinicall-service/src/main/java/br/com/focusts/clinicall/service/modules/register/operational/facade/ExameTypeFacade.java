
package br.com.focusts.clinicall.service.modules.register.operational.facade;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.operational.po.ExameTypePO;
import org.springframework.data.domain.Page;

public interface ExameTypeFacade extends CrudFacade<ExameTypePO, Long>{

    Page<ExameTypePO> findByCodeStartingWithOrNameStartingWith(PageSearchTO pageSearchTO);

    Page<ExameTypePO> findByCodeContainingOrNameContaining(PageSearchTO pageSearchTO);
}

