
package br.com.focusts.clinicall.service.modules.tables.financial.facade;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.financial.po.BrandPO;

public interface BrandFacade extends CrudFacade<BrandPO,java.lang.Long>{

    public Page<BrandPO> findByNameContaining(PageSearchTO pageSearchTO);

}

