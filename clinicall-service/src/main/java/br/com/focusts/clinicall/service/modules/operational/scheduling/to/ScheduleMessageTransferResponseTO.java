package br.com.focusts.clinicall.service.modules.operational.scheduling.to;

import br.com.focusts.clinicall.fw.to.AbstractTO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.po.SchedulePO;

public class ScheduleMessageTransferResponseTO extends AbstractTO {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private SchedulePO scheduleSource;
	private SchedulePO scheduleTarget;
	private String msg;


	public ScheduleMessageTransferResponseTO(SchedulePO scheduleSource, SchedulePO scheduleTarget, String msg) {
		this.scheduleSource = scheduleSource;
		this.scheduleTarget = scheduleTarget;
		this.msg = msg;
	}

	public String getMsg() {
		return msg;
	}

	public SchedulePO getScheduleSource() {
		return scheduleSource;
	}

	public SchedulePO getScheduleTarget() {
		return scheduleTarget;
	}

	
}
