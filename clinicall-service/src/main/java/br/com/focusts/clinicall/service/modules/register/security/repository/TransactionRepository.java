package br.com.focusts.clinicall.service.modules.register.security.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.register.security.po.TransactionPO;


@Repository
public interface TransactionRepository extends CrudRepository<TransactionPO, Long>{

    public Page<TransactionPO> findByNameContainingOrCodeContainingOrView_NameContaining(String name, String code, String viewName, Pageable pageable);
    
    public List<TransactionPO> findByMainAndView_Id(Boolean main, Long viwId);


}
