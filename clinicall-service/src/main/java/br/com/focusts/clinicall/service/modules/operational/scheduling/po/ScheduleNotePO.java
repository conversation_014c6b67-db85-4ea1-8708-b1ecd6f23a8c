package br.com.focusts.clinicall.service.modules.operational.scheduling.po;

import javax.persistence.Basic;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

import org.hibernate.envers.Audited;

import com.fasterxml.jackson.annotation.JsonIgnore;

import br.com.focusts.clinicall.fw.po.AbstractPO;


@Entity
@Audited
@Table(name = "schedule_note")
public class ScheduleNotePO extends AbstractPO<Long>{

	private static final long serialVersionUID = 1L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	private Long id;
	
	@JsonIgnore
    @JoinColumn(name = "schedule_id", referencedColumnName = "schedule_id")
    @NotNull
    @OneToOne
    private SchedulePO schedule;
	
	@NotNull
	private String note;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public SchedulePO getSchedule() {
		return schedule;
	}

	public void setSchedule(SchedulePO schedule) {
		this.schedule = schedule;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	
}
