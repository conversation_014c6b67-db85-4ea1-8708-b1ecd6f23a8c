package br.com.focusts.clinicall.service.modules.operational.scheduling.to;

public class PerformerShiftTO {

	private Integer day;
	private boolean morning;
	private boolean evening;
	private boolean night;

	public Integer getDay() {
		return day;
	}

	public void setDay(Integer day) {
		this.day = day;
	}

	public Boolean getMorning() {
		return morning;
	}

	public void setMorning(boolean morning) {
		this.morning = morning;
	}

	public boolean getEvening() {
		return evening;
	}

	public void setEvening(boolean evening) {
		this.evening = evening;
	}

	public boolean getNight() {
		return night;
	}

	public void setNight(boolean night) {
		this.night = night;
	}

}
