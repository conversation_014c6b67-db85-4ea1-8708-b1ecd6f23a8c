package br.com.focusts.clinicall.service.modules.operational.reception.repository;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPendingPO;

@Repository
public interface OrderPendingRepository extends CrudRepository<OrderPendingPO,Long>, QuerydslPredicateExecutor<OrderPendingPO>{

	public List<OrderPendingPO> findByOrder_idAndPending_typeNotIn(Long orderId, List<String> type);

	public List<OrderPendingPO> findByOrderItem_id(Long orderItemId);

	@Query("SELECT op FROM OrderPendingPO op WHERE op.resolution_date IS NULL AND op.order.id =:orderId")
	public List<OrderPendingPO> checkPendency(@Param("orderId") Long orderId);

	@Query("SELECT op FROM OrderPendingPO op WHERE op.resolution_date IS NULL AND op.orderItem.id =:orderItemId")
	public List<OrderPendingPO> checkPendencyOrderItem(@Param("orderItemId") Long orderItemId);

}
