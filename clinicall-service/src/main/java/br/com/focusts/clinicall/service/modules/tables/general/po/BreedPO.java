package br.com.focusts.clinicall.service.modules.tables.general.po;

import java.lang.annotation.Repeatable;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.validator.constraints.UniqueElements;

import br.com.focusts.clinicall.fw.po.AbstractPO;

@Entity
@Audited
@Table(name = "breed")
public class BreedPO extends AbstractPO<Long> {

	private static final long serialVersionUID = 1L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "breed_id")
	private Long id;

	@Basic(optional = false)
	@NotBlank
	@Size(max = 2)
	@Column(unique=true)
	private String code;

	@Basic(optional = false)
	@NotBlank
	@Size(max = 250)
	private String name;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

}
