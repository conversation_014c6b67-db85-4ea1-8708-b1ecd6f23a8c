
package br.com.focusts.clinicall.service.modules.register.operational.facade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.operational.po.EventPO;
import br.com.focusts.clinicall.service.modules.register.operational.service.EventService;

@Component
@Transactional
public class DefaultEventFacade extends AbstractCrudFacade <EventPO, java.lang.Long> implements EventFacade  {

	@Autowired
	private EventService eventService;

	@Override
	public CrudService<EventPO,java.lang.Long> getCrudService() {
            return eventService;
	}

	@Override
	public Page<EventPO> findAll(PageSearchTO pageSearchTO) {
            return eventService.findAll(pageSearchTO);
	}        
      
}
