
package br.com.focusts.clinicall.service.modules.register.accreditation.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.register.accreditation.po.SpecialityPO;

@Repository
public interface SpecialityRepository extends CrudRepository<SpecialityPO,java.lang.Long>{

    // public Page<SpecialityPO> findByNameContainingOrDescriptionContainingOrJurisprudenceContainingOrOccupation_nameContaining(java.lang.String name,java.lang.String description,java.lang.String jurisprudence,String occupationName , Pageable pageable);
    public Page<SpecialityPO> findByNameStartingWithOrOccupation_nameStartingWith(java.lang.String name,String occupationName , Pageable pageable);

    @Query("SELECT s FROM ProfessionalSpecialityPO psp " +
            "JOIN SpecialityPO s ON s.id = psp.speciality.id " +
            "WHERE psp.professional.id = :professionalId " +
            "AND s.name LIKE :name% " +
            "AND psp.speciality.id <> :specialityId")
    public Page<SpecialityPO> findByIdInAndNameStartingWith(Long professionalId, Long specialityId,String name, Pageable pageable);

    @Query(nativeQuery = true, value ="SELECT s.* FROM speciality s " +
            "LEFT JOIN professional_speciality ps ON ps.speciality_id = s.speciality_id " +
            "LEFT JOIN professional p2 ON p2.professional_id = ps.professional_id " +
            "JOIN performer p ON p.professional_id = p2.professional_id " +
            "WHERE  s.name LIKE :name% " +
            "GROUP BY s.speciality_id ",
            countQuery = "SELECT COUNT(s.speciality_id) FROM speciality s " +
                    "LEFT JOIN professional_speciality ps ON ps.speciality_id = s.speciality_id " +
                    "LEFT JOIN professional p2 ON p2.professional_id = ps.professional_id " +
                    "JOIN performer p ON p.professional_id = p2.professional_id " +
                    "WHERE  s.name LIKE :name% "  +
                    "GROUP BY s.speciality_id ")
    public Page<SpecialityPO> 
    findByInternal(String name, Pageable pageable);

}



