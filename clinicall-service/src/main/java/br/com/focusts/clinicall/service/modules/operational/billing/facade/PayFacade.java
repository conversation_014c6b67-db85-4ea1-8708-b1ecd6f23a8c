
package br.com.focusts.clinicall.service.modules.operational.billing.facade;

import java.util.ArrayList;
import java.util.List;

import br.com.focusts.clinicall.service.modules.operational.billing.po.*;
import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.billing.to.PayBondPayTO;
import br.com.focusts.clinicall.service.modules.operational.billing.to.PayInfoGlossTO;
import br.com.focusts.clinicall.service.modules.operational.billing.to.ValuePaidRequestTO;
import br.com.focusts.clinicall.service.modules.tables.financial.to.AccountTO;

import javax.xml.bind.JAXBException;

public interface PayFacade extends CrudFacade<PayPO, java.lang.Long> {

  public Page<PayPO> find(PageSearchTO pageSearchTO);

  public GlossPO insertGloss(GlossPO glossPO);

  public BillingPayOrderItemPO finByBillingPayOrderItemId(Long billingPayOrderItemId);

  public PayInfoGlossTO findByInfoGloss(Long payId);

  public Void closedPay(Long payId);

  public void deleteGloss(Long glossId);

  public PayInvoicePO insertPayInvoice(PayInvoicePO payInvoice);

  public void deletePayInvoice(PayInvoicePO payInvoice);

  public InvoicePO calcImpostos(Long payId);

  public BillingPayOrderItemPO updateValuePaid(Long billingPayOrderItemId, ValuePaidRequestTO valuePaid);

  public void deleteBillingPayOrderItem(Long id);

  public List<BillingOrderItemPO> getRecoveryBillingPayorderItem(ArrayList<Long> billingIdList);

  public List<BillingPayOrderItemPO> recoveryBillingPayorderItem(Long payId,
      ArrayList<BillingOrderItemPO> billingOrderItemList);

  public Boolean checkPayGloss(Long glossId);

  Double calcCreditValue(Long payId);

  Page<BillingPayOrderItemPO> searchBillingPayOrderItem(PageSearchTO pageSearchTO, Long payId);

  PayPO saveAutomatic(PayPO billingPayPO, String xmlBase64, String xmlName) throws JAXBException;

  List<PayEartefactPO> findEartefact(Long payId);

  public Page<PayXmlPO> findPayXMlByPayId(Long payId, String type, PageSearchTO pageSearchTO);

  Page<AccountTO> findByAccount(PageSearchTO pageSearchTO);

  List<String> findProtocolByPaiId(Long payId);

  List<PayBondPayTO> findBondByPayId(Long payid);

  void processBondPay(List<PayBondPayTO > payBondPay);

  AccountTO getInsuranceAccount(Long insuranceId);
}
