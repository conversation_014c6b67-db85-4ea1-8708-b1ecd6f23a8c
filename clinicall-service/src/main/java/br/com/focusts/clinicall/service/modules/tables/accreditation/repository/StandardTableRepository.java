
package br.com.focusts.clinicall.service.modules.tables.accreditation.repository;

import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.TableTypePO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import br.com.focusts.clinicall.service.modules.tables.accreditation.po.StandardTablePO;

@Repository
public interface StandardTableRepository extends CrudRepository<StandardTablePO,java.lang.Long>{

    public Page<StandardTablePO> findByNameContaining(java.lang.String name, Pageable pageable);

    Page<StandardTablePO> findByTypeAndNameStartingWith(String type, String name, Pageable pageable);

    Page<StandardTablePO> findByTypeAndNameStartingWithOrType(String type, String name, String type2, Pageable pageable);
}



