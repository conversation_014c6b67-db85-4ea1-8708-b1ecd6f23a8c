package br.com.focusts.clinicall.service.modules.tables.tiss.po;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import br.com.focusts.clinicall.fw.po.AbstractPO;



@Entity
@Table(name = "accommodation")
public class AccommodationPO extends AbstractPO<Long> {
	
		/**
		 * 
		 */
		private static final long serialVersionUID = 1L;

		@Id
		@GeneratedValue(strategy = GenerationType.IDENTITY)
		@Basic(optional = false)
		@Column(name = "accommodation_id")
		private Long id;
		
		@NotBlank 
		@Size(max = 2)
		private String code;
		
		@NotBlank
		@Size(max = 50)
		private String name;

		public Long getId() {
			return id;
		}

		public void setId(Long id) {
			this.id = id;
		}

		public String getCode() {
			return code;
		}

		public void setCode(String code) {
			this.code = code;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}
		
}
