package br.com.focusts.clinicall.service.modules.register.security.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;

import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.service.modules.register.security.po.UserMetaPO;
import br.com.focusts.clinicall.service.modules.register.security.repository.UserMetaRepository;

@Component
public class DefaultUserMetaService extends AbstractCrudService<UserMetaPO, Long> implements UserMetaService { 
	
	@Autowired
	private UserMetaRepository userMetaRepository;

	@Override
	public CrudRepository<UserMetaPO, Long> getCrudRepository() {
		return userMetaRepository;
	}

	@Override
	public UserMetaPO findByUserIdAndMetaKey(Long userId, String metaKey) {
		return userMetaRepository.findByUser_idAndMeta_key(userId, metaKey);
	}
	
	@Override
	public List<UserMetaPO> findByUserId(Long userId) {
		return userMetaRepository.findByUserId(userId);
	}
	
	
	
}
