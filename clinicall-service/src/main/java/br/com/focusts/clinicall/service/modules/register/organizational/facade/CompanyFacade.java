
package br.com.focusts.clinicall.service.modules.register.organizational.facade;

import java.time.LocalTime;
import java.util.List;

import br.com.focusts.clinicall.service.modules.register.organizational.to.CompanyTO;
import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.EartefactPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.CompanyCostCenterPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.CompanyPO;

public interface CompanyFacade extends CrudFacade<CompanyPO,java.lang.Long>{

    public Page<CompanyPO> findByNameContainingOrTradeNameContainingOrCnesContainingOrAddress_addressContainingOrCompany_nameContaining(PageSearchTO pageSearchTO);
    public Page<CompanyPO> findByCompanyParentIsNullAndNameContaining(PageSearchTO pageSearchTO);
    public Page<CompanyPO> findByParentIsNotNullOrUnityTrueAndNameContaining(PageSearchTO pageSearchTO);

    public List<CompanyPO> findByCompanyParentIsNull();
    public List<CompanyPO> findByCompanyActiveTrue();

    public List<CompanyPO> findByCompanyParentIsNotNull();

    public void deleteImage(Long id);
    
    public List<CompanyPO> findByCompanyParentId(Long companyParentId);

    public CompanyCostCenterPO findByCompanyCostCenterId(Long companyId, Long costCenterId);


    List<CompanyCostCenterPO> findCompanyCostCenterByCompany(Long companyId);

    void uploadCetification(String certificadoBase64, String password, Long companyId) throws Exception;

    EartefactPO findCetification(Long companyId);

    void deletCetification(Long companyId);

    public LocalTime findMaxHourShiftCompany(Long companyId);

    Page<CompanyTO> searchCompany(PageSearchTO pageSearchTO);

     public CompanyTO findByIdCompanyTO(Long id);
}

