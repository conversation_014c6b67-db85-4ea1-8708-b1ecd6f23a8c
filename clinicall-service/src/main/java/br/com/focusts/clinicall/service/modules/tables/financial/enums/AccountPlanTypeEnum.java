package br.com.focusts.clinicall.service.modules.tables.financial.enums;

import br.com.focusts.clinicall.fw.enums.DescriptionEnum;

public enum AccountPlanTypeEnum implements DescriptionEnum {

	RECEITA {

		public String getName() {
			return "Receita";
		}

		public String getValue() {

			return "R";
		}

		public String getDescription() {
			return "Receita";
		}

	},
	DESPESA {

		public String getName() {
			return "Despesa";
		}

		public String getValue() {

			return "D";
		}

		public String getDescription() {
			return "Despesa";
		}

	},
	AGRUPAMENTO {

		public String getName() {
			return "Agrupamento";
		}

		public String getValue() {

			return "A";
		}

		public String getDescription() {
			return "Agrupamento";
		}

	};

}