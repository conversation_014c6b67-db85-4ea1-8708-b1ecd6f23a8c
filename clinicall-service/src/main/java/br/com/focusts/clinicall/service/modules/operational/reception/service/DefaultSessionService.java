package br.com.focusts.clinicall.service.modules.operational.reception.service;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.checkerframework.checker.units.qual.s;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import com.querydsl.core.Tuple;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;

import br.com.focusts.clinicall.fw.exception.ApplicationException;
import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderItemPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderItemPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QSessionItemPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QSessionPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.SessionPO;
import br.com.focusts.clinicall.service.modules.operational.reception.repository.OrderRepository;
import br.com.focusts.clinicall.service.modules.operational.reception.repository.SessionSORepository;
import br.com.focusts.clinicall.service.modules.operational.reception.to.OrderItemTO;
import br.com.focusts.clinicall.service.modules.operational.reception.to.OrderSessionTO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QInsurancePO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QProcedurePO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.QPerformerPO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.QProfessionalPO;
import br.com.focusts.clinicall.service.modules.register.organizational.service.CompanyCostCenterService;
import br.com.focusts.clinicall.service.modules.tables.general.po.QPersonPO;

@Service
public class DefaultSessionService extends AbstractCrudService<SessionPO, Long> implements SessionService {
  @Autowired
  private SessionSORepository sessionRepository;

  @Autowired
  private OrderRepository orderRepository;

  @PersistenceContext
  private EntityManager em;

  public DefaultSessionService(EntityManager em) {
    this.em = em;
  }

  @Autowired
  private CompanyCostCenterService companyCostCenterService;

  @Override
  public CrudRepository<SessionPO, Long> getCrudRepository() {
    return sessionRepository;
  }

  @Override
  public void delete(Long aLong) {

    super.delete(aLong);
  }

  @Override
  public SessionPO createSession(OrderPO orderPO) {

    var costCenter = companyCostCenterService.findById(orderPO.getCompanyCostCenter().getId());

    var session = new SessionPO();
    session = orderPO.getSession();
    session.setProcedure(orderPO.getMainProcedure());
    session.setCompany(costCenter.getCompany());
    session.setDateat(orderPO.getDate());
    session.setWeekdays(session.getWeekdays());
    var weekdayshorary = session.getWeekdayshorary().split(",");
    session.setWeekdayshorary(String.join("", weekdayshorary));
    session = sessionRepository.save(session);

    return session;
  }

  public SessionPO createSessionParent(OrderItemPO orderItem, SessionPO sessionPO) {

    var costCenter = companyCostCenterService.findById(orderItem.getOrder().getCompanyCostCenter().getId());

    var session = new SessionPO();
    session = sessionPO;
    session.setProcedure(orderItem.getProcedure());
    session.setCompany(costCenter.getCompany());
    session.setDateat(orderItem.getDate());
    session.setWeekdays(session.getWeekdays());
    var weekdayshorary = session.getWeekdayshorary().split(",");
    session.setWeekdayshorary(String.join("", weekdayshorary));
    if (orderItem.getOrder().getSession() != null) {
      session.setParent(orderItem.getOrder().getSession());
    }

    session = sessionRepository.save(session);

    return session;
  }

  public SessionPO createNewSession(OrderPO orderPO) {
    var session = new SessionPO();

    session.setProcedure(orderPO.getMainProcedure());
    if (orderPO.getCompanyCostCenter() == null) {
      throw new ApplicationException("É necessário informar o Centro de Resultado no cadastro do usuário.");
    }
    var costCenter = companyCostCenterService.findById(orderPO.getCompanyCostCenter().getId());
    session.setCompany(costCenter.getCompany());
    session.setWeekdays("1111111");
    session.setWeekdayshorary("00:0000:0000:0000:0000:0000:0000:00");
    session.setDateat(orderPO.getDate());
    session.setFrequency(1);
    session.setQuantity(1L);
    session.setBlocked(false);
    session.setSchedulebysession(false);
    session.setOrderbysession(false);

    session = sessionRepository.save(session);

    return session;
  }

  @Override
  public Page<OrderSessionTO> findSessionByPatientId(Long patientId, Boolean realized, PageSearchTO pageSearchTO) {
    PageRequest pageRequest = null;
    if (pageSearchTO != null) {
      pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage());
    }

    QSessionPO sessionMain = new QSessionPO("sessionMain");
    QSessionPO sessionParent = new QSessionPO("sessionParent");
    QSessionItemPO sessionItemMain = new QSessionItemPO("sessionItemMain");
    QSessionItemPO sessionItemParent = new QSessionItemPO("sessionItemParent");
    QOrderPO order = QOrderPO.orderPO;

    JPAQuery<OrderSessionTO> query = new JPAQuery<>(em);
    JPQLQuery<Long> sessionItemCount = new JPAQuery<>(em)
        .select(sessionItemMain.id.count())
        .from(sessionItemMain)
        .where(sessionItemMain.session.id.eq(sessionMain.id));

    // Total de itens para o parent_id
    JPQLQuery<Long> parentItemCount = new JPAQuery<>(em)
        .select(sessionItemParent.id.count())
        .from(sessionItemParent)
        .where(sessionItemParent.session.id.eq(sessionParent.id));

    // Soma das subconsultas para obter o total realizado
    Expression<Long> totalRealizada = Expressions.numberTemplate(Long.class, "{0} + {1}", sessionItemCount,
        parentItemCount);

    // Consulta principal
    query.from(order)
        .join(sessionMain).on(sessionMain.id.eq(order.session.id))
        .leftJoin(sessionParent).on(sessionParent.parent.id.eq(sessionMain.id))
        .orderBy(sessionMain.dateat.desc());

    if (realized) {
      query.where(order.patient.id.eq(patientId)
          .and(sessionMain.quantity.add(sessionParent.quantity.coalesce(0L))
              .eq(totalRealizada)));
    } else {
      query.where(order.patient.id.eq(patientId)
          .and(sessionMain.quantity.add(sessionParent.quantity.coalesce(0L))
              .ne(totalRealizada)));
    }
    Long total = query.select(sessionMain.id.count()).fetchOne();
    query.limit(pageRequest.getPageSize()).offset(pageRequest.getOffset());
    query.select(Projections.bean(
        OrderSessionTO.class,
        sessionMain.id.as("id"),
        order.number.as("number"),
        sessionMain.dateat.as("dateat"),
        sessionMain.quantity.add(sessionParent.quantity.coalesce(0L)).as("quantity"),
        ExpressionUtils.as(totalRealizada, "numberRealized")));

    var sessionList = query.fetch();
    return new PageImpl<>(sessionList, pageRequest, total);
  }

  public Boolean checkOrderRealized(Long orderId) {
    var result = true;
    JPAQuery<Tuple> query = new JPAQuery<Tuple>(em);

    QOrderItemPO orderItemPO = QOrderItemPO.orderItemPO;
    QOrderPO orderPO = QOrderPO.orderPO;
    QSessionPO sessionPO = QSessionPO.sessionPO;
    QSessionItemPO sessionItemPO = QSessionItemPO.sessionItemPO;

    var resultQuery = query.select(sessionItemPO.id.count(), sessionPO.quantity, sessionPO.id)
        .from(orderItemPO)
        .join(sessionItemPO).on(sessionItemPO.orderItem.eq(orderItemPO))
        .join(sessionPO).on(sessionPO.eq(sessionItemPO.session))
        .join(orderPO).on(orderPO.eq(orderItemPO.order))
        .where(orderPO.id.eq(orderId))
        .groupBy(sessionPO.id).fetch();

    for (var r : resultQuery) {
      Long l1 = r.get(1, Long.class);
      Long l2 = r.get(0, Long.class);
      if (l1 != null) {
        assert l2 != null;
        int compareValue = l1.compareTo(l2);
        result = compareValue == 0;
      } else {
        result = false;
      }

    }

    return result;

  }

  public Integer checkOrderSessionToBeCarriedOut(Long orderId) {
    QSessionPO sessionMain = new QSessionPO("sessionMain");
    QSessionPO sessionParent = new QSessionPO("sessionParent");
    QSessionItemPO sessionItemMain = new QSessionItemPO("sessionItemMain");
    QSessionItemPO sessionItemParent = new QSessionItemPO("sessionItemParent");
    QOrderPO order = QOrderPO.orderPO;

    JPAQuery<OrderSessionTO> query = new JPAQuery<>(em);
    JPQLQuery<Long> sessionItemCount = new JPAQuery<>(em)
        .select(sessionItemMain.id.count())
        .from(sessionItemMain)
        .where(sessionItemMain.session.id.eq(sessionMain.id));

    // Total de itens para o parent_id
    JPQLQuery<Long> parentItemCount = new JPAQuery<>(em)
        .select(sessionItemParent.id.count())
        .from(sessionItemParent)
        .where(sessionItemParent.session.id.eq(sessionParent.id));

    // Soma das subconsultas para obter o total realizado
    Expression<Long> totalRealizada = Expressions.numberTemplate(Long.class, "{0} + {1}", sessionItemCount,
        parentItemCount);

    // Consulta principal
    query.from(order)
        .join(sessionMain).on(sessionMain.id.eq(order.session.id))
        .leftJoin(sessionParent).on(sessionParent.parent.id.eq(sessionMain.id))
        .orderBy(sessionMain.dateat.desc());

    query.where(order.id.eq(orderId)
        .and(sessionMain.quantity.add(sessionParent.quantity.coalesce(0L))
            .ne(totalRealizada)));

    query.select(Projections.bean(
        OrderSessionTO.class,
        sessionMain.quantity.add(sessionParent.quantity.coalesce(0L)).as("quantity"),
        ExpressionUtils.as(totalRealizada, "numberRealized")));

    var session = query.fetchOne();

    if (session == null) {
      return null;
    }

    Integer result = (int) (session.getQuantity() - session.getNumberRealized());

    return result;

  }

  public List<OrderItemTO> findOrderItemBySessionAndPatientId(Long patientId, Long sessionId) {

    List<OrderItemTO> orderItemList = new ArrayList<OrderItemTO>();

    JPAQuery<OrderItemTO> query = new JPAQuery<OrderItemTO>(em);

    QSessionPO sessionPO = QSessionPO.sessionPO;
    QSessionPO sessionParent = new QSessionPO("sessionParent");
    QSessionItemPO sessionItemPO = QSessionItemPO.sessionItemPO;
    QOrderPO orderPO = QOrderPO.orderPO;
    QInsurancePO insurancePO = QInsurancePO.insurancePO;
    QOrderItemPO orderItemPO = QOrderItemPO.orderItemPO;
    QPerformerPO performerPO = QPerformerPO.performerPO;
    QProfessionalPO professionalPO = QProfessionalPO.professionalPO;
    QPersonPO personPO = QPersonPO.personPO;
    QProcedurePO procedurePO = QProcedurePO.procedurePO;

    query.select(
        Projections.bean(
            OrderItemTO.class,
            orderItemPO.id.as("orderItemId"),
            orderItemPO.date.as("date"),
            orderPO.number.as("number"),
            orderItemPO.name.as("procedureName"),
            orderItemPO.code.as("procedureCode"),
            procedurePO.id.as("procedureId"),
            personPO.name.as("performerName"),
            performerPO.id.as("performerId"),
            insurancePO.alias.as("insuranceName"),
            insurancePO.id.as("insuranceId"),
            orderItemPO.sequence.as("sequence"),
            orderItemPO.total.as("total")))
        .distinct()
        .from(sessionPO)
        .leftJoin(sessionParent).on(sessionParent.parent.id.eq(sessionPO.id))
        .join(orderPO).on(orderPO.session.id.eq(sessionPO.id))
        .join(insurancePO).on(insurancePO.id.eq(orderPO.insurance.id))
        .join(orderItemPO).on(orderItemPO.order.id.eq(orderPO.id))
        .join(procedurePO).on(procedurePO.id.eq(orderItemPO.procedure.id))
        .join(performerPO).on(performerPO.id.eq(orderItemPO.performer.id))
        .join(professionalPO).on(professionalPO.id.eq(performerPO.professional.id))
        .join(personPO).on(personPO.id.eq(professionalPO.person.id))
        .where(orderPO.patient.id.eq(patientId))
        .where(sessionPO.id.eq(sessionId))
        .where(orderItemPO.procedure.id.eq(sessionPO.procedure.id)
            .or(orderItemPO.procedure.id.eq(sessionParent.procedure.id)))
        .orderBy(orderItemPO.sequence.asc())
        .where(JPAExpressions.selectOne()
            .from(sessionItemPO)
            .where(sessionItemPO.orderItem.id.eq(orderItemPO.id))
            .notExists())
        .orderBy(orderItemPO.date.asc());

    return query.fetch();

  }

  @Override
  public List<SessionPO> findByIdOrParent_id(Long sessionId) {
    return sessionRepository.findByIdOrParent_id(sessionId);
  }

  @Override
  public SessionPO findByIdOrParent_idAndProcedure(Long sessionId, Long procedureId) {
    return sessionRepository.findByIdOrParent_idAndProcedure(sessionId, procedureId);
  }
}
