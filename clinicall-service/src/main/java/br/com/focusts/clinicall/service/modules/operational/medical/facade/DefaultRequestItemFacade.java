package br.com.focusts.clinicall.service.modules.operational.medical.facade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.service.modules.operational.medical.po.RequestItemPO;
import br.com.focusts.clinicall.service.modules.operational.medical.service.RequestItemService;

@Component
@Transactional
public class DefaultRequestItemFacade extends AbstractCrudFacade<RequestItemPO, Long> implements RequestItemFacade {
   
    @Autowired
	private RequestItemService requestItemService;
    
    @Override
    public CrudService<RequestItemPO, Long> getCrudService() {        
        return requestItemService;
    }

	

}
