//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2022.06.08 at 12:20:52 PM BRT 
//


package br.com.focusts.clinicall.service.modules.tables.tiss.version.v20203;

import java.math.BigInteger;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for ct_guiaSolicInternacao complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ct_guiaSolicInternacao">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="identificacaoGuiaSolicitacaoInternacao" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_cabecalhoSolicitacao"/>
 *         &lt;element name="dadosBeneficiario" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_beneficiario"/>
 *         &lt;element name="dadosSolicitanteAtendimento" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_solicitante" minOccurs="0"/>
 *         &lt;element name="dadosSolicitante" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_solicitante"/>
 *         &lt;element name="prestadorSolicitado" minOccurs="0">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_identificacaoPrestador">
 *                 &lt;sequence>
 *                   &lt;element name="nomePrestador" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_nome"/>
 *                 &lt;/sequence>
 *               &lt;/extension>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="caraterInternacao" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_eletivaEmergencia"/>
 *         &lt;element name="tipoInternacao" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_tipoInternacao"/>
 *         &lt;element name="indicacaoClinica" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_indicacaoClinica"/>
 *         &lt;element name="regimeInternacao" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_regimeInternacao"/>
 *         &lt;element name="hipotesesDiagnosticas" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_hipoteseDiagnostica"/>
 *         &lt;element name="procedimentosExamesSolicitados" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_procedimentosSolicitados"/>
 *         &lt;element name="OPMsSolicitadas" type="{http://www.ans.gov.br/padroes/tiss/schemas}ct_OPMSolicitadas" minOccurs="0"/>
 *         &lt;element name="diasSolicitados" type="{http://www.w3.org/2001/XMLSchema}integer"/>
 *         &lt;element name="dataProvavelAdmisHosp" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_data" minOccurs="0"/>
 *         &lt;element name="observacao" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_observacao" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_guiaSolicInternacao", propOrder = {
    "identificacaoGuiaSolicitacaoInternacao",
    "dadosBeneficiario",
    "dadosSolicitanteAtendimento",
    "dadosSolicitante",
    "prestadorSolicitado",
    "caraterInternacao",
    "tipoInternacao",
    "indicacaoClinica",
    "regimeInternacao",
    "hipotesesDiagnosticas",
    "procedimentosExamesSolicitados",
    "opMsSolicitadas",
    "diasSolicitados",
    "dataProvavelAdmisHosp",
    "observacao"
})
public class CtGuiaSolicInternacao {

    @XmlElement(required = true)
    protected CtCabecalhoSolicitacao identificacaoGuiaSolicitacaoInternacao;
    @XmlElement(required = true)
    protected CtBeneficiario dadosBeneficiario;
    protected CtSolicitante dadosSolicitanteAtendimento;
    @XmlElement(required = true)
    protected CtSolicitante dadosSolicitante;
    protected CtGuiaSolicInternacao.PrestadorSolicitado prestadorSolicitado;
    @XmlElement(required = true)
    protected StEletivaEmergencia caraterInternacao;
    @XmlElement(required = true)
    protected String tipoInternacao;
    @XmlElement(required = true)
    protected String indicacaoClinica;
    @XmlElement(required = true)
    protected String regimeInternacao;
    @XmlElement(required = true)
    protected CtHipoteseDiagnostica hipotesesDiagnosticas;
    @XmlElement(required = true)
    protected CtProcedimentosSolicitados procedimentosExamesSolicitados;
    @XmlElement(name = "OPMsSolicitadas")
    protected CtOPMSolicitadas opMsSolicitadas;
    @XmlElement(required = true)
    protected BigInteger diasSolicitados;
    protected XMLGregorianCalendar dataProvavelAdmisHosp;
    protected String observacao;

    /**
     * Gets the value of the identificacaoGuiaSolicitacaoInternacao property.
     * 
     * @return
     *     possible object is
     *     {@link CtCabecalhoSolicitacao }
     *     
     */
    public CtCabecalhoSolicitacao getIdentificacaoGuiaSolicitacaoInternacao() {
        return identificacaoGuiaSolicitacaoInternacao;
    }

    /**
     * Sets the value of the identificacaoGuiaSolicitacaoInternacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtCabecalhoSolicitacao }
     *     
     */
    public void setIdentificacaoGuiaSolicitacaoInternacao(CtCabecalhoSolicitacao value) {
        this.identificacaoGuiaSolicitacaoInternacao = value;
    }

    /**
     * Gets the value of the dadosBeneficiario property.
     * 
     * @return
     *     possible object is
     *     {@link CtBeneficiario }
     *     
     */
    public CtBeneficiario getDadosBeneficiario() {
        return dadosBeneficiario;
    }

    /**
     * Sets the value of the dadosBeneficiario property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtBeneficiario }
     *     
     */
    public void setDadosBeneficiario(CtBeneficiario value) {
        this.dadosBeneficiario = value;
    }

    /**
     * Gets the value of the dadosSolicitanteAtendimento property.
     * 
     * @return
     *     possible object is
     *     {@link CtSolicitante }
     *     
     */
    public CtSolicitante getDadosSolicitanteAtendimento() {
        return dadosSolicitanteAtendimento;
    }

    /**
     * Sets the value of the dadosSolicitanteAtendimento property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtSolicitante }
     *     
     */
    public void setDadosSolicitanteAtendimento(CtSolicitante value) {
        this.dadosSolicitanteAtendimento = value;
    }

    /**
     * Gets the value of the dadosSolicitante property.
     * 
     * @return
     *     possible object is
     *     {@link CtSolicitante }
     *     
     */
    public CtSolicitante getDadosSolicitante() {
        return dadosSolicitante;
    }

    /**
     * Sets the value of the dadosSolicitante property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtSolicitante }
     *     
     */
    public void setDadosSolicitante(CtSolicitante value) {
        this.dadosSolicitante = value;
    }

    /**
     * Gets the value of the prestadorSolicitado property.
     * 
     * @return
     *     possible object is
     *     {@link CtGuiaSolicInternacao.PrestadorSolicitado }
     *     
     */
    public CtGuiaSolicInternacao.PrestadorSolicitado getPrestadorSolicitado() {
        return prestadorSolicitado;
    }

    /**
     * Sets the value of the prestadorSolicitado property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtGuiaSolicInternacao.PrestadorSolicitado }
     *     
     */
    public void setPrestadorSolicitado(CtGuiaSolicInternacao.PrestadorSolicitado value) {
        this.prestadorSolicitado = value;
    }

    /**
     * Gets the value of the caraterInternacao property.
     * 
     * @return
     *     possible object is
     *     {@link StEletivaEmergencia }
     *     
     */
    public StEletivaEmergencia getCaraterInternacao() {
        return caraterInternacao;
    }

    /**
     * Sets the value of the caraterInternacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link StEletivaEmergencia }
     *     
     */
    public void setCaraterInternacao(StEletivaEmergencia value) {
        this.caraterInternacao = value;
    }

    /**
     * Gets the value of the tipoInternacao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoInternacao() {
        return tipoInternacao;
    }

    /**
     * Sets the value of the tipoInternacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoInternacao(String value) {
        this.tipoInternacao = value;
    }

    /**
     * Gets the value of the indicacaoClinica property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndicacaoClinica() {
        return indicacaoClinica;
    }

    /**
     * Sets the value of the indicacaoClinica property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndicacaoClinica(String value) {
        this.indicacaoClinica = value;
    }

    /**
     * Gets the value of the regimeInternacao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRegimeInternacao() {
        return regimeInternacao;
    }

    /**
     * Sets the value of the regimeInternacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRegimeInternacao(String value) {
        this.regimeInternacao = value;
    }

    /**
     * Gets the value of the hipotesesDiagnosticas property.
     * 
     * @return
     *     possible object is
     *     {@link CtHipoteseDiagnostica }
     *     
     */
    public CtHipoteseDiagnostica getHipotesesDiagnosticas() {
        return hipotesesDiagnosticas;
    }

    /**
     * Sets the value of the hipotesesDiagnosticas property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtHipoteseDiagnostica }
     *     
     */
    public void setHipotesesDiagnosticas(CtHipoteseDiagnostica value) {
        this.hipotesesDiagnosticas = value;
    }

    /**
     * Gets the value of the procedimentosExamesSolicitados property.
     * 
     * @return
     *     possible object is
     *     {@link CtProcedimentosSolicitados }
     *     
     */
    public CtProcedimentosSolicitados getProcedimentosExamesSolicitados() {
        return procedimentosExamesSolicitados;
    }

    /**
     * Sets the value of the procedimentosExamesSolicitados property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtProcedimentosSolicitados }
     *     
     */
    public void setProcedimentosExamesSolicitados(CtProcedimentosSolicitados value) {
        this.procedimentosExamesSolicitados = value;
    }

    /**
     * Gets the value of the opMsSolicitadas property.
     * 
     * @return
     *     possible object is
     *     {@link CtOPMSolicitadas }
     *     
     */
    public CtOPMSolicitadas getOPMsSolicitadas() {
        return opMsSolicitadas;
    }

    /**
     * Sets the value of the opMsSolicitadas property.
     * 
     * @param value
     *     allowed object is
     *     {@link CtOPMSolicitadas }
     *     
     */
    public void setOPMsSolicitadas(CtOPMSolicitadas value) {
        this.opMsSolicitadas = value;
    }

    /**
     * Gets the value of the diasSolicitados property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getDiasSolicitados() {
        return diasSolicitados;
    }

    /**
     * Sets the value of the diasSolicitados property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setDiasSolicitados(BigInteger value) {
        this.diasSolicitados = value;
    }

    /**
     * Gets the value of the dataProvavelAdmisHosp property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataProvavelAdmisHosp() {
        return dataProvavelAdmisHosp;
    }

    /**
     * Sets the value of the dataProvavelAdmisHosp property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataProvavelAdmisHosp(XMLGregorianCalendar value) {
        this.dataProvavelAdmisHosp = value;
    }

    /**
     * Gets the value of the observacao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getObservacao() {
        return observacao;
    }

    /**
     * Sets the value of the observacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setObservacao(String value) {
        this.observacao = value;
    }


    /**
     * <p>Java class for anonymous complex type.
     * 
     * <p>The following schema fragment specifies the expected content contained within this class.
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;extension base="{http://www.ans.gov.br/padroes/tiss/schemas}ct_identificacaoPrestador">
     *       &lt;sequence>
     *         &lt;element name="nomePrestador" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_nome"/>
     *       &lt;/sequence>
     *     &lt;/extension>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "nomePrestador"
    })
    public static class PrestadorSolicitado
        extends CtIdentificacaoPrestador
    {

        @XmlElement(required = true)
        protected String nomePrestador;

        /**
         * Gets the value of the nomePrestador property.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getNomePrestador() {
            return nomePrestador;
        }

        /**
         * Sets the value of the nomePrestador property.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setNomePrestador(String value) {
            this.nomePrestador = value;
        }

    }

}
