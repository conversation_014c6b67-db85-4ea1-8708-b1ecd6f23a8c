package br.com.focusts.clinicall.service.modules.tables.general.po;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.service.modules.tables.accreditation.po.TableTypeVersionPO;

@Entity
@Table(name = "occupation_tiss")
@Audited
public class OccupationTissPO extends AbstractPO<Long> {
	
    private static final long serialVersionUID = 1L;
    @Id
    @Basic(optional = false)
    @Column(name = "id")
    private Long id;
    
    @Basic(optional = false)
	@NotBlank 
	@Size(max = 10)
    private String code;
    
    @JoinColumn(name = "occupation_id", referencedColumnName = "occupation_id")
    @ManyToOne
    private OccupationPO occupation;
    
    @JoinColumn(name = "tiss_id", referencedColumnName = "id")
    @ManyToOne
    private TableTypeVersionPO tiss;

    public OccupationTissPO() {
    }

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public OccupationPO getOccupation() {
		return occupation;
	}

	public void setOccupation(OccupationPO occupation) {
		this.occupation = occupation;
	}

	public TableTypeVersionPO getTiss() {
		return tiss;
	}

	public void setTiss(TableTypeVersionPO tiss) {
		this.tiss = tiss;
	}

  
}
