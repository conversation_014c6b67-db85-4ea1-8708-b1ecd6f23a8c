package br.com.focusts.clinicall.service.modules.operational.reception.to;

import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateDeserializer;
import br.com.focusts.clinicall.service.modules.system.json.serializer.LocalDateSerializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
public class OrderHistoricPatientTO {
    private Long id;
    private String number;
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate date;
    private Long quantity;
    private String insuranceName;
    private String procedureName;
    private String performerName;
    private String companyName;
    private String procedureCode;
    private String status;
    private String state;
    private String cover;
    private Double value;
    private String token;
    private String tokenStatus;
}
