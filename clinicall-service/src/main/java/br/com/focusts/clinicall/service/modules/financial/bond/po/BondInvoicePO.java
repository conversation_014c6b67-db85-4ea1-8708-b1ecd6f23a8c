package br.com.focusts.clinicall.service.modules.financial.bond.po;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.InvoicePO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

/**
 * Notas Fiscais Relacionadas ao Titulo
 */
@Entity
@Table(name = "bond_invoice")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BondInvoicePO  extends AbstractPO<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * Id Sequencial
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "bond_invoice_id")
    private Long id;

    /**
     * Titulo
     * @see br.com.focusts.clinicall.service.modules.financial.bond.po.BondPO
     */
    @NotNull
    @JoinColumn(name = "bond_id", referencedColumnName = "bond_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private BondPO bond;

    /**
     * Nota Fiscal
     * @see br.com.focusts.clinicall.service.modules.operational.billing.po.InvoicePO
     */
    @NotNull
    @JoinColumn(name = "invoice_id", referencedColumnName = "invoice_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private InvoicePO invoice;

    @Override
    public Long getId() {
        return this.id;
    }
}
