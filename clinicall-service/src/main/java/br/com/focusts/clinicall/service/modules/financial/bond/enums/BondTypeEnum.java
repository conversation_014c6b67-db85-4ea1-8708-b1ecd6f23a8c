package br.com.focusts.clinicall.service.modules.financial.bond.enums;

import br.com.focusts.clinicall.fw.enums.DescriptionEnum;

/**
 * Tipo de Titulo
 */
public enum BondTypeEnum implements DescriptionEnum {

    /**
     * Titulo a Pagar
     */
    PAYABLE {
        public String getName() {
            return "PAYABLE";
        }

        public String getValue() {
            return "P";
        }

        public String getDescription() {
            return "Conta a Pagar";
        }
    },

    /**
     * Titulo a Receber
     */
    RECEIVABLE {

        public String getName() {
            return "RECEIVABLE";
        }

        public String getValue() {
            return "R";
        }

        public String getDescription() {
            return "Conta a Receber";
        }
    }
}
