
package br.com.focusts.clinicall.service.modules.register.accreditation.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.KitPO;
import br.com.focusts.clinicall.service.modules.register.accreditation.repository.KitRepository;


@Service
public class DefaultKitService extends AbstractCrudService<KitPO,java.lang.Long> implements KitService {

	@Autowired
	private KitRepository kitRepository;

	@Override
	public CrudRepository<KitPO,java.lang.Long> getCrudRepository() {
		return kitRepository;
	}

	
	
}
