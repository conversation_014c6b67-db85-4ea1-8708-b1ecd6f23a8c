package br.com.focusts.clinicall.service.modules.register.accreditation.repository;

import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.InsurancePO;
import br.com.focusts.clinicall.service.modules.register.accreditation.to.InsuranceResponseTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface InsuranceQueryDslRepository {

    public Page<InsuranceResponseTO> findInsuranceSimpleSearch(PageSearchTO pageSearchTO, Boolean active);

    public List<InsuranceResponseTO> findInsuranceSimpleAll();
    public Page<InsurancePO> findByNameContainingAndActiveTrue(String name,Long idUser, PageSearchTO pageSearchTO);
    public Page<InsurancePO> findByIdNotInAndNameContainingAndActiveTrue(List<Long> unacceptedInsuranceListId, String name,Long idUser, PageSearchTO pageSearchTO);
}
