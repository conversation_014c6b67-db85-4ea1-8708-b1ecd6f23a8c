package br.com.focusts.clinicall.service.modules.operational.medical.facade;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.medical.po.AttachmentPO;
import br.com.focusts.clinicall.service.modules.operational.medical.po.RequestPO;
import br.com.focusts.clinicall.service.modules.operational.medical.service.AttachmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Transactional
public class DefaultAttachmentFacade extends AbstractCrudFacade<AttachmentPO, Long> implements AttachmentFacade {

	@Autowired
	private AttachmentService attachmentService;

    @Override
	public CrudService<AttachmentPO, Long> getCrudService() { return attachmentService; }

	@Override
	public Page<AttachmentPO> findByDatedContainingByPatientId(PageSearchTO pageSearchTO, Long id) {
		return attachmentService.findByDatedContainingByPatientId(pageSearchTO, id);
	}

	@Override
	public Page<AttachmentPO> findByPatientIdAndType(Long patientId, Integer type, PageSearchTO pageSearchTO) {
		return attachmentService.findByPatientIdAndType(patientId,type,pageSearchTO);
	}

	@Override
	public Page<AttachmentPO> findByPatientIdAndTypeAndDocumentId(Long patientId, Integer type, Long documentId, PageSearchTO pageSearchTO) {
		return attachmentService.findByPatientIdAndTypeAndDocumentId(patientId,type,documentId,pageSearchTO);
	}

	@Override
	public String insertDescriptionCapture(String description, Long attachementId) {
		return attachmentService.insertDescriptionCapture(description, attachementId);
	}

	@Override
	public Boolean checkedCapture(Boolean checked, Long attachementId) {
		return attachmentService.checkedCapture(checked, attachementId);
	}


}
