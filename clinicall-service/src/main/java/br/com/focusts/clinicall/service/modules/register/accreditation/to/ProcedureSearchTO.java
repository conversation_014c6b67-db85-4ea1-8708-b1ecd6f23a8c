package br.com.focusts.clinicall.service.modules.register.accreditation.to;

import br.com.focusts.clinicall.fw.to.PageSearchTO;

public class ProcedureSearchTO {
	
	private Long insuranceId;
	
	private Long performerId;
	
	private Long professionalId;
	
	private PageSearchTO pageSearchTO;

	private Long companyId;

	public Long getInsuranceId() {
		return insuranceId;
	}

	public void setInsuranceId(Long insuranceId) {
		this.insuranceId = insuranceId;
	}

	public Long getProfessionalId() {
		return professionalId;
	}

	public void setProfessionalId(Long professionalId) {
		this.professionalId = professionalId;
	}

	public PageSearchTO getPageSearchTO() {
		return pageSearchTO;
	}

	public void setPageSearchTO(PageSearchTO pageSearchTO) {
		this.pageSearchTO = pageSearchTO;
	}

	public Long getPerformerId() {
		return performerId;
	}

	public void setPerformerId(Long performerId) {
		this.performerId = performerId;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}
}
