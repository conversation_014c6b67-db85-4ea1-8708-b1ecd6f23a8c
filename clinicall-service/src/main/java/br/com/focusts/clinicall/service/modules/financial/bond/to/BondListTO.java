package br.com.focusts.clinicall.service.modules.financial.bond.to;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BondListTO {
    private Long id;
    private String name;
    private Double value;
    private String document;
    private String companyName;
    private String historicName;
    private Short parcel;
    private Short parcelQuantity;
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate dueDate;
    private Double totalPay;

    public BondListTO() {
    }

    public BondListTO(Long id, String name, Double value, String document, String companyName, String historicName,
            Short parcel, Short parcelQuantity, LocalDate dueDate, Double totalPay) {
        this.id = id;
        this.name = name;
        this.value = value;
        this.document = document;
        this.companyName = companyName;
        this.historicName = historicName;
        this.parcel = parcel;
        this.parcelQuantity = parcelQuantity;
        this.dueDate = dueDate;
        this.totalPay = totalPay;
    }

}
