package br.com.focusts.clinicall.service.modules.register.organizational.to;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CompanyCostCenterTO {
    private Long id;
    private Long companyId;
    private String companyName;
    private String companyCnpj;
    private Long costCenterId;
    private String costCenterName;

    public CompanyCostCenterTO() {
    }

    public CompanyCostCenterTO(Long id) {
        this.id = id;
    }

    public CompanyCostCenterTO(Long id, Long companyId, String companyName, String companyCnpj, Long costCenterId, String costCenterName) {
        this.id = id;
        this.companyId = companyId;
        this.companyName = companyName;
        this.companyCnpj = companyCnpj;
        this.costCenterId = costCenterId;
        this.costCenterName = costCenterName;
    }
}
