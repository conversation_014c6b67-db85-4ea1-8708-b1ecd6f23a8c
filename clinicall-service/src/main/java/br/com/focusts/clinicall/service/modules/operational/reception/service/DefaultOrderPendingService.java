package br.com.focusts.clinicall.service.modules.operational.reception.service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import br.com.focusts.clinicall.fw.enums.DescriptionEnum;
import br.com.focusts.clinicall.fw.enums.DescriptionEnumUtils;
import br.com.focusts.clinicall.fw.exception.ApplicationException;
import br.com.focusts.clinicall.service.modules.operational.reception.enums.OrderStateEnum;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.PendingPO;
import br.com.focusts.clinicall.service.modules.operational.reception.repository.OrderRepository;
import br.com.focusts.clinicall.service.modules.operational.reception.repository.PendingRepository;
import br.com.focusts.clinicall.service.modules.register.security.repository.UserRepository;
import br.com.focusts.clinicall.service.modules.system.constants.GlobalKeyParameterConstants;
import br.com.focusts.clinicall.service.modules.system.po.ParameterPO;
import br.com.focusts.clinicall.service.modules.system.repository.ParameterRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPendingPO;
import br.com.focusts.clinicall.service.modules.operational.reception.repository.OrderPendingRepository;

@Service
public class DefaultOrderPendingService extends AbstractCrudService<OrderPendingPO, Long>
		implements OrderPendingService {

	@Autowired
	private OrderPendingRepository orderPendingRepository;

	@Autowired
	private OrderRepository orderRepository;

	@Autowired
	private ParameterRepository parameterRepository;


	@Autowired
	private PendingRepository pendingRepository;

	@Autowired
	private UserRepository userRepository;



	@Override
	public CrudRepository<OrderPendingPO, Long> getCrudRepository() {
		return orderPendingRepository;
	}

	@Override
	public List<OrderPendingPO> findByOrderId(Long orderId) {
		List<String> typeList = new ArrayList<>();
		typeList.add("C");
		typeList.add("B");
		return orderPendingRepository.findByOrder_idAndPending_typeNotIn(orderId, typeList);
	}

	@Override
	public List<OrderPendingPO> findByOrderItemId(Long orderItemId) {
		return orderPendingRepository.findByOrderItem_id(orderItemId);
	}

	@Override
	public List<OrderPendingPO> checkPendency(Long orderId) {
		return orderPendingRepository.checkPendency(orderId);
	}

	@Override
	public List<OrderPendingPO> checkPendencyOrderItem(Long orderItemId) {
		return orderPendingRepository.checkPendencyOrderItem(orderItemId);
	}

	@Override
	public OrderPendingPO save(OrderPendingPO po) {
//		var order = orderRepository.findById(po.getOrder().getId());
//		if(order.get().getState().equals(OrderStateEnum.FATURADO.getValue())
//		|| order.get().getState().equals(OrderStateEnum.BAIXADO.getValue())
//		|| order.get().getState().equals(OrderStateEnum.PRE_FATURA.getValue())
//		|| order.get().getState().equals(OrderStateEnum.PRE_FATURA_FATURADO.getValue())
//		|| order.get().getState().equals(OrderStateEnum.BAIXADO.getValue())
//		|| order.get().getState().equals(OrderStateEnum.BAIXA_GLOSA_PARCIAL.getValue())
//		|| order.get().getState().equals(OrderStateEnum.BAIXA_GLOSA_RECORRIVEL.getValue())
//		|| order.get().getState().equals(OrderStateEnum.BAIXA_GLOSA_ACATADA.getValue())
//		){
//			throw new ApplicationException("OS, " + DescriptionEnumUtils.fromValue(OrderStateEnum.class, order.get().getState()).getDescription());
//		}
		return super.save(po);
	}

	@Override
	public OrderPO addPending(OrderPO order, String stateCorrent, String context) {
		if(stateCorrent.equals(OrderStateEnum.BLOQUEADO.getValue()) && order.getState().equals(OrderStateEnum.CANCELADA.getValue())){
			throw new ApplicationException("OS Cancelada, impossivel bloquear");
		}

//		order = orderRepository.findById(order.getId()).get();
		List<OrderPendingPO> orderPendingList = new ArrayList<OrderPendingPO>();
		OrderPendingPO newOrderPending = new OrderPendingPO();
		Optional<OrderPendingPO> pendingFilterBlock = Optional.empty();
		Optional<OrderPendingPO> pendingFilterCancelation = Optional.empty();

		PendingPO pendingBlock = new PendingPO();
		PendingPO pendingCancelation = new PendingPO();

		orderPendingList = checkPendency(order.getId());

		ParameterPO parameterBlock =  parameterRepository.findByName(GlobalKeyParameterConstants.DEFAULT_BLOCK_PENDING);;
		ParameterPO parameterCancelation = parameterRepository.findByName(GlobalKeyParameterConstants.DEFAULT_GUIDE_CANCELLATION_PENDING);;


		switch (stateCorrent){
			case "B":// BLOQUEADO
				if(parameterBlock == null || parameterBlock.getValue() == null || parameterBlock.getValue().isBlank() || parameterBlock.getValue().isEmpty()) {
					throw new ApplicationException("Definir pendência para Bloqueio na tela de Pendência");
				}else{
					pendingBlock = pendingRepository.findById(Long.parseLong(parameterBlock.getValue())).get();
				}

				if(orderPendingList.isEmpty()){
					newOrderPending = createNewOrderPending(order, pendingBlock);
					save(newOrderPending);
				}else {
					var idPending = pendingBlock.getId();
					pendingFilterBlock = orderPendingList.stream().filter(opl -> opl.getPending().getId().equals(idPending)).findFirst();
					if(pendingFilterBlock.isEmpty()){
						newOrderPending = createNewOrderPending(order, pendingBlock);
						save(newOrderPending);
					}
				}
				break;
			case "C":// CANCELAMENTO
				if(parameterCancelation == null || parameterCancelation.getValue() == null || parameterCancelation.getValue().isBlank() || parameterCancelation.getValue().isEmpty()) {
					throw new ApplicationException("Definir pendência para Cancelamento de Guia na tela de Pendência");
				}else{
					pendingCancelation = pendingRepository.findById(Long.parseLong(parameterCancelation.getValue())).get();
				}

				if(orderPendingList.isEmpty()){
					newOrderPending = createNewOrderPending(order, pendingCancelation);
					save(newOrderPending);
				}else {
					var idPending = pendingCancelation.getId();
					pendingFilterCancelation = orderPendingList.stream().filter(opl -> opl.getPending().getId().equals(idPending)).findFirst();
					if(pendingFilterCancelation.isEmpty()){
						newOrderPending = createNewOrderPending(order, pendingCancelation);
						save(newOrderPending);
					}
				}
				break;

			case "N":// NORMAL
			case "P": // PENDeNTE
				var stateCurrentPO = order.getState();
				if(!orderPendingList.isEmpty()){

					if (parameterBlock != null || parameterBlock.getValue() == null || !parameterBlock.getValue().isBlank() || !parameterBlock.getValue().isEmpty()){
						pendingBlock = pendingRepository.findById(Long.parseLong(parameterBlock.getValue())).get();
						var pendingBlockId =  pendingBlock.getId();
						pendingFilterBlock = orderPendingList.stream().filter(opl -> opl.getPending().getId().equals(pendingBlockId)).findFirst();
					}

					if(parameterCancelation != null && parameterCancelation.getValue() != null && !parameterCancelation.getValue().isBlank() && !parameterCancelation.getValue().isEmpty()) {
						pendingCancelation = pendingRepository.findById(Long.parseLong(parameterCancelation.getValue())).get();
						var pendingCancelationId =  pendingCancelation.getId();
						pendingFilterCancelation = orderPendingList.stream().filter(opl -> opl.getPending().getId().equals(pendingCancelationId)).findFirst();
					}


					if (stateCurrentPO.equals(OrderStateEnum.CANCELADA.getValue())){
						if(pendingFilterCancelation.isPresent()){
							pendingFilterCancelation.get().setResolution_date(LocalDate.now());
							setContextNoteResolutionPending(pendingFilterCancelation.get(), context);
							save(pendingFilterCancelation.get());
						}
					}

					if(stateCurrentPO.equals(OrderStateEnum.BLOQUEADO.getValue())){
						if(pendingFilterBlock.isPresent()){
							pendingFilterBlock.get().setResolution_date(LocalDate.now());
							setContextNoteResolutionPending(pendingFilterBlock.get(), context);
							save(pendingFilterBlock.get());
						}
					}

				}

				if (stateCurrentPO.equals(OrderStateEnum.CANCELADA.getValue())){
					var pendingCancelationId = pendingCancelation.getId();
					var filterCancelation =  orderPendingList.stream().filter(opl -> !opl.getPending().getId().equals(pendingCancelationId)).findFirst();
					if (filterCancelation.isPresent()){
						var pendingBlockId = pendingBlock.getId();
						var filterBlock =  orderPendingList.stream().filter(opl -> opl.getPending().getId().equals(pendingBlockId)).findFirst();
						if (filterBlock.isPresent()){
							stateCorrent = OrderStateEnum.BLOQUEADO.getValue();
						}else {
							var filterNotInBlockAndCancelation =  orderPendingList.stream().filter(opl -> !opl.getPending().getId().equals(pendingBlockId) && !opl.getPending().getId().equals(pendingCancelationId) ).findFirst();
							if (filterNotInBlockAndCancelation.isPresent()){
								stateCorrent = OrderStateEnum.PENDENTE.getValue();
							}
						}
					}else {
						stateCorrent = OrderStateEnum.NORMAL.getValue();
					}
				}

				if (stateCurrentPO.equals(OrderStateEnum.BLOQUEADO.getValue())){
					var pendingBlockId = pendingBlock.getId();
					var filterBlock =  orderPendingList.stream().filter(opl -> !opl.getPending().getId().equals(pendingBlockId)).findFirst();
					if (filterBlock.isPresent()){
						stateCorrent = OrderStateEnum.PENDENTE.getValue();
					}else {
						stateCorrent = OrderStateEnum.NORMAL.getValue();
					}
				}


//				if(pendingFilterBlock.isPresent()){
//					stateCorrent = OrderStateEnum.PENDENTE.getValue();
//				}else{
//					stateCorrent = OrderStateEnum.NORMAL.getValue();
//				}

				break;

		}
		order.setState(stateCorrent);
		return order;

	}

	@Override
	public OrderPendingPO createNewOrderPending(OrderPO order, PendingPO peding) {
		OrderPendingPO newOrderPending = new OrderPendingPO();
		newOrderPending.setPending(peding);
		newOrderPending.setOrder(order);
		newOrderPending.setDate(LocalDate.now());
		newOrderPending.setNote(peding.getName());
		var user = userRepository.findById(getUserIdLogged().get());
		if(user.isPresent()){
			newOrderPending.setUser(user.get());
		}

		return newOrderPending;
	}
	private OrderPendingPO setContextNoteResolutionPending(OrderPendingPO orderPending,String context) {
		if (context != null && !context.isBlank()) {
			switch (context) {
				case "BILLING":
					orderPending.setResolutionNote("Bloqueio resolvido pela tela de faturamento");
					break;
				case "ORDER":
					orderPending.setResolutionNote("Bloqueio resolvido pela tela de atendimento");
					break;
			}
		}

		return orderPending;
	}

}
