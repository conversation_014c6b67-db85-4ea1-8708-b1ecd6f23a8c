package br.com.focusts.clinicall.service.modules.tables.tiss.to;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalTime;
@Getter
@Setter
public class OrderItemMatMedTaxTO {
    private LocalDate date;
    private LocalTime hour;
    private LocalTime hourend;
    private String code;
    private Double quantity;
    private Double valueUnitary;
    private Double valueTotal;
    private String name;
    private String measureTiss;
    private String tableType;
    private String type;

}
