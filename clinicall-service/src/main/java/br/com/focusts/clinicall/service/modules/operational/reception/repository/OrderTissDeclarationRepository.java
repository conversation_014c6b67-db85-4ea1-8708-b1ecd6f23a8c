package br.com.focusts.clinicall.service.modules.operational.reception.repository;

import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderTissDeclarationPO;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface OrderTissDeclarationRepository extends CrudRepository<OrderTissDeclarationPO, Long> {

    List<OrderTissDeclarationPO> findByOrderTiss_id(Long orderTissId);
}
