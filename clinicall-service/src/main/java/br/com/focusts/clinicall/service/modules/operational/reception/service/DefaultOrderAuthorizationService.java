
package br.com.focusts.clinicall.service.modules.operational.reception.service;

import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderPO;
import br.com.focusts.clinicall.service.modules.operational.reception.repository.OrderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import br.com.focusts.clinicall.fw.exception.ApplicationException;
import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderAuthorizationPO;
import br.com.focusts.clinicall.service.modules.operational.reception.repository.OrderAuthorizationRepository;

import java.util.Optional;


@Service
public class DefaultOrderAuthorizationService extends AbstractCrudService<OrderAuthorizationPO,java.lang.Long> implements OrderAuthorizationService {

	@Autowired
	private OrderAuthorizationRepository orderAuthorizationRepository;
	@Autowired
	private OrderRepository orderRepository;

	@Override
	public CrudRepository<OrderAuthorizationPO,java.lang.Long> getCrudRepository() {
		return orderAuthorizationRepository;
	}

	@Override
	public OrderAuthorizationPO findByOrderId(Long orderId) {
		return orderAuthorizationRepository.findByOrder_id(orderId);
	}

	@Override
	public OrderAuthorizationPO save(OrderAuthorizationPO orderAuthorizationPO) {

		if (orderAuthorizationPO.getOrder() != null){
			Optional<OrderPO> orderPO =  orderRepository.findById(orderAuthorizationPO.getOrder().getId());

			if (orderPO.isEmpty()){
				throw new ApplicationException("OS não encontrada");
			}

			if (orderAuthorizationPO.getReleaseDate().isAfter(orderPO.get().getDate())){
				throw new ApplicationException("Data da Liberação da senha não pode ser maior que a data de abertura da OS");
			}

			if (orderAuthorizationPO.getReleaseValidity().isBefore(orderAuthorizationPO.getReleaseDate())) {
				//TODO: COLOCAR NO ARQUIVO DE MENSAGENS
				throw new ApplicationException("A Data de Validade não pode ser menor que a Data de Lançamento");
			}
		}

		// TODO Auto-generated method stub
		return super.save(orderAuthorizationPO);
	}


}
