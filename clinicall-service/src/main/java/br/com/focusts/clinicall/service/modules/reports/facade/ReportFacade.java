package br.com.focusts.clinicall.service.modules.reports.facade;

import java.io.InputStream;

import br.com.focusts.clinicall.service.modules.operational.billing.to.FilterTO;
import br.com.focusts.clinicall.service.modules.operational.scheduling.to.ScheduleTO;
import br.com.focusts.clinicall.service.modules.reports.to.ReportResultTO;
import br.com.focusts.clinicall.service.modules.reports.to.ReportTO;

public interface ReportFacade {

	public InputStream generate(ReportTO report) throws Exception;

	public ReportResultTO generateGuideOrder(Long orderId) throws Exception;

	public ReportResultTO generateReportFichaPatient(Long patientId) throws Exception;
	public ReportResultTO generateReportApac(Long orderSusId) throws Exception ;

	public ReportResultTO generateReportBudget(Long budgetId) throws Exception;
	public ReportResultTO generateReportCapture(Long documentId) throws Exception;

	public ReportResultTO generateReportOsByOS(Long orderId) throws Exception ;

	public ReportResultTO generateReportCheckBilling(FilterTO filterTo, String type) throws Exception ;
    ReportResultTO generateReceiptByOS(Long orderId, String type) throws Exception;
	public ReportResultTO generateReportSheetByOS(Long orderId) throws Exception ;

	public ReportResultTO generateReportSchedulePatientSearch(ScheduleTO scheduleTO, String type) throws Exception;
	public ReportResultTO generateReportControlSheet(Long performerId) throws Exception;
	public ReportResultTO generateReportControlSheetSchedule(Long performerId, String dateStart, String dateEnd) throws Exception;
}
