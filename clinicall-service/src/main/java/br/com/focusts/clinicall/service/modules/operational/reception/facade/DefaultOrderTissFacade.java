
package br.com.focusts.clinicall.service.modules.operational.reception.facade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderTissPO;
import br.com.focusts.clinicall.service.modules.operational.reception.service.OrderTissService;

@Component
@Transactional
public class DefaultOrderTissFacade extends AbstractCrudFacade <OrderTissPO, java.lang.Long> implements OrderTissFacade  {

	@Autowired
	private OrderTissService orderTissService;

	@Override
	public CrudService<OrderTissPO,java.lang.Long> getCrudService() {
            return orderTissService;
	}

	@Override
	public OrderTissPO findByOrderId(Long orderId) {
        return orderTissService.findByOrderId(orderId);
	}        
      
}
