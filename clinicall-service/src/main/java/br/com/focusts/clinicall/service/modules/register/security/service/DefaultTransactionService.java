package br.com.focusts.clinicall.service.modules.register.security.service;

import br.com.focusts.clinicall.fw.exception.ApplicationException;
import br.com.focusts.clinicall.service.modules.system.po.TransactionFilterPO;
import br.com.focusts.clinicall.service.modules.system.repository.TransactionFilterRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.security.po.TransactionPO;
import br.com.focusts.clinicall.service.modules.register.security.repository.TransactionRepository;

import java.util.List;

@Service
public class DefaultTransactionService extends AbstractCrudService<TransactionPO, Long> implements TransactionService {

	@Autowired
	private TransactionRepository transactionRepository;

	@Autowired
	private TransactionFilterRepository transactionFilterRepository;

	@Override
	public CrudRepository<TransactionPO, Long> getCrudRepository() {
		return transactionRepository;
	}

	@Override
	public Page<TransactionPO> findByNameContainingOrCodeContainingOrViewNameContaining(PageSearchTO pageSearchTO) {

		PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage(),
				Sort.Direction.fromString(pageSearchTO.getSortDirection()), pageSearchTO.getFieldSort());

		return transactionRepository.findByNameContainingOrCodeContainingOrView_NameContaining(
				pageSearchTO.getArgument(), pageSearchTO.getArgument(), pageSearchTO.getArgument(), pageRequest);
	}

	@Override
	public List<TransactionFilterPO> findByTransaction_id(Long id) {
		var transactionFilterList = transactionFilterRepository.findByTransaction_id(id);
		if (transactionFilterList.isEmpty()) {
			throw new ApplicationException("Sem Filtros disponíveis");
		}
		return transactionFilterList;
	}

}
