package br.com.focusts.clinicall.service.modules.operational.billing.po;

import br.com.focusts.clinicall.service.modules.operational.billing.service.StrategyTransfer;
import br.com.focusts.clinicall.service.modules.operational.billing.to.TransferTO;
import br.com.focusts.clinicall.service.modules.operational.util.CalcOperational;
import br.com.focusts.clinicall.service.modules.register.organizational.enums.PerformerTypePaymentEnum;
import br.com.focusts.clinicall.service.modules.system.constants.GlobalKeyParameterConstants;

public class StrategyTransferProduction implements StrategyTransfer {

    @Override
    public TransferItemPO calcParticipation(TransferTO transferTO, Boolean receivedUco,
            Boolean discountTaxOnInsurance) {

        var orderItem = transferTO.getOrderItemTO();
        var transferItem = transferTO.getTransferItem();

        Double valueTranfer = 0.0;
        Double retention = 0.0;
        Double result = 0.0;
        Double valueFilmMed = 0.0;
        Double valueFilmPercentage = 0.0;
        Double procedureUco = orderItem.getUcoValue() != null ? orderItem.getUcoValue() : 0.0;
        Double procedureFilme = orderItem.getFilmValue() != null ? orderItem.getFilmValue() : 0.0;
        Double procedureValueTotal = orderItem.getProcedureValue();

        Double percentUcoValue = CalcOperational.calUcoOrFilmeOrProcedurePercent(procedureUco,
                orderItem.getProcedureValue(),
                GlobalKeyParameterConstants.RECEIVE_UCO);
        Double percentFilmValue = CalcOperational.calUcoOrFilmeOrProcedurePercent(procedureFilme,
                orderItem.getProcedureValue(),
                GlobalKeyParameterConstants.RECEIVE_FILM);

        valueTranfer = procedureValueTotal; // valor pago total
        Double itemValue = procedureValueTotal;

        transferItem.setUcoValue(0.0);
        if (orderItem.getUcoValue() != null && orderItem.getUcoValue() > 0) {
            var ucoValue = valueTranfer * percentUcoValue / 100;
            transferItem.setUcoValue(ucoValue); // valor do uco que o medico recebe
        }

        if (orderItem.getFilmValue() > 0
                && (orderItem.getParticipationValueFilm() == null || orderItem.getParticipationValueFilm() == 0)) {
            valueTranfer = valueTranfer - procedureFilme;
            retention += procedureFilme;
            transferItem.setFilmValue(0.0);

        } else if (orderItem.getFilmValue() > 0 && orderItem.getParticipationValueFilm() != null
                && orderItem.getParticipationValueFilm() > 0) {
            valueFilmPercentage = valueTranfer * percentFilmValue / 100;
            valueTranfer = valueTranfer - procedureFilme;
            valueFilmMed = CalcOperational.calcValueDefinitvParcicpation(procedureFilme,
                    orderItem.getParticipationValueFilm());
            transferItem.setFilmpercent(orderItem.getParticipationValueFilm());
            transferItem.setFilmValue(valueFilmPercentage);
        } else {
            transferItem.setFilmValue(0.0);
        }

        var participationValue = 0.0;

        if (orderItem.getParticipationValue() != null) {
            participationValue = orderItem.getParticipationValue();

        }
        var percentValue = CalcOperational.getPercent(
                orderItem.getParticipationType().equals("%") ? PerformerTypePaymentEnum.PERCENTAGE.getValue()
                        : PerformerTypePaymentEnum.MONEY.getValue(),
                participationValue, itemValue);
        if (Double.isNaN(percentValue)) {
            percentValue = 0.0;
        }
        result = CalcOperational.calcValueDefinitvParcicpation(percentValue, valueTranfer);
        transferItem.setValue(CalcOperational.roudingDouble(result + valueFilmMed));
        transferItem.setTax(0.0);

        if (orderItem.getTaxType() != null
                && (orderItem.getTaxType().equals("%") || orderItem.getTaxType().equals("$"))) {
            var percentTax = CalcOperational.getPercent(
                    orderItem.getTaxType().equals("%") ? PerformerTypePaymentEnum.PERCENTAGE.getValue()
                            : PerformerTypePaymentEnum.MONEY.getValue(),
                    orderItem.getTaxValue(), transferItem.getValue());
            var discontTax = CalcOperational.calcDiscount(percentTax, transferItem.getValue());
            transferItem.setValue(discontTax);
            transferItem.setTax(transferItem.getTax() + percentTax);
        }

        // TODO: Implement a subquery logic here
        // if(!orderItem.getOrderItemFeeList().isEmpty()){
        // for (var oif: orderItem.getOrderItemFeeList()){
        // if(oif.getParticipationValue() == null){
        // retention += oif.getTotal();
        // }
        // }
        // }

        // if(!orderItem.getOrderItemProductList().isEmpty()){
        // for (var oip: orderItem.getOrderItemProductList()){
        // if(oip.getParticipationValue() == null ){
        // retention += oip.getTotal();
        // }
        // }
        // }

        transferItem.setRetention(retention);

        transferItem.setPercent(percentValue);
        transferItem.setTotal(valueTranfer);
        var calcBaseValue = (transferItem.getFilmValue() + transferItem.getTotal());

        calcBaseValue = CalcOperational.calcDiscount(transferItem.getTax(), calcBaseValue);
        transferItem.setCalcBase(calcBaseValue);
        return transferItem;
    }

    @Override
    public TransferItemPO calcParticipationMatMedTax(TransferTO transferTO, Boolean discountTaxOnInsurance) {
        var orderItemFee = transferTO.getOrderItemFeeTO() != null ? transferTO.getOrderItemFeeTO() : null;
        var orderItemProduct = transferTO.getOrderItemProductTO() != null ? transferTO.getOrderItemProductTO() : null;
        var orderItemAuxiliary = transferTO.getOrderItemAuxiliaryTO() != null ? transferTO.getOrderItemAuxiliaryTO()
                : null;
        var orderItem = transferTO.getOrderItemTO();
        var participationItem = transferTO.getParticipationItemTO() != null ? transferTO.getParticipationItemTO()
                : null;
        var orderItemThirdParty = transferTO.getOrderItemThirdPartyTO() != null ? transferTO.getOrderItemThirdPartyTO()
                : null;

        Double valueTranfer = 0.0;
        Double valueMed = 0.0;
        Double participationPercentMedic = 0.0;

        Double procedureValueTotal = 0.0;
        if (orderItem != null) {
            procedureValueTotal = orderItem.getProcedureValue();
        }

        var transferItem = transferTO.getTransferItem();

        if (orderItemFee != null) {
            if (orderItemFee.getParticipationValue() != null && orderItemFee.getParticipationValue() > 0) {
                participationPercentMedic = orderItemFee.getParticipationValue();
            } else {
                participationPercentMedic = 0.0;
            }
        } else if (orderItemProduct != null) {
            if (orderItemProduct.getParticipationValue() != null && orderItemProduct.getParticipationValue() > 0) {
                participationPercentMedic = orderItemProduct.getParticipationValue();
            } else {
                participationPercentMedic = 0.0;
            }
        } else if (orderItemAuxiliary != null) {
            if (orderItemAuxiliary.getParticipationValue() != null && orderItemAuxiliary.getParticipationValue() > 0) {
                participationPercentMedic = orderItemAuxiliary.getParticipationValue();
            } else {
                participationPercentMedic = 0.0;
            }
        } else if (orderItemThirdParty != null) {
            participationPercentMedic = CalcOperational.getPercent(
                    orderItemThirdParty.getParticipationType().equals("%")
                            ? PerformerTypePaymentEnum.PERCENTAGE.getValue()
                            : PerformerTypePaymentEnum.MONEY.getValue(),
                    orderItemThirdParty.getParticipationValue(), orderItemThirdParty.getProcedureValue());
            valueTranfer = CalcOperational.roudingDouble(orderItemThirdParty.getValue());
            valueMed = CalcOperational.roudingDouble(orderItemThirdParty.getValue());
        }

        switch (transferTO.getType()) {
            case "FEE" -> {
                valueTranfer = orderItemFee.getTotal();
                valueMed = CalcOperational
                        .roudingDouble(CalcOperational.calcValueDefinitvParcicpation(participationPercentMedic,
                                valueTranfer));
            }
            case "PRODUCT" -> {
                valueTranfer = orderItemProduct.getTotal();
                valueMed = CalcOperational
                        .roudingDouble(CalcOperational.calcValueDefinitvParcicpation(participationPercentMedic,
                                valueTranfer));
            }
            case "AUXILIARY" -> {
                valueTranfer = orderItemAuxiliary.getValueTotal();
                valueMed = CalcOperational
                        .roudingDouble(CalcOperational.calcValueDefinitvParcicpation(participationPercentMedic,
                                valueTranfer));
            }
            case "REQUESTER" -> {
                valueTranfer = participationItem.getProcedureValue();
                participationPercentMedic = CalcOperational.getPercent(
                        participationItem.getParticipationType().equals("%")
                                ? PerformerTypePaymentEnum.PERCENTAGE.getValue()
                                : PerformerTypePaymentEnum.MONEY.getValue(),
                        participationItem.getParticipationValue(), valueTranfer);
                valueMed = CalcOperational
                        .roudingDouble(CalcOperational.calcValueDefinitvParcicpation(participationPercentMedic,
                                valueTranfer));
            }
        }

        if (orderItemAuxiliary != null) {
            if (orderItemAuxiliary.getTaxType() != null && !orderItemAuxiliary.getTaxType().equals("N")) {
                transferItem.setValue(
                        CalcOperational.calcDiscount(
                                orderItemAuxiliary.getTaxValue() != null ? orderItemAuxiliary.getTaxValue() : 0,
                                valueMed));
                transferItem.setTax(orderItemAuxiliary.getTaxValue() != null ? orderItemAuxiliary.getTaxValue() : 0);
            } else {
                transferItem.setTax(0.0);
                transferItem.setValue(valueMed);
            }
        } else if (orderItemThirdParty != null) {
            if (orderItemThirdParty.getTax() != null && orderItemThirdParty.getTax() > 0) {
                transferItem.setValue(
                        CalcOperational.calcDiscount(
                                orderItemThirdParty.getTax(),
                                valueMed));
                transferItem.setTax(orderItemThirdParty.getTax());
            } else {
                transferItem.setTax(0.0);
                transferItem.setValue(valueMed);
            }
        }else if (participationItem != null) {
            if (participationItem.getTaxValue() != null && participationItem.getTaxValue() > 0) {
                transferItem.setValue(
                        CalcOperational.calcDiscount(
                                participationItem.getTaxValue(),
                                valueMed));
                transferItem.setTax(participationItem.getTaxValue());
            } else {
                transferItem.setTax(0.0);
                transferItem.setValue(valueMed);
            }
        }
            else {
            transferItem.setTax(0.0);
            transferItem.setValue(valueMed);
        }

        transferItem.setPercent(participationPercentMedic);
        transferItem.setTotal(valueTranfer);
        valueTranfer = CalcOperational.calcDiscount(transferItem.getTax(), valueTranfer);
        transferItem.setCalcBase(valueTranfer);

        return transferItem;

    }

}
