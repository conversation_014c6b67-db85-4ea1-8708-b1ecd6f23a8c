
package br.com.focusts.clinicall.service.modules.operational.reception.service;

import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderItemPO;
import br.com.focusts.clinicall.service.modules.register.organizational.to.OrderItemThirdPartyTO;
import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.OrderItemThirdPartyPO;

import java.util.List;

public interface OrderItemThirdPartyService extends CrudService<OrderItemThirdPartyPO,java.lang.Long>{

    public Page<OrderItemThirdPartyTO> find(PageSearchTO pageSearchTO, Long orderItemId);

    public OrderItemThirdPartyPO save(OrderItemThirdPartyPO orderItemThirdPartyPO);

    void deleteById(List<Long> list);
}
