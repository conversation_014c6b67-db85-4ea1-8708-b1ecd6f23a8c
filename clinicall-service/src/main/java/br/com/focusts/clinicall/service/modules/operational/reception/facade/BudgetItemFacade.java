
package br.com.focusts.clinicall.service.modules.operational.reception.facade;

import br.com.focusts.clinicall.fw.facade.CrudFacade;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.BudgetItemPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.BudgetPO;
import org.springframework.data.domain.Page;

public interface BudgetItemFacade extends CrudFacade<BudgetItemPO, Long>{

    BudgetItemPO insertBudgetItem(BudgetItemPO budgetItemPO);
}

