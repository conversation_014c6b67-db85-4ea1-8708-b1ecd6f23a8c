
package br.com.focusts.clinicall.service.modules.register.organizational.service;

import org.springframework.data.domain.Page;

import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.register.organizational.po.ContactsPO;

public interface ContactsService extends CrudService<ContactsPO,java.lang.Long>{

    public Page<ContactsPO> findByNameContaining(PageSearchTO pageSearchTO);

}
