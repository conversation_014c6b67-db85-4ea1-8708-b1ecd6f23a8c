package br.com.focusts.clinicall.service.modules.operational.medical.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import br.com.focusts.clinicall.fw.service.AbstractCrudService;
import br.com.focusts.clinicall.service.modules.operational.medical.po.RequestItemPO;
import br.com.focusts.clinicall.service.modules.operational.medical.repository.RequestItemRepository;

@Service
public class DefaultRequestItemService extends AbstractCrudService<RequestItemPO, java.lang.Long> implements RequestItemService{

    @Autowired
    private RequestItemRepository requestItemRepository;

    @Override
    public CrudRepository<RequestItemPO, Long> getCrudRepository() {
    
        return requestItemRepository;
    }

    
    
    
}
