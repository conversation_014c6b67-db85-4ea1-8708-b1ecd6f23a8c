package br.com.focusts.clinicall.service.modules.operational.billing.po;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Entity
@Table(name = "transfer_filter")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TransferFilterPO extends AbstractPO<Long> {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "transfer_filter_id")
    private Long id;

    @ManyToOne(cascade = CascadeType.PERSIST)
    @JoinColumn(name = "transfer_id", referencedColumnName = "transfer_id")
    private TransferPO transfer;

    @JoinColumn(name = "filter_id", referencedColumnName = "filter_id")
    @NotNull
    @ManyToOne
    private FilterPO filter;

    private Long sequence;

    @Size(max = 250)
    private String value;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public TransferPO getTransfer() {
        return transfer;
    }

    public void setTransfer(TransferPO transfer) {
        this.transfer = transfer;
    }

    public FilterPO getFilter() {
        return filter;
    }

    public void setFilter(FilterPO filter) {
        this.filter = filter;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Long getSequence() {
        return sequence;
    }

    public void setSequence(Long sequence) {
        this.sequence = sequence;
    }

}
