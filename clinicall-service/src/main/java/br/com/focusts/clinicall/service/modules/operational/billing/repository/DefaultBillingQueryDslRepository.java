package br.com.focusts.clinicall.service.modules.operational.billing.repository;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import br.com.focusts.clinicall.service.modules.tables.accreditation.po.QGroupPO;
import com.querydsl.core.types.dsl.NumberTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Repository;

import com.querydsl.core.Tuple;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.impl.JPAQuery;

import br.com.focusts.clinicall.fw.exception.ApplicationException;
import br.com.focusts.clinicall.fw.to.PageSearchTO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.BillingPO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.FilterPO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.QBillingOrderItemPO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.QBillingPO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.QBillingPayOrderItemPO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.QBillingPayPO;
import br.com.focusts.clinicall.service.modules.operational.billing.po.QPayPO;
import br.com.focusts.clinicall.service.modules.operational.billing.to.BillingTotalizersTo;
import br.com.focusts.clinicall.service.modules.operational.billing.to.FilterTO;
import br.com.focusts.clinicall.service.modules.operational.billing.to.ForBillingOrderTO;
import br.com.focusts.clinicall.service.modules.operational.billing.to.ForBillingTO;
import br.com.focusts.clinicall.service.modules.operational.billing.to.OrderXmlBillingTO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderAuthorizationPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderItemPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderItemTissPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderPatientDetailPO;
import br.com.focusts.clinicall.service.modules.operational.reception.po.QOrderTissPO;
import br.com.focusts.clinicall.service.modules.operational.util.CalcOperational;
import br.com.focusts.clinicall.service.modules.register.accreditation.po.QProcedurePO;
import br.com.focusts.clinicall.service.modules.register.operational.po.QPatientPO;
import br.com.focusts.clinicall.service.modules.system.constants.GlobalKeyParameterConstants;
import br.com.focusts.clinicall.service.modules.system.po.QParameterPO;
import br.com.focusts.clinicall.service.modules.system.po.QParameterValuePO;
import br.com.focusts.clinicall.service.modules.system.repository.ParameterRepository;
import br.com.focusts.clinicall.service.modules.tables.general.po.QPersonPO;

@Repository
public class DefaultBillingQueryDslRepository implements BillingQueryDslRepository {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private ParameterRepository parameterRepository;

    @Override
    public Page<BillingPO> findByReleaseOrInsurance_nameStartingWithOrCover(boolean release, String insuranceName,
            String cover, PageSearchTO pageSearchTO) {
        PageRequest pageRequest = null;
        String arguments = "";

        if (pageSearchTO != null) {
            pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage());
            arguments = pageSearchTO.getArgument();
        }

        QBillingPO qBilling = QBillingPO.billingPO;
        QBillingPayPO qBillingPay = QBillingPayPO.billingPayPO;
        QBillingPayOrderItemPO qBillingPayOrderItemPO = QBillingPayOrderItemPO.billingPayOrderItemPO;
        QPayPO qPay = QPayPO.payPO;

        JPAQuery<Tuple> billingJPAQuery = new JPAQuery<>(entityManager);
        billingJPAQuery
                .select(qBilling, qBillingPayOrderItemPO.value.sum())
                .from(qBilling)
                .leftJoin(qBillingPay).on(qBillingPay.billing.eq(qBilling))
                .leftJoin(qPay).on(qPay.id.eq(qBillingPay.pay.id))
                .leftJoin(qBillingPayOrderItemPO).on(qBillingPayOrderItemPO.billingPay.eq(qBillingPay))
                .where(qBilling.release.eq(release))
                .orderBy(qBilling.dateat.desc())
                // .orderBy(qBilling.id.desc())
                .groupBy(qBilling);

        if (!arguments.isBlank()) {
            billingJPAQuery.where(qBilling.cover.eq(arguments)
                    .or(qBilling.insurance.name.startsWithIgnoreCase(arguments)));
        }
        Long total = billingJPAQuery.fetchCount();
        billingJPAQuery.limit(pageRequest.getPageSize()).offset(pageRequest.getOffset());

        var resultBillingList = billingJPAQuery.fetch();
        List<BillingPO> billingPOList = new ArrayList<>();
        for (var t : resultBillingList) {
            BillingPO billing = new BillingPO();
            billing = t.get(0, BillingPO.class);
            assert billing != null;
            billing.setTotalPayment(false);
            billing.setPartialPayment(false);
            if (t.get(1, Double.class) != null) {
                if (Objects.equals(CalcOperational.roudingDouble(t.get(1, Double.class)),
                        CalcOperational.roudingDouble(billing.getTotal()))) {
                    billing.setTotalPayment(true);
                }
                if (!Objects.equals(CalcOperational.roudingDouble(t.get(1, Double.class)),
                        CalcOperational.roudingDouble(billing.getTotal())) && t.get(1, Double.class) != null) {
                    billing.setPartialPayment(true);
                }
            }
            billingPOList.add(billing);

        }
        return new PageImpl<>(billingPOList, pageRequest, total);
    }

    public Page<OrderXmlBillingTO> findByDisticOrderBillingRealeseNew(Long billingId, Long typeGuideId,
            PageSearchTO pageSearchTO) {

        PageRequest pageRequest = PageRequest.of(pageSearchTO.getPage(), pageSearchTO.getSizePage());
        String filter = pageSearchTO.getArgument();

        QBillingOrderItemPO qBillingOrderItemPO = QBillingOrderItemPO.billingOrderItemPO;
        QOrderItemPO qOrderItemPO = QOrderItemPO.orderItemPO;
        QOrderPO qOrderPO = QOrderPO.orderPO;
        QOrderTissPO qOrderTissPO = QOrderTissPO.orderTissPO;
        QPatientPO patient = QPatientPO.patientPO;
        QPersonPO person = QPersonPO.personPO;
        QOrderAuthorizationPO orderAutorization = QOrderAuthorizationPO.orderAuthorizationPO;
        QOrderPatientDetailPO orderPatientDetail = QOrderPatientDetailPO.orderPatientDetailPO;

        JPAQuery<OrderXmlBillingTO> query = new JPAQuery<>(entityManager);

        query.select(
                Projections.bean(
                        OrderXmlBillingTO.class,
                        qOrderPO.id.as("id"),
                        person.name.as("patientName"),
                        patient.id.as("patientId"),
                        qOrderPO.date.as("date"),
                        orderAutorization.releaseDate.as("releaseDate"),
                        orderAutorization.releasePassword.as("releasePassWord"),
                        qOrderPO.number.as("number"),
                        qOrderTissPO.operatorGuideNumber.as("operatorGuideNumber"),
                        qOrderPO.value.as("total"),
                        qOrderPO.enrollment.as("enrollment")))
                .from(qOrderPO)
                .leftJoin(qOrderTissPO).on(qOrderTissPO.order.id.eq(qOrderPO.id))
                .leftJoin(patient).on(patient.id.eq(qOrderPO.patient.id))
                .leftJoin(person).on(person.id.eq(patient.person.id))
                .leftJoin(orderAutorization).on(orderAutorization.order.id.eq(qOrderPO.id));

        if (typeGuideId != 0) {
            query.where(qOrderTissPO.typeGuide.id.eq(typeGuideId));
        }

        if (!filter.isEmpty() || !filter.isBlank()) {
            query.where(qOrderPO.number.startsWithIgnoreCase(filter));
        }
        var sub = JPAExpressions.selectOne().from(qBillingOrderItemPO)
                .join(qOrderItemPO)
                .on(qOrderItemPO.id.eq(qBillingOrderItemPO.orderItem.id).and(qOrderItemPO.order.id.eq(qOrderPO.id)))
                .where(qBillingOrderItemPO.billing.id.eq(billingId).and(qBillingOrderItemPO.release.eq(false)))
                .exists();

        query.where(sub);

        query.orderBy(qOrderPO.number
                .castToNum(Integer.class)
                .coalesce(99999999)
                .asc()).limit(pageRequest.getPageSize()).offset(pageRequest.getOffset());

        long total = query.fetchCount();
        var orderList = query.fetch();

        return new PageImpl<OrderXmlBillingTO>(orderList, pageRequest, total);
    }

    public ForBillingTO findForBillingOrder(FilterTO filterTo) {

        List<String> parameterNameList = new ArrayList<>();

        parameterNameList.add(GlobalKeyParameterConstants.USAGE_TOKEN);
        parameterNameList.add(GlobalKeyParameterConstants.ALERT_TOKEN_INVALID);

        var parameterToken = parameterRepository.findValueSystemAndContext(
                parameterNameList, filterTo.getBilling().getInsurance().getId().toString());

        var parameterBilliable = parameterRepository.findByName(GlobalKeyParameterConstants.BILLABLE);

        PageRequest pageRequest = null;
        String arguments = "";
        if (filterTo.getPageSearchTO() != null) {
            pageRequest = PageRequest.of(filterTo.getPageSearchTO().getPage(),
                    filterTo.getPageSearchTO().getSizePage());
            arguments = filterTo.getPageSearchTO().getArgument();
        }

        var statusFilter = "";

        BillingPO billing = filterTo.getBilling();

        List<FilterPO> filter = filterTo.getFilterList();
        // primeira querydsl para evitar trazer guias que já estão faturadas
        JPAQuery<ForBillingOrderTO> query = new JPAQuery<ForBillingOrderTO>(entityManager);

        QOrderItemPO qOrderItemFilterDate = QOrderItemPO.orderItemPO;
        QOrderItemPO qOrderItemBillable = new QOrderItemPO("qOrderItemBillable");
        QOrderItemPO qOrderItemRelease = new QOrderItemPO("qOrderItemRelease");
        QOrderItemPO qOrderItemToken = new QOrderItemPO("qOrderItemToken");
        QOrderItemPO qOrderItemGroup = new QOrderItemPO("qOrderItemGroup");
        QProcedurePO qProcedureGroup = new QProcedurePO("qProcedureGroup");
        QGroupPO qGroup = QGroupPO.groupPO;
        QOrderItemTissPO qOrderItemTiss = QOrderItemTissPO.orderItemTissPO;
        QProcedurePO qProcedurePO = QProcedurePO.procedurePO;
        QBillingOrderItemPO qBillingOrderItemPO = QBillingOrderItemPO.billingOrderItemPO;
        QOrderPO qOrderPO = QOrderPO.orderPO;
        QOrderTissPO qOrderTissPO = QOrderTissPO.orderTissPO;
        QPatientPO qPatientPO = QPatientPO.patientPO;
        QPersonPO qPersonPatient = new QPersonPO("qPersonPatient");

        QOrderAuthorizationPO qOrderAuthorizationPO = QOrderAuthorizationPO.orderAuthorizationPO;

        QParameterPO parameterPO = QParameterPO.parameterPO;
        QParameterValuePO parameterValuePO = QParameterValuePO.parameterValuePO;

        BooleanExpression parameterCondition = parameterValuePO.value.eq("1")
                .or(parameterValuePO.value.isNull());

        BooleanExpression subQueryToken = JPAExpressions.selectOne().from(qOrderItemToken)
                .leftJoin(qOrderItemTiss).on(qOrderItemTiss.orderItem.id.eq(qOrderItemToken.id))
                .where(qOrderItemToken.order.id.eq(qOrderPO.id).and(qOrderItemTiss.token.isNull())).exists();

        query.from(qOrderPO)
                .join(qOrderTissPO).on(qOrderTissPO.order.id.eq(qOrderPO.id))
                .leftJoin(qOrderAuthorizationPO).on(qOrderAuthorizationPO.order.id.eq(qOrderPO.id))
                .join(qPatientPO).on(qPatientPO.id.eq(qOrderPO.patient.id))
                .join(qPersonPatient).on(qPersonPatient.id.eq(qPatientPO.person.id))
                .where(qOrderPO.insurance.id.eq(billing.getInsurance().getId()));

        if (billing.getAgreement() != null) {
            query.where(qOrderPO.agreement.eq(billing.getAgreement()));
        }

        if (arguments != null && !arguments.isBlank()) {
            query.where(qPersonPatient.name.startsWithIgnoreCase(arguments)
                    .or(qOrderPO.number.startsWithIgnoreCase(arguments))
                    .or(qOrderAuthorizationPO.releasePassword.startsWithIgnoreCase(arguments)));
        }

        var billingAllUnits = filter.stream().filter(f -> f.getKey().equals("BILLING_ALL_UNITS")).findFirst();

        if ((billingAllUnits.isPresent() && billingAllUnits.get().getValue().equals("false"))
                || billingAllUnits.isEmpty()) {
            if (billing.getCompanyCostCenter() != null) {
                query.where(qOrderPO.companyCostCenter.id.eq(billing.getCompanyCostCenter().getId()));
            } else {
                query.where(qOrderPO.companyCostCenter.company.id.eq(billing.getCompany().getId()));
            }
        }

        // verifica os filtros vindo do front para montar a query!;
        for (int i = 0; i < filter.size(); i++) {

            switch (filter.get(i).getKey()) {
                case "Status":
                    var status = filter.get(i).getValue();

                    switch (status) {
                        // Aberta
                        case "1":
                            query.where(qOrderPO.status.eq(filter.get(i).getValue()));
                            break;

                        // Fechada
                        case "2":
                            query.where(qOrderPO.status.eq(filter.get(i).getValue()));
                            break;

                        // Normalcontacts.contactList
                        case "3":
                            query.where(qOrderPO.state.eq("N"));
                            query.where(qOrderPO.status.eq("2"));
                            break;

                        // Bloqueada
                        case "4":
                            query.where(qOrderPO.state.eq("B"));
                            break;

                        // pendente
                        case "5":
                            query.where(qOrderPO.state.eq("P"));
                            break;

                        // todas
                        case "6":
                            break;
                        // cancelada
                        case "7":
                            query.where(qOrderPO.state.eq("C"));
                            break;

                        default:
                            break;
                    }

                    break;
                // Atribui a qury todas as guias lançada até a data informada
                case "Data Limite":
                    if (filter.get(i).getValue().equalsIgnoreCase("Invalid date")) {
                        throw new ApplicationException("Período informado inválido");

                    }
                    var dateLime = LocalDate.parse(filter.get(i).getValue());
                    query.where(qOrderPO.date.loe(dateLime))
                            .where(JPAExpressions.selectOne().from(qOrderItemFilterDate)
                                    .where(qOrderItemFilterDate.order.id.eq(qOrderPO.id))
                                    .groupBy(qOrderItemFilterDate.order.id)
                                    .having(qOrderItemFilterDate.date.max().loe(dateLime)).exists());
                    break;
                // atribui a query um periodo;
                case "Periodo":
                    var date = filter.get(i).getValue().split(",");
                    if (date[0].equals("Invalid date") || date[1].equals("Invalid date")) {
                        throw new ApplicationException("Período informado inválido");
                    }
                    var start = LocalDate.parse(date[0].trim());
                    var end = LocalDate.parse(date[1].trim());
                    query.where(qOrderPO.date.between(start, end))
                            .where(JPAExpressions.selectOne().from(qOrderItemFilterDate)
                                    .where(qOrderItemFilterDate.order.id.eq(qOrderPO.id))
                                    .groupBy(qOrderItemFilterDate.order.id)
                                    .having(qOrderItemFilterDate.date.max().loe(end)).exists());
                    break;
                // atribui a query um paciente;
                case "Paciente":
                    query.where(qOrderPO.patient.id.in(Integer.parseInt(filter.get(i).getValue())));
                    break;
                case "GROUP":
                    query.where(JPAExpressions.selectOne().from(qOrderItemGroup)
                            .leftJoin(qProcedureGroup).on(qProcedureGroup.id.eq(qOrderItemGroup.procedure.id))
                            .where(qOrderItemGroup.order.id.eq(qOrderPO.id))
                            .where(qProcedureGroup.group.id.in(Integer.parseInt(filter.get(i).getValue()))).exists());
                    break;
                // Atribui a qury tipo de guia
                case "Tipo de Guia":
                    query.where(qOrderTissPO.typeGuide.id.eq(Long.parseLong(filter.get(i).getValue())));
                    break;
                default:
                    break;
            }
        }
        if (filterTo.getOrderReleaseIdList() != null && !filterTo.getOrderReleaseIdList().isEmpty()) {
            query.where(qOrderPO.id.notIn(filterTo.getOrderReleaseIdList()));
        }

        query.where(JPAExpressions.selectOne().from(qOrderItemBillable)
                .leftJoin(parameterValuePO)
                .on(parameterValuePO.parameter.id.eq(parameterBilliable.getId())
                        .and(parameterValuePO.key.eq(qOrderItemBillable.procedure.id.stringValue())))
                .where(parameterCondition.and(qOrderItemBillable.order.id.eq(qOrderPO.id))).exists());

        query.where(JPAExpressions.selectOne().from(qBillingOrderItemPO)
                .join(qOrderItemRelease)
                .on(qOrderItemRelease.id.eq(qBillingOrderItemPO.orderItem.id)
                        .and(qOrderItemRelease.order.id.eq(qOrderPO.id)))
                .where(qBillingOrderItemPO.release.eq(false))
                .notExists());

        Double total = 0.0;
        Long totalItems = 0L;
        var result = query.select(qOrderPO.value.sum().coalesce(0.0), qOrderPO.id.count()).fetch();
        for (Tuple tuple : result) {
            total = tuple.get(0, Double.class);
            totalItems = tuple.get(1, Long.class);
        }

        if (filterTo.getPageSearchTO().getFieldSort().equals("number")) {
            if (filterTo.getPageSearchTO().getSortDirection().equals("asc")) {
                query.orderBy(qOrderPO.number.castToNum(Integer.class).coalesce(Expressions.constant(99999999))
                        .asc());
            } else {
                query.orderBy(qOrderPO.number.castToNum(Integer.class).coalesce(Expressions.constant(99999999))
                        .desc());
            }
        } else {
            if (filterTo.getPageSearchTO().getSortDirection().equals("asc")) {
                query.orderBy(qPersonPatient.name.asc());
            } else {
                query.orderBy(qPersonPatient.name.desc());
            }
        }

        if (pageRequest != null) {
            query.limit(pageRequest.getPageSize()).offset(pageRequest.getOffset());
        }
        List<Expression<?>> select = new ArrayList<>();
        select.add(qOrderPO.id);
        select.add(qOrderPO.number);
        select.add(qOrderPO.date);
        select.add(qOrderPO.state);
        select.add(qOrderPO.status);
        select.add(qOrderPO.value);
        select.add(qOrderAuthorizationPO.releasePassword.as("releasePassWord"));
        select.add(qPersonPatient.name.as("patientName"));
        select.add(qPatientPO.id.as("patientId"));
        if (!parameterToken.isEmpty()) {
            var parameterUsageToken = parameterToken.stream()
                    .filter(p -> p.getName().equals(GlobalKeyParameterConstants.USAGE_TOKEN)).findFirst();
            var parameterAlertToken = parameterToken.stream()
                    .filter(p -> p.getName().equals(GlobalKeyParameterConstants.ALERT_TOKEN_INVALID)).findFirst();
            if ((parameterUsageToken.isPresent() && parameterUsageToken.get().getValue().equals("1"))
                    && (parameterAlertToken.isPresent() && parameterAlertToken.get().getValue().equals("1"))) {
                select.add(ExpressionUtils.as(subQueryToken, "isInCompletToken"));
            }

        }

        query.select(Projections.bean(ForBillingOrderTO.class, select.toArray(new Expression<?>[0])));
        var orderList = query.fetch();
        if (orderList.isEmpty()) {
            throw new ApplicationException("204", "Sem registro para o filtro escolhido.");
        }
        ForBillingTO forBilling = new ForBillingTO();
        forBilling.setOrderList(new PageImpl<ForBillingOrderTO>(orderList, pageRequest, totalItems));
        forBilling.setTotalizers(new BillingTotalizersTo());
        forBilling.getTotalizers().setQuantity(totalItems.intValue());
        forBilling.getTotalizers().setTotal(total);
        return forBilling;

    }

}
