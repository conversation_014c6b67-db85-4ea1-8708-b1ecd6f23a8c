package br.com.focusts.clinicall.service.modules.tables.general.po;

import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import br.com.focusts.clinicall.fw.po.AbstractPO;

@Entity
@Audited
@Table(name = "firm")
public class FirmPO extends AbstractPO<Long>{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "firm_id")
	private Long id;
	
	@Basic(optional = false)
	@NotBlank
	@Valid
	@Size(max = 255)
	private String name;
	 
	@Basic(optional = false)
	@NotBlank
	@Valid
	@Column(name = "fullname")
	@Size(max = 255)
	private String fullName;
	
	@JoinColumn(name = "registry_type_id", referencedColumnName = "registry_type_id")
	@ManyToOne
	@Valid
	@Basic(optional = false)
	private RegistryTypePO registryType;
	
	@JoinColumn(name = "address_id", referencedColumnName = "address_id")
	@ManyToOne(cascade = CascadeType.ALL)
	private AddressPO address;

	@JoinColumn(name = "contact_list_id")
    @ManyToOne(cascade = { CascadeType.ALL, CascadeType.DETACH, CascadeType.MERGE })
    private ContactListPO contactList;
	
	@Basic(optional = false)
	@NotBlank
	@Valid
	@Size(max = 15)
	private String registry;
	
	@Valid
	@Size(max = 7)
	private String cnes;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	public RegistryTypePO getRegistryType() {
		return registryType;
	}

	public void setRegistryType(RegistryTypePO registryType) {
		this.registryType = registryType;
	}

	public AddressPO getAddress() {
		return address;
	}

	public void setAddress(AddressPO address) {
		this.address = address;
	}

	public String getRegistry() {
		return registry;
	}

	public void setRegistry(String registry) {
		this.registry = registry;
	}

	public String getCnes() {
		return cnes;
	}

	public void setCnes(String cnes) {
		this.cnes = cnes;
	}

	public ContactListPO getContactList() {
		return contactList;
	}

	public void setContactList(ContactListPO contactList) {
		this.contactList = contactList;
	}
	
	
}
