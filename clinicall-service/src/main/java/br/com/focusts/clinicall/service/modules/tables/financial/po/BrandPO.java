package br.com.focusts.clinicall.service.modules.tables.financial.po;

import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import br.com.focusts.clinicall.fw.po.AbstractPO;
import br.com.focusts.clinicall.service.modules.system.po.ImagePO;
import org.hibernate.envers.NotAudited;


@Entity
@Table(name = "brand")
public class BrandPO extends AbstractPO<Long> {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "brand_id")
	private Long id;
	
	@NotBlank
	@Size(max = 250)
	private String name;
	
    @JoinColumn(name = "image_id", referencedColumnName = "image_id" )
    @ManyToOne(cascade = CascadeType.PERSIST)
	@NotAudited
    private ImagePO image;
    
	@NotNull
	private Boolean active;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public ImagePO getImage() {
		return image;
	}

	public void setImage(ImagePO image) {
		this.image = image;
	}

	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}
	
	
}
