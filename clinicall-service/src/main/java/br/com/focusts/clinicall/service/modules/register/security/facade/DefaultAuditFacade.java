
package br.com.focusts.clinicall.service.modules.register.security.facade;


import java.util.LinkedHashMap;
import java.util.List;

import br.com.focusts.clinicall.service.modules.system.constants.GlobalKeyParameterConstants;
import br.com.focusts.clinicall.service.modules.system.service.ParameterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import br.com.focusts.clinicall.fw.facade.AbstractCrudFacade;
import br.com.focusts.clinicall.fw.service.CrudService;
import br.com.focusts.clinicall.service.modules.register.security.po.AuditPO;
import br.com.focusts.clinicall.service.modules.register.security.service.AuditService;
import br.com.focusts.clinicall.service.modules.register.security.to.AuditFilterTO;

@Component
@Transactional
public class DefaultAuditFacade extends AbstractCrudFacade<AuditPO, Long> implements AuditFacade {

    @Autowired
    private AuditService auditService;

    @Autowired
    private ParameterService parameterService;

    @Override
    public CrudService<AuditPO, Long> getCrudService() {
        return auditService;
    }

    @Override
    public List<String> listColumnsNameFromTable(String table) throws Exception {
        return auditService.listColumnsNameFromTable(table, getDataBaseName());
    }

    @Override
    public List<String> listTablesName() throws Exception {
        return auditService.listTablesName(getDataBaseName());
    }

    @Override
    public List<LinkedHashMap<String, String>> find(AuditFilterTO auditFilterTO) throws Exception {
        return auditService.find(auditFilterTO,getDataBaseName());
    }

    private String getDataBaseName() {
        String database = "";
        database = auditService.dataBaseName();
        return database;
    }

}
