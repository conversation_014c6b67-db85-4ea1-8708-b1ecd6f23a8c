package br.com.focusts.clinicall.service.modules.partners.to.nuclearisTO;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class NuclearisAgendamentoTO {
    private NuclearisPatientTO paciente;
    private NuclearisPlanoTO plano;
    private NuclearisSolicitanteTO prestadorSolicitante;
    private Long codigoExameTerceiro;
    private String cdAgendamentoNuclearis;
    private String chaveAgendamento;
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dataHoraAgendamento;
    private String situacaoAgendamento;
    private String cnpjInstituicao;
    private NuclearisUsuarioTO usuario;
    private String observacao;
    private String qtdProcedimentos;
    private String tipoGuia;
    private String instituicaoSolicitante;
    private String hipoteseDiagnostica;
    private String nomeExameTerceiro;
    private String numeroOS;
    private String valorDoExame;
    private String desconto;
    private String peso;
    private String altura;
    private String cdAgendamentoIntegracao;
    private String cdAgendamentoCancelamento;

}